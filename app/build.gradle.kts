
import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import java.io.FileInputStream
import java.util.Properties

plugins {
    id("com.android.application")
    id("com.google.gms.google-services")
    id("com.google.firebase.firebase-perf")
    id("com.google.firebase.crashlytics")
    id("kotlin-android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
    id("androidx.navigation.safeargs")
    id("applovin-quality-service")
    id("org.jetbrains.kotlin.plugin.compose")
    id("com.google.devtools.ksp")
}

val buildPropertiesFile = rootProject.file("build.properties")
val buildLocalPropertiesFile = rootProject.file("localbuild.properties")

val buildProperties = Properties().apply {
    load(FileInputStream(buildPropertiesFile))
}
val buildLocalProperties = Properties().apply {
    if (buildLocalPropertiesFile.exists()) {
        load(FileInputStream(buildLocalPropertiesFile))
    }
}

android {
    namespace = "com.duaag.android"
    compileSdk = 34

    signingConfigs {
        val keystorePropertiesFile = rootProject.file("keystore.properties")
        val keystoreProperties = Properties().apply {
            load(FileInputStream(keystorePropertiesFile))
        }

        create("duaProdRelease") {
            storeFile = file(keystoreProperties["duaStoreFile"] as String)
            storePassword = keystoreProperties["duaStorePassword"] as String
            keyPassword = keystoreProperties["duaKeyPassword"] as String
            keyAlias = keystoreProperties["duaKeyAlias"] as String
        }

        create("ruaProdRelease") {
            storeFile = file(keystoreProperties["ruaStoreFile"] as String)
            storePassword = keystoreProperties["ruaStorePassword"] as String
            keyPassword = keystoreProperties["ruaKeyPassword"] as String
            keyAlias = keystoreProperties["ruaKeyAlias"] as String
        }
    }

    defaultConfig {
        applicationId = "com.duaag.android"
        minSdk = 24
        targetSdk = 34
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        signingConfig = signingConfigs.getByName("duaProdRelease")
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")

            the<CrashlyticsExtension>().mappingFileUploadEnabled = true
        }

        debug {
            the<CrashlyticsExtension>().mappingFileUploadEnabled = false
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    flavorDimensions.add("app")
    flavorDimensions.add("environment")

    productFlavors {
        create("dua") {
            dimension = "app"
            applicationId = "com.duaag.android"
            versionCode = 169
            versionName = "4.12.0"
        }

        create("rua") {
            dimension = "app"
            applicationId = "ro.rua.android"
            versionCode = 16
            versionName = "1.3.0"
        }

        create("dev") {
            dimension = "environment"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
        }

        create("qa") {
            dimension = "environment"
            applicationIdSuffix = ".qa"
            versionNameSuffix = "-qa"
        }

        create("prod") {
            dimension = "environment"
        }
    }

    buildFeatures {
        compose = true
        dataBinding = true
        viewBinding = true
        buildConfig = true
    }

    packaging {
        jniLibs {
            useLegacyPackaging = true

            //Rive
            jniLibs.pickFirsts.add("lib/x86/libc++_shared.so")
            jniLibs.pickFirsts.add("lib/x86_64/libc++_shared.so")
            jniLibs.pickFirsts.add("lib/armeabi-v7a/libc++_shared.so")
            jniLibs.pickFirsts.add("lib/arm64-v8a/libc++_shared.so")
        }
    }

    lint {
        abortOnError = false
        checkReleaseBuilds = false
    }

    applicationVariants.all {
        val flavors = productFlavors
        val app = flavors[0].name
        val environment = flavors[1].name
        val buildConfigKey = "${app}_${environment}"

        buildConfigField("String", "MAIN_API", buildProperties["${buildConfigKey}_MAIN_API"] as String)
        buildConfigField("String", "CHAT_WEB_SOCKET_URL", buildProperties["${buildConfigKey}_CHAT_WEB_SOCKET_URL"] as String)
        buildConfigField("String", "S3_URL", buildProperties["${buildConfigKey}_S3_URL"] as String)
        buildConfigField("String", "IOS_ID", buildProperties["${buildConfigKey}_IOS_ID"] as String)
        buildConfigField("int", "DATABASE_VERSION", buildProperties["${app}_DATABASE_VERSION"] as String)
        buildConfigField("int", "CLEVERTAP_USER_PROPERTIES_VERSION", buildProperties["${app}_CLEVERTAP_USER_PROPERTIES_VERSION"] as String)
        buildConfigField("String[]", "LANGUAGE_SUPPORT", buildProperties["${app}_LANGUAGE_SUPPORT"] as String)
        buildConfigField("String", "GIPHY_API_KEY", System.getenv("GIPHY_API_KEY")
                ?: buildLocalProperties["${app}_GIPHY_API_KEY"] as String)
        buildConfigField("String", "INVITATION_LINK", buildProperties["${app}_INVITATION_LINK"] as String)
        buildConfigField("String", "DOMAIN_URI_PREFIX", buildProperties["${app}_DOMAIN_URI_PREFIX"] as String)
        buildConfigField("int", "DYNAMIC_LINKS_ANDROID_MINIMUM_VERSION", buildProperties["${app}_DYNAMIC_LINKS_ANDROID_MINIMUM_VERSION"] as String)
        buildConfigField("String", "DYNAMIC_LINKS_IOS_MINIMUM_VERSION", buildProperties["${app}_DYNAMIC_LINKS_IOS_MINIMUM_VERSION"] as String)
        buildConfigField("String", "DYNAMIC_LINKS_APP_STORE_ID", buildProperties["${app}_DYNAMIC_LINKS_APP_STORE_ID"] as String)
        buildConfigField("String", "DYNAMIC_LINKS_IMAGE_URL", buildProperties["${app}_DYNAMIC_LINKS_IMAGE_URL"] as String)
        buildConfigField("String", "INSTAGRAM_APP_ID", System.getenv("INSTAGRAM_APP_ID")
            ?: buildLocalProperties["${app}_INSTAGRAM_APP_ID"] as String)

        buildConfigField("String", "UXCAM_API_KEY", System.getenv("UXCAM_API_KEY")
            ?: buildLocalProperties["${app}_${environment}_UXCAM_API_KEY"] as String)

        buildConfigField("String", "FINGERPRINT_API_KEY", System.getenv("FINGERPRINT_API_KEY")
            ?: buildLocalProperties["${app}_${environment}_FINGERPRINT_API_KEY"] as String)

        buildConfigField("String", "GOOGLE_WEB_CLIENT_ID", System.getenv("GOOGLE_WEB_CLIENT_ID")
            ?: buildLocalProperties["${app}_${environment}_GOOGLE_WEB_CLIENT_ID"] as String)


        buildConfigField("String", "INSTAGRAM_REDIRECT_URI", buildProperties["${app}_INSTAGRAM_REDIRECT_URI"] as String)
        buildConfigField("int", "FOR_UPDATE_VERSION_CODE", buildProperties["${app}_FOR_UPDATE_VERSION_CODE"] as String)
        resValue("string", "app_name", buildProperties["${app}_APP_NAME"] as String)
        resValue("string", "app_label", buildProperties["${app}_${environment}_APP_LABEL"] as String)
        resValue("string", "ad_mob_app_id", System.getenv("AD_MOB_APP_ID") ?: buildLocalProperties["${app}_AD_MOB_APP_ID"] as String)


        resValue("string", "applovin_sdk_key", System.getenv("APPLOVIN_SDK_KEY")
            ?: buildLocalProperties["${app}_APPLOVIN_SDK_KEY"] as String)
        resValue("string", "applovin_unit_id_card", System.getenv("APPLOVIN_UNIT_ID_CARD")
            ?: buildLocalProperties["${app}_APPLOVIN_UNIT_ID_CARD"] as String)

        resValue("string", "applovin_unit_id_chat", System.getenv("APPLOVIN_UNIT_ID_CHAT")
            ?: buildLocalProperties["${app}_${environment}_APPLOVIN_UNIT_ID_CHAT"] as String)

        resValue("string", "applovin_unit_id_rewarded", System.getenv("APPLOVIN_UNIT_ID_REWARDED")
            ?: buildLocalProperties["${app}_${environment}_APPLOVIN_UNIT_ID_REWARDED"] as String)

        resValue("string", "clevertap_account_id", System.getenv("CLEVERTAP_ACCOUNT_ID")
            ?: buildLocalProperties["${app}_${environment}_CLEVERTAP_ACCOUNT_ID"] as String)
        resValue("string", "clevertap_token", System.getenv("CLEVERTAP_TOKEN")
            ?: buildLocalProperties["${app}_${environment}_CLEVERTAP_TOKEN"] as String)

        resValue("string", "maps_api_key", System.getenv("MAPS_API_KEY")
            ?: buildLocalProperties["${app}_MAPS_API_KEY"] as String)


        resValue("string", "appsflyer_key", System.getenv("APPSFLYER_KEY")
            ?: buildLocalProperties["${app}_APPSFLYER_KEY"] as String)

        resValue("string", "ply_console_public_key", System.getenv("PLY_CONSOLE_PUBLIC_KEY")
            ?: buildLocalProperties["${app}_PLY_CONSOLE_PUBLIC_KEY"] as String)

        resValue("string", "mapbox_access_token", System.getenv("MAPBOX_ACCESS_TOKEN")
            ?: buildLocalProperties["${app}_MAPBOX_ACCESS_TOKEN"] as String)

        resValue("string", "purchasely_key", System.getenv("PURCHASELY_KEY")
            ?: buildLocalProperties["${app}_PURCHASELY_KEY"] as String)


        resValue("string", "default_country_code", buildProperties["${app}_DEFAULT_COUNTRY_CODE"] as String)
        resValue("string", "dynamic_link_host", buildProperties["${app}_DYNAMIC_LINK_HOST"] as String)
        resValue("string", "one_link_dynamic_link_host", buildProperties["${app}_ONE_LINK_DYNAMIC_LINK_HOST"] as String)

        buildConfigField("boolean", "IS_SUPPORTED_MARKETPLACE", buildProperties["${app}_IS_SUPPORTED_MARKETPLACE"] as String)
        buildConfigField("String[]", "SOCIAL_MEDIA_SUPPORT", buildProperties["${app}_SOCIAL_MEDIA_SUPPORT"] as String)
        buildConfigField("boolean", "IS_FRIENDSHIP_ENABLED", buildProperties["${app}_IS_FRIENDSHIP_ENABLED"] as String)
        buildConfigField("int", "DAYS_OF_TRIAL", buildProperties["${app}_DAYS_OF_TRIAL"] as String)
        buildConfigField("String", "ALBANIAN_LANGUAGE", buildProperties["ALBANIAN_LANGUAGE"] as String)
        buildConfigField("String[]", "ALBANIAN_COUNTRIES_CODE", buildProperties["ALBANIAN_COUNTRIES_CODE"] as String)
        buildConfigField("String[]", "SKIPPED_ORIGIN", buildProperties["SKIPPED_ORIGIN"] as String)
        buildConfigField("String", "SSL_PINNING_BASE_URL", buildProperties["${app}_${environment}_SSL_PINNING_BASE_URL"] as String)
        buildConfigField("String", "RE_CAPTCHA_API_KEY", System.getenv("RE_CAPTCHA_API_KEY")
            ?: buildLocalProperties["${app}_${environment}_RE_CAPTCHA_API_KEY"] as String)

        mergedFlavor.manifestPlaceholders["file_provider"] = buildProperties["${app}_${environment}_FILE_PROVIDER"] as String

        // Strings for specific platform
        buildConfigField("String", "LOVE", buildProperties["${app}_LOVE"] as String)
        buildConfigField("String", "PRIVACY_POLICY_LINK_KEY", buildProperties["${app}_PRIVACY_POLICY_LINK_KEY"] as String)
        buildConfigField("String", "VIDEO_HELP_LINK_KEY", buildProperties["${app}_VIDEO_HELP_LINK_KEY"] as String)
        buildConfigField("String", "LOVE_STORY_LINK_KEY", buildProperties["${app}_LOVE_STORY_LINK_KEY"] as String)
        buildConfigField("String", "TERMS_AND_CONDITIONS_LINK_KEY", buildProperties["${app}_TERMS_AND_CONDITIONS_LINK_KEY"] as String)
        buildConfigField("String", "ABOUT_DUA_LINK_KEY", buildProperties["${app}_ABOUT_DUA_LINK_KEY"] as String)
        buildConfigField("String", "DEFAULT_NATIONALITY_KEY", buildProperties["${app}_DEFAULT_NATIONALITY_KEY"] as String)
        buildConfigField("String", "DEFAULT_NATIONALITY_1_KEY", buildProperties["${app}_DEFAULT_NATIONALITY_1_KEY"] as String)
        buildConfigField("String", "DEFAULT_CAPITAL_KEY", buildProperties["${app}_DEFAULT_CAPITAL_KEY"] as String)
        buildConfigField("String", "FAQ_KEY", buildProperties["${app}_FAQ_KEY"] as String)
        buildConfigField("String", "SAFETY_TIPS_KEY", buildProperties["${app}_SAFETY_TIPS_KEY"] as String)
        buildConfigField("String", "SUBMIT_REQUEST_KEY", buildProperties["${app}_SUBMIT_REQUEST_KEY"] as String)
        buildConfigField("String", "HELP_CENTER_LINK", buildProperties["${app}_HELP_CENTER_LINK"] as String)

        applovin {
            apiKey = (System.getenv("APPLOVIN_AD_REVIEW_KEY") ?: buildLocalProperties["${app}_APPLOVIN_AD_REVIEW_KEY"]) as String
        }
    }
}

dependencies {
    val composeBom = platform("androidx.compose:compose-bom:2024.06.00")
    val daggerVersion = "2.47"
    val navVersion = "2.8.5"
    val cameraxVersion = "1.0.1"
    val cameraViewVersion = "1.0.0-alpha21"
    val roomVersion = "2.6.1"
    val lifecycleVersion = "2.6.2"
    val workVersion = "2.9.0"
    val applovinVersion = "12.6.0"
    val umpVersion = "2.1.0"
    val adMobAdapterVersion = "********"
    val metaAdapterVersion = "********"
    val mintegralAdapterVersion = "*********"
    val ironsourceAdapterVersion = "*******.0"
    val balloonVersion = "1.4.0"
    val constraintLayoutVersion = "2.1.2"
    val glideVersion = "4.13.1"
    val glideCompilerVersion = "4.13.1"
    val glideTransformationsVersion = "4.3.0"
    val okhttpVersion = "4.10.0"
    val browserVersion = "1.4.0"
    val recyclerViewVersion = "1.3.2"
    val materialVersion = "1.9.0"
    val appCompatVersion = "1.6.1"
    val coroutinesVersion = "1.7.3"
    val preferenceVersion = "1.2.1"
    val twilioVideoVersion = "7.6.1"
    val twilioAudioSwitchVersion = "1.1.8"
    val gsonVersion = "2.9.0"
    val gsonConverterVersion = "2.9.0"
    val inAppReviewVersion = "2.0.1"
    val inAppUpdateVersion = "2.1.0"
    val uiCropVersion = "2.2.8"
    val espressoVersion = "3.4.0"
    val testRunnerVersion = "1.4.0"
    val webSocketVersion = "1.5.2"
    val reCaptchaVersion = "17.0.1"
    val facebookAndroidVersion = "16.0.0"
    val lottieVersion = "5.1.1"
    val googleServicesAuthVersion = "21.2.0"
    val servicesAuthApiPhoneVersion = "18.1.0"
    val appsflyerVersion = "6.16.2"
    val appsflyerPurchaseConnector = "2.0.1"
    val purchaselyVersion = "5.0.4"
    val flexboxVersion = "3.0.0"
    val clevertapVersion = "6.2.1"
    val playServicesMapsVersion = "18.2.0"
    val playServicesLocationVersion = "21.3.0"
    val uxCamVersion = "3.6.39"
    val mapboxVersion = "11.3.1"
    val adsIdentifier = "18.1.0"
    val timberVersion = "5.0.1"
    val blurry = "4.0.1"
    val installReferrer = "2.2"
    val fingerprintProVersion = "2.7.0"
    val credentialsVersion = "1.3.0"
    val riveVersion = "9.6.5"



    // Internal Projects
    implementation(project(":cardstackview"))
    implementation(project(":countrycodepicker"))

    // File Tree
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    // Testing
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test:runner:$testRunnerVersion")
    androidTestImplementation("androidx.test.espresso:espresso-core:$espressoVersion")
    testImplementation("org.hamcrest:hamcrest-all:1.3")


    // Jetpack Compose
    implementation(composeBom)
    androidTestImplementation(composeBom)
    implementation("androidx.compose.material3:material3")
    implementation("androidx.compose.ui:ui-tooling-preview")
    debugImplementation("androidx.compose.ui:ui-tooling")
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    debugImplementation("androidx.compose.ui:ui-test-manifest")
    implementation("androidx.lifecycle:lifecycle-runtime-compose:2.6.0")

    // UI Components
    implementation("androidx.constraintlayout:constraintlayout:$constraintLayoutVersion")
    implementation("androidx.lifecycle:lifecycle-extensions:2.2.0")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion")
    implementation("androidx.work:work-runtime-ktx:$workVersion")
    implementation("androidx.appcompat:appcompat:$appCompatVersion")
    implementation("androidx.recyclerview:recyclerview:$recyclerViewVersion")
    implementation("androidx.cardview:cardview:1.0.0")
    implementation("com.google.android.material:material:$materialVersion")

    // Play Services
    implementation("com.google.android.play:review:$inAppReviewVersion")
    implementation("com.google.android.play:review-ktx:$inAppReviewVersion")
    implementation("com.google.android.play:app-update:$inAppUpdateVersion")
    implementation("com.google.android.play:app-update-ktx:$inAppUpdateVersion")
    implementation("com.google.android.gms:play-services-location:$playServicesLocationVersion")
    implementation("com.google.android.gms:play-services-maps:$playServicesMapsVersion")
    implementation("com.google.android.gms:play-services-auth:$googleServicesAuthVersion")
    implementation("com.google.android.gms:play-services-auth-api-phone:$servicesAuthApiPhoneVersion")
    implementation("com.google.android.gms:play-services-recaptcha:$reCaptchaVersion")
    implementation("com.google.android.gms:play-services-ads-identifier:$adsIdentifier")

    // Firebase
    implementation(platform("com.google.firebase:firebase-bom:33.1.2"))
    implementation("com.google.firebase:firebase-config")
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-messaging")
    implementation("com.google.firebase:firebase-dynamic-links")
    implementation("com.google.firebase:firebase-perf")

    // Networking
    implementation(platform("com.squareup.okhttp3:okhttp-bom:$okhttpVersion"))
    implementation("com.squareup.okhttp3:okhttp")
    implementation("com.squareup.okhttp3:logging-interceptor")
    implementation("com.google.code.gson:gson:$gsonVersion")
    implementation("com.squareup.retrofit2:converter-gson:$gsonConverterVersion")

    // Room
    implementation("androidx.room:room-runtime:$roomVersion")
    ksp("androidx.room:room-compiler:$roomVersion")
    implementation("androidx.room:room-ktx:$roomVersion")
    androidTestImplementation("androidx.room:room-testing:$roomVersion")

    // Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutinesVersion")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion")

    // Dependency Injection
    implementation("com.google.dagger:dagger:$daggerVersion")
    ksp("com.google.dagger:dagger-compiler:$daggerVersion")

    // Image Loading
    implementation("com.github.bumptech.glide:glide:$glideVersion")
    ksp("com.github.bumptech.glide:compiler:$glideCompilerVersion")
    implementation("jp.wasabeef:glide-transformations:$glideTransformationsVersion")

    // Navigation
    implementation("androidx.navigation:navigation-runtime-ktx:$navVersion")
    implementation("androidx.navigation:navigation-fragment-ktx:$navVersion")
    implementation("androidx.navigation:navigation-ui-ktx:$navVersion")
    implementation("androidx.viewpager2:viewpager2:1.0.0")

    // Ads & Mediation
    implementation("com.applovin:applovin-sdk:$applovinVersion")
    implementation("com.applovin.mediation:google-adapter:$adMobAdapterVersion")
    implementation("com.applovin.mediation:facebook-adapter:$metaAdapterVersion")
    implementation("com.applovin.mediation:mintegral-adapter:$mintegralAdapterVersion")
    implementation("com.applovin.mediation:ironsource-adapter:$ironsourceAdapterVersion")
    implementation("com.google.android.ump:user-messaging-platform:$umpVersion")

    // Clevertap
    implementation("com.clevertap.android:clevertap-android-sdk:$clevertapVersion")

    // Appsflyer
    implementation("com.appsflyer:af-android-sdk:$appsflyerVersion")
    implementation("com.appsflyer:purchase-connector:$appsflyerPurchaseConnector")

    // Camera
    implementation("androidx.camera:camera-core:$cameraxVersion")
    implementation("androidx.camera:camera-camera2:$cameraxVersion")
    implementation("androidx.camera:camera-lifecycle:$cameraxVersion")
    implementation("androidx.camera:camera-view:$cameraViewVersion")

    // Twilio
    implementation("com.twilio:video-android:$twilioVideoVersion")
    implementation("com.twilio:video-android-ktx:$twilioVideoVersion")
    implementation("com.twilio:audioswitch:$twilioAudioSwitchVersion")

    // Purchasely
    implementation("io.purchasely:core:$purchaselyVersion")
    implementation("io.purchasely:google-play:$purchaselyVersion")

    // MLKit
    implementation("com.google.mlkit:face-detection:16.1.6")
    implementation("com.google.android.gms:play-services-mlkit-text-recognition:19.0.0")

    // AWS SDK
    implementation("com.amazonaws:aws-android-sdk-mobile-client:2.70.0@aar") { isTransitive = true }
    implementation("com.amazonaws:aws-android-sdk-auth-userpools:2.70.0@aar") { isTransitive = true }
    implementation("com.amazonaws:aws-android-sdk-s3:2.70.0")

    // Google Play Install Referrer
    implementation("com.android.installreferrer:installreferrer:$installReferrer")

    // Miscellaneous
    implementation("com.skyfishjy.ripplebackground:library:1.0.1")
    implementation("com.github.yalantis:ucrop:$uiCropVersion")
    implementation("androidx.preference:preference-ktx:$preferenceVersion")
    implementation("org.java-websocket:Java-WebSocket:$webSocketVersion")
    implementation("id.zelory:compressor:3.0.1")
    implementation("de.hdodenhof:circleimageview:3.1.0")
    implementation("androidx.browser:browser:$browserVersion")
    implementation("com.giphy.sdk:ui:2.1.11")
    implementation("com.facebook.shimmer:shimmer:0.5.0")
    implementation("com.github.skydoves:balloon:$balloonVersion")
    implementation("com.google.guava:guava:31.1-android")
    implementation("com.airbnb.android:lottie:$lottieVersion")
    implementation("com.jakewharton.timber:timber:$timberVersion")
    implementation("com.google.android.flexbox:flexbox:$flexboxVersion")
    implementation("com.uxcam:uxcam:$uxCamVersion") {
        exclude(group = "com.google.android.material", module = "material")
    }

    implementation("com.uxcam:uxcam-ktx:1.2.33")

    implementation("com.mapbox.maps:android:$mapboxVersion")
    implementation("androidx.core:core-splashscreen:1.0.1")
    implementation("com.amplifyframework.ui:liveness:1.2.6")
    implementation("com.amplifyframework:aws-auth-cognito:2.16.1")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
    implementation("net.swiftzer.semver:semver:1.1.1")
    implementation("jp.wasabeef:blurry:$blurry")
    implementation("com.facebook.android:facebook-android-sdk:$facebookAndroidVersion")

    //Fingerprint Pro
    implementation("com.fingerprint.android:pro:$fingerprintProVersion")

    //Credentials
    implementation("androidx.credentials:credentials:$credentialsVersion")
    implementation("androidx.credentials:credentials-play-services-auth:$credentialsVersion")
    implementation("com.google.android.libraries.identity.googleid:googleid:1.1.1")

    //Rive
    implementation("app.rive:rive-android:$riveVersion")

}


repositories {
    mavenCentral()
    google()
    maven { url = uri("https://android-sdk.is.com") }
    maven { url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea") }
    maven { url = uri("https://sdk.uxcam.com/android/") }
}




