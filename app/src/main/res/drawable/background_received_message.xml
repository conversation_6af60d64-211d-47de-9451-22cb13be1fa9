<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="?attr/colorControlHighlight">
    <item>
<shape>
    <solid
        android:color="@color/transparent"/>
    <stroke android:color="@color/border"
        android:width="1dp"/>
    <corners
        android:radius="2dp"
        android:bottomLeftRadius="0dp"
        android:topLeftRadius="20dp"
        android:topRightRadius="20dp"
        android:bottomRightRadius="20dp"
        />
</shape>
    </item>
</ripple>