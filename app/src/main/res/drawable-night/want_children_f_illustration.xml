<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="240dp"
    android:height="120dp"
    android:viewportWidth="240"
    android:viewportHeight="120">
  <group>
    <clip-path
        android:pathData="M61,0h118.5v120h-118.5z"/>
    <path
        android:pathData="M65.08,48.58L72.61,56.95C77.15,62.01 77.92,69.41 74.52,75.3L68.94,84.98C61.33,98.16 75.05,113.38 88.94,107.16L99.03,102.65C105.25,99.87 112.56,101.42 117.12,106.49L124.54,114.75C134.7,126.06 153.4,117.76 151.83,102.64L150.67,91.39C149.97,84.64 153.69,78.21 159.88,75.44L170.2,70.83C184.08,64.62 181.91,44.28 167.04,41.13L156.18,38.84C149.51,37.43 144.51,31.87 143.81,25.09L142.68,14.1C141.11,-1.04 121.07,-5.32 113.46,7.87L107.87,17.54C104.47,23.43 97.67,26.46 91.02,25.05L80.01,22.73C65.15,19.58 54.93,37.28 65.08,48.58Z"
        android:fillColor="#242424"/>
    <path
        android:pathData="M126.43,19.13C126.43,19.13 126.59,18.21 127.43,18.94C128.26,19.67 134.12,25.2 134.35,27.05C134.57,28.89 134.39,29.87 133.65,31.43C132.91,32.99 133.06,34.11 133.29,35.04C133.53,35.97 133.33,38.68 132.79,39.67L130.32,45.32C130.32,45.32 125.38,51.66 124.04,52.36C122.71,53.05 120.52,53.14 120.52,53.14C120.52,53.14 119.65,54.24 119.5,54.99L117.95,60.72L117.96,61.34L119.43,62.35L119.54,65.07L119.53,68.99L110.31,67.71L106.79,67.16L107.33,65.59C107.33,65.59 107.57,65.32 107.36,64.66C107.15,63.99 102.7,58.59 102.7,58.59L103.92,56.82L107.3,49.41L107.61,43.02L106.91,37.84C106.91,37.84 104.74,36.51 105,33.54C105.25,30.57 106.47,30.21 107.82,30.45L108.49,30.85C108.49,30.85 109.35,28.63 110.7,28.33C112.05,28.02 114.39,26.77 114.78,25.93C115.18,25.1 115.93,24.04 116.67,24.11C117.4,24.18 120.77,23.6 123.18,21.94C125.6,20.29 126.43,19.13 126.43,19.13L126.43,19.13Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M128.9,19.41C128.9,19.41 125.74,26.36 115.43,24.64L126.64,17.37L128.9,19.41V19.41Z"
        android:fillColor="#EEEEEE"/>
    <path
        android:pathData="M106.63,37.81L108.11,38.2C108.11,38.2 107.94,38.43 108.1,39.71C108.26,40.99 109.81,49.94 106.67,53.48C103.53,57.03 102.06,56.87 98.84,56.58L106.63,37.81L106.63,37.81Z"
        android:fillColor="#DFDFDF"/>
    <path
        android:pathData="M112.88,49.39L118.68,57.7L120.63,52.88L112.88,49.39Z"
        android:fillColor="#BEBEBE"/>
    <path
        android:pathData="M126.91,18.44C128.14,19.56 129.34,20.73 130.5,21.93C132.77,24.27 135.62,26.84 134.21,30.42C133.43,32.41 132.91,33.11 133,33.46C133.09,33.81 133.67,36.58 133.37,37.96C133.02,39.53 132.4,41.07 131.74,42.53C130.51,45.22 128.96,47.9 126.86,50.05C125.85,51.08 124.61,52.2 123.26,52.74C121.62,53.4 119.76,52.97 118.19,52.31C116.29,51.51 114.55,50.34 112.91,49.1"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.94,53.05C119,54.61 117.95,61.34 117.95,61.34C117.95,61.34 121.58,63.61 122.81,63.4"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.98,11.14C124.4,8.14 127.62,5.74 131.68,7.06C133.62,7.69 135.45,9.02 136.77,10.55C139.82,14.05 141.07,18.21 141.94,22.63C142.63,26.17 142.67,29.86 141.81,33.37C141.08,36.36 139.74,39.04 139.58,42.16C139.32,47.41 141.99,53.75 138.86,58.53C137.7,60.31 135.82,61.53 133.82,62.26C131.37,63.15 128.68,63.37 126.12,62.9C123.83,62.48 121.41,60.02 119.02,61.04C117.82,61.55 118.27,59.42 118.27,59.42C118.27,59.42 119.3,53.7 121.52,53.11C123.74,52.52 127.11,51.51 131.37,43.96C131.37,43.96 133.82,38.29 133.65,36.72C133.48,35.15 132.84,34.41 133.26,33.06C133.69,31.72 134.88,29.82 134.75,28.36C134.62,26.9 134.15,25.31 133.03,24.34C131.9,23.38 126.68,18.08 126.68,18.08C126.68,18.08 127.18,18.69 126.57,19.46C125.97,20.24 122.55,24.7 116.23,24.79C116.23,24.79 115.21,27.47 110.59,28.76C110.59,28.76 109.45,29.9 109.29,30.36C109.12,30.82 109.06,31.3 108.51,31.13C107.96,30.97 105.79,29.34 105.39,33.33C104.99,37.33 108.21,38.25 108.21,38.25C108.21,38.25 107.44,37.96 107.37,38.83C107.29,39.81 107.71,40.97 107.83,41.94C108.09,44.1 108.16,46.3 107.81,48.45C107.22,52.09 104.78,57.14 100.55,57.42C96.29,57.71 93.05,53.36 92.8,49.45C92.79,49.29 92.79,49.12 92.86,48.97C93.05,48.56 93.64,48.59 94.1,48.61C96.08,48.7 97.85,47.19 98.7,45.4C100.52,41.57 100.15,35.8 99.31,31.74C98.4,27.38 100.35,21.61 102.23,17.75C103.69,14.76 105.84,12.05 108.69,10.34C112.87,7.84 118.14,8.08 121.98,11.14L121.98,11.14Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M133,30.18C132.77,30.14 132.62,30.04 132.42,29.93C131.73,29.54 130.91,29.45 130.13,29.47C129.84,29.47 129.55,29.48 129.27,29.42C128.48,29.25 128.38,28.72 128.58,28.5C128.78,28.29 129.25,28.34 129.36,28.36C130.13,28.53 130.92,28.58 131.67,28.82C131.96,28.92 132.24,29.04 132.49,29.2C132.63,29.28 132.75,29.38 132.87,29.48C132.98,29.57 133.09,29.65 133.15,29.78C133.2,29.91 133.18,30.07 133.07,30.15C133.04,30.16 133.02,30.18 133,30.18H133Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M121.91,27.59C122.03,27.64 122.14,27.72 122.21,27.82C122.27,27.9 122.3,28.01 122.27,28.11C122.2,28.32 121.9,28.42 121.7,28.45C121.46,28.49 121.21,28.48 120.96,28.45C120.32,28.38 119.76,28.11 119.18,27.86C118.82,27.71 118.43,27.59 118.04,27.62C117.74,27.66 117.45,27.78 117.17,27.9C117.03,27.97 116.89,28.03 116.76,28.12C116.71,28.16 116.67,28.2 116.61,28.22C116.52,28.26 116.41,28.23 116.34,28.17C116.26,28.11 116.23,28 116.25,27.91C116.27,27.78 116.36,27.68 116.46,27.6C116.57,27.52 116.66,27.43 116.77,27.35C116.99,27.18 117.2,27.07 117.47,27.01C117.86,26.92 118.24,26.87 118.64,26.89C119.11,26.91 119.58,27 120.04,27.09C120.51,27.19 120.98,27.31 121.44,27.45C121.56,27.48 121.74,27.52 121.91,27.59Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M125.5,34.84C125.58,35.39 125.64,35.94 125.69,36.48C125.75,37.13 125.82,37.77 125.83,38.41C125.83,38.94 125.87,39.65 125.52,40.08C125.16,40.53 124.62,40.82 124.06,40.91C123.39,41.02 122.81,40.71 122.18,40.54"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.72,34.25C130.66,34.45 130.58,34.67 130.43,34.82C130.42,34.83 130.41,34.84 130.4,34.84C130.14,34.79 129.84,34.74 129.51,34.73C129.4,34.55 129.34,34.31 129.32,34.07L130.24,33.88L129.32,33.45C129.43,32.76 129.87,32.27 130.24,32.31C130.53,32.34 130.69,32.66 130.76,32.91C130.88,33.34 130.84,33.83 130.72,34.25Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M120.85,32.48C120.81,32.79 120.65,33.34 120.37,33.61C120.12,33.55 119.82,33.5 119.49,33.49C119.48,33.48 119.48,33.48 119.48,33.48C119.37,33.3 119.33,33.08 119.32,32.88L120.24,32.75L119.34,32.27C119.38,32.01 119.45,31.76 119.57,31.54C119.7,31.31 119.92,31.03 120.21,31.07C120.6,31.11 120.93,31.75 120.85,32.48Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M108.56,33.23C108.41,33.44 108.28,33.7 108.25,33.95C108.2,34.32 108.53,34.61 108.59,34.96C108.66,35.36 108.45,35.65 108.05,35.73C107.27,35.88 106.39,34.41 106.7,33.48"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.71,32.52C128.71,32.52 130.27,32.1 131.63,32.84L128.71,32.52Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M128.71,32.52C128.71,32.52 130.27,32.1 131.63,32.84"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.75,31.84C120.09,30.96 118.92,31.42 118.92,31.42L118.03,30.82"
        android:fillColor="#000000"/>
    <path
        android:pathData="M121.75,31.84C120.09,30.96 118.92,31.42 118.92,31.42L118.03,30.82"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.08,30.63L119.74,31.13"
        android:strokeWidth="0.502886"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.63,32.84L132.63,32.45"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.64,32.04L130.9,32.38"
        android:strokeWidth="0.502886"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.66,14.24V14.24L111.67,14.28L111.88,14.84C111.9,14.83 111.91,14.83 111.93,14.82C111.93,14.82 111.93,14.82 111.93,14.82C113.58,14.22 115.23,15.19 115.25,15.21C115.41,15.31 115.56,15.24 115.82,15.11C115.85,15.1 115.87,15.09 115.88,15.08C116.51,14.86 117.42,14.76 118.1,15.11C118.71,15.42 119.27,16.2 119.1,16.91C119.07,17.03 119.11,17.15 119.21,17.23C119.3,17.31 119.42,17.33 119.54,17.28C119.74,17.2 119.96,17.15 120.16,17.14C120.44,17.13 120.67,17.35 120.94,17.6C121.06,17.72 121.19,17.84 121.34,17.95L120.1,18.46L119.5,18.71L117.43,19.57L117.41,19.57L116.84,19.81L116.81,19.82L114.74,20.68L114.72,20.69L114.37,20.83L114.38,20.86L114.4,20.92L114.41,20.92L114.61,21.42L115.12,21.22L116.87,20.49L117.87,20.08L119.56,19.38L120.56,18.97L121.92,18.4L122.5,18.16C122.66,18.1 122.74,17.92 122.68,17.76C122.66,17.71 122.63,17.67 122.6,17.64C122.6,17.64 122.6,17.64 122.6,17.63C122.51,17.56 122.39,17.52 122.28,17.56C121.96,17.66 121.74,17.48 121.38,17.14C121.05,16.82 120.67,16.47 120.13,16.5C120.01,16.5 119.89,16.52 119.76,16.54C119.71,15.7 119.09,14.9 118.39,14.54C118.14,14.41 117.86,14.33 117.58,14.28C117.43,14.26 117.28,14.25 117.14,14.25C116.84,14.24 116.54,14.27 116.26,14.32C116.12,14.35 115.98,14.38 115.86,14.42C115.79,14.44 115.72,14.46 115.66,14.48C115.64,14.49 115.6,14.51 115.54,14.53C115.52,14.54 115.48,14.56 115.44,14.58C114.97,14.33 113.36,13.61 111.69,14.22C111.68,14.23 111.66,14.23 111.65,14.24H111.66Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M106.29,24.4C106.29,24.44 106.3,24.48 106.32,24.52C106.37,24.64 106.48,24.7 106.6,24.71C106.64,24.71 106.69,24.7 106.73,24.68L107.32,24.44L108.8,23.83L109.8,23.41L111.49,22.72L112.49,22.3L114.12,21.63L114.62,21.42L114.41,20.92L114.41,20.92L114.39,20.86L114.37,20.83L114.15,20.92L114.12,20.94L112.05,21.79L112.04,21.8L111.46,22.04L111.43,22.05L109.36,22.9L108.77,23.15L107.4,23.71C107.43,23.54 107.44,23.35 107.44,23.19C107.45,22.81 107.46,22.49 107.67,22.3C107.81,22.17 108.01,22.05 108.21,21.97C108.33,21.92 108.4,21.82 108.41,21.7C108.42,21.58 108.36,21.46 108.26,21.4C107.64,21.01 107.49,20.07 107.69,19.41C107.93,18.69 108.65,18.12 109.25,17.83C109.26,17.82 109.28,17.82 109.31,17.81C109.58,17.71 109.74,17.66 109.78,17.47C109.79,17.45 110.3,15.56 111.93,14.82C111.93,14.82 111.93,14.82 111.93,14.82L111.68,14.26L111.67,14.23C111.67,14.24 111.66,14.24 111.66,14.24C111.5,14.31 111.36,14.39 111.23,14.47H111.22C109.9,15.28 109.35,16.7 109.2,17.16C109.16,17.17 109.12,17.19 109.09,17.2C109.03,17.22 108.99,17.24 108.98,17.24C108.76,17.35 108.54,17.48 108.32,17.64C108.19,17.73 108.06,17.84 107.94,17.95C107.76,18.12 107.59,18.3 107.44,18.51C107.34,18.65 107.25,18.8 107.18,18.96C107.14,19.04 107.11,19.13 107.08,19.21C106.84,19.97 106.96,20.97 107.52,21.6C107.42,21.67 107.32,21.75 107.24,21.83C106.83,22.2 106.81,22.71 106.8,23.16C106.78,23.66 106.76,23.95 106.46,24.1C106.35,24.16 106.28,24.28 106.29,24.4H106.29Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M116.86,19.89L117.46,19.67C117.43,19.57 117.14,18.69 118.07,18.3C119.05,17.89 119.55,18.78 119.57,18.82L120.13,18.52C119.86,18.01 119.03,17.2 117.83,17.71C116.31,18.35 116.85,19.88 116.86,19.89H116.86V19.89Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M112.32,16.72C112.36,16.72 112.41,16.71 112.45,16.7C112.61,16.63 112.69,16.44 112.63,16.28L112.46,15.88C112.39,15.72 112.21,15.64 112.04,15.71C111.88,15.78 111.8,15.96 111.87,16.13L112.03,16.52C112.08,16.64 112.2,16.72 112.32,16.72Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M113.06,16.69C113.09,16.69 113.11,16.69 113.14,16.68C113.31,16.65 113.42,16.48 113.39,16.3L113.28,15.79C113.24,15.62 113.07,15.51 112.9,15.55C112.73,15.58 112.61,15.75 112.65,15.93L112.76,16.43C112.79,16.58 112.92,16.69 113.06,16.69H113.06Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M111.79,17.19C111.85,17.2 111.91,17.18 111.97,17.14C112.12,17.04 112.16,16.84 112.06,16.7L111.8,16.3C111.7,16.15 111.5,16.11 111.35,16.21C111.2,16.31 111.16,16.51 111.26,16.66L111.52,17.05C111.58,17.14 111.68,17.19 111.78,17.19H111.79Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M113.76,16.73C113.77,16.74 113.78,16.73 113.8,16.73C113.97,16.72 114.1,16.56 114.08,16.38L114.03,15.87C114.01,15.7 113.86,15.57 113.68,15.59C113.5,15.61 113.38,15.76 113.39,15.94L113.45,16.45C113.46,16.61 113.6,16.73 113.76,16.74H113.76V16.73Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M111.36,17.73C111.45,17.73 111.54,17.7 111.6,17.63C111.72,17.5 111.72,17.3 111.59,17.18L111.25,16.85C111.12,16.73 110.92,16.74 110.79,16.87C110.67,16.99 110.68,17.2 110.81,17.32L111.15,17.64C111.21,17.7 111.28,17.73 111.36,17.73V17.73Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M114.17,21L114.77,20.78C114.74,20.68 114.45,19.8 115.39,19.41C116.36,19 116.86,19.89 116.88,19.93L117.44,19.63C117.17,19.12 116.34,18.31 115.14,18.82C113.61,19.46 114.16,20.99 114.17,21H114.17V21Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M111.48,22.12L112.08,21.89C112.05,21.79 111.76,20.92 112.7,20.52C113.67,20.11 114.17,21 114.19,21.04L114.75,20.74C114.48,20.24 113.65,19.42 112.45,19.93C110.93,20.58 111.47,22.1 111.48,22.12H111.48V22.12Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M108.79,23.23L109.39,23C109.36,22.91 109.07,22.03 110.01,21.63C110.98,21.22 111.48,22.12 111.5,22.16L112.07,21.85C111.79,21.35 110.97,20.53 109.76,21.04C108.24,21.69 108.79,23.21 108.79,23.23H108.79V23.23Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122.39,18.68C122.69,18.68 122.94,18.44 122.94,18.14C122.94,17.84 122.69,17.59 122.39,17.59C122.09,17.59 121.85,17.84 121.85,18.14C121.85,18.44 122.09,18.68 122.39,18.68Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M120.03,19.66C120.33,19.66 120.58,19.42 120.58,19.11C120.58,18.81 120.33,18.57 120.03,18.57C119.73,18.57 119.49,18.81 119.49,19.11C119.49,19.42 119.73,19.66 120.03,19.66Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M117.95,17.44C118.13,17.44 118.28,17.29 118.28,17.11C118.28,16.93 118.13,16.78 117.95,16.78C117.77,16.78 117.62,16.93 117.62,17.11C117.62,17.29 117.77,17.44 117.95,17.44Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M117.68,17.02C117.86,17.02 118.01,16.88 118.01,16.7C118.01,16.51 117.86,16.37 117.68,16.37C117.5,16.37 117.35,16.51 117.35,16.7C117.35,16.88 117.5,17.02 117.68,17.02Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M117.27,16.67C117.45,16.67 117.6,16.52 117.6,16.34C117.6,16.16 117.45,16.02 117.27,16.02C117.09,16.02 116.94,16.16 116.94,16.34C116.94,16.52 117.09,16.67 117.27,16.67Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M116.77,16.46C116.95,16.46 117.1,16.31 117.1,16.13C117.1,15.95 116.95,15.8 116.77,15.8C116.59,15.8 116.44,15.95 116.44,16.13C116.44,16.31 116.59,16.46 116.77,16.46Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M116.21,16.44C116.39,16.44 116.54,16.29 116.54,16.11C116.54,15.93 116.39,15.78 116.21,15.78C116.03,15.78 115.89,15.93 115.89,16.11C115.89,16.29 116.03,16.44 116.21,16.44Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M115.67,16.62C115.85,16.62 116,16.47 116,16.29C116,16.11 115.85,15.96 115.67,15.96C115.49,15.96 115.34,16.11 115.34,16.29C115.34,16.47 115.49,16.62 115.67,16.62Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M115.11,16.87C115.29,16.87 115.44,16.72 115.44,16.54C115.44,16.36 115.29,16.21 115.11,16.21C114.93,16.21 114.78,16.36 114.78,16.54C114.78,16.72 114.93,16.87 115.11,16.87Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M110.59,18.82C110.77,18.82 110.92,18.67 110.92,18.49C110.92,18.31 110.77,18.16 110.59,18.16C110.41,18.16 110.26,18.31 110.26,18.49C110.26,18.67 110.41,18.82 110.59,18.82Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M110.08,19.16C110.26,19.16 110.41,19.01 110.41,18.83C110.41,18.65 110.26,18.5 110.08,18.5C109.9,18.5 109.75,18.65 109.75,18.83C109.75,19.01 109.9,19.16 110.08,19.16Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.67,19.6C109.85,19.6 110,19.45 110,19.27C110,19.09 109.85,18.95 109.67,18.95C109.49,18.95 109.34,19.09 109.34,19.27C109.34,19.45 109.49,19.6 109.67,19.6Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.36,20.12C109.54,20.12 109.69,19.97 109.69,19.79C109.69,19.61 109.54,19.46 109.36,19.46C109.18,19.46 109.04,19.61 109.04,19.79C109.04,19.97 109.18,20.12 109.36,20.12Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.23,20.71C109.41,20.71 109.56,20.56 109.56,20.38C109.56,20.2 109.41,20.05 109.23,20.05C109.04,20.05 108.9,20.2 108.9,20.38C108.9,20.56 109.04,20.71 109.23,20.71Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.33,21.27C109.51,21.27 109.66,21.13 109.66,20.95C109.66,20.76 109.51,20.62 109.33,20.62C109.15,20.62 109,20.76 109,20.95C109,21.13 109.15,21.27 109.33,21.27Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M117.34,20.77C117.64,20.77 117.89,20.52 117.89,20.22C117.89,19.92 117.64,19.68 117.34,19.68C117.04,19.68 116.8,19.92 116.8,20.22C116.8,20.52 117.04,20.77 117.34,20.77Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M114.59,21.91C114.89,21.91 115.14,21.67 115.14,21.36C115.14,21.06 114.89,20.82 114.59,20.82C114.29,20.82 114.05,21.06 114.05,21.36C114.05,21.67 114.29,21.91 114.59,21.91Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M111.96,23C112.27,23 112.51,22.75 112.51,22.45C112.51,22.15 112.27,21.91 111.96,21.91C111.67,21.91 111.42,22.15 111.42,22.45C111.42,22.75 111.67,23 111.96,23Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.28,24.1C109.58,24.1 109.82,23.86 109.82,23.56C109.82,23.26 109.58,23.02 109.28,23.02C108.97,23.02 108.73,23.26 108.73,23.56C108.73,23.86 108.97,24.1 109.28,24.1Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M106.8,25.13C107.1,25.13 107.34,24.89 107.34,24.59C107.34,24.29 107.1,24.04 106.8,24.04C106.5,24.04 106.25,24.29 106.25,24.59C106.25,24.89 106.5,25.13 106.8,25.13Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M109.49,21.28L110.12,21.15C109.98,20.5 110.05,19.98 110.32,19.61C110.73,19.06 111.46,18.99 111.47,18.99C111.57,18.98 111.67,18.92 111.72,18.83C111.77,18.74 111.78,18.63 111.73,18.53C111.69,18.43 111.33,17.53 112.57,16.92C113.82,16.3 114.5,17.29 114.57,17.4C114.62,17.48 114.7,17.53 114.79,17.55C114.88,17.56 114.97,17.54 115.04,17.48C115.71,16.95 116.27,16.75 116.72,16.88C117.35,17.07 117.61,17.88 117.61,17.89L118.22,17.71C118.21,17.66 117.87,16.57 116.92,16.27C116.32,16.09 115.64,16.26 114.9,16.79C114.43,16.27 113.5,15.75 112.29,16.35C111.02,16.97 110.96,17.9 111.05,18.42C110.7,18.52 110.18,18.73 109.81,19.22C109.42,19.74 109.31,20.44 109.49,21.28Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M117.33,14.36C117.33,14.36 117.87,14.36 117.88,13.66C117.88,13.66 117.89,13.37 117.69,12.94C117.69,12.94 117.23,13.07 117.04,13.65C116.85,14.23 117.33,14.36 117.33,14.36L117.33,14.36Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M116.16,14.43C116.16,14.43 116.61,14.14 116.24,13.55C116.24,13.55 116.09,13.3 115.69,13.04C115.69,13.04 115.37,13.41 115.53,14C115.68,14.58 116.15,14.43 116.15,14.43L116.16,14.43Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M111.43,14.5C111.43,14.5 110.9,14.55 110.81,13.86C110.81,13.86 110.77,13.57 110.92,13.13C110.92,13.13 111.4,13.21 111.65,13.76C111.89,14.32 111.43,14.5 111.43,14.5H111.43V14.5Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M108.17,17.9C107.96,18.05 107.62,17.85 107.48,17.66C107.3,17.41 107.25,17.07 107.25,16.77C107.25,16.73 107.56,16.8 107.59,16.81C107.99,16.91 108.5,17.3 108.27,17.77C108.24,17.83 108.21,17.87 108.17,17.9V17.9Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M107.4,18.83C107.26,19.04 106.87,18.98 106.67,18.85C106.41,18.68 106.24,18.39 106.13,18.1C106.12,18.07 106.43,18.02 106.46,18.02C106.87,17.97 107.49,18.14 107.45,18.67C107.44,18.73 107.42,18.78 107.4,18.83H107.4Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M116.32,18.26C116.56,18.26 116.75,18.06 116.75,17.82C116.75,17.58 116.56,17.38 116.32,17.38C116.08,17.38 115.88,17.58 115.88,17.82C115.88,18.06 116.08,18.26 116.32,18.26Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M111.18,20.38C111.42,20.38 111.62,20.18 111.62,19.94C111.62,19.7 111.42,19.51 111.18,19.51C110.94,19.51 110.75,19.7 110.75,19.94C110.75,20.18 110.94,20.38 111.18,20.38Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M113.88,19.62C113.88,19.62 113.9,19.5 113.9,19.49C113.92,19.36 113.91,19.23 113.89,19.11C113.85,18.76 113.73,18.42 113.57,18.11C113.43,17.82 113.27,17.5 113.03,17.27C112.99,17.23 112.93,17.18 112.87,17.16C112.85,17.16 112.86,17.55 112.86,17.57C112.91,17.95 113.04,18.33 113.21,18.67C113.38,19.01 113.6,19.37 113.88,19.62Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M113.49,19.65C113.5,19.5 113.4,19.31 113.32,19.19C113.12,18.88 112.82,18.65 112.5,18.5C112.4,18.45 112.21,18.37 112.1,18.42C112.05,18.45 112.24,18.81 112.26,18.83C112.42,19.08 112.64,19.29 112.9,19.44C113.03,19.51 113.16,19.57 113.3,19.61C113.31,19.61 113.49,19.65 113.49,19.65L113.49,19.65Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M114.21,19.38C114.21,19.38 114.29,19.29 114.29,19.29C114.36,19.2 114.4,19.1 114.44,19C114.54,18.68 114.53,18.32 114.45,18C114.43,17.95 114.26,17.48 114.21,17.53C114.1,17.63 114.04,17.79 114.01,17.92C113.88,18.38 113.93,18.98 114.21,19.38H114.21L114.21,19.38Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M120.17,68.92C120.17,68.92 103.56,67.46 101.44,66.46C101.44,66.46 86.5,90.71 85.74,95.71C85.74,95.71 119.68,99.95 120.31,99.41C120.54,99.21 124.45,89.06 124.42,84.02C124.35,75.05 120.17,68.92 120.17,68.92Z"
        android:strokeWidth="0.628608"
        android:fillColor="#FBF5F4"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M117.67,71.15C117.82,71.62 118.49,71.69 118.68,71.19C118.89,70.61 118.14,70.09 117.64,70.2C116.94,70.37 117.05,71.24 117.25,71.75C117.35,72.02 117.51,72.27 117.78,72.4C118.07,72.53 118.65,72.32 118.85,72.49L119.22,71.86C118.46,71.74 117.48,72.22 116.8,71.85C116.51,71.69 116.3,71.42 116.02,71.25C115.77,71.11 114.81,71.02 115.06,70.5C114.85,70.94 115.5,71.32 115.71,70.88C115.98,70.35 115.62,69.7 114.98,69.79C114.29,69.89 114.07,70.7 114.36,71.26C114.53,71.6 114.8,71.72 115.16,71.78C115.65,71.86 115.89,72.13 116.29,72.4C117.18,73.02 118.06,72.43 119.02,72.59C119.36,72.65 119.69,72.24 119.39,71.96C119.11,71.72 118.85,71.68 118.5,71.71C118.11,71.74 118.02,71.72 117.91,71.33C117.88,71.21 117.86,71.08 117.85,70.96C118.03,70.98 118.22,70.97 118.4,70.94C118.25,70.48 117.52,70.68 117.67,71.15H117.67Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M112.64,70.77C112.79,71.24 113.46,71.31 113.65,70.81C113.86,70.23 113.11,69.71 112.61,69.82C111.91,69.99 112.03,70.86 112.22,71.37C112.32,71.64 112.48,71.89 112.75,72.02C113.04,72.16 113.62,71.94 113.82,72.12L114.19,71.49C113.43,71.36 112.45,71.84 111.77,71.47C111.49,71.31 111.27,71.04 110.99,70.87C110.74,70.73 109.78,70.64 110.03,70.12C109.82,70.56 110.47,70.94 110.68,70.5C110.95,69.97 110.59,69.32 109.95,69.41C109.26,69.51 109.04,70.33 109.33,70.88C109.5,71.22 109.78,71.34 110.13,71.4C110.63,71.48 110.86,71.75 111.26,72.03C112.15,72.64 113.03,72.05 113.99,72.21C114.33,72.27 114.67,71.86 114.36,71.58C114.08,71.34 113.82,71.3 113.47,71.33C113.08,71.36 112.99,71.34 112.88,70.95C112.85,70.83 112.83,70.7 112.82,70.58C113,70.6 113.19,70.59 113.37,70.57C113.22,70.11 112.49,70.3 112.64,70.77H112.64Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M107.71,70.4C107.86,70.87 108.53,70.94 108.71,70.44C108.93,69.86 108.18,69.34 107.67,69.45C106.98,69.62 107.09,70.49 107.28,71C107.38,71.27 107.54,71.52 107.81,71.65C108.11,71.78 108.69,71.57 108.89,71.74L109.25,71.11C108.5,70.99 107.52,71.47 106.84,71.1C106.55,70.94 106.34,70.67 106.06,70.5C105.81,70.36 104.84,70.27 105.1,69.75C104.88,70.19 105.53,70.57 105.75,70.13C106.02,69.6 105.66,68.95 105.01,69.04C104.32,69.14 104.11,69.95 104.39,70.51C104.57,70.85 104.84,70.97 105.2,71.03C105.69,71.11 105.92,71.38 106.32,71.65C107.21,72.27 108.09,71.68 109.05,71.84C109.39,71.9 109.73,71.49 109.42,71.21C109.15,70.97 108.89,70.93 108.53,70.96C108.14,70.99 108.05,70.97 107.94,70.58C107.91,70.46 107.89,70.33 107.89,70.21C108.07,70.23 108.25,70.22 108.43,70.19C108.29,69.73 107.56,69.93 107.71,70.4H107.71Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M120.93,77.81C120.93,77.81 120.35,76.38 118.91,76.27C117.46,76.16 116.67,77.49 116.67,77.49C116.67,77.49 116.17,78.88 114.45,78.75C112.72,78.62 112.36,77.18 112.36,77.18C112.36,77.18 112.14,75.97 110.48,75.63C108.81,75.29 108.17,76.87 108.17,76.87"
        android:strokeWidth="0.377165"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M120.93,77.83C120.93,77.83 120.14,79.16 118.7,79.05C117.25,78.94 116.67,77.51 116.67,77.51C116.67,77.51 116.38,76.06 114.66,75.93C112.94,75.8 112.36,77.16 112.36,77.16C112.36,77.16 111.97,78.33 110.27,78.41C108.57,78.5 108.17,76.85 108.17,76.85"
        android:strokeWidth="0.377165"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M108.18,76.84C108.18,76.84 107.6,75.41 106.15,75.3C104.71,75.19 103.91,76.52 103.91,76.52C103.91,76.52 103.41,77.91 101.69,77.79C99.97,77.66 99.6,76.22 99.6,76.22C99.6,76.22 99.39,75.01 97.72,74.67C96.06,74.32 95.42,75.9 95.42,75.9"
        android:strokeWidth="0.377165"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M108.17,76.87C108.17,76.87 107.38,78.2 105.94,78.09C104.5,77.98 103.91,76.54 103.91,76.54C103.91,76.54 103.62,75.09 101.9,74.96C100.18,74.83 99.6,76.2 99.6,76.2C99.6,76.2 99.21,77.36 97.51,77.45C95.81,77.54 95.42,75.88 95.42,75.88"
        android:strokeWidth="0.377165"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M107.67,37.14L107.36,39.27"
        android:strokeLineJoin="round"
        android:strokeWidth="0.502886"
        android:fillColor="#00000000"
        android:strokeColor="#FBC300"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.03,42.48C107.93,42.48 108.65,41.76 108.65,40.86C108.65,39.97 107.93,39.25 107.03,39.25C106.14,39.25 105.42,39.97 105.42,40.86C105.42,41.76 106.14,42.48 107.03,42.48Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.502886"
        android:fillColor="#FBC300"
        android:strokeColor="#FBC300"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.1,40.74C107.1,40.74 107.73,40.19 107.25,39.39C107.25,39.39 106.4,39.91 107.1,40.74Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.314304"
        android:fillColor="#FBC300"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.61,42.31C107.61,42.31 108.12,41.45 107.12,41.04C107.12,41.04 106.81,41.82 107.61,42.31Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.314304"
        android:fillColor="#FBC300"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.07,42.06C106.07,42.06 105.87,41.08 106.95,41.02C106.95,41.02 106.99,41.86 106.07,42.06Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.314304"
        android:fillColor="#FBC300"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.53,40.72C108.53,40.72 107.81,40.03 107.19,40.91C107.19,40.91 107.88,41.39 108.53,40.72Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.314304"
        android:fillColor="#FBC300"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.7,40.28C105.7,40.28 105.93,41.25 106.93,40.84C106.93,40.84 106.61,40.07 105.7,40.28Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.314304"
        android:fillColor="#FBC300"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.07,52.46C106.07,52.46 103.37,58.26 102.03,58.85"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.11,72.61L123.83,92.18C117.56,84.98 119.27,68.26 119.16,67.08C119.04,65.9 118.72,61.32 118.72,61.32C120.24,61.74 122.18,61.89 123.66,62.41C126.51,63.41 129.7,64.42 131.63,66.93C132.91,68.6 133.95,71.68 134.23,73.7C134.58,76.27 134.69,78.86 134.81,81.45C134.92,84.04 135.03,86.77 135.13,89.43C135.21,91.66 135.28,93.88 135.35,96.1C135.39,97.31 135.43,98.53 135.46,99.74C135.46,99.82 135.46,99.9 135.46,99.98C135.46,99.98 123.46,100.02 121.93,99.53L123.28,92.24"
        android:fillColor="#000000"/>
    <path
        android:pathData="M119.78,62.37C119.3,62.96 119.57,63.92 120.37,64.05C120.51,64.08 120.69,64.02 120.72,63.86C120.83,63.2 120.01,63.07 119.78,63.67C119.48,64.45 120.04,65.35 120.74,65.68C120.96,65.79 121.21,65.61 121.15,65.36C120.98,64.58 120.09,65.01 120.01,65.62C119.9,66.46 120.56,67.21 121.36,67.35C121.72,67.41 121.86,66.93 121.51,66.81C120.58,66.46 120.18,68.24 120.17,68.84C120.15,69.2 120.28,69.58 120.66,69.7C120.96,69.8 121.27,69.68 121.41,69.4C121.51,69.21 121.39,68.97 121.17,68.97C120.53,68.97 119.92,69.47 119.95,70.15C119.98,70.83 120.64,71.28 121.27,71.21C121.47,71.18 121.65,70.98 121.52,70.78C121.3,70.44 120.85,70.4 120.58,70.72C120.23,71.12 120.39,72 120.55,72.46C120.7,72.87 121.06,73.34 121.55,73.31C121.94,73.29 122.17,72.82 121.85,72.55C121.73,72.46 121.56,72.43 121.45,72.55C120.94,73.11 120.71,73.91 121.01,74.63C121.19,75.08 121.76,75.44 122.1,74.94C122.23,74.75 122.06,74.53 121.86,74.52C121.13,74.47 120.41,75.01 120.38,75.78C120.35,76.55 121.04,77.12 121.77,77.14C121.99,77.15 122.12,76.9 122.02,76.72C121.88,76.47 121.74,76.36 121.46,76.3C121.3,76.26 121.17,76.35 121.11,76.5C120.84,77.19 120.67,78.09 120.95,78.81C121.14,79.3 121.6,79.65 122.13,79.43C122.32,79.35 122.41,79.13 122.25,78.96C121.99,78.68 121.6,78.64 121.28,78.87C120.86,79.17 120.76,79.83 120.73,80.3C120.68,81.42 121.35,82.45 122.51,82.57C122.63,82.58 122.76,82.47 122.78,82.36C122.85,82.03 122.67,81.75 122.33,81.69C121.92,81.62 121.66,81.94 121.57,82.31C121.37,83.18 121.68,84.54 122.82,84.33C123.1,84.28 123.08,83.85 122.82,83.78C121.9,83.55 121.56,84.48 121.65,85.22C121.72,85.8 122.12,86.67 122.79,86.09C122.9,86 122.91,85.78 122.79,85.69C122.58,85.53 122.39,85.51 122.18,85.7C121.86,85.98 121.92,86.48 121.97,86.86C122.01,87.15 122.07,87.62 122.38,87.79C122.61,87.92 122.87,87.82 122.99,87.59C123.14,87.32 122.77,87 122.55,87.25C122.02,87.84 122.41,88.78 123.2,88.82C123.57,88.84 123.57,88.27 123.2,88.25C122.92,88.24 122.76,87.86 122.95,87.65L122.5,87.31L122.63,87.22C122.59,87.09 122.56,86.96 122.54,86.82C122.53,86.71 122.43,86.14 122.6,86.1L122.32,86.03L122.39,86.09V85.69C122.33,85.74 122.21,85.2 122.21,85.18C122.18,84.93 122.08,84.17 122.67,84.32V83.78C121.79,83.94 122.17,82.51 122.24,82.21L122.51,82C121.93,81.94 121.51,81.51 121.36,80.96C121.29,80.68 121.29,80.41 121.32,80.12C121.33,80.02 121.51,79 121.85,79.36L121.97,78.88C121.72,78.99 121.47,78.65 121.43,78.45C121.38,78.23 121.38,77.99 121.39,77.77C121.41,77.38 121.51,77.01 121.66,76.64L121.31,76.84L121.53,77L121.77,76.57C120.68,76.54 120.75,75.01 121.86,75.08L121.61,74.65C121.63,74.63 121.44,73.91 121.45,73.81C121.48,73.47 121.62,73.19 121.84,72.95H121.44L121.41,72.7L121.2,72.51C121.11,72.38 121.07,72.22 121.04,72.08C121,71.95 120.98,71.81 120.97,71.67C120.97,71.57 120.99,71 121.03,71.06L121.27,70.63C120.45,70.73 120.25,69.54 121.17,69.53L120.93,69.11C120.76,68.91 120.72,68.71 120.79,68.5C120.82,68.39 120.85,68.28 120.88,68.17C120.93,67.99 121.14,67.26 121.36,67.35L121.52,66.8C121.23,66.75 121,66.64 120.81,66.41C120.72,66.31 120.65,66.2 120.61,66.07C120.55,65.88 120.55,65.69 120.61,65.5L121.03,65.18C120.75,65.05 120.55,64.89 120.41,64.61C120.32,64.42 120.37,63.97 120.27,63.86L120.2,63.58L120.18,63.7L120.52,63.5C120.2,63.45 119.94,63.07 120.18,62.76C120.41,62.48 120.01,62.07 119.78,62.36L119.78,62.37Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M147.26,79.25C147.26,79.25 146.45,77.7 143.76,79.17L129.54,72.02L112.63,80.17L111.65,81.68C111.65,81.68 110.82,91.43 118.35,91.53C125.88,91.64 147.26,79.25 147.26,79.25Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M144.77,79.1C144.77,79.1 139.24,81.06 133.78,77.21C128.31,73.36 131.04,67.54 132.59,66.21C134.13,64.88 135.32,63.06 135.32,63.06C136.72,59.21 142.67,57.25 142.67,57.25C142.67,57.25 150.31,55.57 154.72,63.06C159.13,70.55 154.25,74.57 152.36,76.18"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M144.77,79.1C144.77,79.1 139.24,81.06 133.78,77.21C128.31,73.36 131.04,67.54 132.59,66.21C134.13,64.88 135.32,63.06 135.32,63.06C136.72,59.21 142.67,57.25 142.67,57.25C142.67,57.25 150.31,55.57 154.72,63.06C159.13,70.55 154.25,74.57 152.36,76.18"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M142.11,57.69C142.11,57.69 141.13,60.83 145.15,62.54C149.17,64.24 151.52,63.47 151.83,66.2C152.14,68.93 148.89,68.96 149.33,72.1C149.33,72.1 149.64,73.76 149.51,73.99C149.38,74.22 149.1,74.48 149.4,74.63C149.71,74.79 152.34,74.48 152.35,75.95C152.35,75.95 155.77,73.61 156.23,70.91C156.68,68.21 156.71,64.96 153.76,61.92C153.76,61.92 151.47,58.1 146.71,57.28C141.95,56.45 142.11,57.69 142.11,57.69L142.11,57.69Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M149.66,74.27C149.66,74.27 152.16,74.3 152.19,75.69C152.21,77.08 151.54,78.4 150,78.96C148.45,79.53 146.39,79.74 146.1,77.85"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M149.66,74.27C149.66,74.27 152.16,74.3 152.19,75.69C152.21,77.08 151.54,78.4 150,78.96C148.45,79.53 146.39,79.74 146.1,77.85"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M150.82,75.82C150.82,75.82 149.35,75.7 148.66,76.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M136.78,62.79C136.78,62.79 137.55,62.39 138.25,62.66C138.74,62.85 139.12,63.4 139.12,63.4"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.84,66.7C143.84,66.7 145.8,67.52 145.88,69.1"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M135.71,65.4C135.71,65.4 135.48,66.47 136.3,66.84C137.13,67.21 137.95,67.03 137.95,67.03"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.32,69.45C141.32,69.45 141.33,70.48 142.08,70.95C142.83,71.41 143.78,71.28 143.78,71.28"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M137.23,68.29C137.23,68.29 135.92,68.48 136.01,69.45C136.11,70.42 137.23,70.85 137.23,70.85"
        android:strokeLineJoin="round"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M134.31,71.42C134.31,71.42 134.5,72.73 135.5,73.23C136.86,73.91 138.14,73.44 138.14,73.44"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.24,71.93C130.24,71.93 121.71,72.77 115.69,78.36C115.69,78.36 112.67,79.43 111.86,81.2"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M130.24,71.93C130.24,71.93 121.71,72.77 115.69,78.36C115.69,78.36 112.67,79.43 111.86,81.2"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.97,75.81C131.97,75.81 130.63,77.52 131.97,78.74C133.31,79.97 134.3,79.7 134.3,79.7"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M131.97,75.81C131.97,75.81 130.63,77.52 131.97,78.74C133.31,79.97 134.3,79.7 134.3,79.7"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.48,73.92C129.48,73.92 126.21,75.49 125.95,76.14C125.69,76.79 124.5,77.56 124.5,77.56"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M129.48,73.92C129.48,73.92 126.21,75.49 125.95,76.14C125.69,76.79 124.5,77.56 124.5,77.56"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.83,80.15C118.83,80.15 114.86,78.37 116.09,76.02C117.32,73.67 119.34,73.2 120.77,75.13C120.77,75.13 120.21,74.17 121.13,74.11C122.04,74.05 122.61,75.07 122.61,77.07C122.61,77.07 121.39,79.9 118.83,80.15Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.628608"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.96,76.32C115.96,76.32 116.85,76.48 117.13,76.8C117.13,76.8 116.01,76.23 116.37,75.54C116.4,75.48 116.48,75.35 116.52,75.3C116.75,75.03 117.07,74.62 118.17,75.81C118.17,75.81 116.7,74.82 117.49,74.34C117.49,74.34 118.08,73.76 119.06,75.21"
        android:strokeLineJoin="round"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.22,80.32C143.22,80.32 136.91,78.25 129.71,80.81L143.22,80.32Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M143.22,80.32C143.22,80.32 136.91,78.25 129.71,80.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.68,82.27C118.68,82.27 116.54,82.16 115.69,82.61C115.69,82.61 112.02,80.61 109.42,82.44C106.81,84.26 103.14,89.51 102.6,92.38C102.06,95.25 104.13,96.9 107.39,97.35"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M118.68,82.27C118.68,82.27 116.54,82.16 115.69,82.61C115.69,82.61 112.02,80.61 109.42,82.44C106.81,84.26 103.14,89.51 102.6,92.38C102.06,95.25 104.13,96.9 107.39,97.35"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.97,79.14C146.97,79.14 144.64,80.39 144.41,81.65C144.18,82.92 144.37,87.86 140.51,91.95C136.64,96.05 122.93,100.34 116.81,99.65C110.68,98.96 105.01,98.73 104.94,96.62C104.86,94.52 108.96,88.81 112.98,88.2C117,87.59 119.64,89.04 119.64,89.04"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M146.97,79.14C146.97,79.14 144.64,80.39 144.41,81.65C144.18,82.92 144.37,87.86 140.51,91.95C136.64,96.05 122.93,100.34 116.81,99.65C110.68,98.96 105.01,98.73 104.94,96.62C104.86,94.52 108.96,88.81 112.98,88.2C117,87.59 119.64,89.04 119.64,89.04"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.51,82.38C130.51,82.38 128.13,79.2 125.95,78.59C123.77,77.98 123.62,76.75 123.62,76.75C123.62,76.75 118.72,78.78 118.68,80.81C118.64,82.84 121.9,88.97 128.14,89.96C134.38,90.96 137.23,88.81 137.23,88.81"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M130.51,82.38C130.51,82.38 128.13,79.2 125.95,78.59C123.77,77.98 123.62,76.75 123.62,76.75C123.62,76.75 118.72,78.78 118.68,80.81C118.64,82.84 121.9,88.97 128.14,89.96C134.38,90.96 137.23,88.81 137.23,88.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.26,83.02C133.49,83.02 133.67,82.84 133.67,82.6C133.67,82.37 133.49,82.19 133.26,82.19C133.03,82.19 132.84,82.37 132.84,82.6C132.84,82.84 133.03,83.02 133.26,83.02Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M137.53,84.54C137.76,84.54 137.95,84.35 137.95,84.12C137.95,83.89 137.76,83.71 137.53,83.71C137.3,83.71 137.11,83.89 137.11,84.12C137.11,84.35 137.3,84.54 137.53,84.54Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M139.44,81.78C139.67,81.78 139.85,81.59 139.85,81.36C139.85,81.13 139.67,80.95 139.44,80.95C139.21,80.95 139.02,81.13 139.02,81.36C139.02,81.59 139.21,81.78 139.44,81.78Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M142.81,83.49C143.04,83.49 143.23,83.3 143.23,83.07C143.23,82.84 143.04,82.66 142.81,82.66C142.58,82.66 142.39,82.84 142.39,83.07C142.39,83.3 142.58,83.49 142.81,83.49Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M140.75,88.22C140.98,88.22 141.16,88.03 141.16,87.8C141.16,87.57 140.98,87.39 140.75,87.39C140.52,87.39 140.33,87.57 140.33,87.8C140.33,88.03 140.52,88.22 140.75,88.22Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M129.97,91.95C130.2,91.95 130.38,91.77 130.38,91.53C130.38,91.3 130.2,91.12 129.97,91.12C129.74,91.12 129.55,91.3 129.55,91.53C129.55,91.77 129.74,91.95 129.97,91.95Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M126.99,96.13C127.22,96.13 127.41,95.94 127.41,95.71C127.41,95.48 127.22,95.3 126.99,95.3C126.76,95.3 126.57,95.48 126.57,95.71C126.57,95.94 126.76,96.13 126.99,96.13Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M137.23,94.05C137.46,94.05 137.65,93.86 137.65,93.63C137.65,93.4 137.46,93.21 137.23,93.21C137,93.21 136.82,93.4 136.82,93.63C136.82,93.86 137,94.05 137.23,94.05Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M119.23,92.37C119.46,92.37 119.65,92.18 119.65,91.95C119.65,91.72 119.46,91.54 119.23,91.54C119,91.54 118.82,91.72 118.82,91.95C118.82,92.18 119,92.37 119.23,92.37Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M124.06,91.95C124.29,91.95 124.47,91.77 124.47,91.53C124.47,91.3 124.29,91.12 124.06,91.12C123.82,91.12 123.64,91.3 123.64,91.53C123.64,91.77 123.82,91.95 124.06,91.95Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M122.19,94.88C122.42,94.88 122.61,94.69 122.61,94.46C122.61,94.23 122.42,94.04 122.19,94.04C121.96,94.04 121.77,94.23 121.77,94.46C121.77,94.69 121.96,94.88 122.19,94.88Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M119.47,98.75C119.7,98.75 119.89,98.56 119.89,98.33C119.89,98.1 119.7,97.91 119.47,97.91C119.24,97.91 119.06,98.1 119.06,98.33C119.06,98.56 119.24,98.75 119.47,98.75Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M114.92,96.55C115.15,96.55 115.34,96.36 115.34,96.13C115.34,95.9 115.15,95.71 114.92,95.71C114.69,95.71 114.5,95.9 114.5,96.13C114.5,96.36 114.69,96.55 114.92,96.55Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M109.18,95.3C109.41,95.3 109.6,95.11 109.6,94.88C109.6,94.65 109.41,94.46 109.18,94.46C108.95,94.46 108.76,94.65 108.76,94.88C108.76,95.11 108.95,95.3 109.18,95.3Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M111.62,90.64C111.85,90.64 112.04,90.46 112.04,90.23C112.04,90 111.85,89.81 111.62,89.81C111.39,89.81 111.2,90 111.2,90.23C111.2,90.46 111.39,90.64 111.62,90.64Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M114.08,93.82C114.31,93.82 114.5,93.63 114.5,93.4C114.5,93.17 114.31,92.98 114.08,92.98C113.85,92.98 113.67,93.17 113.67,93.4C113.67,93.63 113.85,93.82 114.08,93.82Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M108.76,98.53C108.99,98.53 109.18,98.34 109.18,98.11C109.18,97.88 108.99,97.69 108.76,97.69C108.53,97.69 108.34,97.88 108.34,98.11C108.34,98.34 108.53,98.53 108.76,98.53Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M121.47,89.24C121.7,89.24 121.89,89.05 121.89,88.82C121.89,88.59 121.7,88.4 121.47,88.4C121.24,88.4 121.06,88.59 121.06,88.82C121.06,89.05 121.24,89.24 121.47,89.24Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M115.34,87.24C115.57,87.24 115.75,87.05 115.75,86.82C115.75,86.59 115.57,86.4 115.34,86.4C115.11,86.4 114.92,86.59 114.92,86.82C114.92,87.05 115.11,87.24 115.34,87.24Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M117.09,83.71C117.32,83.71 117.51,83.52 117.51,83.29C117.51,83.06 117.32,82.87 117.09,82.87C116.86,82.87 116.67,83.06 116.67,83.29C116.67,83.52 116.86,83.71 117.09,83.71Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M112.28,85.57C112.51,85.57 112.7,85.38 112.7,85.15C112.7,84.92 112.51,84.73 112.28,84.73C112.05,84.73 111.87,84.92 111.87,85.15C111.87,85.38 112.05,85.57 112.28,85.57Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M109.59,84.73C109.82,84.73 110.01,84.54 110.01,84.31C110.01,84.08 109.82,83.89 109.59,83.89C109.36,83.89 109.18,84.08 109.18,84.31C109.18,84.54 109.36,84.73 109.59,84.73Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M115.75,80.56C115.98,80.56 116.17,80.37 116.17,80.14C116.17,79.91 115.98,79.73 115.75,79.73C115.52,79.73 115.34,79.91 115.34,80.14C115.34,80.37 115.52,80.56 115.75,80.56Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M124.47,75.1C124.7,75.1 124.89,74.92 124.89,74.69C124.89,74.46 124.7,74.27 124.47,74.27C124.24,74.27 124.06,74.46 124.06,74.69C124.06,74.92 124.24,75.1 124.47,75.1Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M129.29,79.14C129.52,79.14 129.71,78.95 129.71,78.72C129.71,78.49 129.52,78.3 129.29,78.3C129.06,78.3 128.87,78.49 128.87,78.72C128.87,78.95 129.06,79.14 129.29,79.14Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M128.66,76.37C128.89,76.37 129.07,76.18 129.07,75.95C129.07,75.72 128.89,75.53 128.66,75.53C128.43,75.53 128.24,75.72 128.24,75.95C128.24,76.18 128.43,76.37 128.66,76.37Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M132.84,87.39C133.07,87.39 133.26,87.2 133.26,86.97C133.26,86.74 133.07,86.55 132.84,86.55C132.61,86.55 132.42,86.74 132.42,86.97C132.42,87.2 132.61,87.39 132.84,87.39Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M126.57,89.24C126.8,89.24 126.99,89.05 126.99,88.82C126.99,88.59 126.8,88.4 126.57,88.4C126.34,88.4 126.16,88.59 126.16,88.82C126.16,89.05 126.34,89.24 126.57,89.24Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M127.41,85.15C127.64,85.15 127.82,84.96 127.82,84.73C127.82,84.5 127.64,84.31 127.41,84.31C127.18,84.31 126.99,84.5 126.99,84.73C126.99,84.96 127.18,85.15 127.41,85.15Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M126.15,81.23C126.39,81.23 126.57,81.04 126.57,80.81C126.57,80.58 126.39,80.39 126.15,80.39C125.93,80.39 125.74,80.58 125.74,80.81C125.74,81.04 125.93,81.23 126.15,81.23Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M123.22,85.57C123.45,85.57 123.64,85.38 123.64,85.15C123.64,84.92 123.45,84.73 123.22,84.73C122.99,84.73 122.8,84.92 122.8,85.15C122.8,85.38 122.99,85.57 123.22,85.57Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M123.43,82.6C123.66,82.6 123.84,82.42 123.84,82.19C123.84,81.96 123.66,81.77 123.43,81.77C123.19,81.77 123.01,81.96 123.01,82.19C123.01,82.42 123.19,82.6 123.43,82.6Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M132.42,94.05C132.65,94.05 132.84,93.86 132.84,93.63C132.84,93.4 132.65,93.21 132.42,93.21C132.19,93.21 132,93.4 132,93.63C132,93.86 132.19,94.05 132.42,94.05Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M119.89,80.98C120.12,80.98 120.31,80.79 120.31,80.56C120.31,80.33 120.12,80.14 119.89,80.14C119.66,80.14 119.47,80.33 119.47,80.56C119.47,80.79 119.66,80.98 119.89,80.98Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M123.84,78.73C124.07,78.73 124.26,78.54 124.26,78.31C124.26,78.08 124.07,77.89 123.84,77.89C123.61,77.89 123.43,78.08 123.43,78.31C123.43,78.54 123.61,78.73 123.84,78.73Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M118.59,74.63L105.85,73.4"
        android:strokeLineJoin="round"
        android:strokeWidth="0.628608"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.69,78.62C118.69,78.62 114.64,77.52 115.46,75.09C116.27,72.66 118.13,71.91 119.79,73.53C119.79,73.53 119.11,72.71 119.97,72.51C120.84,72.31 121.53,73.2 121.83,75.12C121.83,75.12 121.09,77.99 118.69,78.62Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.502886"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.38,75.4C115.38,75.4 116.24,75.42 116.56,75.68C116.56,75.68 115.41,75.31 115.65,74.6C115.67,74.53 115.72,74.4 115.75,74.35C115.93,74.05 116.18,73.61 117.4,74.58C117.4,74.58 115.85,73.86 116.54,73.28C116.54,73.28 117.01,72.64 118.16,73.87"
        android:strokeLineJoin="round"
        android:strokeWidth="0.502886"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.93,97.9C132.68,97.23 131.9,96.92 131.27,97.24C130.63,97.56 129.84,97.83 129.27,97.62C128.13,97.18 125.8,95.93 123.18,95.66C120.56,95.4 118.5,94.67 118.06,95.66C117.62,96.65 119.54,98.23 123.39,98.55C123.39,98.55 119.04,100.34 115.63,99.66C112.21,98.98 112.99,102.97 113.25,103.82C113.51,104.66 122.34,108.86 127.2,106.61C128.75,105.9 130.04,105.15 131.09,104.46C133.12,103.12 133.95,100.56 133.08,98.28L132.93,97.9L132.93,97.9Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.19,103.48C115.19,103.48 125.82,103.58 126.01,105.3C126.01,105.3 124.13,109.19 120.44,109.19C116.74,109.18 115.19,103.48 115.19,103.48Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122,108.93C122,108.93 120.01,110.56 116.46,110.25C112.91,109.94 105.37,106.68 104.98,106.42C104.59,106.16 101.04,105.69 101.04,105.69L104.1,97.02C104.1,97.02 106.49,98.75 109.24,97.81C112,96.88 117.06,95.94 117.2,97.55C117.2,97.55 117.23,99.37 113.68,99.98C113.68,99.98 117.89,103.16 124.3,102.56C128.27,102.18 125.23,107 116.06,104.61"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122,108.93C122,108.93 120.01,110.56 116.46,110.25C112.91,109.94 105.37,106.68 104.98,106.42C104.59,106.16 101.04,105.69 101.04,105.69L104.1,97.02C104.1,97.02 106.49,98.75 109.24,97.81C112,96.88 117.06,95.94 117.2,97.55C117.2,97.55 117.23,99.37 113.68,99.98C113.68,99.98 117.89,103.16 124.3,102.56C128.27,102.18 125.23,107 116.06,104.61"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.87,104.47C124.87,104.47 126.41,104.89 126.01,105.97C125.61,107.05 118.45,108.5 115.41,107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.93,107.02C124.93,107.02 120.73,110.88 115.19,108.72"
        android:strokeLineJoin="round"
        android:strokeWidth="0.75433"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.75,84.38C78.61,79.84 80.07,75.43 82.1,71.28C83.99,67.41 86.89,60.93 90.71,58.61C93.92,56.65 102.73,57.69 102.73,57.69C102.73,57.69 108.03,64.22 107.29,65.5C106.94,66.1 106.85,66.91 106.67,67.58C105.61,71.63 104.27,75.6 102.11,79.22C100.77,81.47 99.13,83.65 97.17,85.41C95.07,87.29 92.53,88.57 90.19,90.11L95.21,66.4L91.41,89.33L105.35,95.32C106.18,95.69 106.57,96.64 106.24,97.49C105.01,100.58 106.07,106.89 104.19,106.89C98.26,106.89 92.32,106.01 86.65,104.3C83.61,103.38 81.59,102.02 79.72,99.47C78.14,97.3 77.03,94.97 76.92,92.35C76.82,90.06 77.17,87.79 77.54,85.54C77.6,85.15 77.67,84.76 77.75,84.38Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M105.99,64.88C105.12,65.24 104.66,66.07 104.83,66.99C104.85,67.11 104.99,67.21 105.11,67.2C105.73,67.18 105.82,66.25 105.19,66.14C104.09,65.95 104.31,67.65 104.32,68.22C104.32,68.41 104.5,68.54 104.68,68.5C105.26,68.36 104.97,67.48 104.39,67.69C103.59,67.98 103.87,69.16 104.32,69.6L104.38,69.16C103.84,69.53 103.22,70.29 103.41,71C103.54,71.48 104.13,71.62 104.39,71.18C104.5,70.99 104.36,70.74 104.15,70.75C103.11,70.78 102.65,71.88 103.33,72.65L103.6,72.18C102.5,71.82 101.72,73.14 102.57,73.93L102.62,73.49C102.14,73.75 101.71,74.38 101.93,74.95C102.06,75.29 102.59,75.54 102.78,75.12C102.86,74.95 102.77,74.67 102.54,74.69C101.71,74.79 101.24,75.62 101.6,76.38L101.85,75.96C101.06,75.89 100.51,76.59 100.65,77.34C100.71,77.69 101.26,77.54 101.19,77.19C101.12,76.77 101.49,76.49 101.85,76.52C102.08,76.54 102.18,76.27 102.09,76.09C101.92,75.75 102.14,75.31 102.54,75.26L102.29,74.83L102.26,74.91L102.5,74.77C102.4,74.64 102.41,74.49 102.53,74.33C102.61,74.16 102.74,74.05 102.91,73.97C103.07,73.89 103.1,73.65 102.97,73.53C102.53,73.12 102.89,72.54 103.45,72.72C103.74,72.81 103.9,72.45 103.73,72.25C103.39,71.86 103.67,71.33 104.15,71.31L103.9,70.89C104.02,70.69 104.01,70.45 104.12,70.24C104.25,69.99 104.44,69.8 104.66,69.64C104.81,69.54 104.86,69.34 104.72,69.2C104.55,69.06 104.46,68.87 104.45,68.64C104.53,68.42 104.56,68.19 104.53,67.95L104.89,68.22C104.88,67.99 104.75,66.64 105.11,66.63L105.38,66.84C105.27,66.24 105.58,65.66 106.14,65.42C106.48,65.29 106.33,64.74 105.99,64.88L105.99,64.88Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M100.52,77.67C100.47,77.96 100.47,78.19 100.53,78.47C100.57,78.65 100.8,78.74 100.95,78.64C101.6,78.22 100.99,77.34 100.32,77.7C99.57,78.1 99.62,79.58 100.13,80.15C100.35,80.4 100.7,80.08 100.57,79.8C99.76,78.12 97.4,81.33 99.29,81.5C99.46,81.51 99.56,81.35 99.58,81.21C99.65,80.58 98.91,80.45 98.53,80.81C97.9,81.4 97.77,82.58 97.82,83.37C97.84,83.56 98.06,83.75 98.25,83.62C98.87,83.15 98.22,82.41 97.57,82.66C96.8,82.95 96.06,83.94 96.56,84.74C96.64,84.88 96.88,84.94 97,84.79C97.2,84.54 97.21,84.24 96.95,84C96.63,83.7 96.21,83.88 95.94,84.15C95.61,84.48 95.09,85.15 95,85.61C94.92,85.96 95.15,86.29 95.53,86.25C95.72,86.23 95.92,86.01 95.77,85.82C95,84.78 93.42,85.46 93.76,86.75C93.83,87.03 94.23,87.02 94.31,86.75C94.38,86.49 94.29,86.22 94.03,86.09C93.61,85.87 93.2,86.25 92.89,86.49C92.61,86.71 92.1,86.97 92.01,87.36C91.93,87.69 92.13,87.94 92.43,88.01C92.58,88.05 92.75,87.97 92.78,87.82C92.88,87.39 92.6,86.97 92.13,86.96C91.6,86.94 91.31,87.45 91.19,87.89C91.09,88.24 91.63,88.39 91.73,88.04C91.75,87.93 91.79,87.84 91.85,87.75C91.9,87.56 92.03,87.53 92.24,87.67L92.58,87.47C92.53,87.46 93.76,86.61 93.76,86.61H94.31C94.15,86.01 94.88,85.55 95.29,86.11L95.53,85.68C95.65,85.67 95.86,85.23 95.93,85.13C96.13,84.84 96.38,84.67 96.6,84.39L97.04,84.45C96.62,83.78 97.6,83.4 97.96,83.13L98.39,83.37C98.36,82.95 98.38,82.55 98.49,82.14C98.54,81.95 98.61,81.76 98.7,81.59C98.69,81.61 99.02,81.13 99.01,81.21L99.29,80.93C98.9,80.9 99.72,79.33 100.08,80.09L100.53,79.75C100.22,79.41 100.25,78.42 100.66,78.15L101.08,78.32L101.07,77.82C101.13,77.46 100.58,77.31 100.52,77.67H100.52Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M101.15,59.81C100.67,60.37 101.13,61.21 101.88,60.92C102.1,60.83 102.17,60.53 101.95,60.4C101.82,60.33 101.75,60.31 101.61,60.3C101.44,60.29 101.34,60.44 101.32,60.58C101.22,61.4 101.95,62.12 102.78,62.05C103.46,61.99 103.26,61.25 102.72,61.13C102.56,61.1 102.43,61.18 102.37,61.33C101.98,62.29 103.01,62.79 103.8,62.75C104,62.74 104.17,62.52 104.04,62.33C103.8,61.96 103.29,62.07 103.25,62.52C103.2,63.16 103.99,63.62 104.51,63.77C104.72,63.83 104.98,63.56 104.82,63.35C104.58,63.02 104.04,63 103.88,63.44C103.65,64.09 104.41,64.35 104.88,64.38C105.13,64.4 105.26,64.06 105.08,63.9L104.95,63.78C104.78,63.61 104.47,63.73 104.47,63.98C104.46,64.78 104.74,65.74 105.73,65.57C106.09,65.51 105.94,64.97 105.58,65.03C105.03,65.12 105.03,64.33 105.04,63.98L104.55,64.18L104.68,64.3L104.88,63.82C104.68,63.83 104.5,63.77 104.34,63.64L104.66,63.22C104.34,63.13 103.77,62.88 103.83,62.44L103.69,62.69L103.75,62.67L103.5,62.53L103.56,62.61L103.8,62.19C103.38,62.2 102.68,62.07 102.91,61.48L102.57,61.68L102.71,61.77V61.48C102.52,61.48 102.35,61.43 102.19,61.33C101.92,61.11 101.82,60.87 101.89,60.58L101.61,60.87L101.66,60.89L101.73,60.37L101.55,60.21C101.79,59.93 101.39,59.53 101.15,59.81L101.15,59.81Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M137.23,94.05C137.23,94.05 139.5,103.12 130.97,104.89L133.42,101.49L132.7,96.27L137.23,94.05Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M118.51,42.18C118.51,42.18 117.99,41.75 117.81,42.03C117.63,42.31 117.71,46.95 120.3,48C122.89,49.05 125.63,44.87 125.46,44.16C125.3,43.46 123.71,44.44 121.5,43.74C119.28,43.03 118.51,42.18 118.51,42.18Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M118.89,43.07C118.89,43.07 120.84,44.81 124.4,44.53C124.4,44.53 124.17,45.49 122.58,45.37C121.22,45.27 119.3,44.66 119.04,43.76C118.81,43 118.89,43.07 118.89,43.07V43.07Z"
        android:fillColor="#ffffff"/>
  </group>
</vector>
