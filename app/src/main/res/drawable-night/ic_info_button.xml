<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="22dp"
    android:height="22dp"
    android:viewportWidth="22"
    android:viewportHeight="22">
  <path
      android:pathData="M8,11L11,14L14,11"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#8F9193"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M9,7L11,9L13,7"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#8F9193"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,21C16.523,21 21,16.523 21,11C21,5.477 16.523,1 11,1C5.477,1 1,5.477 1,11C1,16.523 5.477,21 11,21Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="11"
          android:startY="6.5"
          android:endX="11"
          android:endY="11"
          android:type="linear">
        <item android:offset="0" android:color="#FF090A0C"/>
        <item android:offset="1" android:color="#00090A0C"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M11,21C16.523,21 21,16.523 21,11C21,5.477 16.523,1 11,1C5.477,1 1,5.477 1,11C1,16.523 5.477,21 11,21Z"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#1F2327"
      android:strokeLineCap="round"/>
</vector>
