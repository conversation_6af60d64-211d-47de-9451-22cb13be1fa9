<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingBottom="8dp"
    android:paddingEnd="12dp"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/in_app_consumables_name"
        style="@style/text_style_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/others"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/impressions" />

    <TextView
        android:id="@+id/in_app_consumables_count"
        style="@style/text_style_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/in_app_consumables_name"
        app:layout_constraintTop_toBottomOf="@+id/in_app_consumables_name"
        tools:text="150 left" />
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>