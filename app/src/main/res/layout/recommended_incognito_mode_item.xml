<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/selectableItemBackground"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:cardPreventCornerOverlap="false"
    app:cardUseCompatPadding="true"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/user_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@drawable/incognito_card_background"
        android:padding="40dp">

        <ImageView
            android:id="@+id/incognito_mask"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="4dp"
            android:src="@drawable/ic_mask"
            app:layout_constraintBottom_toTopOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="100dp"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:scaleType="fitStart"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintWidth_max="100dp" />

        <TextView
            android:id="@+id/title"
            style="@style/text_style_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/ghost_mode"
            android:textColor="@color/gray_50"
            android:layout_marginTop="12dp"
            app:layout_constraintBottom_toTopOf="@id/desc"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/incognito_mask"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/desc"
            style="@style/text_style_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/incognito_mode_title"
            android:textColor="@color/gray_50"
            android:layout_marginBottom="8dp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@+id/btn_go_cognito"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_go_cognito"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/pink_button_32_rounded"
            android:backgroundTint="@color/gray_50"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/ghost_profile"
            android:textAllCaps="false"
            android:textColor="@color/gray_400"
            app:layout_constraintBottom_toTopOf="@+id/btn_not_now"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btn_not_now"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/not_now"
            android:textColor="@color/gray_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
