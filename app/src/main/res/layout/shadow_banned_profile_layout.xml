<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:background="@drawable/background_color_border_20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/shdb_image"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="31dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_upload_image_warning" />

        <TextView
            android:id="@+id/sdb_title"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/shadow_inline_alert_title"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/shdb_image"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/shdb_description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/shadow_inline_alert_desc"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/sdb_title"
            app:layout_constraintTop_toBottomOf="@+id/sdb_title" />

        <com.duaag.android.views.DuaButton
            style="@style/text_style_200"
            android:id="@+id/shdb_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/update_photo"
            android:layout_marginVertical="8dp"
            app:buttonType="Link"
            android:drawableEnd="@drawable/ic_right_small_arrow"
            android:drawableTint="@color/gray_200"
            android:drawablePadding="14dp"
            android:includeFontPadding="false"
            app:layout_constraintTop_toBottomOf="@id/shdb_description"
            app:layout_constraintStart_toStartOf="@id/sdb_title"
            app:layout_constraintBottom_toBottomOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>