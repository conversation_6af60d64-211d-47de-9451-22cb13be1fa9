<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="wrap_content"
    android:layout_height="136dp"
    android:minWidth="119dp"
    android:layout_marginEnd="12dp"
    android:background="@drawable/inapp_packages_rounded_rect">

    <ImageView
        android:id="@+id/in_app_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_impressions_icon" />

    <TextView
        android:id="@+id/in_app_title"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="2dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:maxLines="1"
        android:text="@{profileInAppPackages.packageType}"
        android:textColor="@color/title_secondary"
        app:layout_constraintBottom_toTopOf="@+id/price_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Impressions" />

    <TextView
        android:id="@+id/price_txt"
        style="@style/text_style_75"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:maxLines="1"
        android:text="@{profileInAppPackages.price}"
        android:textColor="@color/description_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="from 0,99 CHF " />

    <ImageButton
        android:id="@+id/info_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_info_button"
        android:background="@color/transparent"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>