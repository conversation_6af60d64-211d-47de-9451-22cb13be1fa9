<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="clickListener"
            type="com.duaag.android.settings.fragments.account_settings.delete_account.adapters.DeleteReasonsAdapter.ReasonsClickListener" />

        <variable
            name="item"
            type="com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData.DeleteReasonsModel" />
    </data>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/transparent"
        app:rippleColor="@color/white"
        android:layout_marginEnd="16dp"
        android:onClick="@{() -> clickListener.onClick(item)}">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:animateLayoutChanges="true"
    android:layout_height="wrap_content">
        <TextView
            android:id="@+id/textView24"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="21dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            style="@style/text_style_100"
            android:text="@{item.title}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/checkbox"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="I'd rather not say" />

        <ImageView
            android:id="@+id/checkbox"
            android:layout_width="28dp"
            android:layout_height="28dp"
            tools:src="@drawable/ic_checked_radio_button"
            app:layout_constraintBottom_toBottomOf="@+id/textView24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/textView24"
            app:itemSelected="@{item.isSelected}" />

        <View
            android:id="@+id/view8"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/border"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/checkbox"
            app:layout_constraintStart_toStartOf="@+id/textView24" />
</androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>
</layout>