<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fremium_layout"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="46dp"
    android:elevation="6dp"
    android:background="@drawable/fremium_rect">

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="82dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="78dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView21"
            android:layout_width="18dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_diamond"
            app:tint="@color/gray_50" />

        <TextView
            android:id="@+id/dua_premium_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="6dp"
            android:includeFontPadding="false"
            style="@style/text_style_400"
            tools:text="dua Premium"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/gray_50" />
    </LinearLayout>

    <TextView
        android:id="@+id/textView54"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="21dp"
        android:text="@string/go_premium_desc"
        android:textAlignment="center"
        android:textColor="@color/gray_50"
        style="@style/text_style_100"
        android:fontFamily="@font/tt_norms_pro_normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2" />
</androidx.constraintlayout.widget.ConstraintLayout>