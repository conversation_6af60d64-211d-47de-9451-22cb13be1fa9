<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.duaag.android.R"/>

        <variable
            name="viewModel"
            type="com.duaag.android.auth_interfaces.HasEmailInput" />
        <variable
            name="isChangeLogin"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".signup.fragment.InputEmailFragment">

        <TextView
            android:id="@+id/txt_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:layout_marginTop="40dp"
            android:layout_marginHorizontal="16dp"
            android:textColor="@color/title_primary"
            style="@style/text_style_400"
            android:text="@string/email_adress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_email_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            tools:text="@string/change_the_email_address_with_a_new_one"
            app:addDifferentTypeface="@{isChangeLogin?R.string.change_the_email_address_with_a_new_one:R.string.please_provide_your_email_adress_in_order_to_verify_your_account_and_to_retrieve_a_verification_badge}"
            app:typefaceColor="@{R.color.description_primary }"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="@+id/txt_email"
            app:layout_constraintStart_toStartOf="@+id/txt_email"
            app:layout_constraintTop_toBottomOf="@+id/txt_email" />

        <TextView
            android:id="@+id/email_label"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/email_"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="@id/txt_email_description"
            app:layout_constraintEnd_toEndOf="@id/txt_email_description"
            app:layout_constraintTop_toBottomOf="@id/txt_email_description" />

        <EditText
            android:id="@+id/edit_text_email"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:hint="@string/enter_email_address"
            android:text="@={viewModel.returnEmailLiveData()}"
            android:imeOptions="actionNext"
            android:importantForAutofill="no"
            android:inputType="textEmailAddress"
            android:padding="16dp"
            android:textColor="@color/title_primary"
            android:textColorHint="@color/gray_200"
            android:textCursorDrawable="@drawable/ic_typing_indicator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email_label">

            <requestFocus />
        </EditText>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="?android:attr/progressBarStyleHorizontal"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/edit_text_email"
            app:layout_constraintTop_toBottomOf="@id/edit_text_email"
            app:layout_constraintStart_toStartOf="@+id/edit_text_email"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/error_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/pink_500"
            android:fontFamily="@font/tt_norms_pro_normal"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="@+id/edit_text_email"
            app:layout_constraintStart_toStartOf="@+id/edit_text_email"
            app:layout_constraintTop_toBottomOf="@+id/edit_text_email" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>