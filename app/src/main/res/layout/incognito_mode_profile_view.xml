<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <View
        android:id="@+id/top_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/border"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/bottom_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/border"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_mask_bold"
            app:tint="@color/title_primary" />

        <TextView
            android:id="@+id/title"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="24dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/ghost_mode_active_inline"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toBottomOf="@+id/image"
            app:layout_constraintEnd_toStartOf="@id/button"
            app:layout_constraintStart_toEndOf="@id/image"
            app:layout_constraintTop_toTopOf="@+id/image" />


        <com.duaag.android.views.DuaButton
            android:id="@+id/button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="16dp"
            android:minHeight="0dp"
            android:paddingHorizontal="24dp"
            android:stateListAnimator="@null"
            android:text="@string/turn_off_ghost_button"
            app:buttonType="Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>