<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingTop="56dp"
    android:paddingBottom="24dp"
    android:clipToPadding="false"
    android:animateLayoutChanges="true"
    xmlns:tools="http://schemas.android.com/tools">


    <ImageView
        android:id="@+id/envelope_body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_body" />

    <ImageView
        android:id="@+id/envelope_head"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="0.59dp"
        android:elevation="-2dp"
        app:layout_constraintBottom_toTopOf="@+id/envelope_body"
        app:layout_constraintEnd_toEndOf="@+id/envelope_body"
        app:layout_constraintStart_toStartOf="@+id/envelope_body"
        app:srcCompat="@drawable/ic_head" />

    <ImageView
        android:id="@+id/envelope_letter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="-1dp"
        tools:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/envelope_body"
        app:layout_constraintEnd_toEndOf="@+id/envelope_body"
        app:layout_constraintStart_toStartOf="@+id/envelope_body"
        app:layout_constraintTop_toTopOf="@+id/envelope_head"
        app:srcCompat="@drawable/ic_paper" />

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="24dp"
        android:text="@string/thank_you_we_ve_sent_an_invitation_link_to_your_friend"
        android:textAlignment="center"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/envelope_body" />

    <TextView
        android:id="@+id/body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/invite_a_friend_envelope_desc"
        android:textAlignment="center"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="@+id/title"
        app:layout_constraintStart_toStartOf="@+id/title"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <Button
        android:id="@+id/got_it_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/got_it"
        android:textAllCaps="false"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:stateListAnimator="@null"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        app:layout_constraintEnd_toEndOf="@+id/body"
        app:layout_constraintStart_toStartOf="@+id/body"
        app:layout_constraintTop_toBottomOf="@id/body" />

</androidx.constraintlayout.widget.ConstraintLayout>