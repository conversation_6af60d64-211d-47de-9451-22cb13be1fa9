<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/imageView8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_account_icon"
        android:layout_marginTop="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_300"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:gravity="center_horizontal|center_vertical"
        android:text="@string/account_created"
        android:layout_marginTop="32dp"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView8" />


    <TextView
        android:id="@+id/textView47"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:text="@string/please_continue"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/continue_button"
        app:buttonType="Primary"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginTop="32dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:stateListAnimator="@null"
        android:text="@string/continue_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView47" />

    <TextView
        android:id="@+id/sign_out_btn"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="40dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:gravity="center"
        android:text="@string/sign_out"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/continue_button"
        app:layout_constraintVertical_bias="0.0" />


</androidx.constraintlayout.widget.ConstraintLayout>