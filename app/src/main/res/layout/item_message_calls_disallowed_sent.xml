<?xml version="1.0" encoding="utf-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_style_75"
            android:id="@+id/txt_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:paddingBottom="12dp"
            app:layout_constraintBottom_toTopOf="@id/txt_message"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/description_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="gone" />

        <com.duaag.android.views.CostumBubbleLayout
            android:id="@+id/txt_message"
            android:paddingStart="12dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="9dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            app:layout_constraintEnd_toEndOf="@id/pending_dot"
            app:layout_constraintTop_toBottomOf="@+id/txt_date"
            app:viewPartMain="@id/content_txt"
            app:viewPartSlave="@id/timeStamp"
            tools:background="@drawable/background_sent_message">

            <TextView
                android:id="@+id/content_txt"
                style="@style/text_style_200"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginStart="8dp"
                android:autoLink="web"
                android:clickable="false"
                android:drawablePadding="6dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:gravity="center"
                android:maxWidth="274dip"
                android:text="@string/you_disallowed_calls"
                android:textColorLink="@color/pink_500"
                app:drawableStartCompat="@drawable/ic_call_disallowed" />

            <TextView
                android:id="@+id/timeStamp"
                style="@style/text_style_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_alignBottom="@id/content_txt"
                android:layout_toEndOf="@id/content_txt"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textColor="@color/gray_200"
                tools:text="12:42" />
        </com.duaag.android.views.CostumBubbleLayout>

        <ImageView
            android:id="@+id/pending_dot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="3dp"
            android:src="@drawable/ic_pendingdot"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/txt_message"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/txt_message" />
    </androidx.constraintlayout.widget.ConstraintLayout>
