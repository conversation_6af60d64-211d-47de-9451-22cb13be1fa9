<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.duaag.android.premium_subscription.PremiumActivity">

    <fragment
        android:id="@+id/nav_host_settings"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"/>

    <FrameLayout
        android:id="@+id/paywallFrame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:indeterminateTint="@color/border"
        android:layout_gravity="center" />

</FrameLayout>