<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <TextView
        android:id="@+id/title"
        style="@style/text_style_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="56dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="56dp"
        android:fontFamily="@font/tt_norms_pro_bold"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/whats_new_on_dua"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/feature_rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="16dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/continue_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:listitem="@layout/new_features_item" />

    <Button
        android:id="@+id/continue_button"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/close"
        android:stateListAnimator="@null"
        android:textAllCaps="false"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>