<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true">

    <ImageView
        android:id="@+id/flag"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        tools:src="@drawable/flag_albania"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/community_name"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:ellipsize="end"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        android:textSize="@dimen/text_size_200"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/checked"
        app:layout_constraintStart_toEndOf="@+id/flag"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Albanian" />

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/checked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:layout_marginEnd="24dp"
        android:background="@android:color/transparent"
        android:buttonTint="@color/checkbox_color"
        android:focusableInTouchMode="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/yours"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_corners_8_dp"
        android:backgroundTint="@color/bg_secondary_old"
        android:text="Yours"
        android:visibility="gone"
        android:paddingVertical="4dp"
        android:paddingHorizontal="8dp"
        android:layout_marginEnd="24dp"
        android:textColor="@color/label_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
