<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layoutDirection="locale"
    app:chainUseRtl="true"
    android:layout_marginBottom="22dp"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/image"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        android:padding="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:background="@drawable/circular_stroke"
        tools:srcCompat="@drawable/ic_notification_boost" />

    <TextView
        android:id="@+id/row_title"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="8dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/title_secondary"
        app:layout_constraintBottom_toTopOf="@id/row_description"
        app:layout_constraintEnd_toStartOf="@+id/action_button"
        app:layout_constraintStart_toEndOf="@+id/image"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Sofie has likeffsdfsdfsdfsdfsdfsdfsdffsdfsdfsdffsdsfdfdsd you" />

    <TextView
        android:id="@+id/row_description"
        style="@style/text_style_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="6dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/title_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/action_button"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/image"
        app:layout_constraintTop_toBottomOf="@+id/row_title"
        app:layout_constraintVertical_bias="0.5"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Today" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/action_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        tools:text="Message"
        style="@style/text_style_200"
        android:visibility="gone"
        tools:visibility="visible"
        android:textColor="@color/title_primary"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:background="@drawable/rounded_shape_32_dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>