<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginStart="32dp"
        android:src="@drawable/ic_info_spam_link_read"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/title"
        style="@style/text_style_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/spam_alert_title"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="11dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/spam_alert_desc"
        android:textColor="@color/title_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/continue_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="24dp"
        android:stateListAnimator="@null"
        android:text="@string/i_understand"
        app:buttonType="Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description"/>

</androidx.constraintlayout.widget.ConstraintLayout>