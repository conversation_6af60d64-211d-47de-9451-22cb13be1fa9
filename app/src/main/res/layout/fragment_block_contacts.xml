<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <TextView
        android:id="@+id/textView10"
        style="@style/text_style_100"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/block_contacts_desc"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="32dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView10">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/snack_constraint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/notif_off"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/notif_title"
                app:srcCompat="@drawable/contacts_icon"
                android:tint="@color/icon_secondary"/>

            <TextView
                style="@style/text_style_200"
                android:id="@+id/notif_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="32dp"
                android:text="@string/contact_book_not_allowed_title"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:textColor="@color/title_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/notif_off"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                style="@style/text_style_100"
                android:id="@+id/notif_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="32dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/description_primary"
                android:text="@string/contact_book_not_allowed_desc"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/notif_title"
                app:layout_constraintTop_toBottomOf="@+id/notif_title" />

            <com.duaag.android.views.DuaButton
                android:id="@+id/notif_button"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="16dp"
                app:buttonType="Primary"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingStart="24dp"
                android:paddingTop="4dp"
                android:paddingEnd="24dp"
                android:paddingBottom="4dp"
                android:text="@string/go_to_settings"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/notif_title"
                app:layout_constraintTop_toBottomOf="@+id/notif_description" />

            <View
                android:id="@+id/botttom_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/border"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <View
                android:id="@+id/top_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/border"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:background="@android:color/transparent"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:iconifiedByDefault="false"
            app:queryHint="@string/search"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/snack_constraint" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/contacts_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_view" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/empty_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/contact_book_illustration" />

        <TextView
            style="@style/text_style_300"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/allow_contact_book_title"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/text_style_200"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/allow_contact_book_desc"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/continue_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="32dp"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            app:buttonType="Primary" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/missing_permissions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/no_contact_permission_book_illustration" />

        <TextView
            style="@style/text_style_300"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/contact_book_not_allowed_title"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/text_style_200"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/contact_book_not_allowed_desc"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/settings_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="32dp"
            android:stateListAnimator="@null"
            android:text="@string/go_to_settings"
            app:buttonType="Primary" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/add_contact_manually_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="32dp"
            android:stateListAnimator="@null"
            android:text="@string/add_contact_manually"
            app:buttonType="Link" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
