<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".signup.fragment.BirthdayFragment">

    <TextView
        android:id="@+id/txt_name"
        style="@style/text_style_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/select_your_gender"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/male_cardView"
        android:layout_width="142dp"
        android:layout_height="142dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="8dp"
        android:checkable="true"
        android:clickable="true"
        android:focusable="true"
        app:cardElevation="0dp"
        app:cardForegroundColor="@color/transparent"
        app:layout_constraintEnd_toStartOf="@+id/female_cardView"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_name"
        app:rippleColor="@color/transparent"
        app:strokeColor="@color/gender_male_stroke_selected"
        app:cardCornerRadius="24dp"
        app:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/background">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView3"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginTop="20dp"
                android:scaleType="centerInside"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/male_illustration_color" />

            <TextView
                android:id="@+id/textView43"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/male"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/female_cardView"
        android:layout_width="142dp"
        android:layout_height="142dp"
        android:checkable="true"
        android:clickable="true"
        android:focusable="true"
        app:cardElevation="0dp"
        android:layout_marginStart="8dp"
        app:cardForegroundColor="@color/transparent"
        app:cardBackgroundColor="@color/background"
        app:layout_constraintBottom_toBottomOf="@+id/male_cardView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/male_cardView"
        app:layout_constraintTop_toTopOf="@+id/male_cardView"
        app:rippleColor="@color/transparent"
        app:strokeColor="@color/gender_female_color_selector"
        app:cardCornerRadius="24dp"
        app:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView32"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginTop="20dp"
                android:scaleType="centerInside"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/female_illustration_color" />

            <TextView
                android:id="@+id/textView44"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/roboto_medium_numbers"
                android:text="@string/female"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
