<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="40dp">

        <ImageView
            android:id="@+id/imageView5"
            android:layout_width="40dp"
            android:layout_height="4dp"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:background="@drawable/bottom_sheet_thumb"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imageView5">
            <LinearLayout
                android:id="@+id/button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/img1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_unread_chat"
                    android:padding="7dp"
                    android:background="@drawable/circle_chat_bacground"
                    />
                <TextView
                    android:id="@+id/text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@color/transparent"
                    android:backgroundTint="@color/transparent"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:textAlignment="center"
                    android:elevation="0dp"
                    android:text="@string/unread"
                    android:textAllCaps="false"
                    android:layout_gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/delete_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/delete_image"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_x_circle"
                    android:padding="7dp"
                    android:background="@drawable/circle_chat_bacground"
                    />
                <TextView
                    android:id="@+id/delete_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@color/transparent"
                    android:backgroundTint="@color/transparent"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:textAlignment="center"
                    android:elevation="0dp"
                    android:text="@string/delete"
                    android:textAllCaps="false"
                    android:layout_gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/button2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/img2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="7dp"
                    android:src="@drawable/ic_unmatch"
                    android:background="@drawable/circle_chat_bacground"
                    />
                <TextView
                    android:id="@+id/text2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@color/transparent"
                    android:backgroundTint="@color/transparent"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:textAlignment="center"
                    android:layout_gravity="center"
                    android:elevation="0dp"
                    android:text="@string/unmatch"
                    android:textAllCaps="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/button3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/img3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_report_red"
                    android:padding="7dp"
                    android:background="@drawable/circle_chat_bacground"
                    />
                <TextView
                    android:id="@+id/text3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@color/transparent"
                    android:backgroundTint="@color/transparent"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/red_500"
                    style="@style/text_style_100"
                    android:textAlignment="center"
                    android:layout_gravity="center"
                    android:elevation="0dp"
                    android:text="@string/report"
                    android:textAllCaps="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/remove_rmod_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/img4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_report_red"
                    android:padding="7dp"
                    android:background="@drawable/circle_chat_bacground"
                    />
                <TextView
                    android:id="@+id/text4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@color/transparent"
                    android:backgroundTint="@color/transparent"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/red_500"
                    style="@style/text_style_100"
                    android:textAlignment="center"
                    android:layout_gravity="center"
                    android:elevation="0dp"
                    android:text="@string/remove_title"
                    android:textAllCaps="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>