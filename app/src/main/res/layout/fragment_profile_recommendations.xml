<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="42dp"
    tools:context=".onboarding_permissions.NotificationPermissionStepFragment">

    <TextView
        android:id="@+id/notNowButton"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:layout_marginEnd="32dp"
        android:background="?selectableItemBackgroundBorderless"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/not_now"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/notification_image"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:adjustViewBounds="true"
        android:src="@drawable/profile_recommendations_illustration"
        app:layout_constraintBottom_toTopOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notNowButton"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="42dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/profile_recommendations_title"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notification_image"
        app:layout_constraintWidth_max="488dp" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/profile_recommendations_desc"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintWidth_max="488dp" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/btn_continue"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="48dp"
        android:stateListAnimator="@null"
        android:text="@string/continue_text"
        app:buttonType="Primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description"
        app:layout_constraintWidth_max="320dp" />


</androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>