<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:overScrollMode="never">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".signup.fragment.InputPhoneFragment">

        <TextView
            android:id="@+id/sign_up_title"
            style="@style/text_style_700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/set_up_your_account"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/phone_label"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            tools:text="@string/phone"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/radioGroup" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/auth_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phone_label">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/phone_input_container"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:visibility="invisible"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.rilixtech.widget.countrycodepicker.CountryCodePicker
                android:id="@+id/cc_picker"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:ccp_defaultNameCode="@string/default_country_code"
                app:ccp_enablePhoneAutoFormatter="true"
                app:ccp_hideNameCode="true"
                app:ccp_showFlag="false"
                app:ccp_textColor="@color/gray_200"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="parent" />

            <EditText
                android:id="@+id/phone_number_input"
                style="@style/text_style_100"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:background="@color/background"
                android:ems="10"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:gravity="start"
                android:hint="@string/enter_phone_number"
                android:imeOptions="actionNext"
                android:importantForAutofill="no"
                android:inputType="phone"
                android:padding="10dp"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/gray_200"
                android:textCursorDrawable="@drawable/ic_typing_indicator"
                app:layout_constraintBottom_toBottomOf="@+id/phone_input_separator"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/phone_input_separator"
                app:layout_constraintTop_toTopOf="@+id/phone_input_separator">
            </EditText>

            <View
                android:id="@+id/phone_input_separator"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/border"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/phone_number_input"
                app:layout_constraintStart_toEndOf="@id/cc_picker"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <EditText
            android:id="@+id/email_input"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:hint="@string/enter_email_address"
            android:imeOptions="actionNext"
            android:importantForAutofill="no"
            android:inputType="textEmailAddress"
            android:paddingStart="16dp"
            android:textColor="@color/title_primary"
            android:visibility="invisible"
            android:textCursorDrawable="@drawable/ic_typing_indicator"
            android:textColorHint="@color/gray_200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
        </EditText>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/phone_error_text"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            tools:text="@string/phone_number_is_not_valid"
            android:textColor="@color/red_500"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/auth_method" />

        <TextView
            android:id="@+id/password_label"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/password"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/auth_method" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/password_input_layout"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:layout_marginTop="4dp"
            android:layout_marginHorizontal="24dp"
            app:passwordToggleDrawable="@drawable/ic_password_selector"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@color/gray_200"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_label">

            <EditText
                android:id="@+id/password_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="16dp"
                android:background="#00000000"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:hint="@string/enter_password"
                android:imeOptions="actionDone"
                android:inputType="textPassword"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/gray_200"
                android:textCursorDrawable="@drawable/ic_pink_cursor"
                style="@style/text_style_100" />

        </com.google.android.material.textfield.TextInputLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/password_input_layout"
            app:layout_constraintHorizontal_bias="0.54"
            app:layout_constraintStart_toStartOf="@+id/password_input_layout"
            app:layout_constraintTop_toBottomOf="@+id/password_info_text"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/password_info_text"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/password_guidelines"
            android:textColor="@color/gray_200"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_input_layout" />

        <Button
            android:id="@+id/create_account"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/button_outline_rounded_32dp"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            android:textColor="@color/text_color_selector"
            android:fontFamily="@font/tt_norms_pro_medium"
            style="@style/text_style_200"
            android:textAllCaps="false"
            android:layout_marginHorizontal="24dp"
            app:layout_constraintTop_toBottomOf="@id/password_info_text"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/set_up_your_account_desc"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/sign_up_title"
            app:layout_constraintTop_toBottomOf="@+id/sign_up_title" />

        <RadioGroup
            android:id="@+id/radioGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="@id/description"
            app:layout_constraintTop_toBottomOf="@id/description">


            <RadioButton
                android:id="@+id/radioButton2"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/phone"
                android:textColor="@color/title_primary" />

            <RadioButton
                android:id="@+id/radioButton3"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/email_"
                android:textColor="@color/title_primary" />

        </RadioGroup>



    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>