<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="32dp"
    tools:context=".onboarding_permissions.LocationPermissionStepFragment">
    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="?android:attr/selectableItemBackground"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        android:id="@+id/location_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="41dp"
        android:src="@drawable/location_illustration_step"
        app:layout_constraintBottom_toTopOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/enable_location_title"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@id/description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/location_image"
        app:layout_constraintWidth_max="488dp" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/enable_location_desc"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toTopOf="@id/allow_location_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintWidth_max="488dp" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/allow_location_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        android:stateListAnimator="@null"
        android:text="@string/allow_location_btn"
        app:buttonType="Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/description"
        app:layout_constraintStart_toStartOf="@+id/description"
        app:layout_constraintTop_toBottomOf="@+id/description"
        app:layout_constraintWidth_max="320dp" />
</androidx.constraintlayout.widget.ConstraintLayout>