<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layoutDirection="ltr"
        >

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/imageView34"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="156dp"
                    android:layout_marginTop="64dp"
                    android:layout_marginEnd="155dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_diamond" />

<!--                <ImageView-->
<!--                    android:id="@+id/imageView35"-->
<!--                    android:layout_width="42dp"-->
<!--                    android:layout_height="15dp"-->
<!--                    android:layout_marginStart="220dp"-->
<!--                    android:layout_marginEnd="113dp"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintHorizontal_bias="0.664"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/imageView34"-->
<!--                    app:srcCompat="@drawable/ic_user_premium" />-->

                <TextView
                    android:id="@+id/dua_premium_txt"
                    style="@style/text_style_300"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="53dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="52dp"
                    android:text="@string/you_are_premium"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textAlignment="center"
                    android:textColor="@color/title_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView34" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/premiumBenefitsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="40dp"
                    android:layout_marginBottom="88dp"
                    android:overScrollMode="never"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/dua_premium_txt"
                    tools:listitem="@layout/premium_benefits_item" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>

        <Button
            android:id="@+id/close_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/close"
            android:background="@color/background"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            style="@style/text_style_200"
            android:paddingBottom="44dp"
            android:paddingTop="24dp"
            android:textAllCaps="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>