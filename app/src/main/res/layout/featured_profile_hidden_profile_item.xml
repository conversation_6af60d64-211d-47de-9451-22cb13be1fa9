<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/hidden_profile_img"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/hidden_profile_illustration"
        app:layout_constraintBottom_toTopOf="@+id/hidden_profile_title"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="16dp"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.41000003"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/hidden_profile_title"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:textAlignment="center"
        android:layout_marginHorizontal="32dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/your_profile_is_hidden"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/hidden_profile_description"
        app:layout_constraintEnd_toEndOf="@+id/hidden_profile_img"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/hidden_profile_img"
        app:layout_constraintTop_toBottomOf="@+id/hidden_profile_img"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/hidden_profile_description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/hidden_profile_featured_profiles"
        android:textAlignment="center"
        android:layout_marginBottom="48dp"
        app:layout_constraintBottom_toTopOf="@id/unhide_profile_button"
        android:textColor="@color/description_secondary"
        app:layout_constraintEnd_toEndOf="@+id/hidden_profile_title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/hidden_profile_title"
        app:layout_constraintTop_toBottomOf="@+id/hidden_profile_title"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/unhide_profile_button"
        android:layout_width="wrap_content"
        android:layout_height="52dp"
        android:layout_marginHorizontal="40dp"
        android:paddingHorizontal="32dp"
        android:stateListAnimator="@null"
        android:text="@string/unhide_my_profile"
        android:textAllCaps="false"
        app:buttonType="Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/hidden_profile_description"
        app:layout_constraintStart_toStartOf="@+id/hidden_profile_description"
        app:layout_constraintTop_toBottomOf="@+id/hidden_profile_description"
        app:layout_constraintVertical_bias="0.26999998"
        app:layout_constraintVertical_chainStyle="packed" />
</androidx.constraintlayout.widget.ConstraintLayout>