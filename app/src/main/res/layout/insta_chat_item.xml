<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="76dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:paddingEnd="16dp">

    <TextView
        android:id="@+id/chat_name_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="17dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        android:maxWidth="170dp"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@+id/chat_message_txt"
        app:layout_constraintEnd_toStartOf="@+id/badge"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/profile_image"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="A dua's user name goes here" />

    <TextView
        android:id="@+id/chat_message_txt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:maxLines="1"
        style="@style/text_style_100"
        tools:text="Hey, I want to meet you.  😊 ...Hey, I want to meet you.  😊 ..."
        app:layout_constraintBottom_toBottomOf="@+id/profile_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/chat_name_txt"
        app:layout_constraintTop_toBottomOf="@+id/chat_name_txt" />


    <com.duaag.android.views.CircularProgressView
        android:id="@+id/time_left"
        android:layout_width="74dp"
        android:layout_height="74dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/profile_image"
        app:layout_constraintStart_toStartOf="@id/profile_image"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/profile_image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/first_page_second_face" />


    <TextView
        android:id="@+id/meet_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_75"
        android:layout_marginTop="48dp"
        android:gravity="center"
        android:background="@drawable/pink_button_32_rounded"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp"
        android:text="@string/meet_instachat"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/profile_image"
        app:layout_constraintEnd_toEndOf="@id/profile_image"
        app:layout_constraintTop_toTopOf="@id/profile_image"
        app:layout_constraintBottom_toBottomOf="@id/time_left" />

    <ImageView
        android:id="@+id/badge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/profile_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/profile_image" />

    <ImageView
        android:id="@+id/badge_one"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/chat_name_txt"
        app:layout_constraintStart_toEndOf="@+id/chat_name_txt"
        app:layout_constraintTop_toTopOf="@+id/chat_name_txt"
        tools:srcCompat="@drawable/ic_verification_badge_black_1" />

    <ImageView
        android:id="@+id/star"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/chat_name_txt"
        app:layout_constraintStart_toEndOf="@+id/badge_one"
        app:layout_constraintTop_toTopOf="@+id/chat_name_txt"
        app:srcCompat="@drawable/ic_form_superlike" />

</androidx.constraintlayout.widget.ConstraintLayout>