<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/filters_tooltip_container"
    android:layout_width="236dp"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="@drawable/send_instachat_to_her_rect"
    android:backgroundTint="@color/tooltip_bg_primary"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintWidth="wrap_content_constrained">

    <TextView
        android:id="@+id/tooltip_text"
        style="@style/text_style_200"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/try_filters_desc"
        android:textColor="@color/tooltip_title_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
