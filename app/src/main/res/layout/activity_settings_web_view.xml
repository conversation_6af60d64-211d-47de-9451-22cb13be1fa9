<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_height="match_parent"
        android:layout_width="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background"
            android:fitsSystemWindows="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:toolbarId="@+id/toolbar">


            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_scrollFlags="scroll|enterAlways"
                app:navigationIcon="@drawable/ic_chevron_left">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/toolbar_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_demibold"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.3"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Terms and Conditions" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.appcompat.widget.Toolbar>


        </com.google.android.material.appbar.AppBarLayout>

        <com.duaag.android.settings.OlderVersionsWebView xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/dua_webView"
            app:layout_constraintTop_toBottomOf="@id/appBar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp"
            tools:context=".settings.SettingsWebViewActivity" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>




