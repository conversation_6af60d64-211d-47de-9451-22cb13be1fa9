<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center_horizontal|center_vertical"
        android:text="@string/is_your_pose_right"
        android:textColor="@color/title_primary"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/textView47"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="14dp"
        android:text="@string/do_you_want_to_continue_with_this_picture_or_take_another_one"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        android:fontFamily="@font/tt_norms_pro_normal"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/label" />

    <LinearLayout
        android:id="@+id/images_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="35dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView47">

        <ImageView
            android:id="@+id/pose_image"
            android:layout_width="142dp"
            android:layout_height="192dp"
            android:layout_marginRight="1dp"
            android:scaleType="centerCrop"
            android:transitionName="poseTransition"
            tools:src="@drawable/hamit_bossi" />

        <ImageView
            android:id="@+id/camera_image"
            android:layout_width="142dp"
            android:layout_height="192dp"
            android:layout_marginLeft="1dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/hamit_bossi" />

    </LinearLayout>

    <Button
        android:id="@+id/continue_button"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="44dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:text="@string/continue_text"
        android:textAllCaps="false"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        android:stateListAnimator="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/images_container" />

    <TextView
        android:id="@+id/try_again"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text="@string/try_again"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/continue_button"
        app:layout_constraintVertical_bias="0.0" />


</androidx.constraintlayout.widget.ConstraintLayout>