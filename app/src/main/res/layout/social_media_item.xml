<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.duaag.android.R"/>

    </data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/social_media_icon"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_facebook_icon" />

    <TextView
        android:id="@+id/social_media_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="21dp"
        android:layout_marginBottom="21dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_100"
        android:textColor="@color/title_primary"
        tools:text="Facebook"
        app:layout_constraintBottom_toBottomOf="@+id/social_media_icon"
        app:layout_constraintStart_toEndOf="@+id/social_media_icon"
        app:layout_constraintTop_toTopOf="@+id/social_media_icon" />

    <ImageView
        android:id="@+id/imageView19"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="13dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/social_media_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/social_media_title"
        app:srcCompat="@drawable/ic_arrow_rightt" />
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>