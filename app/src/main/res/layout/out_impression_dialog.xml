<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:clipToPadding="false"
    android:paddingBottom="24dp">

    <TextView
        android:id="@+id/impressions_txt"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:gravity="center"
        android:text="@string/invite_your_friends_and_get_rewarded"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/impressions_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:textAlignment="center"
        android:layout_marginHorizontal="24dp"
        android:text="@string/invite_your_friends_desc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/impressions_txt" />

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/impressions_txt"
        app:layout_constraintVertical_bias="0.0"
        android:visibility="gone">

        <TextView
            android:id="@+id/count_down_timer"
            style="@style/text_style_900"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_impression_desc"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            tools:text="11:59:59"/>

        <TextView
            android:id="@+id/txt_impression_desc"
            style="@style/text_style_300"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="6dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:includeFontPadding="false"
            android:lineSpacingExtra="0pt"
            android:text="@string/or_wait"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            android:visibility="gone" />


    </RelativeLayout>

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="170dp"
        android:layout_marginTop="32dp"
        android:layout_marginHorizontal="24dp"
        android:src="@drawable/invite_friends_pop_up_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/impressions_desc" />

    <Button
        android:id="@+id/refer_friend_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        android:stateListAnimator="@null"
        android:text="@string/invite_a_friend"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@id/image"/>

    <TextView
        android:id="@+id/notNowButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:text="@string/not_now"
        android:textColor="@color/title_primary"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_200"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/refer_friend_button" />

</androidx.constraintlayout.widget.ConstraintLayout>