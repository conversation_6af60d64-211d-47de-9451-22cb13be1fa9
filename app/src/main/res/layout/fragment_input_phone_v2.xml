<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:overScrollMode="never">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".signup.fragment.InputPhoneFragment">

        <TextView
            android:id="@+id/sign_up_title"
            style="@style/text_style_500"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/onboarding_phone_heading"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/phone_input_container"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sign_up_title">

            <com.rilixtech.widget.countrycodepicker.CountryCodePicker
                android:id="@+id/cc_picker"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:ccp_defaultNameCode="@string/default_country_code"
                app:ccp_enablePhoneAutoFormatter="true"
                app:ccp_hideNameCode="true"
                app:ccp_showFlag="false"
                style="@style/text_style_100"
                app:ccp_textColor="@color/fields_label"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="@id/phone_input_separator"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/phone_number_input"
                style="@style/text_style_100"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:background="@color/background"
                android:ems="10"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:gravity="start"
                android:hint="@string/enter_phone_number"
                android:imeOptions="actionNext"
                android:importantForAutofill="no"
                android:inputType="phone"
                android:padding="10dp"
                android:textColor="@color/fields_label"
                android:textColorHint="@color/gray_200"
                android:textCursorDrawable="@drawable/cursor"
                app:layout_constraintBottom_toBottomOf="@+id/phone_input_separator"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/phone_input_separator"
                app:layout_constraintTop_toTopOf="@+id/phone_input_separator">

            </EditText>

            <View
                android:id="@+id/phone_input_separator"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/border"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/phone_number_input"
                app:layout_constraintStart_toEndOf="@id/cc_picker"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/phone_error_text"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            tools:text="@string/phone_number_is_not_valid"
            android:textColor="@color/red_500"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phone_input_container" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/continue_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="32dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginBottom="32dp"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            app:buttonType="PrimaryWithState"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/phone_error_text"
            app:layout_constraintVertical_bias="1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>