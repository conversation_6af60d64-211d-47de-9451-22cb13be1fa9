<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="verificationItem"
            type="com.duaag.android.signup.models.ImageResult" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imageView3"
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerCrop"
            app:imageUrlCorner="@{verificationItem.key}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:phoneImageCorner="@{verificationItem.location}"
            tools:srcCompat="@tools:sample/avatars" />

        <ImageView
            android:id="@+id/imageView4"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            app:layout_constraintEnd_toEndOf="@+id/imageView3"
            app:layout_constraintTop_toTopOf="@+id/imageView3"
            app:stateImg="@{verificationItem.valid}"
            tools:srcCompat="@tools:sample/avatars" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>