<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/parent_trophy_layout"
            android:layout_width="match_parent"
            android:background="@drawable/bottom_sheet_rounded_24dp"
            android:paddingTop="48dp"
            android:paddingBottom="24dp"
            android:clipToPadding="false"
            android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/trophy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:elevation="2dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_trophy_body" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button1"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    android:elevation="1dp"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button2"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button3"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button4"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button5"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button6"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button7"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button8"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button9"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button10"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button11RightSide"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button12"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_like"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.62"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy"
                    app:layout_constraintVertical_bias="0.62" />


                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button11"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button22"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button33"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button44"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button55"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button66"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button77"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button88"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button99"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button1010"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button1111"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />
                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/like_button1212"
                    android:layout_width="11dp"
                    android:layout_height="11dp"
                    android:background="@android:color/transparent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_disslike"
                    app:layout_constraintEnd_toEndOf="@+id/trophy"
                    app:layout_constraintHorizontal_bias="0.38"
                    app:layout_constraintStart_toStartOf="@+id/trophy"
                    app:layout_constraintTop_toTopOf="@+id/trophy" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginEnd="24dp"
                    android:text="@string/reward_title"
                    android:textAlignment="center"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_300"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/trophy" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="24dp"
                    android:text="@string/reward_description"
                    android:textAlignment="center"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:textColor="@color/description_primary"
                    style="@style/text_style_100"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView2" />

                <Button
                    android:id="@+id/button_claim"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:text="@string/claim_your_rewards"
                    android:textAllCaps="false"
                    android:background="@drawable/modal_button_outline_rounded_32dp"
                    android:stateListAnimator="@null"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_200"
                    app:layout_constraintEnd_toEndOf="@+id/textView3"
                    app:layout_constraintStart_toStartOf="@+id/textView3"
                    app:layout_constraintTop_toBottomOf="@+id/textView3" />


        </androidx.constraintlayout.widget.ConstraintLayout>
</layout>