<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="4dp"
    android:id="@+id/unlock_root"
    android:minHeight="250dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_eye_unlock"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:scaleType="centerCrop"
        android:tint="@color/gray_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/eye_off" />


    <TextView
        android:id="@+id/unlock_profile_content"
        style="@style/text_style_100"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:gravity="center"
        android:singleLine="false"
        android:text="@string/Add_profile_info"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ic_eye_unlock" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/update_profile_btn"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginTop="32dp"
        android:paddingHorizontal="47dp"
        android:paddingVertical="12dp"
        android:stateListAnimator="@null"
        android:text="@string/update_profile_button"
        app:buttonType="Primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/unlock_profile_content" />
</androidx.constraintlayout.widget.ConstraintLayout>
