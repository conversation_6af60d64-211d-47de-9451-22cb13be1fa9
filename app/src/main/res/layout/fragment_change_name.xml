<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".settings.fragments.account_settings.adjust_profile_info.ChangeNameFragment">

    <TextView
        android:id="@+id/change_name_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_marginHorizontal="16dp"
        android:fontFamily="@font/tt_norms_pro_bold"
        android:textColor="@color/title_primary"
        style="@style/text_style_400"
        android:text="@string/change_your_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/adjust_name_note"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        android:text="@string/change_name_in_six_months_an"
        app:layout_constraintEnd_toEndOf="@+id/change_name_title"
        app:layout_constraintStart_toStartOf="@+id/change_name_title"
        app:layout_constraintTop_toBottomOf="@+id/change_name_title" />

    <TextView
        android:id="@+id/name_hint"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/name"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/adjust_name_note" />

    <EditText
        android:id="@+id/name"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/edit_text_rounded_corners_12_dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        tools:text="Add your name..."
        android:imeOptions="actionNext"
        android:importantForAutofill="no"
        android:inputType="textCapWords"
        android:padding="16dp"
        android:singleLine="true"
        android:textColor="@color/title_primary"
        android:textCursorDrawable="@drawable/ic_typing_indicator"
        android:textColorHint="@color/gray_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/name_hint">

        <requestFocus />
    </EditText>

    <TextView
        android:id="@+id/name_description"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:minLines="2"
        tools:text="@string/your_name_will_be_displayed_in_dua_application"
        android:textColor="@color/red_500"
        android:layout_marginTop="4dp"
        android:layout_marginHorizontal="24dp"
        android:textAlignment="textStart"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/name" />


    <com.duaag.android.views.DuaButton
        app:buttonType="PrimaryWithState"
        android:id="@+id/btn_save"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:stateListAnimator="@null"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="24dp"
        android:layout_marginHorizontal="24dp"
        android:text="@string/save_information"
        android:enabled="false"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>