<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/shadow_locked_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/camera_illustration"
        app:layout_constraintBottom_toTopOf="@+id/shadow_locked_title"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="16dp"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.41000003"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/shadow_locked_title"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:textAlignment="center"
        android:layout_marginHorizontal="32dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/add_profile_photo_title"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/shadow_lock_description"
        app:layout_constraintEnd_toEndOf="@+id/shadow_locked_icon"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/shadow_locked_icon"
        app:layout_constraintTop_toBottomOf="@+id/shadow_locked_icon"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/shadow_lock_description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/add_profile_photo_desc"
        android:textAlignment="center"
        android:layout_marginBottom="48dp"
        app:layout_constraintBottom_toTopOf="@id/shadow_lock_btn"
        android:textColor="@color/description_secondary"
        app:layout_constraintEnd_toEndOf="@+id/shadow_locked_title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/shadow_locked_title"
        app:layout_constraintTop_toBottomOf="@+id/shadow_locked_title"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/shadow_lock_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:paddingHorizontal="32dp"
        android:stateListAnimator="@null"
        android:text="@string/add_photo_button"
        android:textAllCaps="false"
        app:buttonType="Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/shadow_lock_description"
        app:layout_constraintVertical_bias="0.26999998"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Dadovanje Fotografije" />
</androidx.constraintlayout.widget.ConstraintLayout>