<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/rmod_conversation"
    android:layout_gravity="center"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


        <TextView
            android:id="@+id/chat_name_txt"
            style="@style/text_style_75"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/rmod_background"
            android:drawableLeft="@drawable/target_icon"
            android:drawablePadding="4dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:gravity="center"
            android:paddingHorizontal="6dp"
            android:layout_marginTop="60dp"
            android:text="@string/recommended_match"
            android:textAllCaps="true"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0"
            app:layout_constraintVertical_chainStyle="packed" />

        <ImageView
            android:id="@+id/rmod_profile_image"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/chat_name_txt"
            tools:srcCompat="@drawable/first_page_second_face" />

        <com.duaag.android.views.CircularProgressView
            android:id="@+id/rmod_percentage_progress_view"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/background"
            android:padding="2.5dp"
            app:clockwise="true"
            app:layout_constraintBottom_toBottomOf="@+id/rmod_profile_image"
            app:layout_constraintEnd_toEndOf="@+id/rmod_profile_image" />

        <TextView
            android:id="@+id/percentage_txt"
            style="@style/text_style_100"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/title_primary"
            android:textAlignment="center"
            app:layout_constraintStart_toStartOf="@id/rmod_percentage_progress_view"
            app:layout_constraintEnd_toEndOf="@id/rmod_percentage_progress_view"
            app:layout_constraintTop_toTopOf="@id/rmod_percentage_progress_view"
            app:layout_constraintBottom_toBottomOf="@id/rmod_percentage_progress_view"
            tools:text="50%" />

        <TextView
            android:id="@+id/rmod_title"
            style="@style/text_style_400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginHorizontal="32dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/you_should_meet_chat_rmod_title_an"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintTop_toBottomOf="@+id/rmod_profile_image" />

        <TextView
            android:id="@+id/rmod_description"
            style="@style/text_style_200"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:layout_marginHorizontal="32dp"
            android:text="@string/you_should_meet_chat_rmod_desc"
            android:textColor="@color/description_primary"
            android:textAlignment="center"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/rmod_title" />


        <TextView
            android:id="@+id/view_compatibility_btn"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:background="@drawable/rounded_shape_32_dp"
            android:text="@string/view_compatibility_score"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            style="@style/text_style_200"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:layout_marginTop="28dp"
            android:textColor="@color/label_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rmod_description" />

        <TextView
            android:id="@+id/chat_ends_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/chat_for_free_in_rmod"
            style="@style/text_style_100"
            android:textColor="@color/gray_200"
            android:fontFamily="@font/tt_norms_pro_normal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_compatibility_btn" />

        <TextView
            android:id="@+id/rmod_chat_ends_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="23:58:30"
            style="@style/text_style_400"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chat_ends_text" />

        <TextView
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:drawableStart="@drawable/info_circle_small"
            app:drawableTint="@color/gray_200"
            android:drawablePadding="8dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:gravity="center"
            android:text="@string/disappear_after_24h_rmod"
            android:layout_marginBottom="32dp"
            android:textColor="@color/gray_200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rmod_chat_ends_time"
            app:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>
