<?xml version="1.0" encoding="utf-8"?>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true">
        <TextView
            style="@style/text_style_75"
            android:id="@+id/txt_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:paddingBottom="12dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/description_secondary"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/txt_message"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/txt_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:maxWidth="274dip"
            android:paddingStart="12dp"
            android:paddingTop="8dp"
            android:clickable="false"
            android:paddingBottom="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_date"
            app:viewPartMain="@id/content_txt"
            app:viewPartSlave="@id/timeStamp"
            tools:background="@drawable/background_received_message">

            <ImageView
                android:id="@+id/img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_call_allowed"
                android:layout_alignParentStart="true"
                android:layout_alignTop="@id/content_txt"
                android:layout_alignBottom="@id/description_txt"/>

            <TextView
                android:id="@+id/content_txt"
                style="@style/text_style_200"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/img"
                android:layout_marginStart="8dp"
                android:maxWidth="274dip"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/title_primary"
                android:drawablePadding="6dp"
                tools:text="@string/x_allowed_calls"/>

            <TextView
                android:id="@+id/description_txt"
                style="@style/text_style_75"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/content_txt"
                android:layout_alignEnd="@+id/content_txt"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="2dp"
                android:layout_toEndOf="@id/img"
                android:drawablePadding="6dp"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/tap_to_allow"
                android:textColor="@color/description_primary" />

            <TextView
                android:id="@+id/timeStamp"
                style="@style/text_style_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/description_txt"
                android:layout_marginEnd="8dp"
                android:layout_toEndOf="@id/content_txt"
                android:singleLine="true"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textColor="@color/gray_200"
                tools:text="12:42" />

        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>