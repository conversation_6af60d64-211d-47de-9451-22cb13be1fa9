<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include
        android:id="@+id/layout_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/shadow_banned_lock_layout"/>
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/insta_chat_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="16dp"
            android:clipToPadding="false"
            android:overScrollMode="never" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include
        android:id="@+id/empty_view_instachat"
        layout="@layout/empty_instachat_layout"
        android:visibility="visible" />

</FrameLayout>