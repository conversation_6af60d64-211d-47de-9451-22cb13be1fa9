<androidx.cardview.widget.CardView
xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:layout_width="match_parent"
android:layout_height="match_parent"
app:cardBackgroundColor="@android:color/transparent"
app:cardCornerRadius="20dp"
app:cardElevation="0dp"
app:cardPreventCornerOverlap="false"
app:layout_constraintTop_toBottomOf="@id/header">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@drawable/dont_let_go_background"
    android:padding="32dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitStart"
        android:src="@drawable/ic_dont_let_go_card"
        app:layout_constraintBottom_toTopOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_max="100dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_900"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:fontFamily="@font/tt_norms_pro_bold"
        android:text="@string/dont_let_go_50_title"
        android:textColor="@color/gray_50"
        app:layout_constraintBottom_toTopOf="@id/desc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/desc"
        style="@style/text_style_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/dont_let_go_3_desc"
        android:textColor="@color/gray_50"
        app:layout_constraintBottom_toTopOf="@+id/button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/button"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:background="@drawable/pink_button_32_rounded"
        android:backgroundTint="@color/gray_50"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/keep_premium_label"
        android:textAllCaps="false"
        android:textColor="@color/gray_400"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>