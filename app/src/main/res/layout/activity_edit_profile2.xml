<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="false"
        android:layoutDirection="ltr"
        tools:context=".profile_new.editprofile.EditProfileActivity">

                    <com.google.android.material.appbar.AppBarLayout
                        android:id="@+id/appBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/background"
                        android:fitsSystemWindows="true"
                        app:elevation="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:toolbarId="@+id/toolbar">


                        <androidx.appcompat.widget.Toolbar
                            android:id="@+id/toolbar"
                            app:elevation="0dp"
                            android:layout_width="match_parent"
                            android:layout_height="?attr/actionBarSize"
                            app:layout_scrollFlags="scroll|enterAlways"
                            app:navigationIcon="@drawable/ic_close_black_24dp">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <androidx.appcompat.widget.AppCompatImageButton
                                    android:id="@+id/toolbar_info"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="6dp"
                                    android:layout_marginVertical="4dp"
                                    android:visibility="gone"
                                    android:background="@color/transparent"
                                    app:layout_constraintBottom_toBottomOf="@+id/toolbar_title"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@+id/toolbar_title"
                                    app:srcCompat="@drawable/ic_info_1" />
                                <TextView
                                    android:id="@+id/toolbar_title"
                                    android:layout_width="0dp"
                                    android:layout_height="0dp"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:layout_marginVertical="6dp"
                                    android:layout_marginEnd="64dp"
                                    android:textAlignment="center"
                                    android:textColor="@color/title_primary"
                                    android:fontFamily="@font/tt_norms_pro_demibold"
                                    style="@style/text_style_200"
                                    tools:text="Completed Profile"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </androidx.appcompat.widget.Toolbar>


                    </com.google.android.material.appbar.AppBarLayout>

        <fragment
            android:id="@+id/nav_edit_profile"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:defaultNavHost="true"
             app:layout_constraintBottom_toTopOf="@id/fabs_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBar"
            app:navGraph="@navigation/nav_graph_edit_profile" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fabs_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|bottom"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:layout_marginBottom="24dp"
                app:elevation="0dp"
                android:src="@drawable/ic_arrow_forward_white_24dp"
                app:background="@drawable/fab_background"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|bottom"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                app:elevation="0dp"
                android:src="@drawable/ic_baseline_arrow_back_black"
                app:backgroundTint="@color/border"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <ProgressBar
                android:id="@+id/typePercentage"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_gravity="center"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:max="100"

                android:progress="0"
                android:progressDrawable="@drawable/progress_uploading"
                app:layout_constraintBottom_toBottomOf="@+id/fab_next"
                app:layout_constraintEnd_toStartOf="@+id/fab_next"
                app:layout_constraintStart_toEndOf="@+id/fab_back"
                app:layout_constraintTop_toTopOf="@+id/fab_next"
                tools:progress="30"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>