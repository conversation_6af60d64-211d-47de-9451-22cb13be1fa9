<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
<com.google.android.material.card.MaterialCardView
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="6dp"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="24dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/featured_user_image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="9:16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/profile_image_test" />

        <FrameLayout
            android:id="@+id/bottom_shade"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/featured_profile_shade"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@+id/guideline_center" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.google.android.material.card.MaterialCardView>
</com.facebook.shimmer.ShimmerFrameLayout>