<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:animateLayoutChanges="true"
    tools:context=".crosspath.presentation.fragment.AllCrossPathProfilesFragment">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar_expanded"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:fitsSystemWindows="true"
        android:visibility="visible"
        android:paddingTop="12dp"
        android:paddingBottom="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_back_button"
            android:background="@color/transparent"
            android:layout_marginStart="16dp"
            android:padding="8dp"
            android:scaleType="fitCenter"
            app:layout_constraintTop_toTopOf="@id/toolbar_title"
            app:layout_constraintBottom_toBottomOf="@id/toolbar_title"
            app:layout_constraintStart_toStartOf="parent"
            />


        <TextView
            android:id="@+id/toolbar_title"
            style="@style/text_style_300"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginStart="8dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/spotted_profiles_title"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/close_button"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="4dp"
        android:clipToPadding="false"
        android:paddingBottom="80dp"
        android:overScrollMode="never"
        tools:spanCount="2"
        tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        tools:itemCount="2"
        tools:listitem="@layout/cross_path_profile_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar_view" />

</LinearLayout>