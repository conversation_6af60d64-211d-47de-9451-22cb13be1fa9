<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.duaag.android.R"/>
        <variable
            name="showTimer"
            type="Boolean" />
        <variable
            name="viewModel"
            type="com.duaag.android.settings.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".signup.fragment.VerifyCodeFragment">

        <TextView
            android:id="@+id/txt_email_adress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginHorizontal="16dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/title_primary"
            style="@style/text_style_400"
            android:tag="et1"
            android:text="@string/enter_6_digit_code"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/email_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="@+id/txt_email_adress"
            app:layout_constraintStart_toStartOf="@+id/txt_email_adress"
            app:layout_constraintTop_toBottomOf="@+id/txt_email_adress" />

        <com.duaag.android.views.SquarePinField
            android:id="@+id/modified_pinView"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="52dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="51dp"
            android:textColorHint="@color/border"
            android:textColor="@color/others"
            android:textSelectHandle="@drawable/text_handle"
            android:textSize="16sp"
            android:text="@={viewModel.verifyDigits}"
            android:fontFamily="@font/tt_norms_pro_normal"
            app:cornerRadius1="12dp"
            android:inputType="number"
            app:distanceInBetween1="6dp"
            app:lineThickness1="1dp"
            app:fieldBgColor1="@color/background"
            app:fieldColor1="@color/border"
            app:highlightColor1="@color/others"
            app:highlightSingleFieldMode1="true"
            app:highlightType1="completedFields1"
            app:isCursorEnabled1="true"
            android:cursorVisible="true"
            android:textCursorDrawable="@drawable/ic_typing_indicator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/email_description"
            app:noOfFields1="6"
            tools:text="124" />

        <TextView
            android:id="@+id/error_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp"
            android:text="@string/wrong_verification_code"
            android:textAlignment="center"
            android:fontFamily="@font/tt_norms_pro_normal"
            style="@style/text_style_100"
            android:textColor="@color/red_500"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="@+id/modified_pinView"
            app:layout_constraintStart_toStartOf="@+id/modified_pinView"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/didnt_receive"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            app:addDifferentTypeface="@{R.string.didn_t_recieve_your_code_an}"
            app:typefaceColor="@{R.color.pink_500}"
            android:textAlignment="center"
            android:onClick="@{(v) -> viewModel.onResendCodeClicked()}"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/error_code"
            app:visibility="@{!showTimer}" />


        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/modified_pinView"
            app:layout_constraintHorizontal_bias="0.54"
            app:layout_constraintStart_toStartOf="@+id/modified_pinView"
            app:layout_constraintTop_toBottomOf="@+id/modified_pinView"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/timer_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginHorizontal="24dp"
            android:text="@string/you_can_resend_the_code_an"
            android:textAlignment="center"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            android:elevation="-3dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/error_code"
            app:visibility="@{showTimer}"
            tools:text="You can resend the code in: 00:59" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>