<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:clipChildren="false"
    android:clipToPadding="false"
    tools:context=".home.HomeActivity">

    <ImageView
        android:id="@+id/dua_logo"
        android:layout_width="125dp"
        android:layout_height="44dp"
        android:layout_marginStart="24dp"
        android:src="@drawable/app_brand_logo_colored"
        app:layout_constraintBottom_toBottomOf="@+id/notification_feed_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/notification_feed_button" />
    <FrameLayout
        android:id="@+id/new_indicator"
        android:layout_height="wrap_content"
        android:background="@drawable/dua_button_background"
        android:backgroundTint="@color/blue_500"
        app:layout_constraintEnd_toEndOf="@id/chip_group"
        android:elevation="1dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/chip_group"
        android:layout_marginEnd="-10dp"
        android:layout_marginBottom="-20dp"
        android:layout_width="wrap_content">
        <TextView
            android:id="@+id/textView_new"
            style="@style/text_style_50"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:textColor="@color/white"
            android:textAllCaps="true"
            android:text="@string/feature_profiles_new_indicator"/>


    </FrameLayout>

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:singleSelection="true"
        app:checkedChip="@id/chip1"
        app:selectionRequired="true"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/dua_logo"
        app:layout_constraintStart_toStartOf="@id/dua_logo" >

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            android:text="@string/for_you_page"
            style="@style/Colors_Widget.MaterialComponents.Chip.Choice"
            android:layout_height="wrap_content"
            android:id="@+id/chip1" />

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            style="@style/Colors_Widget.MaterialComponents.Chip.Choice"
            android:layout_height="wrap_content"
            tools:text="Popular"
            android:id="@+id/chip2" />

    </com.google.android.material.chip.ChipGroup>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/boost_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        app:layout_constraintTop_toTopOf="@id/notification_feed_button"
        app:layout_constraintBottom_toBottomOf="@id/notification_feed_button"
        android:background="@drawable/dua_button_background"
        android:backgroundTint="@color/card_secondary"
        app:layout_constraintEnd_toStartOf="@id/filter_button">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/boost_active"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginVertical="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:scaleType="centerInside"
            android:scaleX="2.5"
            android:scaleY="2.5"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/boost_textView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/outline_pulse_boost"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/boost_inactive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_boost_gradient"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/boost_textView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/boost_textView"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/boost"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageButton
        android:id="@+id/filter_button"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:scaleType="centerInside"
        android:src="@drawable/ic_filter_v2"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/notification_feed_button"
        app:layout_constraintEnd_toStartOf="@+id/notification_feed_button"
        app:layout_constraintTop_toTopOf="@+id/notification_feed_button" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/notification_feed_button"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginEnd="8dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:layout_marginTop="16dp"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/notification_feed_icon"
            android:background="@null"
            android:scaleType="centerInside"
            android:clickable="false"
            android:focusable="false"
            android:focusableInTouchMode="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/ic_menu_notification"
            />

        <ImageView
            android:id="@+id/badge_view"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_gravity="top|end"
            android:src="@drawable/custom_badge_view"
            android:visibility="gone"
            android:translationX="4dp"
            android:translationY="2dp"
            app:layout_constraintEnd_toEndOf="@+id/notification_feed_icon"
            app:layout_constraintStart_toStartOf="@+id/notification_feed_icon"
            app:layout_constraintTop_toTopOf="@+id/notification_feed_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>


<!--    <LinearLayout
        android:id="@+id/layout_count_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/dua_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/dua_logo">

        <ImageView
            android:id="@+id/impressions_img"
            android:layout_width="24dp"
            android:layout_height="24dp"
            tools:src="@drawable/ic_impressions_count_cards" />

        <TextView
            android:id="@+id/impressions_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:gravity="center"
            tools:text="500"
            android:fontFamily="@font/tt_norms_pro_medium"
            style="@style/text_style_200"
            android:textColor="@color/title_primary"
            android:layout_gravity="center_vertical"
            android:textSize="15sp" />
    </LinearLayout>

    Don't delete
    <LinearLayout
        android:id="@+id/buttons_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:animateLayoutChanges="true"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="8dp"
        app:layout_constraintBottom_toBottomOf="@id/card_stack_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_stack_view">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/dislike_button"
            android:layout_width="@dimen/home_screen_button_dimension"
            android:layout_height="@dimen/home_screen_button_dimension"
            android:layout_gravity="center_vertical"
            app:cardBackgroundColor="@color/white"
            app:cardElevation="2dp"
            android:layout_marginEnd="24dp"
            app:rippleColor="@color/white"
            app:shapeAppearance="@style/MaterialCardViewCircleShape">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/dislike_vector"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerInside"
                android:src="@drawable/ic_dislike" />
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/undo_button"
            android:layout_width="53dp"
            android:layout_height="53dp"
            android:layout_gravity="center_vertical"
            app:cardBackgroundColor="@color/white"
            android:layout_marginEnd="24dp"
            app:cardElevation="2dp"
            app:rippleColor="@color/white"
            app:shapeAppearance="@style/MaterialCardViewCircleShape">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/undo_vector"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerInside"
                android:src="@drawable/ic_rotate_ccw" />
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/instachat_button"
            android:layout_width="53dp"
            android:layout_height="53dp"
            android:layout_gravity="center_vertical"
            app:cardBackgroundColor="@color/white"
            android:layout_marginEnd="24dp"
            app:cardElevation="2dp"
            app:rippleColor="@color/white"
            app:shapeAppearance="@style/MaterialCardViewCircleShape">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/instachat_vector"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerInside"
            android:src="@drawable/ic_instachat" />
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/like_button"
            android:layout_width="@dimen/home_screen_button_dimension"
            android:layout_height="@dimen/home_screen_button_dimension"
            app:cardBackgroundColor="@color/white"
            app:cardElevation="2dp"
            app:rippleColor="@color/white"
            app:shapeAppearance="@style/MaterialCardViewCircleShape">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/like_vector"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerInside"
                android:src="@drawable/ic_for_you_icon" />
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>-->

    <include
        layout="@layout/radar_container"
        android:id="@+id/radar_container"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chip_group"
        android:visibility="visible"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"/>


    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/featured_prof_swipe2ref"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chip_group"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_featured"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            tools:itemCount="10"
            tools:listitem="@layout/featured_profile_item" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.yuyakaido.android.cardstackview.CardStackView
        android:id="@+id/card_stack_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="16dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chip_group"
        app:layout_constraintVertical_bias="0.0"
        tools:listitem="@layout/item_spot" />

    <View
        android:id="@+id/touch_blocker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@android:color/transparent"
        android:elevation="5dp"
        android:translationZ="5dp"
        android:clickable="true"
        android:focusable="true"/>

    <include
        android:id="@+id/premium_failed"
        layout="@layout/premium_payment_failed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="4dp"
        android:layout_marginTop="68dp"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>