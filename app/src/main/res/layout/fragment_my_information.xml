<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel" />
        <variable
            name="type"
            type="com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType" />
    </data>
<ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".profile_new.editprofile.my_information.MyInformationFragment">
        <ProgressBar
            android:id="@+id/edit_progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:minHeight="0dp"
            android:layout_gravity="top"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:progressTint="@color/colorPrimary"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/title_textView"
            myInformationTitle="@{type}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Add your languages and boost your proscxcvxcvxxcvxcvxcvxcvfile" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/filledTextField"
            bindCustomAttributes="@{type}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="4dp"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/jobs_title">

            <EditText
                android:id="@+id/editText_inputProfile"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:hint="@string/add_your_job_title"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:padding="16dp"
                android:background="@color/transparent"
                android:fontFamily="@font/tt_norms_pro_normal"
                style="@style/text_style_100"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/others" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/char_count"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:layout_marginBottom="4dp"
            app:layout_constraintBottom_toTopOf="@+id/filledTextField"
            app:layout_constraintEnd_toEndOf="@+id/filledTextField"
            tools:text="120" />

        <TextView
            android:id="@+id/jobs_title"
            bindTitle="@{type}"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="28dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            style="@style/text_style_100"
            app:layout_constraintStart_toStartOf="@+id/filledTextField"
            app:layout_constraintTop_toBottomOf="@+id/title_textView"
            tools:text="Job Title" />

        <TextView
            android:id="@+id/education_title"
            style="@style/text_style_100"
            bindVisibility="@{type}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/education"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="@+id/filledTextField2"
            app:layout_constraintTop_toBottomOf="@+id/filledTextField" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/filledTextField2"
            bindVisibility="@{type}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            app:counterEnabled="false"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/education_title">

            <EditText
                android:id="@+id/editText_inputProfile2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:singleLine="true"
                android:padding="16dp"
                android:background="@color/transparent"
                android:fontFamily="@font/tt_norms_pro_normal"
                style="@style/text_style_100"
                android:textColorHint="@color/others"
                android:hint="@string/add_your_education" />

        </com.google.android.material.textfield.TextInputLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
</layout>