<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layoutDirection="ltr"
    android:layout_height="match_parent">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/community_title"
        style="@style/text_style_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/choose_your_community"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/community_description"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:minLines="2"
        android:text="@string/choose_your_own_community_desc"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/community_title" />

    <androidx.appcompat.widget.SearchView
        android:id="@+id/search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="32dp"
        android:background="@color/transparent"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:importantForAutofill="no"
        android:theme="@style/DayNightAppTheme"
        app:iconifiedByDefault="false"
        android:focusable="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/community_description"
        app:queryBackground="@android:color/transparent"
        app:queryHint="@string/search" />

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/gray_200"
        android:layout_marginEnd="24dp"
        android:layout_marginStart="24dp"
        app:layout_constraintTop_toBottomOf="@id/search"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/communities"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:paddingStart="3dp"
        android:paddingTop="12dp"
        android:paddingEnd="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search" />

    <include
        android:id="@+id/something_went_wrong_container"
        layout="@layout/something_went_wrong_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search"
        app:layout_constraintVertical_bias="0.5" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/action_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="16dp"
        android:stateListAnimator="@null"
        android:text="@string/add"
        app:buttonType="PrimaryWithState"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>