<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fillViewport="true">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/header_textview"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:text="@string/my_photos"
            android:textColor="@color/title_primary"
            app:layout_constraintHorizontal_bias="0.465"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="28dp"
            android:layout_marginEnd="12dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layoutDirection="ltr"
            android:overScrollMode="never"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/description" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/hide_my_photos_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/background_color_border_20dp"
            android:backgroundTint="@color/bg_card"
            app:layout_constraintBottom_toTopOf="@+id/btn_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recycler_profile"
            app:layout_constraintVertical_bias="0.98">

            <TextView
                android:id="@+id/textView68"
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="2dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/hide_photos_info_title"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toTopOf="@+id/textView67"
                app:layout_constraintEnd_toStartOf="@+id/hide_my_photos_switch"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView67"
                style="@style/text_style_100"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="24dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/hide_photos_info_desc"
                android:textColor="@color/description_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/hide_my_photos_switch"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView68" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/hide_my_photos_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:padding="8dp"
                android:theme="@style/SwitchTheme"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_container"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="44dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.duaag.android.views.DuaButton
                android:id="@+id/done_btn"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:stateListAnimator="@null"
                android:text="@string/done_f"
                android:visibility="gone"
                app:buttonType="Primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/cancel_button"
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:background="@color/transparent"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:gravity="center"
                android:padding="8dp"
                android:stateListAnimator="@null"
                android:text="@string/cancel"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.duaag.android.views.DuaButton
                android:id="@+id/change_photo_btn"
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:padding="8dp"
                android:stateListAnimator="@null"
                android:text="@string/change_your_photo"
                android:textColor="@color/title_primary"
                android:visibility="gone"
                app:buttonType="Primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:gravity="start"
            android:text="@string/drag_your_images"
            android:textColor="@color/description_primary"
            style="@style/text_style_200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_textview" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
