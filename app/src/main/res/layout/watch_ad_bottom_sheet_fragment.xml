<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/image"
        android:layout_width="112dp"
        android:layout_height="160dp"
        android:layout_marginTop="32dp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />

    <ImageView
        android:id="@+id/likeIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        app:layout_constraintEnd_toEndOf="@+id/image"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintTop_toBottomOf="@+id/image"
        app:srcCompat="@drawable/ic_like_ads" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="42dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image"
        tools:text="@string/ads_like_reward_an" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/ads_like_reards_subtitle"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/continueButton"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:padding="8dp"
        android:stateListAnimator="@null"
        android:text="@string/continue_text"
        android:textColor="@color/title_primary"
        app:buttonType="Primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/description" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="40dp"
        android:background="@color/border"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/continueButton" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/noButton"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:stateListAnimator="@null"
        android:text="@string/no_thanks_button"
        android:textColor="@color/title_primary"
        app:buttonType="Link"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />
</androidx.constraintlayout.widget.ConstraintLayout>