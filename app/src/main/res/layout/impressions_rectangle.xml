<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="homeViewModel"
            type="com.duaag.android.home.viewmodels.HomeViewModel" />
    </data>

    <FrameLayout
        android:layout_width="77dp"
        android:layout_height="156dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="12dp"
                    android:src="@drawable/ic_impressions_count_cards" />

                <TextView
                    style="@style/text_style_200"
                    android:id="@+id/impressions_txt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:gravity="center"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/gray_50"
                    tools:text="500" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_impressions_count_instachat" />

                <TextView
                    style="@style/text_style_200"
                    android:id="@+id/instachat_txt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:gravity="center"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/gray_50"
                    tools:text="500" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_airplane" />

                <TextView
                    style="@style/text_style_200"
                    android:id="@+id/flights_txt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:gravity="center"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/gray_50"
                    tools:text="500" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="12dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/impressions_img"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_impressions_count_undo" />

                <TextView
                    style="@style/text_style_200"
                    android:id="@+id/undo_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:gravity="center"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/gray_50"
                    tools:text="500" />
            </LinearLayout>

        </LinearLayout>
    </FrameLayout>
</layout>