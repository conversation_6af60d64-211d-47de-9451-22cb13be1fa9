<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="profileInAppPackages"
            type="com.duaag.android.profile_new.models.ProfileInAppPackagesModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/inapp_packages_rounded_rect">

        <ImageView
            android:id="@+id/imageView36"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="12dp"
            tools:src="@drawable/ic_impressions_icon"
            app:drawableFromInt="@{profileInAppPackages.image}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView57"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="2dp"
            android:maxLines="1"
            android:fontFamily="@font/tt_norms_pro_medium"
            style="@style/text_style_200"
            android:text="@{profileInAppPackages.packageType}"
            android:textColor="@color/title_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/imageView36"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Impressions" />

        <TextView
            android:id="@+id/price_txt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="11dp"
            android:text="@{profileInAppPackages.price}"
            android:textColor="@color/description_secondary"
            android:fontFamily="@font/tt_norms_pro_normal"
            style="@style/text_style_75"
            android:maxLines="1"
            android:layout_marginEnd="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageView36"
            app:layout_constraintTop_toBottomOf="@+id/textView57"
            tools:text="from 0,99 CHF " />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>