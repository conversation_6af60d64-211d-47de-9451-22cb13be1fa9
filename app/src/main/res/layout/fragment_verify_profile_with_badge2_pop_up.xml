<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fillViewport="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/verify_profile_with_badge2_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".image_verification.fragments.VerifyProfileWithBadge2PopUp">


        <Button
            android:id="@+id/not_now_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:background="@color/transparent"
            android:textColor="@color/button_label_secondary"
            android:padding="8dp"
            android:text="@string/not_now"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/user_image"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/badge_icon_outline"
            app:layout_constraintBottom_toBottomOf="@+id/user_image"
            app:layout_constraintEnd_toEndOf="@+id/user_image" />

        <TextView
            android:id="@+id/get_verified_title"
            style="@style/text_style_600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="32dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/profile_progress_title_2"
            android:textAlignment="textStart"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/user_image" />

        <TextView
            android:id="@+id/get_verified_main_desc"
            style="@style/text_style_200"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/onboarding_verification_desc"
            android:textAlignment="textStart"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/get_verified_title" />


        <LinearLayout
            android:id="@+id/table_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="24dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/get_verified_main_desc">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/user_viewfinder"
                    app:tint="@color/icon_active" />

                <TextView
                    android:id="@+id/text1"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/onboarding_verification_rule_1"
                    android:textColor="@color/description_primary" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/badge_outline"
                    app:tint="@color/icon_active" />

                <TextView
                    android:id="@+id/text2"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/onboarding_verification_rule_2"
                    android:textColor="@color/description_primary" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_cover_face"/>

                <TextView
                    android:id="@+id/text3"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/onboarding_verification_rule_3"
                    android:textColor="@color/description_primary" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/distance_icon"
                    app:tint="@color/icon_active" />

                <TextView
                    android:id="@+id/text4"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/onboarding_verification_rule_4"
                    android:textColor="@color/description_primary" />
            </LinearLayout>
        </LinearLayout>

        <com.duaag.android.views.DuaButton
            android:id="@+id/continue_btn"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="20dp"
            android:text="@string/verify_profile_label"
            android:textAllCaps="false"
            app:buttonType="Primary"
            app:layout_constraintBottom_toTopOf="@+id/change_photo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/table_layout"
            app:layout_constraintVertical_bias="1.0" />


        <com.duaag.android.views.DuaButton
            android:id="@+id/change_photo"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/modal_button_outline_rounded_32dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:stateListAnimator="@null"
            android:text="@string/onboarding_verification_change_photo"
            android:textAllCaps="false"
            android:textColor="@color/title_primary"
            app:buttonType="Link"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>