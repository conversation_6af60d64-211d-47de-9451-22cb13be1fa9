<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:contentDescription="@string/onboarding_profilebuilder_heading"
    android:importantForAccessibility="yes">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="32dp">

        <ImageView
            android:id="@+id/landing_item_image"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:src="@drawable/ic_launcher_foreground"
            android:contentDescription="@string/onboarding_profilebuilder_heading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@drawable/be_yourself_f_illustration" />

        <TextView
            android:id="@+id/landing_item_title"
            style="@style/text_style_1000"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:gravity="center"
            android:text="@string/onboarding_profilebuilder_heading"
            android:textColor="@color/title_primary"
            android:importantForAccessibility="yes"
            app:layout_constraintBottom_toTopOf="@id/landing_item_description"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/landing_item_image"
            app:layout_constraintVertical_bias="0.3"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/landing_item_description"
            style="@style/text_style_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="55dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:gravity="center"
            android:text="@string/onboarding_profilebuilder_des"
            android:textColor="@color/title_secondary"
            android:importantForAccessibility="yes"
            app:layout_constraintBottom_toTopOf="@+id/welcome_item_start_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/landing_item_title" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/welcome_item_start_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40dp"
            android:paddingVertical="16dp"
            android:text="@string/onboarding_profilebuilder_aboutyou"
            android:focusable="true"
            android:contentDescription="@string/onboarding_profilebuilder_aboutyou"
            app:buttonType="Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="320dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>