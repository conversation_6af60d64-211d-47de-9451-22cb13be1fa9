<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:paddingTop="24dp">

    <TextView
        android:id="@+id/textView55"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="8dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        style="@style/text_style_200"
        android:text="@string/were_always_here_for_you"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/textView56"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/textView56"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="32dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        style="@style/text_style_100"
        android:text="@string/remember_sign_in_info"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toTopOf="@+id/login_info_rv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/login_info_rv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/log_out_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        tools:itemCount="2"
        tools:listitem="@layout/log_in_item_rv" />

    <Button
        android:id="@+id/log_out_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="44dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_200"
        android:text="@string/sign_out"
        android:textAllCaps="false"
        android:textColor="@color/title_primary"
        android:stateListAnimator="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>