<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layoutDirection="locale"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:id="@+id/today_section_title"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_marginBottom="22dp"
        android:layoutDirection="locale"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/today_section" />
<com.facebook.shimmer.ShimmerFrameLayout
    android:id="@+id/today_section_shimmer_view"
    android:layout_width="match_parent"
    android:layoutDirection="locale"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
       >
        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/shimmer_image"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:background="@drawable/shimmer_background_rounded"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/shimmer_item_title"
            android:layout_width="150dp"
            android:layout_height="16dp"
            android:layout_marginStart="20dp"
            android:background="@drawable/shimmer_background_rounded"
            app:layout_constraintBottom_toTopOf="@id/shimmer_item_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/shimmer_image"
            app:layout_constraintTop_toTopOf="@+id/shimmer_image"
            app:layout_constraintVertical_chainStyle="packed" />

        <View
            android:id="@+id/shimmer_item_desc"
            android:layout_width="50dp"
            android:layout_height="16dp"
            android:layout_marginStart="20dp"
            app:layout_constraintVertical_chainStyle="packed"
            android:layout_marginTop="6dp"
            android:background="@drawable/shimmer_background_rounded"
            app:layout_constraintBottom_toBottomOf="@+id/shimmer_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/shimmer_image"
            app:layout_constraintTop_toBottomOf="@id/shimmer_item_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.facebook.shimmer.ShimmerFrameLayout>
    <TextView
        android:id="@+id/yesterday_section_title"
        style="@style/text_style_400"
        android:layout_marginTop="32dp"
        android:layout_width="wrap_content"
        android:layout_marginBottom="22dp"
        android:layoutDirection="locale"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/yesterday_section" />
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/yesterday_section_shimmer_view"
        android:layout_width="match_parent"
        android:layoutDirection="locale"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/yesterday_shimmer_image"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/yesterday_shimmer_item_title"
                android:layout_width="150dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toTopOf="@id/yesterday_shimmer_item_desc"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image"
                app:layout_constraintTop_toTopOf="@+id/yesterday_shimmer_image"
                app:layout_constraintVertical_chainStyle="packed" />

            <View
                android:id="@+id/yesterday_shimmer_item_desc"
                android:layout_width="50dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                app:layout_constraintVertical_chainStyle="packed"
                android:layout_marginTop="6dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toBottomOf="@+id/yesterday_shimmer_image"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image"
                app:layout_constraintTop_toBottomOf="@id/yesterday_shimmer_item_title" />


            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/yesterday_shimmer_image_2"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/yesterday_shimmer_image" />

            <View
                android:id="@+id/yesterday_shimmer_item_title_2"
                android:layout_width="150dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toTopOf="@id/yesterday_shimmer_item_desc_2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image_2"
                app:layout_constraintTop_toTopOf="@+id/yesterday_shimmer_image_2"
                app:layout_constraintVertical_chainStyle="packed" />

            <View
                android:id="@+id/yesterday_shimmer_item_desc_2"
                android:layout_width="50dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                app:layout_constraintVertical_chainStyle="packed"
                android:layout_marginTop="6dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toBottomOf="@+id/yesterday_shimmer_image_2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image_2"
                app:layout_constraintTop_toBottomOf="@id/yesterday_shimmer_item_title_2" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/yesterday_shimmer_image_3"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/yesterday_shimmer_image_2" />

            <View
                android:id="@+id/yesterday_shimmer_item_title_3"
                android:layout_width="150dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toTopOf="@id/yesterday_shimmer_item_desc_3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image_3"
                app:layout_constraintTop_toTopOf="@+id/yesterday_shimmer_image_3"
                app:layout_constraintVertical_chainStyle="packed" />

            <View
                android:id="@+id/yesterday_shimmer_item_desc_3"
                android:layout_width="50dp"
                android:layout_height="16dp"
                android:layout_marginStart="20dp"
                app:layout_constraintVertical_chainStyle="packed"
                android:layout_marginTop="6dp"
                android:background="@drawable/shimmer_background_rounded"
                app:layout_constraintBottom_toBottomOf="@+id/yesterday_shimmer_image_3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/yesterday_shimmer_image_3"
                app:layout_constraintTop_toBottomOf="@id/yesterday_shimmer_item_title_3" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>
</LinearLayout>