<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="10dp"
    tools:context=".home.fragments.ActivateBoostDialog">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="15dp"
        android:paddingVertical="4dp"
        android:src="@drawable/ic_liked_you_empty_boost"
        app:layout_constraintBottom_toTopOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_300"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:gravity="center"
        android:text="@string/empty_get_boost_title"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon"
        app:layout_constraintVertical_bias="0.5" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_100"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:gravity="center"
        android:text="@string/empty_get_boost_desc"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toTopOf="@+id/button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintVertical_bias="0.5" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/button"
        android:layout_width="wrap_content"
        android:layout_height="52dp"
        android:layout_marginHorizontal="24dp"
        android:paddingHorizontal="32dp"
        android:layout_marginTop="32dp"
        android:stateListAnimator="@null"
        android:text="@string/boost_now_btn"
        app:buttonType="PrimaryWithState"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description"
        app:layout_constraintVertical_bias="0.5" />
</androidx.constraintlayout.widget.ConstraintLayout>