<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/boost_timer_card_container"
    android:layout_width="86dp"
    android:layout_height="52dp"
    android:elevation="3dp"
    app:layout_constraintTop_toBottomOf="@id/boost_active"
    app:layout_constraintEnd_toEndOf="@id/boost_active"
    app:layout_constraintStart_toStartOf="@id/boost_active">

        <ImageView
            android:id="@+id/boost_timer_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:src="@drawable/ic_boost_timer_card"
            app:layout_constraintTop_toTopOf="@id/boost_timer_card_container"
            app:layout_constraintEnd_toEndOf="@id/boost_timer_card_container"
            app:layout_constraintStart_toStartOf="@id/boost_timer_card_container" />

        <TextView
            android:id="@+id/title_timer_boost_card"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:text="@string/mins_left"
            android:layout_marginTop="8dp"
            android:layout_marginHorizontal="8dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/gray_50"
            style="@style/text_style_75"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/timer_boost_card"
            android:layout_width="0dp"
            android:layout_height="16dp"
            tools:text="00:25:07"
            android:layout_marginTop="4dp"
            android:layout_marginHorizontal="8dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/gray_50"
            style="@style/text_style_75"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_timer_boost_card" />
    </androidx.constraintlayout.widget.ConstraintLayout>