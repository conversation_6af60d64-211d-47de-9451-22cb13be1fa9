<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:overScrollMode="never">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        tools:context=".signup.fragment.SignUpNameFragment">

        <TextView
            android:id="@+id/txt_name"
            style="@style/text_style_700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/community"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/community_description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:minLines="2"
            android:text="@string/change_community_note_an"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_name" />


        <TextView
            android:id="@+id/name_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:text="@string/community"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/community_description" />

        <LinearLayout
            android:id="@+id/chosen_community_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="50dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name_text"
            app:layout_constraintVertical_bias="0.001">

            <ImageView
                android:id="@+id/chosen_community_flag"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_gravity="center_vertical"
                tools:src="@drawable/flag_afghanistan"
                android:layout_marginStart="16dp"/>

            <TextView
                android:id="@+id/chosen_community_name"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:clickable="false"
                android:drawableEnd="@drawable/ic_arrow_down"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:hint="@string/choose_your_community"
                android:layout_marginEnd="16dp"
                android:longClickable="false"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:layout_marginStart="16dp"
                android:textColor="@color/title_primary"
                android:textIsSelectable="false" />

        </LinearLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/attention_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="40dp"
            app:layout_constraintTop_toBottomOf="@+id/chosen_community_container"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/attention_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_info_1"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/attention_text"
                style="@style/text_style_100"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/attention_community_change_an"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/attention_icon"
                app:layout_constraintTop_toTopOf="@+id/attention_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>