<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".profile_builder.ProfileBuilderActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        app:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="20dp"
            >

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:src="@drawable/ic_angle_left"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ProgressBar
                android:id="@+id/progressIndicator"
                android:layout_width="0dp"
                android:layout_height="4dp"
                android:layout_marginStart="64dp"
                android:layout_marginEnd="64dp"
                style="?android:attr/progressBarStyleHorizontal"
                android:progressDrawable="@drawable/progress_bar_rounded"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:trackCornerRadius="2dp"
                tools:progress="30" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Error Message Container -->
    <LinearLayout
        android:id="@+id/errorContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="42dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/navigationButtons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appBarLayout"
        tools:visibility="visible">
        <ImageView
            android:id="@+id/errorImage"
            android:src="@drawable/ic_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/errorTitle"
            style="@style/text_style_300"
            android:textColor="@color/title_primary"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:text="@string/smthg_went_wrong"
            />

        <TextView
            android:id="@+id/errorMessage"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="12dp"
           android:text="@string/technical_issues_refresh_page" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/retryButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="24dp"
            android:text="@string/try_again"
            app:buttonType="Primary" />

    </LinearLayout>

    <!-- Main content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appBarLayout" />

    <!-- Navigation Buttons Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/navigationButtons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_diabled_user_gradient"
        android:backgroundTint="@color/transparent"
        android:paddingTop="16dp"
        android:paddingBottom="40dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.duaag.android.views.DuaButton
            android:id="@+id/btn_continue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:paddingHorizontal="20dp"
            android:paddingVertical="16dp"
            android:text="@string/continue_text"
            app:buttonType="Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 