<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="52dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:src="@drawable/ic_location_pin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/location_name"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="1dp"
        android:layout_marginEnd="4dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@{model.address}"
        android:textColor="@color/title_primary"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@+id/distance"
        app:layout_constraintEnd_toStartOf="@+id/fly_button"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:maxLines="1"
        tools:text="My current location" />

    <TextView
        android:id="@+id/distance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/location_name"
        app:layout_constraintTop_toBottomOf="@+id/location_name"
        tools:text="36km" />

    <TextView
        android:id="@+id/fly_button"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/edit_profile_background_button"
        android:drawableLeft="@drawable/ic_plane_small"
        android:drawableTint="@color/title_primary"
        android:drawablePadding="8dp"
        android:enabled="false"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:includeFontPadding="false"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:text="@string/fly"
        android:gravity="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
