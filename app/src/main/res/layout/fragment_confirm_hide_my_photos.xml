<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        tools:context=".image_verification.fragments.ImageDeniedFragment">

        <TextView
            android:id="@+id/caption"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/hide_photos_info_title"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/heading"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/heading"
            style="@style/text_style_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"

            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/hide_photos_desc"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/caption" />

        <ImageView
            android:id="@+id/image"
            android:layout_width="0dp"
            android:layout_height="228dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="12dp"
            android:adjustViewBounds="true"
            android:maxWidth="158dp"
            app:layout_constraintEnd_toStartOf="@+id/blurred_image"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/heading"
            tools:src="@drawable/guideline_man_template" />

        <ImageView
            android:id="@+id/blurred_image"
            android:layout_width="0dp"
            android:layout_height="228dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="32dp"
            android:adjustViewBounds="true"
            android:maxWidth="158dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/image"
            app:layout_constraintTop_toBottomOf="@id/heading"
            tools:src="@drawable/guideline_woman_template" />

        <LinearLayout
            android:id="@+id/reason"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="32dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@+id/confirm_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/image"
            app:layout_constraintVertical_bias="0.0">

            <CheckBox
                android:id="@+id/limitations_checkbox"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/i_understand_profile_check"
                android:textColor="@color/title_primary" />

            <CheckBox
                android:id="@+id/likes_checkbox"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:paddingLeft="12dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/i_understand_photos_check"
                android:textColor="@color/title_primary" />

        </LinearLayout>


        <com.duaag.android.views.DuaButton
            android:id="@+id/confirm_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:enabled="false"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            app:buttonType="PrimaryWithState"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="320dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>