<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.duaag.android.R"/>
        <variable
            name="welcomeItem"
            type="com.duaag.android.signup.models.WelcomeItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/welcome_imageView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="47dp"
            android:layout_marginEnd="48dp"
            app:drawableFromInt="@{welcomeItem.image}"
            app:layout_constraintBottom_toTopOf="@+id/guideline10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />

        <ImageView
            android:id="@+id/main_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_main_buttons"
            android:layout_marginBottom="10dp"
            android:elevation="2dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/welcome_title" />

        <TextView
            android:id="@+id/welcome_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginTop="40dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:textColor="@color/title_primary"
            style="@style/text_style_300"
            android:text="@{welcomeItem.title}"
            android:includeFontPadding="false"
            android:textAlignment="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guideline10"
            tools:text="@string/welcome_title_first" />

        <TextView
            android:id="@+id/welcome_description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:includeFontPadding="false"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            app:layout_constraintEnd_toEndOf="@+id/welcome_title"
            app:layout_constraintStart_toStartOf="@+id/welcome_title"
            app:layout_constraintTop_toBottomOf="@+id/welcome_title"
            tools:text="@string/welcome_desc_first_an" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline10"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.68" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>