<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".signup.fragment.ChoosePicturesFragment">

    <TextView
        android:id="@+id/txt_album"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/album"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txt_recents"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="40dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/recents"
        android:textColor="@color/pink_500"
        style="@style/text_style_200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_selected"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:overScrollMode="never"
        android:paddingTop="44dp"
        android:paddingBottom="44dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/fab_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recyclerview_recents" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_recents"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/recyclerview_selected"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_album" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_next"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginRight="24dp"
        android:src="@drawable/ic_arrow_forward_white_24dp"
        android:visibility="gone"
        app:elevation="0dp"
        app:background="@drawable/fab_background"
        app:layout_constraintBottom_toBottomOf="@id/recyclerview_selected"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/recyclerview_selected"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>