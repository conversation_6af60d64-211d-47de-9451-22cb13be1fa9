<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="25dp"
    android:paddingVertical="24dp"
    >

    <ImageView
        android:id="@+id/ghost_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="24dp"
        android:paddingHorizontal="15dp"
        android:paddingVertical="4dp"
        android:src="@drawable/mask_illustration"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:layout_marginHorizontal="24dp"
        tools:text="@string/ghost_mode_active_inline"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintTop_toBottomOf="@id/ghost_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="24dp"
        android:gravity="center"
        tools:text="@string/ghost_mode_active_desc"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100" />

    <com.duaag.android.views.DuaButton
        app:buttonType="Primary"
        android:id="@+id/button"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:stateListAnimator="@null"
        android:layout_marginTop="32dp"
        android:layout_marginHorizontal="24dp"
        tools:text="@string/got_it"
        app:layout_constraintTop_toBottomOf="@id/description"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>