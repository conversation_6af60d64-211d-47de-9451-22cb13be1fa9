<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/compatibility_score_text"
        style="@style/text_style_400"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="7dp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/redeem_code_title"
        android:textAlignment="textStart"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@id/description"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="32dp" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="32dp"
        android:layout_marginHorizontal="32dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/redeem_code_desc"
        android:textAlignment="textStart"
        android:textColor="@color/description_secondary"
        app:layout_constraintBottom_toTopOf="@+id/code_container" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/code_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="42dp"
        android:layout_marginHorizontal="32dp"
        android:background="@color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/action_btn"
        app:layout_constraintHorizontal_bias="0.0">

        <TextView
            android:id="@+id/email_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/code_label"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/title_primary"
            style="@style/text_style_100"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <EditText
            android:id="@+id/code_input"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:hint="@string/code_placeholder"
            android:imeOptions="actionDone"
            android:importantForAutofill="no"
            android:inputType="text"
            android:paddingStart="16dp"
            android:textColor="@color/title_primary"
            android:textCursorDrawable="@drawable/ic_typing_indicator"
            android:textColorHint="@color/gray_200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email_label">
        </EditText>
        <TextView
            android:id="@+id/email_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/email_already_in_use"
            android:textColor="@color/red_500"
            style="@style/text_style_100"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/code_input"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.duaag.android.views.DuaButton
        android:id="@+id/action_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="16dp"
        android:stateListAnimator="@null"
        android:text="@string/cancel"
        app:buttonType="Secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:indeterminate="true"
        android:theme="@style/ProgressBarTheme"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/action_btn"
        app:layout_constraintEnd_toEndOf="@+id/action_btn"
        app:layout_constraintTop_toTopOf="@id/action_btn"
        app:layout_constraintStart_toStartOf="@id/action_btn"/>

</androidx.constraintlayout.widget.ConstraintLayout>