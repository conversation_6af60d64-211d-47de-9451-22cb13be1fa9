<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:contentDescription="@string/onboarding_allset_heading"
    android:importantForAccessibility="yes">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="32dp">

        <ImageView
            android:id="@+id/all_ready_item_image"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:contentDescription="@string/onboarding_allset_heading"
            android:src="@drawable/ic_launcher_foreground"
            app:layout_constraintBottom_toTopOf="@id/all_ready_item_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:srcCompat="@drawable/all_set_m_illustration" />

        <TextView
            android:id="@+id/all_ready_item_title"
            style="@style/text_style_700"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:gravity="center"
            android:importantForAccessibility="yes"
            android:text="@string/onboarding_allset_heading"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toTopOf="@+id/all_ready_item_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/all_ready_item_image" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/all_ready_item_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40dp"
            android:contentDescription="@string/onboarding_allset_readyfor"
            android:focusable="true"
            android:paddingVertical="16dp"
            android:text="@string/onboarding_allset_readyfor"
            app:buttonType="PrimaryWithState"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="320dp">
        </com.duaag.android.views.DuaButton>

        <ProgressBar
            android:id="@+id/submit_progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/all_ready_item_button"
            app:layout_constraintEnd_toEndOf="@+id/all_ready_item_button"
            app:layout_constraintStart_toStartOf="@+id/all_ready_item_button"
            app:layout_constraintTop_toTopOf="@+id/all_ready_item_button"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>