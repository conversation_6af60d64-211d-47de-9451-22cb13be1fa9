<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="com.duaag.android.R"/>
        <variable
            name="viewModel"
            type="com.duaag.android.settings.SettingsViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        tools:context=".settings.fragments.VerifyNewPasswordFragment">

        <TextView
            android:id="@+id/verify_new_password_txt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            android:gravity="center"
            android:text="@string/verify_your_new_password_an"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/password_label"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/password"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="@id/verify_new_password_txt"
            app:layout_constraintStart_toStartOf="@id/verify_new_password_txt"
            app:layout_constraintTop_toBottomOf="@id/verify_new_password_txt" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/password_input_layout"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_label"
            app:passwordToggleDrawable="@drawable/ic_password_selector"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@color/gray_200">

            <EditText
                android:id="@+id/edit_text_password_verify"
                style="@style/text_style_100"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#00000000"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:hint="@string/enter_password"
                android:imeOptions="actionDone"
                android:inputType="textPassword"
                android:padding="16dp"
                android:text="@={viewModel._newPasswordVerify}"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/gray_200"
                android:textCursorDrawable="@drawable/ic_typing_indicator" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/password_info_text"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/password_guidelines"
            android:textColor="@color/gray_200"
            app:layout_constraintEnd_toEndOf="@+id/password_input_layout"
            app:layout_constraintStart_toStartOf="@+id/password_input_layout"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="?android:attr/progressBarStyleHorizontal"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/password_input_layout"
            app:layout_constraintTop_toBottomOf="@id/password_input_layout"
            app:layout_constraintStart_toStartOf="@+id/password_input_layout" />

        <TextView
            android:id="@+id/error_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/wrong_verification_code"
            android:textAlignment="textStart"
            android:textColor="@color/pink_500"
            style="@style/text_style_100"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="@+id/password_input_layout"
            app:layout_constraintStart_toStartOf="@+id/password_input_layout"
            app:layout_constraintTop_toBottomOf="@id/password_input_layout" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>