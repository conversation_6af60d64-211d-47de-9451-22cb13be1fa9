<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/last_login_tooltip_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingHorizontal="12dp"
    android:paddingVertical="8dp"
    android:background="@drawable/bacground_instagram_item"
    android:backgroundTint="@color/red_500"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintWidth="wrap_content_constrained">

    <TextView
        android:id="@+id/tooltip_text"
        style="@style/text_style_75"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/last_logged_in_text"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
