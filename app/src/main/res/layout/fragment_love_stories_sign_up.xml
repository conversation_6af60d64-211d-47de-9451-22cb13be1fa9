<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".onboarding_permissions.LocationPermissionStepFragment">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.duaag.android.views.WrapContentViewPager2
                android:id="@+id/viewpager_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/pager"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never" />

            </com.duaag.android.views.WrapContentViewPager2>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewpager_container"
                app:tabBackground="@drawable/tab_pager_selector"
                app:tabGravity="center"
                app:tabIndicatorHeight="0dp"
                app:tabMaxWidth="15dp"/>

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/text_style_500"
                android:layout_marginHorizontal="24dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/title_primary"
                app:layout_constraintTop_toBottomOf="@id/tab_layout"
                android:textAlignment="center"
                android:text="@string/onboarding_stories_heading"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/stories_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:paddingBottom="84dp"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:itemCount="4"
                tools:listitem="@layout/item_review_love_story"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <FrameLayout
        android:id="@+id/apply_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gradient_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" >

        <com.duaag.android.views.DuaButton
            android:id="@+id/continue_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="32dp"
            android:stateListAnimator="@null"
            android:text="@string/onboarding_stories_i_want_label"
            app:buttonType="PrimaryWithState" />

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>