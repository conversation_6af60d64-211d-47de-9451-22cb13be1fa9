<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.duaag.android.instagram.models.InstagramMediaModel" />

    </data>

    <ImageView
        android:id="@+id/image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/bacground_instagram_item"
        android:padding="1dp"
        android:clickable="false"
        android:focusableInTouchMode="false"
        tools:src="@drawable/test"
        app:instagramImage="@{model.mediaUrl}"
       />
</layout>