<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.duaag.android.R" />

    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:context="com.duaag.android.settings.fragments.account_settings.AccountSettingsFragment">

            <TextView
                android:id="@+id/header_delete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="22dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/description_primary"
                style="@style/text_style_100"
                android:gravity="center"
                android:textAlignment="center"
                android:text="@string/adjust_your_log_in_information_profile_visibility_and_other_account_settings"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/looking_for_friends"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/header_delete">

                <TextView
                    android:id="@+id/prefernces"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="44dp"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    android:textColor="@color/title_primary"
                    android:text="@string/preferences"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/imageView31"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/prefernces" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/showMe_id"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/imageView31">

                    <ImageView
                        android:id="@+id/friends_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="17dp"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/friends_dsc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_100"
                        tools:text="@string/active"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/friends_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/show_me"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="21dp"
                        android:layout_marginBottom="21dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_100"
                        android:text="@string/everyone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:id="@+id/imageView32"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/showMe_id" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/adjust_profile_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/looking_for_friends">

                <TextView
                    android:id="@+id/title_adjust_profile_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="44dp"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_400"
                    android:text="@string/adjust_profile_info"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/adjust_profile_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:textColor="@color/description_primary"
                    style="@style/text_style_100"
                    android:text="@string/note_you_can_only_change_your_profile_information_once_in_every_six_months_an"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title_adjust_profile_info" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_change_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="11dp"
                    android:paddingBottom="11dp"
                    app:layout_constraintTop_toBottomOf="@+id/name_top_line">

                    <ImageView
                        android:id="@+id/name_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/change_name_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_regular"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_100"
                        android:text="@string/change"
                        android:textAlignment="textEnd"
                        android:maxLines="2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/name_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/name_top"
                        tools:text="Erand" />

                    <TextView
                        android:id="@+id/name_top"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_75"
                        android:text="@string/name"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_change_gender"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="9dp"
                    android:paddingBottom="13dp"
                    app:layout_constraintTop_toBottomOf="@+id/gender_top_line">

                    <ImageView
                        android:id="@+id/gender_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/change_gender_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_regular"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_100"
                        android:text="@string/change"
                        android:textAlignment="textEnd"
                        android:maxLines="2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/gender_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/gender_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/gender_top"
                        tools:text="Male" />

                    <TextView
                        android:id="@+id/gender_top"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_75"
                        android:text="@string/gender"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_change_birthdate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="9dp"
                    android:paddingBottom="13dp"
                    app:layout_constraintTop_toBottomOf="@+id/birthdate_top_line">

                    <ImageView
                        android:id="@+id/birthdate_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/change_birthdate_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_regular"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_100"
                        android:text="@string/change"
                        android:textAlignment="textEnd"
                        android:maxLines="2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/birthdate_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/birthdate_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/birthdate_top"
                        tools:text="02-11-1994" />

                    <TextView
                        android:id="@+id/birthdate_top"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_75"
                        android:text="@string/birthdate"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <View
                    android:id="@+id/name_top_line"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="20dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/adjust_profile_note" />

                <View
                    android:id="@+id/gender_top_line"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/card_change_name"/>


                <View
                    android:id="@+id/birthdate_top_line"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/card_change_gender"/>

                <View
                    android:id="@+id/adjust_profile_bottom_line"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/card_change_birthdate" />




            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/chang_login_method"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/adjust_profile_info">

                <TextView
                    android:id="@+id/title_chang_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_400"
                    android:text="@string/change_sign_in_information"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_change_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="11dp"
                    android:paddingBottom="11dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView27">

                    <ImageView
                        android:id="@+id/phone_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_regular"
                        android:text="@string/change"
                        android:textColor="@color/description_primary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/phone_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/phone_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/phone_top"
                        tools:text="+38349499211" />

                    <TextView
                        android:id="@+id/phone_top"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_75"
                        android:text="@string/phone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_change_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="9dp"
                    android:paddingBottom="13dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView28">

                    <ImageView
                        android:id="@+id/email_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="11dp"
                        android:fontFamily="@font/tt_norms_pro_regular"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_100"
                        android:text="@string/change"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/email_arrow"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/email_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/email_top"
                        tools:text="<EMAIL>" />

                    <TextView
                        android:id="@+id/email_top"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/description_primary"
                        style="@style/text_style_75"
                        android:text="@string/email_"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/imageView27"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="16dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title_chang_login" />

                <View
                    android:id="@+id/imageView28"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    android:layout_marginHorizontal="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/card_change_phone"/>

                <View
                    android:id="@+id/imageView29"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    android:layout_marginHorizontal="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/card_change_email" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/download_data_cl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/chang_login_method">

                <TextView
                    android:id="@+id/download_data_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_400"
                    android:text="@string/download_data"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/download_data_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:textColor="@color/description_primary"
                    style="@style/text_style_100"
                    android:text="@string/tap_download_data"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/download_data_tv"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/download_data"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="17dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/download_data_description">

                    <TextView
                        android:id="@+id/download_data_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_100"
                        android:text="@string/download_my_data"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/download_data_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_chevron_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/imgview"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    android:visibility="visible"
                    app:layout_constraintBottom_toTopOf="@+id/download_data"
                    app:layout_constraintEnd_toEndOf="@+id/download_data"
                    app:layout_constraintStart_toStartOf="@+id/download_data"/>

                <View
                    android:id="@+id/imageView30"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="@+id/download_data"
                    app:layout_constraintStart_toStartOf="@+id/download_data"
                    app:layout_constraintTop_toBottomOf="@+id/download_data"/>

            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ghost_mode_cl"
                android:layout_width="match_parent"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/download_data_cl"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/ghost_mode_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/title_ghost"
                    app:layout_constraintStart_toEndOf="@+id/title_ghost"
                    app:layout_constraintTop_toTopOf="@+id/title_ghost"
                    app:srcCompat="@drawable/ic_ghost_mode_tag" />
                <TextView
                    android:id="@+id/title_ghost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_400"
                    android:text="@string/ghost_mode"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_ghost_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="21dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ripple_background_item"
                    android:elevation="0dp"
                    android:paddingTop="21dp"
                    android:paddingBottom="21dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ghost_description">


                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/ghost_mode_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:theme="@style/SwitchTheme"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                    </com.google.android.material.switchmaterial.SwitchMaterial>

                    <TextView
                        android:id="@+id/ghost_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:textColor="@color/title_primary"
                        style="@style/text_style_100"
                        android:text="@string/ghost_profile"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/ghost_line_2"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    app:layout_constraintBottom_toTopOf="@+id/card_ghost_mode"
                    app:layout_constraintEnd_toEndOf="@+id/card_ghost_mode"
                    app:layout_constraintStart_toStartOf="@+id/card_ghost_mode" />

                <View
                    android:id="@+id/ghost_line"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    android:layout_marginBottom="40dp"
                    app:layout_constraintEnd_toEndOf="@+id/card_ghost_mode"
                    app:layout_constraintStart_toStartOf="@+id/card_ghost_mode"
                    app:layout_constraintTop_toBottomOf="@+id/card_ghost_mode"
                    />

                <TextView
                    android:id="@+id/ghost_description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="18dp"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    style="@style/text_style_100"
                    android:textColorHighlight="#00ffffff"
                    android:textColorLink="@color/title_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/title_ghost"
                    android:text="@string/ghost_mode_desc" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/title_hide_profile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginTop="40dp"
                android:fontFamily="@font/tt_norms_pro_bold"
                android:textColor="@color/title_primary"
                style="@style/text_style_400"
                android:text="@string/hide_profile"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ghost_mode_cl" />

            <TextView
                android:id="@+id/hide_profile_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="18dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/description_primary"
                style="@style/text_style_100"
                android:text="@string/by_hiding_your_profile_you_will_still_be_able_to_chat_however_your_profile_will_not_be_visible_for_other_users"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_hide_profile" />

            <TextView
                android:id="@+id/note_hide_profile_boost"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="18dp"
                android:text="@string/do_not_hide_profile_boost"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/description_primary"
                style="@style/text_style_100"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/hide_profile_description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/card_hide_profile"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="21dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/ripple_background_item"
                android:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/note_hide_profile_boost">


                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/hide_profile_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:theme="@style/SwitchTheme"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                </com.google.android.material.switchmaterial.SwitchMaterial>

                <TextView
                    android:id="@+id/hide_profile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:text="@string/hide_my_profile"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/imageView24"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/border"
                app:layout_constraintBottom_toTopOf="@+id/card_hide_profile"
                app:layout_constraintEnd_toEndOf="@+id/card_hide_profile"
                app:layout_constraintStart_toStartOf="@+id/card_hide_profile" />

            <View
                android:id="@+id/imageView25"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/border"
                app:layout_constraintEnd_toEndOf="@+id/card_hide_profile"
                app:layout_constraintStart_toStartOf="@+id/card_hide_profile"
                app:layout_constraintTop_toBottomOf="@+id/card_hide_profile" />


            <TextView
                android:id="@+id/title_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginTop="40dp"
                android:fontFamily="@font/tt_norms_pro_bold"
                android:textColor="@color/title_primary"
                style="@style/text_style_400"
                android:text="@string/delete_account"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_hide_profile" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/card_delete_my_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="21dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/ripple_background_item"
                android:elevation="0dp"
                android:paddingTop="21dp"
                android:paddingBottom="21dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/note_id">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_chevron_right"
                    app:layout_constraintBottom_toBottomOf="@+id/header"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/header" />

                <TextView
                    android:id="@+id/header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:text="@string/delete_my_account"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/imageView23"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/border"
                app:layout_constraintBottom_toTopOf="@+id/card_delete_my_account"
                app:layout_constraintEnd_toEndOf="@+id/card_delete_my_account"
                app:layout_constraintStart_toStartOf="@+id/card_delete_my_account" />

            <View
                android:id="@+id/imageView26"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/border"
                android:layout_marginBottom="40dp"
                app:layout_constraintEnd_toEndOf="@+id/card_delete_my_account"
                app:layout_constraintStart_toStartOf="@+id/card_delete_my_account"
                app:layout_constraintTop_toBottomOf="@+id/card_delete_my_account"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/note_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="18dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                style="@style/text_style_100"
                android:textColorHighlight="#00ffffff"
                android:textColorLink="@color/title_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_delete"
                tools:text="@string/cancel_your_subscription_an" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>
