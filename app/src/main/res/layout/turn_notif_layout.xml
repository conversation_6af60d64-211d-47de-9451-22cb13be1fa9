<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.duaag.android.R"/>

        <variable
            name="handlers"
            type="com.duaag.android.chat.fragments.ConversationFragment.Handlers.NotificationsHandlers" />
        <variable
            name="description"
            type="String" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/snack_constraint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/dismiss_img"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:onClick="@{()->handlers.onDismissClick()}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_close_black_24dp" />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/notif_off"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/notif_title"
            app:srcCompat="@drawable/ic_bell" />

        <TextView
            style="@style/text_style_200"
            android:id="@+id/notif_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="2dp"
            android:text="@string/notifications_are_off"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toStartOf="@id/dismiss_img"
            app:layout_constraintStart_toEndOf="@+id/notif_off"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/text_style_100"
            android:id="@+id/notif_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:fontFamily="@font/tt_norms_pro_regular"
            android:textColor="@color/description_secondary"
            tools:text="@string/turn_on_your_notifications_and_see_immediately_when_x_writes_back_an"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/notif_title"
            app:layout_constraintTop_toBottomOf="@+id/notif_title" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/notif_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="16dp"
            app:buttonType="Primary"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:onClick="@{()->handlers.onTurnOnNotificationsClick()}"
            android:paddingStart="24dp"
            android:paddingTop="4dp"
            android:paddingEnd="24dp"
            android:paddingBottom="4dp"
            android:text="@string/turn_notifications_chat"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toTopOf="@id/line"
            app:layout_constraintStart_toStartOf="@id/notif_title"
            app:layout_constraintTop_toBottomOf="@+id/notif_description" />

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/border"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
