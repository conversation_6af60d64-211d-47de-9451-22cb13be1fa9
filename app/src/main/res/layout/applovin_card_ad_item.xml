<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?attr/selectableItemBackground"
            android:foreground="?attr/selectableItemBackground"
            app:cardBackgroundColor="@color/gray_50"
            app:cardCornerRadius="20dp"
            app:cardPreventCornerOverlap="false"
            app:cardElevation="0dp"
            android:animateLayoutChanges="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/native_ad"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/user_info"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#80000000">

                    <ImageView
                        android:id="@+id/background_top"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/profile_image_test" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:background="#33000000"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/top_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="12dp"
                        android:layout_marginTop="16dp"
                        app:layout_constraintTop_toTopOf="parent">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/ad_app_icon"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_marginBottom="4dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:src="@drawable/first_page_first_cover" />

                        <TextView
                            style="@style/text_style_200"
                            android:id="@+id/ad_advertiser"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:fontFamily="@font/tt_norms_pro_medium"
                            android:textColor="@color/gray_50"
                            app:layout_constraintBottom_toTopOf="@+id/ad_stars"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintStart_toEndOf="@id/ad_app_icon"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="SushiCO" />

                        <RatingBar
                            android:id="@+id/ad_stars"
                            style="?android:attr/ratingBarStyleSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:isIndicator="true"
                            android:numStars="5"
                            android:stepSize="0.5"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="@+id/ad_advertiser"
                            app:layout_constraintTop_toBottomOf="@+id/ad_advertiser"
                            tools:progress="5" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <FrameLayout
                        android:id="@+id/media_container"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        app:layout_constraintBottom_toTopOf="@+id/bottom_container"
                        app:layout_constraintTop_toBottomOf="@id/top_container">

                        <ImageView
                            android:id="@+id/ad_media"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            tools:background="@drawable/test" />
                    </FrameLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/bottom_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="16dp"
                        android:paddingBottom="20dp"
                        app:layout_constraintBottom_toTopOf="@id/buttons_container">


                        <TextView
                            style="@style/text_style_300"
                            android:id="@+id/ad_headline"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:maxEms="25"
                            android:fontFamily="@font/tt_norms_pro_demibold"
                            android:textColor="@color/gray_50"
                            app:layout_constraintBottom_toTopOf="@+id/ad_body"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="92345678909234567890923456782343456789012345678901234567890" />

                        <TextView
                            style="@style/text_style_100"
                            android:id="@+id/ad_body"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:ellipsize="end"
                            android:maxEms="90"
                            android:maxLines="3"
                            tools:text="This is a text that is going to be bigger than 90 characters to test it in android studio and now it has exeeded that number\n
This is a text that is going to be bigger than 90 characters to test it in android studio and now it has exeeded that number\n"
                            android:fontFamily="@font/tt_norms_pro_normal"
                            android:textColor="@color/gray_50"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintBottom_toTopOf="@id/ad_call_to_action" />

                        <Button
                            android:id="@+id/ad_call_to_action"
                            style="@style/text_style_200"
                            android:layout_width="0dp"
                            android:layout_height="44dp"
                            android:layout_marginEnd="24dp"
                            android:background="@drawable/background_action_ad_button_card"
                            android:fontFamily="@font/tt_norms_pro_medium"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:paddingHorizontal="10dp"
                            android:stateListAnimator="@null"
                            android:textAllCaps="false"
                            android:textColor="@color/gray_400"
                            app:elevation="-1dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/linearLayout3"
                            app:layout_constraintStart_toStartOf="parent"
                            tools:text="Install" />


                        <LinearLayout
                            android:id="@+id/linearLayout3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_attribute_ad_button"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            app:layout_constraintBottom_toBottomOf="@+id/ad_call_to_action"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/ad_call_to_action">


                            <TextView
                                android:id="@+id/textView"
                                style="@style/AppTheme.AdAttribution"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/tt_norms_pro_medium"
                                android:lineHeight="@dimen/text_size_200"
                                android:textColor="@color/gray_75"
                                android:textSize="@dimen/text_size_200" />


                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/dislike_button"
                        android:layout_width="68dp"
                        android:layout_height="68dp"
                        android:background="@color/transparent"
                        android:layout_marginTop="24dp"
                        android:layout_marginHorizontal="16dp"
                        android:src="@drawable/ic_in_app_disslike"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/super_like_button"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_margin="16dp"
                        android:background="@drawable/superlike_background_gradient"
                        android:src="@drawable/ic_superlike"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/like_button"
                        android:layout_width="68dp"
                        android:layout_height="68dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginHorizontal="16dp"
                        android:background="@color/transparent"
                        android:src="@drawable/ic_in_app_like"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/buttons_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="26dp"
                        android:animateLayoutChanges="true"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/dislike_action_button"
                            android:layout_width="@dimen/home_screen_button_dimension"
                            android:layout_height="@dimen/home_screen_button_dimension"
                            android:layout_gravity="center_vertical"
                            app:cardBackgroundColor="@android:color/transparent"
                            app:cardElevation="2dp"
                            android:layout_marginEnd="18dp"
                            app:rippleColor="@color/gray_50"
                            app:shapeAppearance="@style/MaterialCardViewCircleShape">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/dislike_vector"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerInside"
                                android:src="@drawable/ic_dislike" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/undo_button"
                            android:layout_width="53dp"
                            android:layout_height="53dp"
                            android:layout_gravity="center_vertical"
                            app:cardBackgroundColor="@android:color/transparent"
                            android:layout_marginEnd="18dp"
                            app:cardElevation="2dp"
                            app:rippleColor="@color/gray_50"
                            app:shapeAppearance="@style/MaterialCardViewCircleShape">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/undo_vector"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerInside"
                                android:src="@drawable/ic_rotate_ccw" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/instachat_button"
                            android:layout_width="53dp"
                            android:layout_height="53dp"
                            android:layout_gravity="center_vertical"
                            app:cardBackgroundColor="@android:color/transparent"
                            android:layout_marginEnd="18dp"
                            app:cardElevation="2dp"
                            app:rippleColor="@color/gray_50"
                            app:shapeAppearance="@style/MaterialCardViewCircleShape">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/instachat_vector"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerInside"
                                android:src="@drawable/ic_instachat" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/like_action_button"
                            android:layout_width="@dimen/home_screen_button_dimension"
                            android:layout_height="@dimen/home_screen_button_dimension"
                            app:cardBackgroundColor="@android:color/transparent"
                            app:cardElevation="2dp"
                            app:rippleColor="@color/gray_50"
                            app:shapeAppearance="@style/MaterialCardViewCircleShape">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/like_vector"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerInside"
                                android:src="@drawable/ic_like_icon" />
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>


            </FrameLayout>

            <FrameLayout
                android:id="@+id/info_button"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="bottom|end"
                android:src="@drawable/ic_info"
                android:background="@color/transparent"/>

            <View
                android:id="@+id/overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/second_card_overlay" />
        </androidx.cardview.widget.CardView>

<!--        <FrameLayout-->
<!--            android:id="@+id/bottom_background_cover"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="50dp"-->
<!--            android:focusable="true"-->
<!--            android:clickable="true"-->
<!--            android:background="@color/inapp_background_color"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"/>-->
</androidx.constraintlayout.widget.ConstraintLayout>