<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    xmlns:android="http://schemas.android.com/apk/res/android">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="24dp"
    tools:context=".image_verification.fragments.ImageDeniedFragment">

    <TextView
        android:id="@+id/caption"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/verification_failed"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/heading"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/heading"
        style="@style/text_style_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"

        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/verification_failed_description"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintTop_toBottomOf="@id/caption" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="12dp"
        android:adjustViewBounds="true"
        android:src="@drawable/guideline_man_template"
        app:layout_constraintBottom_toBottomOf="@+id/image_guideline"
        app:layout_constraintEnd_toStartOf="@id/image_guideline"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/heading" />

       <TextView
           android:id="@+id/guideline_tag"
           android:text="@string/guideline"
           style="@style/text_style_100"
           android:textColor="@color/white"
           android:elevation="1dp"
           android:layout_margin="8dp"
           android:paddingVertical="2dp"
           android:paddingHorizontal="8dp"
           android:background="@drawable/rounded_shape_32_dp"
           android:backgroundTint="@color/colorPrimary"
           android:fontFamily="@font/tt_norms_pro_normal"
           app:layout_constraintEnd_toEndOf="@id/image_guideline"
           app:layout_constraintBottom_toBottomOf="@id/image_guideline"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"/>
    <ImageView
        android:id="@+id/image_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="9:16"
        android:adjustViewBounds="true"
        android:layout_marginStart="8dp"
        android:layout_marginTop="32dp"
        app:layout_constraintWidth_max="320dp"
        android:src="@drawable/guideline_woman_template"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/image"
        app:layout_constraintTop_toBottomOf="@id/heading" />

    <LinearLayout
        android:id="@+id/reason"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@+id/image_guideline"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintTop_toBottomOf="@+id/image">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/info_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_marginEnd="16dp"
            app:drawableTint="@color/icon_secondary"
            app:srcCompat="@drawable/ic_info" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/reason_title"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/visual_disturbing_title"
                android:textColor="@color/title_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/caption" />

            <TextView
                android:id="@+id/reason_description"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/visual_disturbing_desc"
                android:textColor="@color/description_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/caption" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/guidelines_textView"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/check_guideline_link"
        android:textAlignment="center"
        android:layout_marginBottom="12dp"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/replace_photo_btn"
        app:layout_constraintEnd_toEndOf="@+id/reason"
        app:layout_constraintStart_toStartOf="@+id/reason"
        app:layout_constraintTop_toBottomOf="@id/reason"
        app:layout_constraintVertical_bias="0.0" />
    <com.duaag.android.views.DuaButton
        app:buttonType="PrimaryWithState"
        android:id="@+id/replace_photo_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:stateListAnimator="@null"
        android:text="@string/replace_photo_label"
        app:layout_constraintWidth_max="320dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>