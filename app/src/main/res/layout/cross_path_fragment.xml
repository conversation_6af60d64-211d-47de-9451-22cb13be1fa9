<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:layoutDirection="ltr"
        android:overScrollMode="never">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:layoutDirection="ltr"
            tools:context=".crosspath.presentation.fragment.CrossPathMainFragment">

            <include
                android:id="@+id/spotted_toolbar"
                layout="@layout/spotted_toolbar_layout" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/discovered_profiles"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:overScrollMode="never"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/spotted_toolbar" />

            <TextView
                android:id="@+id/map_title"
                style="@style/text_style_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:text="@string/map_heading"
                android:textColor="@color/title_primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/discovered_profiles" />

            <ImageButton
                android:id="@+id/map_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="@null"
                android:src="@drawable/ic_arrow_rightt"
                app:layout_constraintBottom_toBottomOf="@+id/map_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/map_title"
                app:tint="@color/title_primary" />

            <androidx.cardview.widget.CardView
                android:id="@+id/map_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:clickable="false"
                android:focusable="false"
                android:focusableInTouchMode="false"
                app:cardCornerRadius="24dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toTopOf="@+id/sunny_hill_graphic"
                app:layout_constraintDimensionRatio="1.4:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/map_title">

                <com.mapbox.maps.MapView
                    android:id="@+id/mapView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clickable="false"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    app:mapbox_cameraBearing="0.0"
                    app:mapbox_cameraPitch="0.0"
                    app:mapbox_cameraZoom="2.0" />

            </androidx.cardview.widget.CardView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/expand_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                app:layout_constraintBottom_toBottomOf="@id/map_container"
                app:layout_constraintEnd_toEndOf="@id/map_container"
                app:layout_constraintStart_toStartOf="@id/map_container"
                app:layout_constraintTop_toTopOf="@id/map_container">

                <ImageView
                    android:id="@+id/expand_blur"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:src="@drawable/blur_background_cloud"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <TextView
                    android:id="@+id/expand_text"
                    style="@style/text_style_200"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="16dp"
                    android:clickable="false"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:text="@string/tap_expand_link"
                    android:textColor="@color/description_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <include
                    android:id="@+id/grouped_pins_error"
                    layout="@layout/discovered_cross_paths_error_item"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <requestFocus />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/sunny_hill_graphic"
                layout="@layout/featured_profile_sunny_hill_item"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginHorizontal="24dp"
                android:layout_marginBottom="40dp"
                app:layout_constraintBottom_toTopOf="@id/explore_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"/>

            <TextView
                android:id="@+id/explore_title"
                style="@style/text_style_500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:layout_marginBottom="16dp"
                android:textAlignment="viewStart"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:text="@string/explore_heading"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toTopOf="@id/explore_cross_path_container" />

            <include
                android:id="@+id/explore_cross_path_container"
                layout="@layout/explore_cross_path_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


            <include
                android:id="@+id/network_error"
                layout="@layout/featured_profile_network_item"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:elevation="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/spotted_toolbar" />

            <include
                android:id="@+id/profile_invisible"
                layout="@layout/featured_profile_hidden_profile_item"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:elevation="20dp"
                android:visibility="gone"
                tools:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/spotted_toolbar" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
