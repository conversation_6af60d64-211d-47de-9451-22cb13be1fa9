<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="16dp"
    android:layout_marginTop="12dp"
    android:focusable="true"
    android:clickable="true"
    tools:showIn="@layout/fragment_user_profile"
    android:descendantFocusability="blocksDescendants"
    android:background="@drawable/gradient_circle"
    android:elevation="12dp"
   >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/image_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintVertical_chainStyle="packed"
        android:duplicateParentState="true"
        app:layout_constraintBottom_toTopOf="@id/time_place_section"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">
        <com.duaag.android.crosspath.presentation.views.OverlappingCircularImageView
            android:id="@+id/overlapping_spotted_users"
            android:layout_width="240dp"
            android:layout_height="200dp"
            android:elevation="3dp"
             android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/spotted_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:elevation="3dp"
            android:src="@drawable/spotted_icon_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.45"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/title"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="3"

        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/you_spotted_headline"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@id/image_container"
        app:layout_constraintEnd_toStartOf="@+id/title_name"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/title_name"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="Dea"
        android:textColor="@color/pink_500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/title"
        app:layout_constraintTop_toTopOf="@id/title" />

    <include
        android:id="@+id/time_place_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        layout="@layout/cross_map_overlay_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>