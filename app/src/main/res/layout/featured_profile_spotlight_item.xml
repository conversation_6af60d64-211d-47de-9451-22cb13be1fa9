<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="12dp"
    app:cardCornerRadius="24dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/featured_user_image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="3:4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/profile_image_test" />


        <FrameLayout
            android:id="@+id/bottom_shade"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/featured_profile_shade"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@+id/guideline_center" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="35dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/featured_spot_user_button"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/featured_user_name"
                style="@style/text_style_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textColor="@color/gray_50"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/featured_user_age"
                app:layout_constraintHorizontal_bias="0.0"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintWidth="wrap_content_constrained"
                tools:text="Qendrim Cakaj Cakaj" />

            <TextView
                android:id="@+id/featured_user_age"
                style="@style/text_style_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textColor="@color/gray_50"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/user_activity"
                app:layout_constraintHorizontal_bias="0.0"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintTop_toTopOf="@id/featured_user_name"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/featured_user_name"
                app:layout_constraintWidth="wrap_content_constrained"
                tools:text=", 89" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/user_activity"
                android:layout_width="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginStart="4dp"
                android:layout_marginTop="4dp"
                app:layout_constraintBottom_toBottomOf="@id/featured_user_name"
                app:layout_constraintEnd_toStartOf="@id/featured_user_badge"
                app:layout_constraintTop_toTopOf="@id/featured_user_name"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/featured_user_age"
                tools:srcCompat="@drawable/activity_status_now_illustration"
                app:layout_constraintWidth="wrap_content_constrained"
                android:layout_height="wrap_content"/>
            <ImageView
                android:id="@+id/featured_user_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:scaleType="centerInside"
                app:layout_constraintWidth="wrap_content_constrained"
                app:layout_constraintEnd_toEndOf="parent"
                android:src="@drawable/ic_featured_badge"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/featured_user_age"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/user_activity"
                app:layout_constraintTop_toTopOf="@+id/featured_user_age" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/featured_spotlight_background"
            android:orientation="horizontal"
            android:paddingVertical="8dp"
            android:paddingStart="8dp"
            android:paddingEnd="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="6dp"
                android:src="@drawable/ic_star" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="Spotlight"
                android:textAppearance="@style/text_style_100"
                android:textColor="@color/gray_50" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/featured_spot_user_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/dua_button_background"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="18dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/featured_spot_user_button_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginEnd="8dp"
                android:scaleType="center"
                android:src="@drawable/ic_heart" />

            <TextView
                android:id="@+id/featured_spot_user_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="Like"
                android:textAppearance="@style/text_style_200"
                android:textColor="@color/gray_50" />
        </LinearLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.google.android.material.card.MaterialCardView>