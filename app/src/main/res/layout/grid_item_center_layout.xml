<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/image"
        android:layout_width="109dp"
        android:layout_height="109dp"
        android:layout_gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TableLayout
        android:id="@+id/table_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center_horizontal"
        android:shrinkColumns="0"
        app:layout_constraintEnd_toEndOf="@+id/image"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintTop_toBottomOf="@+id/image">

        <TableRow
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal">

            <TextView
                android:id="@+id/txt_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:ellipsize="marquee"
                android:fontFamily="@font/tt_norms_pro_demibold"
                style="@style/text_style_200"
                android:textColor="@color/title_primary"
                android:singleLine="true"
                android:src="@drawable/ic_location"
                app:layout_constraintEnd_toEndOf="@+id/image"
                app:layout_constraintStart_toEndOf="@+id/imageView10"
                app:layout_constraintTop_toBottomOf="@+id/image"
                tools:text="Qendrim, " />

            <TextView
                android:id="@+id/txt_age"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_normal"
                style="@style/text_style_200"
                android:textColor="@color/title_primary"
                android:src="@drawable/ic_location"
                app:layout_constraintStart_toEndOf="@+id/txt_name"
                app:layout_constraintTop_toTopOf="@+id/txt_name"
                tools:text="26" />

            <ImageView
                android:id="@+id/badge1_img"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:layout_marginStart="2dp"
                android:src="@drawable/ic_image_verification"
                android:scaleType="centerCrop" />


            <ImageView
                android:id="@+id/star"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:layout_marginStart="2dp"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/ic_form_superlike" />
        </TableRow>

    </TableLayout>

    <ImageView
        android:id="@+id/dot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        app:layout_constraintEnd_toEndOf="@+id/image"
        app:srcCompat="@drawable/ic_new_match_budge" />
</androidx.constraintlayout.widget.ConstraintLayout>
