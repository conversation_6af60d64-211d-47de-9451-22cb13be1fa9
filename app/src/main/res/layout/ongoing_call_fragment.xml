<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/call_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".calls.fragments.IncomingCallFragment">


    <ImageView
        android:id="@+id/caller_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_300"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <com.twilio.video.VideoView
        android:id="@+id/primaryVideoView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tviMirror="true" />

    <View
        android:id="@+id/dim_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#A6000000"
        android:clickable="false"
        android:focusableInTouchMode="false"
        android:focusable="false"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/user_info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="136dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/caller_image"
            android:layout_width="156dp"
            android:layout_height="156dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/test" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/caller_info_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/caller_image"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="@+id/caller_image"
            app:layout_constraintTop_toBottomOf="@id/caller_image"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintVertical_chainStyle="spread">

            <TextView
                style="@style/text_style_500"
                android:id="@+id/nameAgeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constrainedWidth="true"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:textAlignment="center"
                android:fontFamily="@font/tt_norms_pro_bold"
                android:textColor="@color/gray_50"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/badge_img"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <FrameLayout
                android:id="@+id/badge_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@+id/nameAgeText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/nameAgeText"
                app:layout_constraintTop_toTopOf="@+id/nameAgeText">

                <ImageView
                    android:id="@+id/badge_approved"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:alpha="0"
                    android:elevation="1dp"
                    android:src="@drawable/ic_image_verification"

                    />

                <ImageView
                    android:id="@+id/badge_processing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:src="@drawable/ic_image_verification_processinng" />

            </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            style="@style/text_style_200"
            android:id="@+id/call_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/gray_50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/caller_info_container"
            tools:text="Duration..." />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/video_status_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@color/gray_400"
        android:text="Room status"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ProgressBar
        android:id="@+id/reconnecting_progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="32dp"
        android:indeterminate="true"
        android:theme="@style/ProgressBarTheme"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_info_container"
        tools:visibility="visible" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/draggable_controls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/call_bottom_sheet_background"
            android:tag="draggable_controls">

            <ImageView
                android:id="@+id/drag_handle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:src="@drawable/call_bottomsheet_drag_handle"
                app:layout_constraintBottom_toTopOf="@+id/control_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/control_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginHorizontal="30dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="56dp"
                android:clickable="false"
                android:columnCount="4"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/drag_handle" />

            <!--
                        <GridLayout
                            android:id="@+id/control_container"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:layout_marginTop="16dp"
                            android:layout_marginBottom="56dp"
                            android:clickable="false"
                            android:columnCount="4"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:gravity="center"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/drag_handle">


                            <ImageButton
                                android:id="@+id/localVideoAction"
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                android:layout_gravity="center"
                                android:background="@drawable/background_call_option_black"
                                android:src="@drawable/ic_video_on"
                                app:background="@drawable/fab_background"
                                app:fabSize="normal"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/switchCameraAction"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageButton
                                android:id="@+id/switchCameraAction"
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                android:layout_gravity="center"
                                android:background="@drawable/background_call_option_black"
                                android:src="@drawable/ic_switch_camera_white"
                                app:background="@drawable/fab_background"
                                app:fabSize="normal"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/audioAction"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/localVideoAction"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageButton
                                android:id="@+id/audioAction"
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                android:layout_gravity="center"
                                android:background="@drawable/background_call_option_black"
                                android:src="@drawable/ic_volume_1"
                                android:visibility="gone"
                                app:background="@drawable/fab_background"
                                app:fabSize="normal"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/muteAction"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/switchCameraAction"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:visibility="visible" />

                            <ImageButton
                                android:id="@+id/muteAction"
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                android:layout_gravity="center"
                                android:background="@drawable/background_call_option_black"
                                android:src="@drawable/ic_mic_on"
                                app:background="@drawable/fab_background"
                                app:fabSize="normal"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/end_call"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/audioAction"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageButton
                                android:id="@+id/end_call"
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                android:background="@drawable/end_call_backround"
                                android:src="@drawable/ic_end_call"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/muteAction"
                                app:layout_constraintTop_toTopOf="parent" />

                        </GridLayout>
            -->
        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>

    <ImageButton
        android:id="@+id/close_btn"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="34dp"
        android:layout_marginTop="56dp"
        android:background="@color/transparent"
        android:src="@drawable/ic_chevron_down"
        app:tint="@color/gray_50"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/small_video_view_container"
        android:layout_width="104dp"
        android:layout_height="164dp"
        android:background="@color/transparent"
        android:visibility="gone"
        app:cardElevation="0dp"
        app:cardCornerRadius="8dp"
        tools:visibility="visible">

        <com.twilio.video.VideoTextureView
            android:id="@+id/small_video_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="false"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:visibility="gone"
            tools:ignore="MissingConstraints"
            tools:layout_editor_absoluteX="280dp"
            tools:layout_editor_absoluteY="16dp"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>