<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="24dp"
    android:layout_marginBottom="20dp">

    <ImageView
        android:id="@+id/image"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.04"
        android:src="@drawable/pink_check" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="@color/title_primary"
        android:textSize="@dimen/text_size_200"
        android:ellipsize="end"
        android:layout_marginEnd="4dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        app:layout_constraintEnd_toStartOf="@+id/description"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@+id/image"
        app:layout_constraintTop_toTopOf="@+id/image"
        tools:text="Likes" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textSize="@dimen/text_size_200"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/image"
        tools:text="50 per week" />


</androidx.constraintlayout.widget.ConstraintLayout>
