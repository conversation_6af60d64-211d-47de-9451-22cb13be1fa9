<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

<ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".settings.fragments.SocialMediaFragment">

        <TextView
            android:id="@+id/textView9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="30dp"
            android:textColor="@color/description_primary"
            android:fontFamily="@font/tt_norms_pro_normal"
            style="@style/text_style_100"
            android:text="@string/engage_with_social_media"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView48"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="72dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            style="@style/text_style_300"
            android:text="@string/engage_on_social_media"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView9" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/social_media_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView48"
            tools:listitem="@layout/social_media_item" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>

</layout>
