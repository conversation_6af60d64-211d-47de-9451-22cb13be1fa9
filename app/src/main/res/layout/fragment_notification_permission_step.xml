<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="32dp"
    tools:context=".onboarding_permissions.NotificationPermissionStepFragment">

    <ImageView
        android:id="@+id/illustration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        android:layout_marginTop="24dp"
        tools:src="@drawable/notification_female_al"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/onboarding_notification_heading"
        android:textAlignment="center"
        android:textColor="@color/red_500"
        app:layout_constraintBottom_toTopOf="@id/description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/illustration"/>

    <TextView
        android:id="@+id/description"
        style="@style/text_style_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/onboarding_notification_desc"
        android:layout_marginTop="6dp"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintWidth_max="488dp" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/continue_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:stateListAnimator="@null"
        android:text="@string/onboarding_activate_notifications"
        app:buttonType="PrimaryWithState"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:indeterminate="true"
        android:theme="@style/ProgressBarTheme"
        android:visibility="gone"
        tools:visibility="visible"
        android:elevation="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/continue_btn"
        app:layout_constraintEnd_toEndOf="@+id/continue_btn"
        app:layout_constraintTop_toTopOf="@id/continue_btn"
        app:layout_constraintStart_toStartOf="@id/continue_btn"/>

</androidx.constraintlayout.widget.ConstraintLayout>