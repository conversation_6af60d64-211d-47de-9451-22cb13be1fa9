<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/guidelines_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:animateLayoutChanges="true"
        android:background="@drawable/rounded_corners_12dp_shape"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:src="@drawable/ic_childrens_v2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/percentage"
            app:layout_constraintHorizontal_bias="0.531"
            app:layout_constraintStart_toEndOf="@+id/icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/wrong_profile_information_description" />

        <View
            android:id="@+id/divider"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/border"
            app:layout_constraintStart_toEndOf="@id/description"
            app:layout_constraintTop_toTopOf="@id/description"
            app:layout_constraintBottom_toBottomOf="@id/description"
            />


        <TextView
            android:id="@+id/percentage"
            style="@style/text_style_200"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="10%"
            app:layout_constraintStart_toEndOf="@id/divider"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
