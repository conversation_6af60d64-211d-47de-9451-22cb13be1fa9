<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingVertical="24dp"
    android:background="@drawable/bottom_sheet_rounded">


    <LinearLayout
        android:id="@+id/title_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/title_primary"
            android:fontFamily="@font/tt_norms_pro_medium"
            style="@style/text_style_300"
            tools:text="Image sent"
            android:textSize="18sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="14dp"
        android:textColor="@color/description_primary"
        android:fontFamily="@font/tt_norms_pro_normal"
        style="@style/text_style_100"
        tools:text="Do you want to continue with this picture or take another one?"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_container" />

    <LinearLayout
        android:id="@+id/verification_failed_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="35dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description">

        <ImageView
            android:id="@+id/pose_image"
            android:layout_width="142dp"
            android:layout_height="192dp"
            android:layout_marginRight="1dp"
            android:scaleType="centerCrop"
            android:transitionName="poseTransition"
            tools:src="@drawable/hamit_bossi" />

        <ImageView
            android:id="@+id/camera_image"
            android:layout_width="142dp"
            android:layout_height="192dp"
            android:layout_marginLeft="1dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/hamit_bossi" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/verification_succeeded_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="35dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description">

        <ImageView
            android:id="@+id/image"
            android:layout_width="142dp"
            android:layout_height="192dp"
            android:scaleType="centerCrop"
            android:transitionName="poseTransition"
            tools:src="@drawable/hamit_bossi" />

    </LinearLayout>

    <TextView
        android:id="@+id/title_suggestions"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/verification_denied_suggestion"
        android:textAlignment="textStart"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="@+id/verification_failed_container"
        app:layout_constraintStart_toStartOf="@+id/verification_failed_container"
        app:layout_constraintTop_toBottomOf="@+id/verification_failed_container" />
    <LinearLayout
        android:id="@+id/suggestion_one"
        android:layout_width="0dp"
        android:orientation="horizontal"
        android:paddingVertical="8dp"
        app:layout_constraintEnd_toEndOf="@+id/verification_failed_container"
        app:layout_constraintStart_toStartOf="@+id/title_suggestions"
        app:layout_constraintTop_toBottomOf="@+id/title_suggestions"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/bulletImageView"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_margin="10dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/bullet_point"
            />
        <TextView
            android:id="@+id/suggestion_one_textView"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            tools:text="Your pose or gesture shown in the image is not right. Please try to copy the pose as shown in the guidelines!"
            android:textAlignment="textStart"
            android:textColor="@color/description_primary"/>

    </LinearLayout>


    <LinearLayout
        android:id="@+id/suggestion_two"
        android:layout_width="0dp"
        android:orientation="horizontal"
        android:paddingVertical="8dp"
        app:layout_constraintEnd_toEndOf="@+id/verification_failed_container"
        app:layout_constraintStart_toStartOf="@+id/title_suggestions"
        app:layout_constraintTop_toBottomOf="@+id/suggestion_one"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/bulletImageView2"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_margin="10dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/bullet_point"
            />
        <TextView
            android:id="@+id/suggestion_two_textView"
            style="@style/text_style_100"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            tools:text="Your pose or gesture shown in the image is not right. Please try to copy the pose as shown in the guidelines!"
            android:textAlignment="textStart"
            android:textColor="@color/description_primary"/>

    </LinearLayout>

    <com.duaag.android.views.DuaButton
        android:id="@+id/continue_button"
        app:buttonType="Primary"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:stateListAnimator="@null"
        android:text="@string/done_f"
        android:layout_marginTop="32dp"
        android:textAllCaps="false"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/suggestion_two" />


</androidx.constraintlayout.widget.ConstraintLayout>