<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/disable_review_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:clickable="true"
    android:focusable="true">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image_review"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="214dp"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toTopOf="@+id/txt_title_review"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:srcCompat="@drawable/ic_warning" />

    <TextView
        android:id="@+id/txt_title_review"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:layout_marginHorizontal="42dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:gravity="center"
        android:text="@string/profile_disabled_title"
        android:textColor="@color/title_primary"
        style="@style/text_style_600"
        app:layout_constraintBottom_toTopOf="@+id/txt_body_review"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image_review" />

    <TextView
        android:id="@+id/txt_body_review"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="42dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="42dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        style="@style/text_style_300"
        android:text="@string/profile_disabled_body"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toTopOf="@+id/review_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_title_review" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/review_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="32dp"
        android:stateListAnimator="@null"
        android:text="@string/review_your_profile"
        app:buttonType="Primary"
        app:layout_constraintBottom_toTopOf="@+id/logout_review_btn"
        app:layout_constraintEnd_toEndOf="@+id/txt_body_review"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@+id/txt_body_review"
        app:layout_constraintTop_toBottomOf="@+id/txt_body_review" />

    <TextView
        android:id="@+id/logout_review_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="168dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:letterSpacing="0"
        android:text="@string/sign_out"
        android:textAllCaps="false"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/review_btn"
        app:rippleColor="#12000000">

    </TextView>
</androidx.constraintlayout.widget.ConstraintLayout>