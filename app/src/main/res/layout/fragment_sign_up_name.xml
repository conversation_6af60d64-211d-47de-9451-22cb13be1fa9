<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:overScrollMode="never"
    android:fillViewport="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        tools:context=".signup.fragment.SignUpNameFragment">

        <TextView
            android:id="@+id/txt_name"
            style="@style/text_style_500"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/whats_your_name"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/illustration" />

        <ImageView
            android:id="@+id/illustration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/name_illustration_female"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/edit_text_name"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:clickable="false"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:hint="@string/enter_your_name"
            android:imeOptions="actionNext"
            android:inputType="textPersonName"
            android:longClickable="false"
            android:padding="16dp"
            android:layout_marginTop="24dp"
            android:textColor="@color/title_primary"
            android:textIsSelectable="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_name"
            tools:text="Esat Bossi" />

        <TextView
            android:id="@+id/name_description"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="100dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:minLines="2"
            android:text="@string/your_name_will_be_displayed_in_dua_application"
            android:textAlignment="textStart"
            android:textColor="@color/gray_200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/edit_text_name"
            app:layout_constraintStart_toStartOf="@+id/edit_text_name"
            app:layout_constraintTop_toBottomOf="@id/edit_text_name"
            app:layout_constraintVertical_bias="0.0" />

        <com.duaag.android.views.DuaButton
            app:buttonType="PrimaryWithState"
            android:id="@+id/next_btn"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="32dp"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>