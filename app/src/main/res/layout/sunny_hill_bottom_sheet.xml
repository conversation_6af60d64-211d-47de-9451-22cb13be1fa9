<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="false">

    <ImageView
        android:id="@+id/background_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/sunny_hill_bottomsheet_background" />

    <TextView
        android:id="@+id/count"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="40dp"
        android:layout_marginStart="24dp"
        android:textColor="@color/gray_50"
        tools:text="1940 check-ins" />

    <TextView
        style="@style/text_style_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="40dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/gray_50"
        app:layout_constraintBottom_toTopOf="@+id/check_in_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/count"
        android:text="@string/soon_people_list_info_text" />

    <ImageButton
        android:id="@+id/close_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="24dp"
        android:background="@color/transparent"
        android:padding="8dp"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />



        <Button
            android:id="@+id/check_in_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/modal_transparent_button_rounded_32dp_light_force"
            android:text="@string/check_in_label"
            android:textAllCaps="false"
            android:textColor="@color/gray_400"
            app:buttonType="Contrast"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>