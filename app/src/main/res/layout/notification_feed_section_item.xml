<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="22dp"
    android:layoutDirection="locale"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/section_title"
        style="@style/text_style_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Today" />

</androidx.constraintlayout.widget.ConstraintLayout>