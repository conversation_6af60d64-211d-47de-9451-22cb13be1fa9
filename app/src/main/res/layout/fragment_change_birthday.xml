<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

        <TextView
            android:id="@+id/change_birthday_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginHorizontal="16dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/title_primary"
            style="@style/text_style_400"
            android:text="@string/change_your_birthdate"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/change_birthday_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginHorizontal="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            android:text="@string/age_screen_subtitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/change_birthday_title"/>

    <TextView
        android:id="@+id/your_birthday_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginHorizontal="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/title_primary"
        style="@style/text_style_100"
        android:text="@string/birthdate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/change_birthday_desc" />


    <TextView
        android:id="@+id/birthday_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/edit_text_rounded_corners_12_dp"
        android:layout_marginTop="4dp"
        android:gravity="start"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/title_primary"
        style="@style/text_style_100"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/your_birthday_title"
        tools:text="27th March 2022" />

    <ImageView
        android:id="@+id/attention_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginStart="16dp"
        android:src="@drawable/ic_info_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/attention_txt" />
    <TextView
        android:id="@+id/attention_txt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="40dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/attention_icon"
        app:layout_constraintTop_toBottomOf="@id/birthday_text"
        tools:text="@string/note_you_can_only_change_your_gender_once_in_every_six_months_an"
        />
    <DatePicker
        android:id="@+id/change_birthdate_date_picker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:calendarTextColor="@color/title_primary"
        style="@style/text_style_100"
        android:calendarViewShown="false"
        android:clipToPadding="false"
        android:datePickerMode="spinner"
        android:theme="@style/DatePickerTheme"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/attention_txt" />
    <com.duaag.android.views.DuaButton
        app:buttonType="PrimaryWithState"
        android:id="@+id/btn_save"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:stateListAnimator="@null"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="24dp"
        android:layout_marginHorizontal="24dp"
        android:text="@string/save_information"
        android:enabled="false"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
