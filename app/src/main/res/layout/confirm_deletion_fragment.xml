<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountPermanentlyFragment">


    <TextView
        android:id="@+id/confirm_deletion_header"
        style="@style/text_style_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="42dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/confirm_deletion_title"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/delete_acc_desc"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="18dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/confirm_deletion_desc"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/confirm_deletion_header" />

    <TextView
        android:id="@+id/note_header"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/note_"
        android:layout_marginTop="24dp"
        android:textColor="@color/title_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/delete_acc_desc" />

    <TextView
        android:id="@+id/note_description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="18dp"
        android:layout_marginTop="6dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/confirm_deletion_note"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/note_header" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/delete_btn"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:text="@string/yes_delete_account_label"
        app:buttonType="Primary"
        android:layout_marginTop="64dp"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintTop_toBottomOf="@+id/note_description" />


    <com.duaag.android.views.DuaButton
        android:id="@+id/keep_btn"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:text="@string/no_keep_account_label"
        app:buttonType="Secondary"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintTop_toBottomOf="@+id/delete_btn" />

</androidx.constraintlayout.widget.ConstraintLayout>
