<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/background">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:textColor="@color/title_primary"
                style="@style/text_style_300"
                android:maxLines="1"
                android:singleLine="true"
                app:layout_constraintBottom_toTopOf="@+id/ad_body"
                app:layout_constraintEnd_toStartOf="@+id/ad_call_to_action"
                app:layout_constraintStart_toEndOf="@+id/ad_app_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Qendrizxczxczxczxczxczxczxczxczxcm" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/description_secondary"
                style="@style/text_style_100"
                app:layout_constraintBottom_toBottomOf="@+id/ad_app_icon"
                app:layout_constraintEnd_toStartOf="@+id/ad_call_to_action"
                app:layout_constraintStart_toStartOf="@+id/ad_headline"
                app:layout_constraintTop_toBottomOf="@+id/ad_headline" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="64dp"
                android:layout_height="64dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/first_page_second_face" />

            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:minHeight="0dp"
                android:minWidth="0dp"
                android:stateListAnimator="@null"
                android:paddingHorizontal="10dp"
                app:elevation="-1dp"
                android:background="@drawable/bacground_action_ad_botton"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:textColor="@color/title_primary"
                style="@style/text_style_75"
                tools:text="Install"
                android:textAllCaps="false"
                app:layout_constraintBottom_toBottomOf="@+id/ad_body"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ad_body"/>

            <ImageButton
                android:id="@+id/report_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="14dp"
                android:paddingHorizontal="4dp"
                app:layout_constraintEnd_toEndOf="@+id/ad_call_to_action"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/ic_down_array_ad"
                android:background="@color/transparent"/>


            <TextView
                android:id="@+id/textView"
                style="@style/AppTheme.AdAttribution"
                app:layout_constraintBottom_toBottomOf="@+id/report_button"
                app:layout_constraintEnd_toStartOf="@+id/report_button"
                app:layout_constraintTop_toTopOf="@id/report_button" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/info_button"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="bottom|end"/>
    </androidx.cardview.widget.CardView>
</FrameLayout>