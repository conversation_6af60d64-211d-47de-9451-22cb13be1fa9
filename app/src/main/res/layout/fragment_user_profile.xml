<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.duaag.android.home.fragments.UserProfileFragment.Handles" />

        <import type="com.duaag.android.utils.AgeUtils" />

        <import type="android.view.View" />

        <variable
            name="handle"
            type="Handles" />


        <variable
            name="userModel"
            type="com.duaag.android.home.models.RecommendedUserModel" />

        <variable
            name="shownPicture"
            type="String" />
        <variable
            name="shownPictureBlurred"
            type="Boolean" />
        <variable
            name="tabPosition"
            type="Integer" />

        <variable
            name="isMatchUser"
            type="Boolean" />

        <variable
            name="isMyProfile"
            type="Boolean" />

        <variable
            name="myUserModel"
            type="com.duaag.android.base.models.UserModel" />
    </data>

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/motionLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layoutDirection="ltr"
        app:layoutDescription="@xml/fragment_user_profile_scene"
        tools:context=".home.fragments.UserProfileFragment">

        <FrameLayout
            android:id="@+id/wrapper"
            android:layout_width="match_parent"
            android:elevation="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_height="wrap_content">

            <com.duaag.android.crosspath.presentation.utils.UserInteractionBanner
                android:id="@+id/user_interaction_header"
                android:layout_width="match_parent"
                android:layoutDirection="locale"
                android:layout_height="wrap_content"/>
        </FrameLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/top_side"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/bottom_sheet_rounded"
            app:layout_constraintBottom_toTopOf="@id/image_bottom_fill"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/image_bottom_fill"
            android:layout_width="wrap_content"
            android:layout_height="10dp"
            app:layout_constraintBottom_toBottomOf="@id/profile_image_container" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/profile_image_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/black_gradient_inverse"
            app:layout_constraintDimensionRatio="1:1.2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <ImageView
                android:id="@+id/img_user"
                loadImageTopRoundedCorners="@{shownPicture}"
                blurred="@{shownPictureBlurred}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:elevation="0dp"
                tools:scaleType="centerCrop"
                tools:srcCompat="@drawable/profile_image_test" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/premium_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:layout_marginStart="16dp"
                android:visibility="gone"
                android:src="@drawable/premium_tag"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/img_user"/>
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_lock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_lock"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/img_user"
                android:layout_marginBottom="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:visibility="visible" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:visibility="@{userModel.profile.pictures.empty ? View.GONE : View.VISIBLE}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabBackground="@drawable/tab_selector_color"
                app:tabIndicator="@drawable/custume_tab_indicator"
                app:tabIndicatorColor="@color/white"
                app:tabIndicatorFullWidth="false"
                app:tabIndicatorHeight="4dp"
                app:tabPaddingEnd="20dp"
                app:tabPaddingStart="20dp" />

            <LinearLayout
                android:id="@+id/linearLayout9"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{userModel.profile.pictures.empty ? View.GONE : View.VISIBLE}">

                <View
                    android:id="@+id/previewView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:onClick="@{()->handle.onPreviewClick()}" />

                <View
                    android:id="@+id/nextView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:onClick="@{()->handle.onNextViewClick()}" />

            </LinearLayout>

            <ImageButton
                android:id="@+id/close_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="26dp"
                android:background="@drawable/background_light_black"
                android:elevation="0dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_keyboard_arrow_down_white_24dp" />

            <ImageButton
                android:id="@+id/report_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginTop="26dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/background_light_black"
                android:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_more_horizontal"/>

            <ProgressBar
                android:id="@+id/progressBar_user"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="5dp"
                android:indeterminate="true"
                android:theme="@style/ProgressBarTheme"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <FrameLayout
                android:id="@+id/add_photo_blocker"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintStart_toStartOf="@id/img_user"
                app:layout_constraintEnd_toEndOf="@id/img_user"
                app:layout_constraintTop_toTopOf="@id/img_user"
                app:layout_constraintBottom_toBottomOf="@id/img_user"
                android:background="#33000000"
             >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="52dp"
                    android:layout_gravity="center">

                    <ImageView
                        android:id="@+id/icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/eye_off"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <TextView
                        android:id="@+id/blocked_photo_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/icon"
                        android:textColor="@color/gray_50"
                        android:textSize="@dimen/text_size_200"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:layout_marginTop="16dp"
                        android:textAlignment="center"
                        android:text="@string/add_photos_unlock_others" />

                    <com.duaag.android.views.DuaButton
                        android:id="@+id/add_photo_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/blocked_photo_text"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginTop="32dp"
                        android:paddingHorizontal="12dp"
                        android:textSize="@dimen/text_size_200"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:onClick="@{()->handle.onAddPhotoClicked()}"
                        android:text="@string/add_photos_button" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.duaag.android.home.TouchFrameLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            android:background="@drawable/bottom_sheet_rounded"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/image_bottom_fill">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nested_scrollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingBottom="155dp">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/thumb"
                        android:layout_width="40dp"
                        android:layout_height="4dp"
                        android:layout_marginTop="2dp"
                        android:layout_gravity="center"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginHorizontal="16dp"
                        app:layout_constraintTop_toTopOf="parent"
                        android:background="@drawable/bottom_sheet_thumb" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_name_and_age"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="12dp"
                        app:layout_constraintTop_toBottomOf="@id/thumb"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_name"
                            android:text="@{userModel.firstName + `, ` + userModel.age}"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textColor="@color/title_primary"
                            android:fontFamily="@font/tt_norms_pro_bold"
                            style="@style/text_style_500"
                            app:layout_constraintEnd_toStartOf="@id/badge"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintWidth="wrap_content_constrained"
                            tools:text="Esa, 24" />
                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/user_activity"
                            android:layout_width="wrap_content"
                            android:visibility="gone"
                            tools:visibility="visible"
                            android:layout_marginStart="4dp"
                            app:layout_constraintBottom_toBottomOf="@id/txt_name"
                            app:layout_constraintEnd_toStartOf="@id/badge"
                            android:layout_marginTop="4dp"
                            app:layout_constraintTop_toTopOf="@id/txt_name"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@id/txt_name"
                            tools:srcCompat="@drawable/activity_status_now_illustration"
                            app:layout_constraintWidth="wrap_content_constrained"
                            android:layout_height="wrap_content"/>
                        <include
                            android:id="@+id/badge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="4dp"
                            app:layout_constraintWidth="wrap_content_constrained"
                            app:layout_constraintBottom_toBottomOf="@+id/txt_name"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@+id/user_activity"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/txt_name"
                            layout="@layout/featured_badge_layout"/>


                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_km_away"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        app:layout_constraintTop_toBottomOf="@id/layout_name_and_age"
                        android:layout_marginTop="2dp"
                        app:visibility='@{(userModel.profile.distance != null &amp;&amp; !userModel.profile.distance.equals("")) &amp;&amp; !isMyProfile}'>

                        <ImageView
                            android:id="@+id/location"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_location"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/txt_km_away"
                            actualAddress="@{userModel.profile.actualAddress}"
                            distance="@{userModel.distanceToFloat()}"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:fontFamily="@font/tt_norms_pro_normal"
                            android:src="@drawable/ic_location"
                            android:textAlignment="textStart"
                            android:textColor="@color/description_primary"
                            style="@style/text_style_200"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/fly_mode"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@id/location"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s." />

                        <ImageView
                            android:id="@+id/fly_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:src="@drawable/ic_flight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/txt_km_away"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <include
                        android:id="@+id/unlock_profile_layout"
                        layout="@layout/unlock_profile_layout"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        android:layout_marginTop="60dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_km_away"
                        app:layout_constraintVertical_bias="0.0" />
                    <FrameLayout
                        android:id="@+id/details_wrapper"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:minHeight="300dp"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_km_away"
>
                        <LinearLayout
                            android:id="@+id/deatils"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:minHeight="300dp"
                            android:orientation="vertical"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/layout_km_away">





                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/main_tags"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="2dp" />

                            <TextView
                                android:id="@+id/my_information"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="24dp"
                                android:fontFamily="@font/tt_norms_pro_medium"
                                style="@style/text_style_200"
                                android:gravity="center_vertical"
                                android:text="@string/my_information"
                                android:textColor="@color/title_primary"
                                />

                            <LinearLayout
                                android:id="@+id/layout_job"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="8dp"
                                android:orientation="horizontal"
                                app:visibility='@{userModel.profile.jobs.toString() != null &amp;&amp; !userModel.profile.jobs.toString().equals("")}'>

                                <ImageView
                                    android:id="@+id/job_icon"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_briefcase_profile_details" />

                                <TextView
                                    android:id="@+id/txt_job"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"
                                    android:text="@{userModel.profile.jobs ?? ``}"
                                    tools:text="33 kilometers away" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layout_education"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="8dp"
                                android:orientation="horizontal"
                                app:visibility='@{userModel.profile.educations.toString() != null &amp;&amp; !userModel.profile.educations.toString().equals("")}'>

                                <ImageView
                                    android:id="@+id/education_icon"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_education_icon_profile_details" />

                                <TextView
                                    android:id="@+id/txt_education"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:text="@{userModel.profile.educations ?? ``}"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"
                                    tools:text="33 kilometers away" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layout_hobby"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="8dp"
                                android:layout_below="@id/layout_education"
                                android:orientation="horizontal"
                                app:visibility='@{userModel.profile.hobbies.toString() != null &amp;&amp; !userModel.profile.hobbies.toString().equals("")}'>

                                <ImageView
                                    android:id="@+id/hobby_icon"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_hobby" />

                                <TextView
                                    android:id="@+id/txt_hobby"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:text="@{userModel.profile.hobbies ?? ``}"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"
                                    tools:text="33 kilometers away" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layout_hometown"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="8dp"
                                android:layout_below="@id/layout_hobby"
                                android:orientation="horizontal">

                                <ImageView
                                    android:id="@+id/hometown_icon"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_location_icon" />

                                <TextView
                                    android:id="@+id/txt_hometown"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"
                                    tools:text="33 kilometers away" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layout_language"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="8dp"
                                android:layout_below="@id/layout_hometown"
                                android:orientation="horizontal">

                                <ImageView
                                    android:id="@+id/language_icon"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_language_profile_details" />

                                <TextView
                                    android:id="@+id/txt_language"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"
                                    tools:text="33 kilometers away" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layout_description"
                                hideIfEmpty="@{userModel.profile.description}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/layout_language"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="24dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/txt_description"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{userModel.profile.description ?? ``}"
                                    android:fontFamily="@font/tt_norms_pro_normal"
                                    style="@style/text_style_200"
                                    android:textColor="@color/title_primary"/>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/additional_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="24dp"
                                android:gravity="center_vertical"
                                android:layout_below="@id/layout_description"
                                android:text="@string/additional_information"
                                android:fontFamily="@font/tt_norms_pro_medium"
                                style="@style/text_style_200"
                                android:textColor="@color/title_primary"
                                android:visibility="gone"
                                tools:visibility="visible"/>


                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/additional_tags"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/additional_text"
                                android:layout_marginHorizontal="16dp"
                                android:layout_marginTop="12dp" />

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_instagram"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:tag="children"
                                android:visibility="gone"
                                android:layout_below="@id/additional_tags"
                                tools:visibility="visible"
                                app:cardBackgroundColor="@android:color/transparent"
                                app:cardElevation="0dp"
                                app:rippleColor="@color/gray_50">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <View
                                        android:id="@+id/instagram_view"
                                        android:layout_width="0dp"
                                        android:layout_height="1dp"
                                        android:layout_marginHorizontal="16dp"
                                        android:background="#E9E9ED"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toEndOf="@+id/add_instagram"
                                        app:layout_constraintStart_toStartOf="@+id/instagram_title" />

                                    <ImageView
                                        android:id="@+id/imageView_instagram"
                                        android:layout_width="18dp"
                                        android:layout_height="18dp"
                                        android:layout_marginHorizontal="16dp"
                                        android:layout_marginTop="24dp"
                                        android:layout_marginBottom="4dp"
                                        android:scaleType="centerInside"
                                        android:src="@drawable/ic_instagram"
                                        app:layout_constraintBottom_toTopOf="@+id/instagram_media"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />

                                    <TextView
                                        android:id="@+id/instagram_title"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="12dp"
                                        android:singleLine="true"
                                        android:text="@string/instagram"
                                        android:fontFamily="@font/tt_norms_pro_medium"
                                        style="@style/text_style_200"
                                        android:textColor="@color/title_primary"
                                        app:layout_constraintBottom_toBottomOf="@+id/imageView_instagram"
                                        app:layout_constraintStart_toEndOf="@+id/imageView_instagram"
                                        app:layout_constraintTop_toTopOf="@+id/imageView_instagram" />

                                    <TextView
                                        android:id="@+id/add_instagram"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="16dp"
                                        android:drawablePadding="8dp"
                                        android:ellipsize="end"
                                        android:maxLines="1"
                                        android:text="0 photos"
                                        android:textAlignment="viewEnd"
                                        android:fontFamily="@font/tt_norms_pro_medium"
                                        style="@style/text_style_100"
                                        android:textColor="@color/title_primary"
                                        app:layout_constraintBottom_toBottomOf="@+id/instagram_title"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintTop_toTopOf="@+id/instagram_title" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/instagram_media"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingStart="4dp"
                                        android:clipToPadding="false"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintTop_toBottomOf="@id/imageView_instagram" />


                                </androidx.constraintlayout.widget.ConstraintLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <Space
                                android:id="@+id/space1"
                                android:layout_below="@id/card_instagram"
                                android:layout_width="match_parent"
                                android:layout_height="0dp" />

                            <include
                                android:id="@+id/cross_map_section"
                                layout="@layout/cross_map_user_layout" />
                            <com.duaag.android.crosspath.presentation.utils.UserInteractionBanner
                                android:id="@+id/user_interaction_footer"
                                android:layout_width="match_parent"
                                android:layoutDirection="locale"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </FrameLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.core.widget.NestedScrollView>

        </com.duaag.android.home.TouchFrameLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/user_profile_background"
            app:layout_constraintBottom_toBottomOf="parent">

            <include
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/featured_like_button"
                android:background="@drawable/user_profile_background"
                app:layout_constraintBottom_toBottomOf="parent"
                layout="@layout/featured_like_user_details_button_layout"/>

            <LinearLayout
                android:id="@+id/buttons_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/dislike_button"
                    android:layout_width="@dimen/home_screen_button_dimension"
                    android:layout_height="@dimen/home_screen_button_dimension"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="20dp"
                    app:cardBackgroundColor="@color/border"
                    app:cardElevation="0dp"
                    app:rippleColor="@color/gray_50"
                    app:shapeAppearance="@style/MaterialCardViewCircleShape">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/dislike_vector"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingTop="6dp"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_dislike_v2"
                        app:tint="@color/title_primary" />
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/super_like_button"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="20dp"
                    app:cardBackgroundColor="@android:color/transparent"
                    app:cardElevation="0dp"
                    app:rippleColor="@color/gray_50"
                    app:shapeAppearance="@style/MaterialCardViewCircleShape">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/home_insta_background">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/instachat_vector"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerInside"
                            android:src="@drawable/ic_instachat_small"
                            android:tint="@color/gray_50" />

                    </FrameLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/like_button"
                    android:layout_width="@dimen/home_screen_button_dimension"
                    android:layout_height="@dimen/home_screen_button_dimension"
                    app:cardBackgroundColor="@android:color/transparent"
                    app:cardElevation="0dp"
                    app:rippleColor="@color/gray_50"
                    app:shapeAppearance="@style/MaterialCardViewCircleShape">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/home_like_background"
                        android:paddingTop="6dp">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/like_vector"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerInside"
                            android:src="@drawable/ic_home_heart" />
                    </FrameLayout>

                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="20dp" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline17"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="20dp" />

    </androidx.constraintlayout.motion.widget.MotionLayout>
</layout>