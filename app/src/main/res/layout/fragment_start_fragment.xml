<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="false"
    tools:context=".login.StartActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_container"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <Button
            android:id="@+id/reset_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Reset"
            android:visibility="gone"
            tools:visibility="visible"
            android:textAllCaps="false"
            android:background="@color/transparent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/language_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/language"
            android:visibility="gone"
            tools:visibility="visible"
            android:textAllCaps="false"
            android:background="@color/transparent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/brand_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="48dp"
            app:layout_constraintBottom_toTopOf="@+id/illustration"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/dua_logo"
                android:layout_width="222dp"
                android:layout_height="50dp"
                android:src="@drawable/app_brand_logo_colored"
                app:layout_constraintDimensionRatio="25:8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/find_people_origin_text"
                style="@style/text_style_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/onboarding_slogan"
                android:textAlignment="center"
                android:textColor="@color/foundation_title_secondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dua_logo" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/illustration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/start_couple_illustration"
            android:layout_marginBottom="32dp"
            app:layout_constraintBottom_toTopOf="@id/container_barrier"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.95" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_margin="8dp"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/illustration"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/brand_section"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/container_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="top"
            app:constraint_referenced_ids="no_account_container,existing_account_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/no_account_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            tools:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.duaag.android.views.DuaButton
                android:id="@+id/create_account_btn"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_gravity="center"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="20dp"
                android:clickable="true"
                android:focusable="true"
                android:stateListAnimator="@null"
                android:text="@string/sign_up"
                app:buttonType="Primary"
                app:layout_constraintBottom_toTopOf="@+id/login_btn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:rippleColor="@color/white" />

            <com.duaag.android.views.DuaButton
                android:id="@+id/login_btn"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_gravity="center"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp"
                android:backgroundTint="#333333"
                android:clickable="true"
                android:focusable="true"
                android:stateListAnimator="@null"
                android:text="@string/sign_in"
                app:buttonType="Primary"
                app:layout_constraintBottom_toTopOf="@+id/agreement"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:rippleColor="@color/white" />


            <TextView
                android:id="@+id/agreement"
                style="@style/text_style_100"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:gravity="center"
                android:text="@string/onboarding_info_full_an"
                android:textColor="@color/foundation_description_secondary"
                android:contentDescription="privacy_policy_agreement"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/existing_account_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:visibility="visible">

            <TextView
                android:id="@+id/login_title"
                style="@style/text_style_500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:text="@string/onboarding_lastlogin_title"
                android:textAlignment="center"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toTopOf="@id/login_description"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/login_description"
                style="@style/text_style_200"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp"
                android:layout_marginBottom="40dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/onboarding_lastlogin_desc"
                android:textAlignment="center"
                android:textColor="@color/description_primary"
                app:layout_constraintBottom_toTopOf="@id/auth_provider_btn" />

            <Button
                android:id="@+id/auth_provider_btn"
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_gravity="center"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp"
                android:background="@drawable/rounded_button_stroke"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:paddingHorizontal="16dp"
                android:stateListAnimator="@null"
                android:textAlignment="center"
                android:textAllCaps="false"
                app:layout_constraintBottom_toTopOf="@id/deactivated_description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:rippleColor="@color/white"
                tools:drawableStart="@drawable/ic_google_icon"
                tools:text="@string/continue_with_google" />


            <TextView
                android:id="@+id/deactivated_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="40dp"
                android:layout_marginBottom="24dp"
                android:drawableLeft="@drawable/info_circle_small"
                android:drawablePadding="16dp"
                style="@style/text_style_100"
                android:textColor="@color/foundation_description_primary"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/onboarding_deactivate_status"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.facebook.login.widget.LoginButton
            android:id="@+id/facebook_login_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/no_internet_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/no_internet_illustration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/no_internet_illustration"
            app:layout_constraintBottom_toTopOf="@+id/no_internet_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/no_internet_title"
            style="@style/text_style_600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/onboarding_nointernet_title"
            android:textAlignment="center"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toTopOf="@+id/no_internet_description"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/no_internet_illustration" />

        <TextView
            android:id="@+id/no_internet_description"
            style="@style/text_style_200"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/onboarding_nointernet_desc"
            android:textAlignment="center"
            android:textColor="@color/description_primary"
            app:layout_constraintBottom_toTopOf="@+id/try_again_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/no_internet_title" />


        <com.duaag.android.views.DuaButton
            android:id="@+id/try_again_btn"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:layout_marginStart="40dp"
            android:layout_marginTop="42dp"
            android:layout_marginEnd="40dp"
            android:clickable="true"
            android:focusable="true"
            android:stateListAnimator="@null"
            android:text="@string/try_again"
            app:buttonType="Primary"
            android:fontFamily="@font/tt_norms_pro_medium"
            style="@style/text_style_200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/no_internet_description"
            app:layout_constraintWidth_max="300dp"
            app:rippleColor="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/loading_account"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginBottom="64dp"
        android:theme="@style/ProgressBarTheme"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>