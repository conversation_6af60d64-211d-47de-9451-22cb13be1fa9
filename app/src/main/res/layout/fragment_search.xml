<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".profile_new.editprofile.SearchFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/frameLayout2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@android:color/transparent"
            android:elevation="0dp"
            android:backgroundTint="@color/white">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:background="@android:color/transparent"
                android:layout_height="wrap_content">

                <ImageButton
                    android:id="@+id/back_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="?attr/actionBarItemBackground"
                    android:src="@drawable/ic_pressbackbttn"
                    app:layout_constraintBottom_toBottomOf="@+id/searchView"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/searchView" />

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/searchView"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="40dp"
                    android:background="@android:color/transparent"
                    android:layout_marginStart="12dp"
                    app:iconifiedByDefault="false"
                    app:searchIcon="@drawable/ic_search_vector_light"
                    style="@style/text_style_200"
                    android:fontFamily="@font/tt_norms_pro_bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/back_button"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_scrollFlags="scroll|enterAlways|snap"
                    app:queryBackground="@android:color/transparent"
                    app:queryHint="Search" />
            <View
                android:id="@+id/vie"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:background="@color/border"
                app:layout_constraintEnd_toEndOf="@+id/searchView"
                app:layout_constraintStart_toStartOf="@+id/searchView"
                app:layout_constraintTop_toBottomOf="@id/searchView" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/searchResults"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>