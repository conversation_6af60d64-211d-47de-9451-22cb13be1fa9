<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/add_profile_info_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/filter_background"
    android:paddingHorizontal="20dp"
    android:paddingVertical="32dp"
    tools:showIn="@layout/fragment_filter_bottomsheet">

    <com.duaag.android.views.DuaButton
        android:id="@+id/add_info_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:stateListAnimator="@null"
        android:text="@string/add_this_info_label"
        app:buttonType="PrimaryWithState"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/add_info_desc"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/add_info_filter_caption"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/add_info_btn" />
</androidx.constraintlayout.widget.ConstraintLayout>