<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.recyclerview.widget.RecyclerView.Adapter" />

        <import type="com.duaag.android.utils.EqualSpacingItemDecoration" />

        <import type="android.view.View" />

        <import type="com.duaag.android.chat.model.MessageModel" />

        <import type="com.duaag.android.chat.viewmodel.ConversationViewModel" />

        <variable
            name="badg2Status"
            type="com.duaag.android.settings.fragments.Badge2Status" />

        <variable
            name="adapter"
            type="Adapter" />

        <variable
            name="viewModel"
            type="ConversationViewModel" />

        <variable
            name="view"
            type="View" />

        <variable
            name="itemDecoration"
            type="EqualSpacingItemDecoration" />

        <variable
            name="handle"
            type="com.duaag.android.chat.fragments.ConversationFragment.Handlers" />

        <variable
            name="notificationsHandlers"
            type="com.duaag.android.chat.fragments.ConversationFragment.Handlers.NotificationsHandlers" />

        <variable
            name="notificationOnDescription"
            type="String" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layoutDirection="ltr"
        tools:context=".chat.fragments.ConversationFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:toolbarId="@+id/toolbar">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/toolbar1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible">

                <ImageButton
                    android:id="@+id/imageButton2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/transparent"
                    android:onClick="@{()->handle.backClick()}"
                    android:src="@drawable/ic_left_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/toolbar_image1"
                    tools:src="@drawable/test"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:onClick="@{()-> handle.onProfileClick(viewModel.conversationModel)}"
                    app:imageUrlCircle="@{viewModel.conversationModel.thumbnailUrl ?? viewModel.conversationModel.pictureUrl}"
                    app:layout_constraintBottom_toBottomOf="@+id/imageButton2"
                    app:layout_constraintStart_toEndOf="@+id/imageButton2"
                    app:layout_constraintTop_toTopOf="@+id/imageButton2" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="8dp"
                    android:gravity="center"
                    android:onClick="@{()-> handle.onProfileClick(viewModel.conversationModel)}"
                    android:orientation="horizontal"
                    android:weightSum="5"
                    app:layout_constraintBottom_toBottomOf="@+id/toolbar_image1"
                    app:layout_constraintEnd_toStartOf="@+id/call_frameLayout"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/toolbar_image1"
                    app:layout_constraintTop_toTopOf="@+id/toolbar_image1"
                    app:layout_constraintWidth="wrap_content_constrained">

                    <TextView
                        android:id="@+id/toolbar_title1"
                        style="@style/text_style_300"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="5"
                        android:ellipsize="marquee"
                        android:fontFamily="@font/tt_norms_pro_demibold"
                        android:maxWidth="150dp"
                        android:singleLine="true"
                        android:text="@{viewModel.conversationModel.name}"
                        android:textColor="@color/title_primary"
                        tools:text="Qendrim CaKaj" />


                    <ImageView
                        android:id="@+id/badge1_img1"
                        android:layout_width="18dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="2dp"
                        android:layout_weight="1"
                        android:src="@drawable/ic_image_verification"
                        app:visibility="@{(viewModel.conversationModel.badge2.equals(badg2Status.APPROVED.status)) ? View.VISIBLE : View.GONE}" />

                    <ImageView
                        android:id="@+id/rmod_icon"
                        android:layout_width="18dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:src="@drawable/rmod_icon"
                        app:visibility="@{viewModel.conversationModel.isRMOD}" />

                    <ImageView
                        android:id="@+id/imageView29_1"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="2dp"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/ic_form_superlike"
                        app:visibility="@{viewModel.conversationModel.showStar}" />

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="2dp"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/ic_instachat_small_icon"
                        app:visibility="@{viewModel.conversationModel.wasInstachat}" />

                </LinearLayout>

                <ImageButton
                    android:id="@+id/imageButton5"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="@color/transparent"
                    android:onClick="@{()->handle.moreClick()}"
                    android:src="@drawable/ic_more_menu_ic"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <FrameLayout
                    android:id="@+id/video_frameLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@+id/imageButton5"
                    app:layout_constraintEnd_toStartOf="@+id/imageButton5"
                    app:layout_constraintTop_toTopOf="@+id/imageButton5">

                <ImageButton
                    android:id="@+id/allow_video_call"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:onClick="@{()->handle.allowVideoCallClick()}"
                    android:alpha="0"
                    android:elevation="1dp"
                    android:background="@color/transparent"
                    android:src="@drawable/ic_video_allowed"/>

                    <ImageButton
                        android:id="@+id/disallow_video_call"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:onClick="@{()->handle.allowVideoCallClick()}"
                        android:background="@color/transparent"
                        android:src="@drawable/ic_video_disallowed"
                        />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/call_frameLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/video_frameLayout"
                    app:layout_constraintEnd_toStartOf="@+id/video_frameLayout"
                    app:layout_constraintTop_toTopOf="@+id/video_frameLayout">


                    <ImageButton
                        android:id="@+id/allow_phone_call"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:alpha="0"
                        android:elevation="1dp"
                        android:onClick="@{()->handle.allowPhoneCallClick()}"
                        android:src="@drawable/ic_call_allowed"/>


                    <ImageButton
                        android:id="@+id/disallow_phone_call"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:onClick="@{()->handle.allowPhoneCallClick()}"
                        android:src="@drawable/ic_phone_icon_gray"/>
                </FrameLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/border"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />

                <View
                    android:id="@+id/toolbar_overlay"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/toolbar_image1"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:background="@color/background"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    android:alpha="0.7" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <include
            android:id="@+id/included_layout"
            layout="@layout/turn_notif_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:description="@{notificationOnDescription}"
            app:handlers="@{notificationsHandlers}"
            app:layout_constraintBottom_toTopOf="@id/conversation_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appBar" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/conversation_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/rmod_other_user_should_respond"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/included_layout">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="52dp"
                android:overScrollMode="never"
                app:setAdapterLinearRevers="@{adapter}"
                tools:itemCount="5"
                tools:listitem="@layout/item_message_received"
                tools:visibility="visible" />


            <!--INSTACHAT EMPTY VIEW-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/instachat_empty_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                app:layout_anchor="@+id/recyclerView"
                app:layout_anchorGravity="center"
                tools:visibility="gone">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline14"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.2" />

                <com.duaag.android.views.CircularProgressView
                    android:id="@+id/time_left"
                    android:layout_width="74dp"
                    android:layout_height="74dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/guideline14" />

                <TextView
                    android:id="@+id/hours_left_txt"
                    style="@style/text_style_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_demibold"
                    android:textColor="@color/title_primary"
                    app:layout_constraintBottom_toBottomOf="@id/time_left"
                    app:layout_constraintEnd_toEndOf="@id/time_left"
                    app:layout_constraintStart_toStartOf="@id/time_left"
                    app:layout_constraintTop_toTopOf="@id/time_left"
                    tools:text="36h" />

                <TextView
                    android:id="@+id/instachat_description_txt"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="24dp"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textAlignment="center"
                    android:textColor="@color/title_secondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/time_left"
                    tools:text="@string/only_x_can_open_the_conversation" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--CHAT EMPTY VIEW-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/empty_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:visibility="@{viewModel.conversationModel.fromMatchView ? View.VISIBLE : View.GONE}"
                app:layout_anchor="@+id/recyclerView"
                app:layout_anchorGravity="center"
                tools:visibility="gone">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    app:layout_constraintBottom_toTopOf="@+id/guideline11"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">


                    <ImageView
                        android:id="@+id/match_type"
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:elevation="4dp"
                        android:scaleType="centerInside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:source="@{viewModel.conversationModel.showStar}"
                        tools:background="@drawable/like_background_gradient"
                        tools:src="@drawable/ic_for_you_icon"
                        tools:targetApi="LOLLIPOP_MR1" />

                    <TextView
                        android:id="@+id/textView21"
                        style="@style/text_style_200"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/new_match_an"
                        android:textColor="@color/title_secondary"
                        app:layout_constraintBottom_toTopOf="@+id/guideline12"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/match_type"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1.0"
                        tools:text="New Match" />


                    <TextView
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:textColor="@color/title_secondary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/match_type"
                        app:layout_constraintTop_toTopOf="@+id/guideline12"
                        app:layout_constraintVertical_bias="0.0"
                        app:matchedSince="@{viewModel.conversationModel.lastMessageTime}"
                        tools:text="Matched since: 13-06-2020" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline12"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintGuide_percent="0.5" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.5" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="148dp"
                    android:layout_height="148dp"
                    android:background="@drawable/ic_match_background"
                    android:elevation="5dp"
                    app:cardCornerRadius="74dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/guideline11">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/emoji_container"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="45sp"
                            app:layout_constraintBottom_toTopOf="@+id/guideline13"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="1.0"
                            app:setEmoji="@{viewModel.conversationModel.showStar}" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline13"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            app:layout_constraintGuide_percent="0.5" />

                        <TextView
                            android:id="@+id/textView26"
                            style="@style/text_style_100"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/tt_norms_pro_normal"
                            android:text="@string/say_something_interesting_an"
                            android:textAlignment="center"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/guideline13"
                            app:layout_constraintVertical_bias="0.0" />


                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--VERIFY PROFILE-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/verify_profile_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/background_gradient_top"
                android:visibility="gone"
                tools:visibility="gone">

                <include
                    android:id="@+id/verify_layout"
                    layout="@layout/verify_your_profile_dialog"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <!--RMOD CONVERSATION-->
        <include
            android:id="@+id/rmod_other_user_should_respond"
            layout="@layout/rmod_other_user_should_respond_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/bottom_cl" />

        <!--UNMATCHED CONTAINER-->
        <include
            android:id="@+id/unmatched_container"
            layout="@layout/unmatched_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/bottom_cl" />

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/loading_spinner"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/appBar"
            app:layout_constraintTop_toBottomOf="@+id/appBar"
            tools:visibility="visible" />

       <!-- <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="24dp"
            android:animateLayoutChanges="true"
            app:layout_constraintBottom_toBottomOf="@+id/snackbar_place"
            app:layout_constraintEnd_toEndOf="parent">-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_marginEnd="1dp"
            android:layout_marginBottom="16dp"
            android:animateLayoutChanges="true"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toTopOf="@+id/bottom_cl"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_cart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_margin="2dp"
                android:backgroundTint="@color/border"
                android:elevation="0dp"
                android:src="@drawable/ic_scroll_down_arrow"
                android:visibility="gone"
                app:backgroundTint="@color/border"
                app:borderWidth="1dp"
                app:elevation="0dp"
                app:fabSize="mini"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/space"
                android:layout_width="1dp"
                android:layout_height="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.33"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.18" />

            <TextView
                style="@style/text_style_50"
                android:id="@+id/text_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/fab_cart"
                android:background="@drawable/conversation_badge_indicaror"
                android:elevation="0dp"
                android:gravity="center"
                android:visibility="gone"
                android:fontFamily="@font/tt_norms_pro_demibold"
                android:textColor="@color/gray_50"
                app:layout_constraintBottom_toBottomOf="@id/space"
                app:layout_constraintEnd_toEndOf="@id/space"
                app:layout_constraintStart_toStartOf="@+id/space"
                app:layout_constraintTop_toTopOf="@id/space"
                tools:text="9+"

                />
        </androidx.constraintlayout.widget.ConstraintLayout>

     <!--   </RelativeLayout>-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/typing_indicator_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/rounded_corners_with_bordr_shape_40dp"
            android:padding="12dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@id/bottom_cl">
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/typing_indicator_lottie"
                android:layout_width="28dp"
                android:layout_height="17dp"
                app:lottie_rawRes="@raw/typing_indicator_lottie"
                app:lottie_repeatMode="restart"
                app:lottie_loop="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
          />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom_cl"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/rmodInputContiner"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_edit_text_holder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="6dp"
                android:background="@drawable/rounded_corners_with_border_shape"
                app:layout_constraintEnd_toStartOf="@id/send_message_button"
               app:layout_constraintStart_toEndOf="@id/send_gif_button"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/text_style_200"
                    android:id="@+id/message_editText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/send_a_message"
                    android:imeOptions="actionDone"
                    android:inputType="textCapSentences|textMultiLine"
                    android:maxLength="999"
                    android:maxLines="3"
                    android:paddingStart="20dp"
                    android:paddingTop="10dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="8dp"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:textColor="@color/title_primary"
                    android:textColorHint="@color/description_secondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/send_message_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@android:color/transparent"
                android:onClick="@{()-> handle.sendClick()}"
                android:src="@drawable/ic_send_disable"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/send_gif_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/tt_norms_pro_bold"
                android:text="gif"
                android:textAllCaps="true"
                android:textColor="@color/background"
                android:textSize="8sp"
                android:background="@drawable/giphy_select_background"
                android:onClick="@{()-> handle.onGifButtonClicked()}"
                app:layout_constraintBottom_toBottomOf="@+id/layout_edit_text_holder"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/layout_edit_text_holder">

            </Button>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rmodInputContiner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/background_rmod_input_shape"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="@string/send_a_message"
                android:maxLength="999"
                android:maxLines="3"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="8dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/description_secondary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/premium_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:src="@drawable/ic_premium_tag"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>