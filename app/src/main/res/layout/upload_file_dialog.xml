<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp">

    <TextView
        android:id="@+id/upload_text"
        style="@style/text_style_100"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/uploading_photos"
        android:textAlignment="viewStart"
        android:textColor="@color/title_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/progress_percentage"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="0%"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toBottomOf="@+id/progress_bar"
        app:layout_constraintEnd_toEndOf="@+id/progress_bar"
        app:layout_constraintStart_toStartOf="@+id/progress_bar"
        app:layout_constraintTop_toTopOf="@+id/progress_bar"
        tools:text="29%" />

    <com.duaag.android.views.CircularProgressView
        android:id="@+id/progress_bar"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_margin="24dp"
        android:rotationY="180"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/upload_text" />

</androidx.constraintlayout.widget.ConstraintLayout>

