<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="matchesItem"
            type="com.duaag.android.chat.model.UserMatchesModel" />

        <variable
            name="clickListener"
            type="com.duaag.android.chat.adapters.MatchesClickListener" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="64dp"
        android:layout_height="90dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:onClick="@{()->clickListener.onClick(matchesItem)}">

        <ImageView
            android:id="@+id/imageView9"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:adjustViewBounds="true"
            app:imageUrlCircleCrop="@{matchesItem.user.profile.thumbnailUrl ?? matchesItem.user.profile.pictureUrl}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@drawable/first_page_first_cover" />

        <ImageView
            android:id="@+id/imageView28"
            android:layout_width="16dp"
            android:layout_height="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView9"
            app:layout_constraintEnd_toEndOf="@+id/imageView9"
            app:srcCompat="@drawable/ic_new_match_budge_border"
            app:visibility="@{matchesItem.showSeen}" />

        <TableLayout
            android:id="@+id/table_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:gravity="center_horizontal"
            android:shrinkColumns="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView9">

            <TableRow
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center">

                <TextView
                    android:id="@+id/matches_name_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_primary"
                    style="@style/text_style_100"
                    android:singleLine="true"
                    android:text="@{matchesItem.user.firstName}"
                    android:textAlignment="center"
                    tools:text="QekajKAJSka" />

                <ImageView
                    android:id="@+id/badge1_img"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="2dp"
                    android:layout_marginEnd="2dp"
                    tools:srcCompat="@drawable/ic_image_verification"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/imageView27"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="2dp"
                    android:layout_marginEnd="2dp"
                    app:srcCompat="@drawable/ic_form_superlike"
                    app:visibility="@{matchesItem.showType}"
                    tools:visibility="gone" />
            </TableRow>
        </TableLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>