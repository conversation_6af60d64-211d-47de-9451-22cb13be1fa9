<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/image"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/text_container"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0"
        tools:srcCompat="@drawable/ic_icon_boost_background" />

    <LinearLayout
        android:id="@+id/text_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/image"
        app:layout_constraintTop_toTopOf="@id/image">


        <TextView
            android:id="@+id/title"
            style="@style/text_style_300"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="30dp"
            android:includeFontPadding="false"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_secondary"
            tools:text="Boost" />

        <TextView
            android:id="@+id/description"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:includeFontPadding="false"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/description_primary"
            tools:text="1 boost per month" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>