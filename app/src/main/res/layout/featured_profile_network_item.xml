<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <ImageView
        android:id="@+id/no_connection_img"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/wifi_illustration"
        app:layout_constraintBottom_toTopOf="@+id/no_connection_title"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="16dp"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.41000003"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/no_connection_title"
        style="@style/text_style_300"
        android:layout_width="0dp"
        android:textAlignment="center"
        android:layout_marginHorizontal="32dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/no_internet_connection"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/no_connection_description"
        app:layout_constraintEnd_toEndOf="@+id/no_connection_img"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/no_connection_img"
        app:layout_constraintTop_toBottomOf="@+id/no_connection_img"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/no_connection_description"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/no_internet_connection_desc"
        android:textAlignment="center"
        android:textColor="@color/description_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/no_connection_title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/no_connection_title"
        app:layout_constraintTop_toBottomOf="@+id/no_connection_title"
        app:layout_constraintVertical_chainStyle="packed" />
</androidx.constraintlayout.widget.ConstraintLayout>