<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/selectableItemBackground"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:cardPreventCornerOverlap="false"
    app:cardUseCompatPadding="true"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/user_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@drawable/dont_let_go_gradient_backgeround">

        <View
            android:id="@+id/background_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="1.5dp"
            android:background="@drawable/card_item_modal_popup_20_dp"
            tools:layout_editor_absoluteX="2dp"
            tools:layout_editor_absoluteY="2dp" />

        <TextView
            android:id="@+id/header"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/premium_expire_caption"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/days_left"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/days_left"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:background="@drawable/background_20dp_radius"
            android:backgroundTint="@color/red_100"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:padding="6dp"
            android:text="@string/days_3_full_caption"
            android:textColor="@color/red_900"
            app:layout_constraintBottom_toBottomOf="@id/header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@id/header"
            app:layout_constraintTop_toTopOf="@id/header" />

        <include
            android:id="@+id/day3AndDay2TypeView"
            layout="@layout/dont_let_go_day3_and_day2_type_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            app:layout_constraintTop_toBottomOf="@id/header" />


        <include
            android:id="@+id/dayOneTypeView"
            android:visibility="gone"
            layout="@layout/dont_let_go_day_one_view_type"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            app:layout_constraintTop_toBottomOf="@id/header" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
