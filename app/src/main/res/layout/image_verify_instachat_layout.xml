<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:clipToPadding="false"
    android:paddingBottom="24dp">


    <TextView
        android:id="@+id/title_textView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:textAlignment="center"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/verify_your_account_in_order_to_use_instachat" />

    <TextView
        android:id="@+id/textView39"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/with_instachat_you_can"
        android:textAlignment="center"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="@+id/title_textView"
        app:layout_constraintStart_toStartOf="@+id/title_textView"
        app:layout_constraintTop_toBottomOf="@+id/title_textView"  />

    <ImageView
        android:id="@+id/imageView18"
        android:layout_width="0dp"
        android:layout_height="242dp"
        android:layout_marginTop="32dp"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toTopOf="@+id/guideline30"
        app:layout_constraintEnd_toEndOf="@+id/textView39"
        app:layout_constraintStart_toStartOf="@+id/textView39"
        app:layout_constraintTop_toBottomOf="@+id/textView39"
        app:srcCompat="@drawable/image_verify_instachat_img" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/verify_instachat"
        app:buttonType="PrimaryWithState"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_below="@+id/timeStamp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:text="@string/verify"
        android:stateListAnimator="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline30" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.8" />
</androidx.constraintlayout.widget.ConstraintLayout>