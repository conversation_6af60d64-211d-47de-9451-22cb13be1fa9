<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".image_verification.fragments.ImageDeniedFragment">

        <TextView
            android:id="@+id/heading"
            style="@style/text_style_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:textAlignment="center"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/verification_failed_description"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView69"
            style="@style/text_style_200"
            android:layout_width="0dp"
            android:layout_marginHorizontal="32dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/onboarding_photos_error_desc"
            android:textAlignment="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/heading" />

        <ImageView
            android:id="@+id/image"
            android:layout_width="156dp"
            android:layout_height="200dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="40dp"
            android:adjustViewBounds="true"
            android:src="@drawable/guideline_man_template"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView69" />

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="8dp"
            android:src="@drawable/ic_upload_image_warning"
            app:layout_constraintBottom_toBottomOf="@+id/image"
            app:layout_constraintStart_toStartOf="@+id/image" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginBottom="30dp"
            android:background="@drawable/rounded_rect_stroke"
            app:layout_constraintBottom_toTopOf="@+id/replace_photo_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/image"
            app:layout_constraintVertical_bias="1.0">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/photo_guidelines_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/clear_image_container"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@+id/dont_hide_face_container"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/clear_face_image"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center_horizontal"
                        tools:src="@drawable/clear_face_male" />

                    <TextView
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/onboarding_photos_guideline_clearface"
                        android:textAlignment="center"
                        android:textColor="@color/description_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/dont_hide_face_container"
                    android:layout_width="70dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@+id/group_image_container"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/clear_image_container"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/hide_face_image"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center_horizontal"
                        tools:src="@drawable/hide_face_male" />

                    <TextView
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/onboarding_photos_guideline_nohiddenface"
                        android:textAlignment="center"
                        android:textColor="@color/description_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/group_image_container"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="40dp"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/dont_hide_face_container"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/group_photo_image"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center_horizontal"
                        tools:src="@drawable/no_groups_img_male" />

                    <TextView
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/onboarding_photos_guideline_nogroup"
                        android:textAlignment="center"
                        android:textColor="@color/description_primary" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/guidelines_text"
                style="@style/text_style_200"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:padding="8dp"
                android:text="@string/onboarding_photos_guidelines"
                android:textAlignment="center"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/photo_guidelines_container" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.duaag.android.views.DuaButton
            android:id="@+id/replace_photo_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:stateListAnimator="@null"
            android:text="@string/replace_photo_label"
            app:buttonType="PrimaryWithState"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="320dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>