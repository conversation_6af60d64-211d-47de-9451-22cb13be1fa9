<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layoutDirection="ltr"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.duaag.android.change_location.fragments.ConfirmLocationFragment">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />


        <LinearLayout
            android:id="@+id/search_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@id/appBar">

            <ImageButton
                android:id="@+id/close_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/transparent"
                android:padding="4dp"
                android:src="@drawable/ic_chevron_left" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/autocomplete"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:elevation="2dp"
                    android:focusable="false"
                    android:focusableInTouchMode="true"
                    android:labelFor="@id/autocomplete"
                    app:iconifiedByDefault="false"
                    app:queryHint="@string/search_for_cities"
                    app:searchIcon="@null" />

            </FrameLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/search_locations"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/search_container" />

        <TextView
            android:id="@+id/popular_locations_text"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/popular_locations"
            android:textColor="@color/description_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_locations"
            app:layout_goneMarginStart="16dp" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/popular_locations"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:overScrollMode="never"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/popular_locations_text" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
