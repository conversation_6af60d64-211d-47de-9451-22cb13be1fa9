<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".chat.fragments.LikedYouFragment">

    <ProgressBar
        android:id="@+id/loading_spinner"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:progressTint="@color/pink_500"
        android:theme="@style/ProgressBarTheme"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


    <com.duaag.android.chat.views.likedyou.LikedYouEmptyView
        android:id="@+id/constraint_empty"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="2dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refresherLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="0dp"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingBottom="80dp"
            android:splitMotionEvents="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <FrameLayout
        android:id="@+id/info_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/info_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="16dp"
            tools:text="@string/likes_older_than_an"
            android:background="@drawable/rounded_corners_8_dp"
            android:elevation="2dp"
            android:padding="8dp"
            style="@style/text_style_75"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/others" />
    </FrameLayout>

    <com.duaag.android.views.DuaButton
        android:id="@+id/see_who_liked_you_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        app:buttonType="Primary"
        android:text="@string/unblurred_liked_you"
        android:visibility="gone"
        tools:visibility="visible"
        android:stateListAnimator="@null"
        android:layout_marginBottom="12dp"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>