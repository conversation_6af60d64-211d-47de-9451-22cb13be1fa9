<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".chat.fragments.UnblurProfileFragment"
    android:paddingBottom="24dp">

    <TextView
        android:id="@+id/unblur_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/unblur_this_profile_title"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/blurry_user_image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/subtitle"
        tools:src="@drawable/overlay_black" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        style="@style/text_style_100"
        android:text="@string/unblur_this_profile_body"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/unblur_dialog_title" />

    <Button
        android:id="@+id/unblur_profile"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/modal_button_outline_rounded_32dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/unblur_profile_button"
        android:textAllCaps="false"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        android:stateListAnimator="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/blurry_user_image" />

    <TextView
        android:id="@+id/go_premium"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:clickable="true"
        android:focusable="true"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_200"
        android:paddingVertical="16dp"
        android:text="@string/go_premium"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/unblur_profile" />

</androidx.constraintlayout.widget.ConstraintLayout>
