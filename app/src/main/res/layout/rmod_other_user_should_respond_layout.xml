<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/rmod_conversation"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <TextView
        android:id="@+id/rmod_title"
        style="@style/text_style_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/rmod_description"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/should_respond_next_rmod" />

    <TextView
        android:id="@+id/rmod_description"
        style="@style/text_style_200"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="48dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textAlignment="center"
        android:textColor="@color/description_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="@string/message_within_hours_rmod" />

</androidx.constraintlayout.widget.ConstraintLayout>