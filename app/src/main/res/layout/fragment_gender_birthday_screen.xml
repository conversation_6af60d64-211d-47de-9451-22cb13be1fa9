<?xml version="1.0" encoding="utf-8"?>
<ScrollView android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/scrollView"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        tools:context=".signup.fragment.GenderAndBirthdayFragment">
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/txt_choose_gender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="14dp"
            android:text="@string/gender_and_birthday"
            android:fontFamily="@font/tt_norms_pro_demibold"
            android:textColor="@color/title_primary"
            style="@style/text_style_400"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_gender_second"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/txt_gender_second"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="@+id/txt_choose_gender"
            app:layout_constraintStart_toStartOf="@+id/txt_choose_gender"
            app:layout_constraintTop_toBottomOf="@+id/txt_choose_gender" />

        <TextView
            android:id="@+id/textView42"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            style="@style/text_style_100"
            app:layout_constraintStart_toStartOf="@+id/txt_gender_second"
            app:layout_constraintTop_toBottomOf="@+id/txt_gender_second" />

        <TextView
            android:id="@+id/your_birthday"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="@string/when_is_your_birthday"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/description_primary"
            style="@style/text_style_75"
            app:layout_constraintStart_toStartOf="@+id/male_cardView"
            app:layout_constraintTop_toBottomOf="@+id/male_cardView" />




        <com.google.android.material.card.MaterialCardView
            android:id="@+id/male_cardView"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="15dp"
            android:checkable="true"
            android:clickable="true"
            android:focusable="true"
            app:cardBackgroundColor="@color/gender_male_card_selector"
            app:cardElevation="0dp"
            app:cardForegroundColor="@color/transparent"
            app:layout_constraintEnd_toStartOf="@+id/female_cardView"
            app:layout_constraintStart_toStartOf="@+id/textView42"
            app:layout_constraintTop_toBottomOf="@+id/textView42"
            app:rippleColor="@color/transparent"
            app:strokeColor="@color/gender_male_stroke_selected"
            app:strokeWidth="1dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/transparent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView3"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:scaleType="centerInside"
                    android:tint="@color/gender_male_color_selector"
                    app:layout_constraintBottom_toBottomOf="@+id/textView43"
                    app:layout_constraintEnd_toStartOf="@+id/textView43"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView43"
                    app:srcCompat="@drawable/ic_male" />

                <TextView
                    android:id="@+id/textView43"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    style="@style/text_style_100"
                    android:text="@string/male"
                    android:textColor="@color/gender_male_color_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/female_cardView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:checkable="true"
            android:clickable="true"
            android:focusable="true"
            app:cardBackgroundColor="@color/gender_female_card_selector"
            app:cardElevation="0dp"
            app:cardForegroundColor="@color/transparent"
            app:layout_constraintBottom_toBottomOf="@+id/male_cardView"
            app:layout_constraintEnd_toEndOf="@+id/txt_gender_second"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/male_cardView"
            app:layout_constraintTop_toTopOf="@+id/male_cardView"
            app:rippleColor="@color/transparent"
            app:strokeColor="@color/gender_female_stroke_selected"
            app:strokeWidth="1dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"

                android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView32"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:scaleType="centerInside"
                    android:tint="@color/gender_female_color_selector"
                    app:layout_constraintBottom_toBottomOf="@+id/textView44"
                    app:layout_constraintEnd_toStartOf="@+id/textView44"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView44"
                    app:srcCompat="@drawable/ic_female" />

                <TextView
                    android:id="@+id/textView44"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    style="@style/text_style_100"
                    android:text="@string/female"
                    android:textColor="@color/gender_female_color_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView32"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>


        <TextView
            android:id="@+id/txt_birthday"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:inputType="textEmailAddress"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/title_primary"
            style="@style/text_style_200"
            app:layout_constraintEnd_toEndOf="@+id/female_cardView"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="@+id/your_birthday"
            app:layout_constraintTop_toBottomOf="@+id/your_birthday"
            tools:text="17th September 2018" />

        <View
            android:id="@+id/view2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="11dp"
            android:background="@color/border"
            app:layout_constraintEnd_toEndOf="@id/txt_birthday"
            app:layout_constraintStart_toStartOf="@+id/txt_birthday"
            app:layout_constraintTop_toBottomOf="@+id/txt_birthday" />

        <DatePicker
            android:id="@+id/date_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="40dp"
            android:calendarTextColor="@color/title_primary"
            android:calendarViewShown="false"
            android:clipToPadding="false"
            android:datePickerMode="spinner"
            android:theme="@style/DatePickerTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view2"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
