<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/image"
        android:layout_width="wrap_content"
        android:layout_height="64dp"
        android:layout_marginBottom="16dp"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        android:backgroundTint="@color/border"
        app:layout_constraintBottom_toTopOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0"
        tools:srcCompat="@drawable/ic_heartgroups" />

    <TextView
        android:id="@+id/title"
        style="@style/text_style_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="8dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@+id/count_down_timer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="@string/invite_your_friends" />

    <TextView
        android:id="@+id/description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:lines="3"
        android:textAlignment="center"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Invite your friends and get free impressions." />

    <TextView
        android:id="@+id/count_down_timer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        tools:text="11:03:59"
        android:textColor="@color/title_primary"
        android:textAlignment="center"
        style="@style/text_style_400"
        android:fontFamily="@font/tt_norms_pro_demibold"
        app:layout_constraintBottom_toTopOf="@+id/description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>