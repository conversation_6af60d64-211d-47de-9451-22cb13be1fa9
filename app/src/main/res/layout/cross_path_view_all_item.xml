<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="80dp"
    android:layout_height="wrap_content"
    android:layoutDirection="locale"
    app:chainUseRtl="true">

    <FrameLayout
        android:id="@+id/image_container"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/circle_background"
        android:backgroundTint="@color/bg_secondary_old"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/user_image"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_people"
            app:tint="@color/label_secondary" />

    </FrameLayout>

    <TextView
        android:id="@+id/user_name"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:maxLines="1"
        android:singleLine="true"
        android:layout_margin="2dp"
        android:text="@string/view_all"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_container" />


</androidx.constraintlayout.widget.ConstraintLayout>