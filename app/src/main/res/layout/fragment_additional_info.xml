<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="type"
            type="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType" />


    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="top"
            app:layout_scrollFlags="scroll|enterAlways|snap"
            tools:context="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment">

            <ProgressBar
                android:id="@+id/edit_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:indeterminate="true"
                android:minHeight="0dp"
                android:progressTint="@color/colorPrimary"
                android:theme="@style/ProgressBarTheme"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/image_fragment"
                bindAdditionalInfoDrawable="@{type}"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:layout_marginTop="48dp"
                android:scaleType="centerInside"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:srcCompat="@drawable/ic_looking_for" />

            <TextView
                android:id="@+id/textview_fragment"
                style="@style/text_style_200"
                bindAdditionalInfoText="@{type}"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textAlignment="center"
                android:textColor="@color/title_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/image_fragment"
                tools:text="TextView" />


            <TextView
                android:id="@+id/search_textView"
                style="@style/text_style_200"
                showSearch="@{type}"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="32dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/rounded_corners_background_10_dp"
                android:drawablePadding="7dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:gravity="center_vertical"
                android:hint="@string/search"
                android:paddingStart="8dp"
                android:tag="additional_info"
                android:textColor="@color/description_primary"
                app:drawableStartCompat="@drawable/ic_search_vector_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textview_fragment"
                tools:visibility="visible" />

            <View
                android:id="@+id/view8"
                showView="@{type}"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/border"
                app:layout_constraintBottom_toTopOf="@id/recyclerView_choices"
                app:layout_constraintEnd_toEndOf="@id/recyclerView_choices"
                app:layout_constraintStart_toStartOf="@id/recyclerView_choices" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView_choices"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="32dp"
                android:nestedScrollingEnabled="true"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_anchorGravity="bottom"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/search_textView" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>


</layout>