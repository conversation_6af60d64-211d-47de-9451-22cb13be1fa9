<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="32dp"
    tools:context=".login.fragments.forgotpass.ForgotPassSetPhoneFragment">


    <TextView
        android:id="@+id/textView66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/email_"
        android:textColor="@color/title_primary"
        style="@style/text_style_100"
        android:fontFamily="@font/tt_norms_pro_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/email_input"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/edit_text_rounded_corners_12_dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:hint="@string/enter_email_address"
        android:imeOptions="actionDone"
        android:importantForAutofill="no"
        android:inputType="textEmailAddress"
        android:paddingLeft="16dp"
        android:textColor="@color/title_primary"
        android:textColorHint="@color/gray_200"
        android:textIsSelectable="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/textView66"
        app:layout_constraintTop_toBottomOf="@id/textView66"/>

    <TextView
        android:id="@+id/email_error_text"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/email_already_in_use"
        android:textColor="@color/red_500"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/email_input"
        app:layout_constraintStart_toStartOf="@+id/email_input"
        app:layout_constraintTop_toBottomOf="@id/email_input"
        tools:visibility="visible" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/btn_continue"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="32dp"
        android:clickable="false"
        android:enabled="false"
        android:stateListAnimator="@null"
        android:text="@string/continue_text"
        app:buttonType="PrimaryWithState"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/email_input"
        app:layout_constraintStart_toStartOf="@+id/email_input"
        app:layout_constraintTop_toBottomOf="@+id/email_error_text"
        app:layout_constraintVertical_bias="1.0" />
</androidx.constraintlayout.widget.ConstraintLayout>