{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.0.0", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 924.000037635292, "w": 1080, "h": 1080, "nm": "Pre-comp 1", "ddd": 0, "assets": [{"id": "image_0", "w": 415, "h": 309, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 851, "h": 851, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA1MAAANTCAYAAABLuEecAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAgAElEQVR4nOzdUXZW15U26ncJ2bclqgEVOQ2okDSgrPjcVyjfVgTIDYiJGxDADYhxNSAfRvZtjE8DglwNKOMO/CinAyi3vwXrXHyoAINA+rT3Xnvv73nGqDESG/aaVXFq8GrONVcJALSwqFtJtrKZ7SRbqdlKkpT8U57l8ht/z0aepOYfKTlKcpQkOc5h9srhECUDwMtK6wIAmLFF3c5mriT5RWqupGQrJVeeB6etjk87SvIoyVGe5ceUHKbkMMd5lL1y1PFZACBMAdCRRd3Opeyk5EpKPkx93nEag5LD1DzKs/yYjRwIWAB0QZgCYDWLeiWb+TDJzvP/GUdwOquSR0kepea7HOdAuALgvIQpAM5mUbdyKVeTfJiSq5laeHq3gyQPcpzvs1cetS4GgPETpgA43aJuZTPXk1xNciXzC1BvVnKY5CA1X2W3HDSuBoCREqYAeN2i7mQzt7JOAeo0y/tW3+U4d20NBOBlwhQAS8s7UL9LcjPrHqBOd5CSe/l9+ap1IQC0J0wBrLsXXaid1qVMxnIM8EF+ype6VQDrS5gCWEcv7kLdyHKUj1WV3MtPuSNUAawfYQpgnSxD1Kcxytc9oQpg7QhTAOtiv96KENU/oQpgbQhTAHN3v97IRm6lZrt1KWtFqAKYPWEKYK4slhiDmuROdsud1oUA0D1hCmBulvei7ia53roU/tfjlNyxUh1gXoQpgDnZr58muR33osbJ6B/ArAhTAHOwqNvZzCJG+qbgSZIvjf4BTJ8wBTB1ulFT9TjH+UiXCmC6hCmgrUXdTrKVzWynZis129nIVmr+KUlSspXTQkLN4fNf8488y1E28iTJ35Mc5TiHs/9Dqm7UHDxJzee5Vu62LgSA8xOmgP4t6laSK9nMr5J8kOQXKbkyyKruksPnoevHJI+T/JjjPMpeOer97D7t16tJFtGNmgd3qQAmSZgCuvVqcNoZLDSd31GSR3mW77ORg0kFrPv1i5TcbF0GnTP2BzAxwhRwMS/C09XkeYCaroMsO1gPslsO2pbyBsb61kFNzWfG/gCmQZgCzm/5jtH1JFeTXMk8R82Okhyk5EF+yvfNuwWLeiXv5duRdvno3hfZLZ+1LgKAtxOmgLN5NUDtNK6mhYPn91qGD1Zf1+upuZt5hlZO90OO83HzIA/AqYQp4HSLupVLuZqS61nPAHWaB0m+ym550PtJ39RbeZbbvZ/DWLlHBTBiwhTwukXdeX4H6np0Q05XcpjkoLctbBZNsPQ4G/k4/1ketS4EgFcJU8ALi3o1m/k0ulDnV3IvNV91srhiOVK5yHKkEpLkSZJPBumG9mW5rGYrm7ny2ptyL78nV954L/AoNS+2bdYc/u/7ciWHKTnMcY6SHE5mKycwC8IUrLvlH9w/TXIzulAXV/Ioyd38vny10u9f1K28l4epudJtYcxATcneyv9sDeXl5xFqrmTj+ZKa4ZanLJ89mNvbcsAoCVOwroSofi1HAG+fa2GFIMW7jW91+qJeyWY+fB6cdka7cXL5gPejnDyBMMbnD4DJEaZg3QhRw1tuAXz7vapF3X6++lyQ4l1qkjvZLXeanL587+x3WY4D72S6/3/kpIP1IMf5PnvupAHnJ0zBOtmvtyJEtXSQ5MvX7r0sg9TD0f5EnzEaNlAtl9J8mJIbs/3n9GShTFd3H4G1IEzBOljUnbyXxWz/EDQ1L0YAv0uyJUixon4D1UmAWs8fwByl5EFqvpv00g+gd8IUzNlyHGcR2/nGaXmH48UWM1jNIsf5rJMFCx7nft2LJxC+NAoI/JwwBXP04l7U7dalAIP4Icf5eOX3zpZdqBtJfhfh/m1+SMmXo9+oCAxGmIK5MdIH6+pxjvPRubZH6kKtpu8Hu4HJEKZgLhZ1K5dyKyU3W5cCNPPuQLUc/72e9bwL1b2zbOsEZkuYgjnQjQJeeJyNfJz//Nn9nuUo363oQvVDqIK1JEzB1O3XT5OM5wFPYAyepOSP+SnfeVduYEIVrBVhCqbKpj7g3Y4iRLUhVMFaEKZgihb1St7Lt8b6AEbtSZYPdQ/zuDIwOGEKpsZYH8DUPE7JHSvVYX6EKZiS+/UL2/oAJsroH8yOMAVTsHwP5tu4HwUwdbpUMCPCFIzdom7nvTx0PwpgRnSpYBaEKRgzQQpgznSpYOKEKRgrG/sA1sUX2S2ftS4COD9hCsZoUa9kMw/jfRiAdfE4x/nI2B9My0brAoCfEaQA1tEH2cz/ZL9ebV0IcHbCFIyJIAWwzi4n+Wv2663WhQBnY8wPxmJRt7OZHyJIAZB8keN8nr1y1LoQ4HTCFIyBrX0AvO6HHOdj96hgvIQpaE2QAuB0FlPAiAlT0NKibuW9/CBIAfAWAhWMlAUU0NJmFoIUAO+w3PT3Tb3SuhDgVcIUtPJNvZXEClwAzuJynuVvAhWMizE/aGG/fprkbusyAJicJ9nIR/nP8qh1IYAwBcNbrkB/3LoMACZLoIKRMOYHQzrZ3AcAq1uO/C3qdutCYN0JUzCkzdy2cAKADlzOpkAFrQlTMJTlPanrrcsAYDY+EKigLXemYAjLe1I/JNlqXQoAs/PD83eojloXAutGZwqGsLwnJUgB0IdfZzN/bl0ErCNhCvr2Tb3lnhQAPbuR/XqrdRGwboz5QZ+sQQdgODXJR9ktB60LgXWhMwV9sgYdgOGUJH+1kAKGI0xBX+7XG8b7ABjY5Wzmr62LgHUhTEEfFnU7GzG7DkALv85+tZACBiBMQR88zgtAWzezX3daFwFzZwEFdM3SCQDG4XGO8xvvT0F/dKaga5u53boEAEjyQTbzl9ZFwJzpTEGXliMVNvgBMBY1ycfZLQ9aFwJzpDMFXSpZtC4BAF5Skvwli7rVuhCYI2EKumIVOgDjdDmb+VPrImCOjPlBV76uj4UpAEaqJvkou+WgdSEwJzpT0IX9uiNIATBiJVV3CromTEE3PNALwLiV7OR+vdm6DJgTY35wUd6VAmA6nuQ4v/T2FHRDZwouyrtSAEzH5WxEdwo6ojMFF6ErBcD06E5BR3Sm4CLey6etSwCAc9Kdgo4IU3AxV1sXAADntpE/eMgXLk6YglV5pBeA6dKdgg4IU7CqDSN+AEyY7hRcmDAFq1g+0nuldRkAcAG6U3BBwhSs5kbrAgDgwnSn4EKEKTivRd1Ocr11GQDQga1c8gNCWJUwBedlHToA81GS/HvrImCqhCk4P+vQAZiPkp3s153WZcAUCVNwHtahAzA/JTW/a10ETJEwBedhHToAc1TcBYZVlNYFwGQsRyAeti4DAHpy0NmXag5f+td/T5KUHKbkKMlRjnOYvXL4xt8LEyJMMX3L7Xpb2cx2arZSs52NbKXmn5IkJVtJLr72tWTbiB8AdKjkUWqOkvyY5HGSH3OcR9krR40rgzMRppiG5RsY27mUKym5kuQXKbmS2lFQAgDGo+QwNY+y7Jb9mN1y0LYgeDNhinFa1O1cyk5KrqTkw9RcaV0SANDUQZ7l+2zkQLhiLIQpxmFRt7KZ66m5kpKr0W0CAE53lJIHSQ7yU74zFkgrwhTtLOpONvNhSq7qPAEAF/AgJQ8EK4YmTDGsZYC6muR6dJ8AgK6V3EvNV0YBGYIwRf+WI3yfpsSDtwDAUB6n5E5+yvfWsNMXYYr+LLtQt5LstC4FAFhjJffyU+4IVXRNmKJbulAAwHgdJLljBJCuCFN04yREJTfjLhQAMG7LEcDfl69aF8K0CVNcjBAFAEzX4ySf6FSxKmGK1QhRAMB8fJvjfOZOFeclTHE+QhQAMFcWVXBOwhRnt6g7eS8LiyUAgBlzn4ozE6Z4t0XdzmYWseIcAFgfj3Ocj3SpeJuN1gUwcvv1VjbzQwQpAGC9fJDN/J/s11utC2G8dKZ4MyN9AAAndKl4I50pXne/fpHNPBSkAACS6FJxCp0pXljU7byXb1NzpXUpAACjVPMwT/OJLhWJzhQn9uun2cwPghQAwFuU/Dab+Vv2607rUmhPZ2rdLepWLuVWSm62LgUAYEJqkjvZLXdaF0I7wtQ6M9YHAHBRX+Q4n2evHLUuhOEJU+tqUXeymW+TbLUuBQBg4mz7W1PuTK2j5f2ohxGkAAC68EE287cs6nbrQhiWMLVuvqm3ktxtXQYAwMx8kM38T76prk+sEWN+62JRt/JevkjNjdalAADMWE3JXn5fvmpdCP0TptbBMkg9tGgCAGAQNTWf5VoxDTRzxvzmbrmxT5ACABhOScmfs19vtS6EfulMzdmLILXduhQAgDXkLaqZE6bmSpACABgDgWrGhKk5EqQAAMbEUoqZEqbmxrIJAIAxEqhmyAKKuRGkAADGqKTmC+9QzYswNSdf14UgBQAwWpfzLH/Nom63LoRuCFNz8U295UFeAIDR+yCb+VsWdat1IVycMDUH39RbeZbbrcsAAOBMPsil/LV1EVycBRRTt6hXspkfWpcBAMC5fZHd8lnrIlidztSULVegf9u6DAAAVnIzX9frrYtgdcLUlHlLCgBgypYb/iykmCxhaqru1y8EKQCAybtsIcV0CVNTdL/eSMnN1mUAANCJD7KZP7UugvOzgGJqFnX7+cIJP70AAJiPmuTj7JYHrQvh7HSmpua9PIwgBQAwNyXJX4z7TYswNSXLh3m3W5cBAEAvLmczf2ldBGdnzG8qluN9j1uXAQBAr4z7TYjO1FQsx/sAAJg3434TIkxNgfE+AIB1ctl2v2kw5jd2xvsAANZRTfJRdstB60I4nc7U2G3mdusSAAAYXEnVnRo7YWrM7tcbSa63LgMAgAZKdvJ19WfBETPmN2Zf18fuSgEArLUnOc4vs1eOWhfC63Smxup+vSFIAQCsvcvZyM3WRfBmOlNjtKjbeS8PhSkAAKI7NVo6U2P0fq4LUgAAPKc7NVI6U2OjKwUAwOt0p0ZIZ2psdKUAAHid7tQI6UyNia4UAACn050aGZ2pMbmUq4IUAACn0J0aGWFqTDbyaesSAAAYsY38oXUJvCBMjYV3pQAAeLetfFN/17oIloSpsdjIrdYlAAAweiVPTTONhTA1Bvt1R1cKAIAzKdnJft1pXQbC1FjcaF0AAACTUVJj1G8ErEZvbVG3s5nHrcsAAGBSnmS3/HPrItadzlRrl7LTugQAACZny6hfe8JUayXXW5cAAMDklNT8qXUR686YX0tG/AAAWN2THOeX2StHrQtZVzpTLV3K1dYlAAAwWVv+PNmWMNXShhE/AABWVhJb/Voy5teKET8AAC7OqF9DOlOtaMkCAHBxW9nMldZFrCthqpWiJQsAwIV5wLchYaqFRd1KvC8FAEAH/JC+GWGqhU1BCgCAzmw//2E9AxOmWngmTAEA0CH38ZsQplq4lA9blwAAwGyUJL9qXcQ6EqaGtqhbqTauAADQKT+sb0CYGprVlQAAdK3kintTwxOmhua+FAAAffBD+8EJU0Pb0IIFAKAHrpIMTpgamn/IAQDoXknNv7YuYt0IU0Na1K2UmGUFAKB7xQ/thyZMDckcKwAAfSnZbl3CuhGmhmTEDwCA/mxlUbdbF7FOhKkhVT8tAACgR+97vHdIwtSQNvzDDQBAj57mcusS1okwNSTLJwAA6E8xCTUsYWpI7kwBANCvf2ldwDoRpoayqLpSAAD0qxjzG5IwNZzt1gUAADB7v2hdwDoRpoay6b4UAAA9c0d/UMLUUFwGBACgf9utC1gnwtRw/JQAAABmRJgaShWmAAAYgMVngxGmAABgXoSpgQhTw/EPNQAAzIgwNRxhCgAAZkSYAgAAWIEwBQAAsAJhCgAAYAXCFAAAwAqEKQAAgBUIUwAAACsQpgAAAFYgTAEAAKxAmAIAAFiBMAUAALACYQoAAGAFwhQAAMAKhCkAAIAVCFMAAAArEKYAAABWIEwBAMB8lLyXW1nU7daFrANhCgAA5qTmRjbzOPv1Yb6u11uXM2fCFAAAzNNOau7l6/o4+/VT3aruldYFzN6i7mQzV5NcT7LVuhwAANZYyb38lC+zVx61LmUOhKk+LOpWNvNpkpsRoAAAGJ+DlNzL78tXrQuZMmGqS8su1I0su1AAADBuJYepuZvjfJe9cti6nKkRprqwDFG3kuy0LgUAAFayHAG8I1SdnTB1EUIUAADz8yDJl9ktB60LGTthahVCFAAA83eQ5I5QdTph6jwWdTub+SLJ1dalAADAIGoe5mk+Mf73OmHqLGznAwBg3blT9Rph6l0WdSfvZZGa7dalAABAYzXJnRzny+yVo9bFtCZMnWbZjVrESB8AAPzc45TcWfd3qoSpN9mvnya5HSN9AABwujUf/ROmXqYbBQAA5/UkNZ/nWrnbupChCVMn3I0CAIDVreHWv43WBYzCN/VWNvNQkAIAgBWV/Dab+Vu+rtdblzKU9e5MLd+NWsTjuwAA0KUvsls+a11E39Y3TC3qlbyXb3WjAACgF49znI/mPPa3nmN+X9frxvoAAKBXH2Qz/5P9OtvlbusXpr6pt1JzL9aeAwBA3y4n+Wv2663WhfRhvcb87tcvUnKzdRkAALCGZnePaj3C1PL9qG9j0QQAALT0Q47z8VzuUc0/TC3qVt7Lw9RcaV0KAAAwn8UU874ztajbghQAAIzKB9nM37Ko260Luaj5dqZeBKnt1qUAAACvmXyHap5hajna94MgBQAAo/YkG/ko/1ketS5kFfMb83txR2q7dSkAAMBbXc6z/C3f1Eley5lfmHJHCgAApmQZqCZ4h2peYerruhCkAABgci5PcSnFfMLUN/VWam60LgMAAFjJ5Lb8zWMBxTf1Vp7ldusyAACAC3uc4/wme+WodSHvMv3O1KLuCFIAADAbH+RS/tq6iLOYdphaviW1aF0GAADQoZLfZr/+uXUZ7zLdMT9vSQEAwJzV1HyWa+Vu60JOM93O1KXcEqQAAGC2Skr+nP2607qQ00wzTO3XT1Nys3UZAABAr0qSv4x1w9/0wtTy/5C3G1cBAAAM44Ncyl9aF/Em0wtT7+Vhkq3WZQAAAAMZ6UKKaYWp5cO8263LAAAABndzbPenprPNb1G3s5nHrcsAAACaGdWDvtPpTC3H+wAAgPX1QTbzp9ZFnJhGmDLeBwAALI1m3G/8Y37G+wAAgFeNYtxv/J2pTWvQAQCAV4xi3G/cnan79UZKFq3LAAAARqcm+Si75aBVAePuTG3kVusSAACAUSqpbbtT4w1T9+sNSycAAIBTlezkfr3Z7vgxWtTtvJeHwhQAAPAOT3KcX7ZYRjHOztT7uS5IAQAAZ3A5G2nSnRpfZ2q5Cv2HJFutSwEAACahSXdqfJ2p5Sp0QQoAADiryy1WpY+rM7Wo27mUH1KEKQAA4Fzq8+7U4VAHjqsz9X6uC1IAAMAKSi7l0yEPHFeYqrnRugQAAGCiSq5nUQdrzownTHlXCgAAuJhBN/uN587U1/WxMAUAAFzQYJv9xtGZ2q87ghQAANCBwbpT4whTcVcKAADoyEb+MMQx7cf8lo/0Pm5dBgAAMBs1yUfZLQd9HtK+M3UpO61LAAAAZqWk9v+Ib/vOlMUTAABA92qO8899LqJo25la1CuCFAAA0IPS9yKKtmFqw+IJAACgJxv5934/39Kl/K7p+QAAwJxdyX7d6evj7cKUET8AAKBfJc/6W3jXLkwZ8QMAAPq2kWv9fbqVS/mw2dkAAMC62O5r1K9NmFrU7dRcaXI2AACwTnob9WsTpjzUCwAADKXk3/r4bJswVYQpAABgICU7WdStrj/bKky5LwUAAAznUq52/cnhw5SV6AAAwLBKkl91/dHhw9QliycAAICBlfyu608OH6bclwIAAIa33fW9qRZhqvP2GgAAwDu93+3uhuHDlPelAACA4ZU87XZKbtgw1dPLwwAAAGfQ6ZTc0J2pzne7AwAAnEnpdkpu2DD1zPIJAACgma0s6nZXHxs2TG1YPgEAADT0fneZZNgwVYz5AQAADT3NB119atgwZZMfAADQTknNv3b1seHCVIeziQAAACspudzVp4YLU5vZHuwsAACANynTvDPlvhQAANDadlcfGi5MVZ0pAABgBDq6gjRkmNKZAgAAxqCTbDLkmN/2gGcBAAC82fv5RRefGXY1OgAAQGtPu9noN1yY2ugm/QEAAFzQ5Mb8AAAAWitd7XMQpgAAAFYgTAEAAOvmX7r4yHBhqtjmBwAAzIfOFAAAwAqEKQAAgBUMF6ZqDgc7CwAAoGc6UwAAACsQpgAAAFYwXJh6lr8PdhYAAMDp/r8uPqIzBQAAsIIhw9TRgGcBAAC8SU3pJpsM+Wjv4WBnAQAAnG5iYUpnCgAAGIOOGj1DdqaEKQAAYDaGC1PHeTTYWQAAAKc5nlpnypgfAAAwBnvlsIvPDBem9sqRJRQAAEBjh119aNh3pqowBQAANNRhJhk2TD3L3wc9DwAA4FU/dvWhYcNUsYQCAABopibdNXiEKQAAYH10mEmGDVPWowMAAC11tBY9SUpXHzqzr+vj1GwPfi4AALDunmS3/HNXHxu2M5UkT/P94GcCAADUbiflhg9T7k0BAABtdLbJL2nTmToY/EwAAGDd1VzqNosMH6b2yqPUHA1+LgAAsN7+b7dXjoYPU0lSdKcAAIAB1TzKXum0qdMmTFVLKAAAgAH1sLuhTZh6mgdNzgUAANZRzUa+6/qjbcLUXjlM6e6xLAAAgLfq+L5U0ipMJcnT7pMhAADAa2oOur4vlbQMUxtG/QAAgN7VJP9vHx9uF6Z2y4EV6QAAQO96euu2XZhKkpqvmp4PAADMW81h9krnm/yS1mHKqB8AANCv3nY1tA1Tu+Ug8YAvAADQi5qn+bKvj7cNU0se8AUAALpX8yh75bCvz7cPU8e5axEFAADQsZqN/rpSyRjC1F45SjHqBwAAdOynfqfg2oeppV4TIwAAsHbu9Tnil4wlTFlEAQAAdKcmud/3IeMIU0lS+1tZCAAArJGaw+cNm16NJ0w9zT2LKAAAgAuq2cidIQ4aT5haLqJwdwoAAFhdzWF+X74a4qjxhKkkOc6D1iUAAAATNuCm8HGFqb3yKBZRAAAAq6k5zudDHTauMLU0yHwjAAAwO72vQ3/Z+MLUbjmwiAIAADinmuP815AHji9MJbGIAgAAOJeag+fXhgYzzjB1nLu6UwAAwBnVbGSQDX4vG2eYSrZSstW6CAAAYAIGXIf+svGFqUXdznt52LoMAABgEgZ7pPfnxhWmToJUzXbrUgAAgIn4Kd+3OHY8YWpRtwQpAADgnAZdh/6y8YQpQQoAADifQR/p/blxhKmv6yI1V1qXAQAATEjNd626UskYwtQ39VZqbrQuAwAAmJTa+n3atmHqm3orz3K7aQ0AAMAUlSTXWhfQxn79NMndZucDAABzsMhu+aTFwW3C1H69muTbJmcDAABz0yRQDR+mFvVKNvMwydbgZwMAAHM1eKAaNkwJUgAAQH8GDVTDhSlBCgAA6N9ggWqYbX6Lup338m0EKQAAoF83sl9vDXFQ/52pZZB6mJrt3s8CAABIapI72S13+jyk3zC1qFvPg9SVXs8BAAB4VU3JXn5fvurrgH7H/C7lliAFAAA0UFLzRb6pveWR/sLUN/VWSm729n0AAIC3u5xn+WsWdbuPj/cTpvbr1TzL7V6+DQAAcHYfZDN/7ePD3YepRd1OyRedfxcAAGA1v85+/XPXH+0+TNncBwAAjM/NfF2vd/nBbsPU/fqFIAUAAIzQciFFh/enugtT9+sNCycAAIARu9zl/aluwtSibmcjg7wyDAAAcAG/zlf1dhcf6iZMbea28T4AAGASNvKn7Nedi36mXLiQ5Xjf4sLfAQAAGM7jHOc32StHq37gYp0p430AAMA0fZCNi+18uFiYMt4HAABM1Ub+dJHtfquHqUW9mqTTPe0AAAADKrmUv6z6m1cPU+/li5V/LwAAwBiU7Kz6mO9qYWq/fmq8DwAAmIGTx3y3zvsbzx+mFnXb47wAAMCMXF5lGcX5w5SlEwAAwNxs5A/n7U6dL0wtN11YOgEAAMzN5Wzmz+f5DecLU5u5fa5fDwAAMB03zrMq/exhSlcKAACYt5L3cuusv/jsYUpXCgAAmLua62ftTp0tTOlKAQAA6+HM3amzhSldKQAAYF0su1Pv3Oz37jClKwUAAKyXcpZ3p94dpi5lp4tqAAAAJuMM7069O0xtnH2bBQAAwExs5VJuvO0XvD1M3a83UrPdYUEAAABTUFLyu7f9greHqeKuFAAAsLY+zH7dOe1vnh6mlosnTv2NAAAAM1eSXD3tb54epi6d/psAAADWxLXTFlGcHqY28mlv5QAAAEzDVt7Ph2/6G28OU4t6xeIJAACAlDx9c6PpzWFq4+0rAAEAANZGyZU3jfq9OUxdevsKQAAAgDWy9aadEq+HKSN+AAAALyvJ6/emXg9Tl6xDBwAAeMUbHvB9PUy945VfAACANbT18wd8Xw1Ty0tVr/wCAAAAXn/A99UwtSlIAQAAnOLfXv43r4apZ8IUAADAKV5Zkf5qmLr05pd9AQAASPL+i8z0Ikwt6lZqrjQpCAAAYPxKnuW3J//mRZjaFKQAAADequZfT/7lizDlvhQAAMDblRdNqBdhaiO/alIMAADAdGxlUa8kL4epku1W1QAAAEzGpbwUpiyfAAAAOItyMuq3DFOWTwAAAJxNzS+SF2N+W2/5pQAAAJwoy30TyzD1TGcKAADgjLazqFvLMGWTHwAAwHlsGfMDAAA4r/fzq2WYsskPAADg7J7m8jJMFZ0pAACAMyqp2d7Iom63rgQAAGBi/mUj7ksBAACcT8nljWwKUwAAAOf0TzpTAAAA51WyvZEqTAEAAJyXzhQAAMAKdKYAAADOb3ujdQUAAABTJEwBAACsQJgCAABYgTAFAACwAmEKAABgBRspOWpdBAAAwNRsJMIUAADAOR0a8wMAAFiBMT8AAIDzqsvOlDAFAABwThs5zmHrIgAAACbmHzpTAAAA5ynrfWgAACAASURBVHe4kb1ylCpQAQAAnFFNzdFym9+GMAUAAHBmm3m0DFM1jxqXAgAAMB3Plnemkmf5R+NSAAAApuP4pDNVdKYAAADO6Ch75egkTB22rQUAAGAi6jI/LcPUsc4UAADAGf09OQlTe+XQenQAAIB3qik5SE7C1PJfHTYqBgAAYEp+TF4OU0/zfbNSAAAApuL5NakXYcpGPwAAgLerOcxeOUpe7UwdtKoHAABgIn48+RcvwpQlFAAAAG/zv8snkpfDVJKX/wYAAAA/c/xi18SrYapaQgEAAHCKo+yV/9018WqYcm8KAADgzX7WfHo1TO2VR+5NAQAAvKam5NuX/8LGG37Jd4OVAwAAMBXH+e+X/+3rYcoSCgAAgFfVPMpeOXz5L70epp7mgVE/AACAV7y2rO/1MLVXjlLy6LW/DgAAsJ5qnuarn//F18PU8pe+9gsBAADWUs3hyyvRT7w5TBn1AwAAOPHGJX1vDlNG/QAAAJLliN+Xb/obbw5TS3d6KgYAAGAa3rDF78TpYWq3HBj1AwAA1lhNyd3T/ubbOlNJeXM7CwAAYC387KHel709TB2fnsIAAABm7t5pI37Ju8LUXjlKctBtPQAAAKNXc5z/etsveHuYWrKIAgAAWC81B296W+pl7w5Tu+UgulMAAMD6qCm5965fdJbOVFLz1UWrAQAAmISaw+yW++/6ZWcLU9fKvdQcXrQmAACAkaspuX2WX3i2MJUkRXcKAACYuTN2pZLzhKnj3NWdAgAAZqxm4+wL+Mq5Pn2/3kjJ4twlAQAAjF3N41wrvzzrLz97ZypxdwoAAJirc3WlkvOGqSQp2Tv37wEAABizmsP8vpxrT8T5w5R3pwAAgHmpKfnkvL/p/GFq6VztLwAAgBG797xpdC6rhanlQV+u9HsBAADGo+Y4n6/yG1ftTCXHuZ2ao5V/PwAAQGvP8nn2yuEqv/V8q9F/zqp0AABgqs65Cv3nVu9MJctV6ZZRAAAA01PzNB9d5AMXC1NJcpw9434AAMCkXGC878TFxvxO3K83U/JFJ98CAADo0wXH+05cvDOVJNfK3SQPOvkWAABAfy483neimzCVnIz7HXb2PQAAgK51MN53opsxvxP7dSfJw06/CQAA0IWah7lWOulKJV12ppKTx3zvdPpNAACAi6p5nKf5pMtPdtuZOrFfHybZ6eXbAAAA51NznN9krzzq8qPddqZOHOc/3J8CAABGYXlPqtMglfTVmUqSRd3OpfyQkq3ezgAAAHibmru5Vv7Yx6f7C1NJsl+vJvm21zMAAADepOaHXCu/6evz/Yz5ndgtD1LTSwoEAAA41XLhxMd9HtFvmEpOHvS14Q8AABjGMkh91NV7Uqfpd8zvZfv1XpLrg50HAACsn5oneZrf9B2kkiE6Uyd2y40kXw12HgAAsF6WQar3jtSJ4cJUkhznZpLOVxICAABrr6bkkz5WoJ9m2DC1V45ynN9GoAIAALpTU/NJdsuDIQ8d7s7UyxZ1K5t5mORKk/MBAIC5WAapa+Xe0Ae3CVOJQAUAAFxUsyCVtAxTyTJQvZcfUrPdtA4AAGBqmgapZOg7Uz+3V46ang8AAExR8yCVtA5T9+sNXSkAAOAcRhGkktZhaiO3mp4PAABMyWiCVNIyTO3Xq7pSAADAGY0qSCVtO1OfNjwbAACYjtEFqaRVmFrU7SQ7Tc4GAACm5Vk+H1uQSlqFqc3cbnIuAAAwPSX/1rqENxn+nalF3c5mHg9+LgAAMFU1yUfZLQetC3nZ8J2pS8b7AACAcymp+VPrIn5u+DBlHToAAHBeJTvZrzuty3jZsGHKI70AAMBqSpJrrYt42bBhasM6dAAAYGU3sqhbrYs4MVyY2q87qbky2HkAAMDclGzkZusiTgzZmbox4FkAAMAcbeQPY+lODROmlo/0Xh/kLAAAYM4uj6U7NUyY8kgvAADQlZF0p4YJUyUfDnIOAACwDrZyqf01ov7DlHXoAABAt0pK/tC6iP7DVMnvej8DAABYN9utH/HtN0wtF09c7fUMAABgHZXU/KllAf2GqUvZ6fX7AADA+irZabmIot8wVaxDBwAAetP0Ed/+wtRyxG+nt+8DAACU/Furo/sLU0b8AACAvpXstFpE0V+YssUPAADoX8mzNo2c/sJU1ZkCAAAGsJFrbY7tw37dSUmzrRoAAMBa2X6+s2FQ/YSpZ96WAgAABnRp+AzST5jayK96+S4AAMDrSpJ/b3FotxZ1K5t50vl3AQAATvckx/ll9srRUAd235nazJXOvwkAAPB2W0Nnke7DVKO1hAAAwForqcM+z9R9mNrIh51/EwAA4N0G3d3QfZiqxvwAAIAGSq5kUQd7oqnbMLWoW96XAgAAGtlKsj3UYd2GKcsnAACAli4Nt8Oh2zBlxA8AAGinpOZfhzqs6zC13en3AAAAzqMM1+DpNkxtDLs9AwAA4BVlqnemBiwcAADgDbayqNtDHGTMDwAAmJf3h5mY6y5MDZT+AAAA3uppPhjimO7C1KauFAAA0FxJ8oshDupyzM9jvQAAQHs1/zTEMd2FqSpMAQAAIzDQevQuw9R2Z98CAABYVRmm0dPtNj8AAID2toc4pMswtd3htwAAAFa3qL13p3SmAACAOZpQmNoYZmMGAADAGFiNDgAAzM8A7+Aa8wMAAFiBMAUAALACYQoAAGAFXYapow6/BQAAMGrdhaln+Udn3wIAABg5Y34AAAArEKYAAABW0GWYOuzwWwAAAKPWXZgqFlAAAAAjcdx/s8c2PwAAgBXoTAEAAPOzVw77PqK7MDVAGw0AAOAMBmn0WEABAADMzcTC1F45SjXqBwAANFaHafR0+87UhjAFAAA0948hDuk2TNU86vR7AAAA51Xz4xDHdBumnuXvnX4PAADgfOpQm8a7DVNFZwoAAGhsoFzSbZh6KkwBAACNHU8xTFmPDgAAtHWUvTLBMb+9cpQiUAEAAI0MuBSv685U8jTfd/5NAACAsxlkk1/SR5iyhAIAAGijZmPanamDzr8JAABwFj8N15kqvXz1fn2Skq1evg0AAPBmT7Jb/nmow7rvTCVG/QAAgOENuHwi6StM1XzXy3cBAABOU/PfQx7XT5hybwoAABhWzcawOaSfO1OJe1MAAMCQanZLP82iU/R3mFE/AABgKHX46bj+wtRGHvT2bQAAgBdqNvLV0If2F6aOc5Cao96+DwAAcOKnfD/0kf2Fqb1yZEU6AADQu5pH2SuHQx/b7wWtOnyrDQAAWDuDd6WSvsPU0zww6gcAAPSo5mmbJk6/YWqvHNnqBwAA9KbmMHulyfWi/vewb+Re72cAAADrqCb5r1aH9x+mdoutfgAAQD+etnuSaZgXgku+HOQcAABgfdQctNjid2KYMHWcu4OcAwAArIsmD/W+rAx20n59mGRnsPMAAID5qnmca+WXLUsYpjO1dGfAswAAgDkrOWhdwnBharccJO3/FwYAACav5jifty5iyM5UojsFAABc3L2WiydODBumlmvSDwc9EwAAmJOa5H7rIpLhO1OJ7hQAALCqmoPnV4iaGz5MXSv3dKcAAIAV1JT2d6VOtOhMJbpTAADAeY2oK5W0ClO6UwAAwPmMqiuVtOtMJSV7zc4GAACmZWRdqaRlmPLuFAAAcDY1T/NJ6yJ+rl2YWnJ3CgAAeJdRvCv1c23D1LI79WXTGgAAgDGrOR7XXakTrTtTyXFup+aodRkAAMAIPcvnY+xKJUlpXUCS5H69mZIvWpcBAACMSM3jXCu/bF3Gadp3ppLkWrkbyygAAIAXajbGvWNhHGEqSY6zZ9wPAAB47l5+X75qXcTbjCdMLecgR508AQCAAdQ8HuvSiZeN487Uy/brwyQ7rcsAAACaqCnZG3tXKhlTZ+qEcT8AAFhnox/vOzG+MLVXDlOy17oMAABgYBMZ7zsxvjCVJLvlQTzmCwAA66Sm5JOxvin1JuMMU0myW24medS6DAAAYADP8nl2y0HrMs5jvGEqSY7zH6k5bF0GAADQo5qHuV5uty7jvMYdpvbKYZ7mP1qXAQAA9KTmcZ7mk9ZlrGLcYSpJ9sqj1PyxdRkAAEDHap7kaT6a0j2pl40/TCXJtXI3HvQFAIA5qdnI51MNUskYH+19m/16L8n11mUAAAAX9Cx3pnhP6mXTClNJsl9/SHKldRkAAMCKau7mWpn8VZ5pjPm97Di/jZXpAAAwVd/OIUglUwxTe+XIynQAAJigmh9yPM3NfW8yvTG/E4u6nUt5mJLt1qUAAADvsFyB/pvslaPWpXRlep2pE8s3qH6rQwUAACO3DFIfzSlIJVPuTJ3QoQIAgPF6EaQOW5fStel2pk6cdKgspQAAgHGp+WGuQSqZQ2fqxKJuZTOLJFdblwIAAGvvRZCa1Wjfy+YTpk7s17tJPm1dBgAArK2ae3maP845SCVzDFNJsl9vJ7nVugwAAFg7M3mQ9yzmGaaSZL9eTc0iJVutSwEAgDVQU/JZfl/uti5kKPMNU4lNfwAAMISaJyn5OLvloHUpQ5r+Nr+3WW76+3WSr1qXAgAAs3TyGO+aBalk7p2pl92vN1PyResyAABgNtZk0cRp1idMJcb+AACgCzVPspHP1+l+1JusV5g6YdsfAACsZvl+1MdzfYj3PNYzTCW6VAAAcF41d/M0d9Z1rO/n1jdMndClAgCAt6t5nJJP1nHJxNvMe5vfWeyW2znOB0ketC4FAABG6M66but7F52pl92vN5LcMvoHAMDaq3mYp/nE3ajTCVNvcr/eTPKpUAUAwNpZjvR9lt1icusdhKnTLOp2NnMj7lMBALAOao5S8mWOc9eCibMRpt5lGapup+ZDnSoAAGZHiFqZMHVWy1XqV2P8DwCAORCiLkyYWoVFFQAATJUQ1Rlh6iL2606SG0muty0EAADe6SDJgxznKyGqG8JUF9yrAgBgvA6S3PFOVPeEqa4ZAQQAoL2a+jxEXSvfty5mroSpvhgBBACgjZpn+Y9cL9+1LmTuhKm+GQEEAGBYNcf5ZfbKYetC5k6YGtJ+/TbJ1dZlAAAwa8LUQDZaF7BWnuUfrUsAAAC6IUwBAACsQJgCAABYgTAFAACwAmEKAABgBcIUAADACoQpAACAFQhTAAAAKxCmAAAAViBMAQAArECYAgAAWIEwBQAAsAJhCgAAYAXCFAAAwAqEKQAAgBUIUwAAACsQpgAAAFYgTAEAwNzslcPWJawDYWpYh60LAAAAuiFMAQAArECYGlLRmQIAoHeHrQtYF8IUAADMy1HrAtaFMDUknSkAAPpWhamhCFNDOhamAADoWfVnzqEIU8PyUwIAAPpUk/y9dRHrQpga0l450nYFAKBXG/68ORRhamj+4QYAoF8/ti5gXQhTQ6t51LoEAABm7NgP74ciTA3tmRlWAAB6tFf88H4gwtTQis4UAAA9MQU1KGFqaE/9Aw4AQG9MQQ1ImBreYesCAACYpaozNSxhamh75ShFoAIAoAebwtSQhKkWnub71iUAADBD/9da9CEJUy1YQgEAQNdqDrNXDluXsU6EqRae5qB1CQAAzI6u1MCEqRb2yqNUj6kBANCZmuIH9kMTplox6gcAQJeO3csfmjDVSs13rUsAAGA2jrJX/LB+YMJUKzpTAAB0pepKtSBMtbJbDtybAgCgAzUl37YuYh0JUy0Z9QMAoAvH+e/WJawjYaolG1cAALiomkfel2pDmGrpaR4Y9QMA4AJqSr5qXcS6EqZa2itHFlEAAHAhx66OtCJMtVb9JAEAgBUZ8WtKmGrNqB8AAKsx4teYMNXaXjmy1Q8AgJUY8WtKmBqDjdxrXQIAABNTc2DEry1hagw84AsAwPnUFD+Qb02YGouSL1uXAADARNQcZrfcb13GuhOmxuI4d3WnAAA4k5KD1iUgTI3H8s0pFwgBAHiXmuN83roIhKmxude6AAAARu+exRPjIEyNyW45SLRsAQA4Vc1x/qt1ESwJU+Nzp3UBAACM1HId+qPWZbAkTI2N7hQAAG9W4wfvoyJMjZP/kgAA8Kqag1wr37cugxeEqTHSnQIA4FW6UiMkTI2X/7IAALCkKzVKwtRY6U4BALBU8zSftC6C1wlT46Y7BQCAd6VGSpgas91ykJq7rcsAAKCZmuN83roI3kyYGrunuZOaJ63LAABgcDXPckdXarxK6wI4g/v1Zkq+aF0GAAADqnmca+WXrcvgdDpTU3Ct3I1lFAAA66Sm5HbrIng7YWo6LKMAAFgPNcm97Jb7rQvh7YSpqbCMAgBgPdQcWjoxDcLUlCyXUTxuXQYAAL1ZjvdZOjEJwtSU7JWjPM3HWbZ+AQCYF+N9EyNMTc1eeZSaz1qXAQBAx4z3TY7V6FN1v/4tJb9tXQYAAJ2oOc5vslcetS6Es9OZmqqn+cRjvgAAs3DyOK8gNTE6U1O2X3eSPGxdBgAAF/JtdsvHrYvg/HSmpmy3HMT7UwAA01XzOMfuw0+VztQc3K+LlNxoXQYAAOdQ8yRP8xtr0KdLZ2oOnuaPqfmhdRkAAJxZTckdQWrahKk5OHl/yoO+AABTsFw4sVu+bF0IF2PMb04W9Uou5W8pudy6FAAA3qim5stcK39sXQgXJ0zNzXLD39/iP1sAgLGpSR7Y3DcfxvzmZrccpOaT1mUAAPAzNT/k2J/T5kSYmqNr5V6sTAcAGI+a/5On+X+yV45al0J3hKm52i23I1ABALRX81iQmif3auZuv95Ocqt1GQAAa2kZpD6yAn2edKbmTocKAKCN5WifIDVjOlPrYtmh+lP8Zw4A0L+a/zHaN3/+YL1O7tcbKflL/OcOANCXmpqDPM3HgtT8+UP1ulk+7PvXlHzQuhQAgJmpqfkq18pe60IYhjtT62avPMrTfJSax61LAQCYkZpnuSNIrRedqXW1qNvPO1S/bl0KAMCk1TxJzR////buJiuu6+rD+HMk+iET8Cp7ADHx27eJ07f0Ks0sQKUBOJIGoIgMIBYZgBHQfxdkADZ4AHYxgZfSBKJyv6p2GqdKYH0gPm7VuR/PryHLSEJ7aV3uPX/2OfuylfZKl6LlMkx13X58R+JveC1IkiRdXx59/oB+GpQuRcvnNr+u20xPCJ4CUboUSZKkBgmCH2ejzw1SHWU3Qlne9veDgykkSZI+Kp+P2kq+y7Pj7Ewp66chE74geIldKkmSpPcLzgj+ZJAS2JnS++zHQ+CZXSpJkqQ3AjhkzCPfH6U5w5TeL2/7+zuJLbxOJElSlwWvSWyzkXZKl6J6cZGsy9mlkiRJ3RUEx0x4RD8NSxej+jFM6ePsUkmSpG4JgiHQZzOdlC5G9eXCWFd3EPcJ/kmih9eOJElqpwC2GbPj2Sh9jAtiXZ9b/yRJUvu4pU/XZpjSzexGjxUeAs/wOpIkSc2VQxRsu6VP1+UiWLfjeSpJktRMhijdmotfVcNQJUmSmsEQpcq46FW15tv/gk0HVUiSpBoxRKlyLnS1OOeDKnp4rUmSpOULghGwBxwaolQ1F7havINYJ9gC7pFYxetOkiQtTsx+PAYOmbDviHMtiotaLc9urHKX+8AmifXZR70GJUlSFXIXKrFDcGwXSsvgQlZl5IEV6xisJEnSzcTsv0OCI9zGpwJcvKq83VhlhXWCe8BXszNW4PUpSZJ+63wLX+KEMUf006BwTeowF6uqn91Y4y5rJL4hWDNcSZLUSfPgNAIGwCmJI8YMPAOlunBxqvrL49bXmLJG4ktglcTahd/hdSxJUnPFhZ+NyNv2BiQGTPjJzpPqzEWommk3VoEeK/RmIet3wOfkoJV/bbH82pEktVl8/Lfc6LOOSIwIhgRDEr8CpwSvmXJKPw0X8vdKC+KCUO22Gz0AVioOV8E9Eo8r/ZySJNVBcEbiUSWfa8zwzc8NSmohw5R0E3nM+9msCyZJUpt8x0Z6WroIqQnulC5AaqR+yu+xkCSpXYIx/ypdhNQUhinppsa8mB2UlSSpLV66HU+6OsOUdFN2pyRJ7RIEe6WLkJrEMCXdxpiXpUuQJKkSwYDNdFK6DKlJDFPSbeStEMeFq5Ak6baCxIvSRUhNY5iSbm+7dAGSJN1KMGQj7ZcuQ2oaw5R0WxvpGLtTkqQmSxyWLkFqIsOUVA27U5KkpnIcunRDhimpChvpmLjwlndJkprDcejSDRmmpOrYnZIkNY3j0KVbSKULkFplP/6fxKely5Ak6UqCX9hMX5QuQ2oqO1NSlRKPSpcgSdIVOQ5duiU7U1LVDuJHYL10GZIkXSo4YzN9VroMqcnsTEnV8+yUJKnugsRO6SKkpjNMSVXLk/1eli5DkqRLjBhzVLoIqekMU9IiTNgmGJUuQ5KkDzh0HLp0e4YpaRH6aej2CUlSTQVj/lG6CKkNHEAhLdJ+/Ezij6XLkCTpgl02ktNnpQrYmZIWKfG0dAmSJF1gV0qqkGFKWqQ8jMJ3eEiS6iCYsu1ZKak6hilp0fIwirPSZUiSOi4YMmWvdBlSmximpEXrpxETHgBRuhRJUmcFied2paRqGaakZeinAeH5KUlSIcEZG2m/dBlS2ximpGXZTC8IfixdhiSpc4K7/KV0EVIbGaakZZrwyPNTkqQlCuAlf02D0oVIbWSYkpYpv8z3EZ6fkiQtQzB0FLq0OIYpadnyuHTPT0mSFs2hE9KCGaakEvL5Kd8/JUlalLy9z6ET0kIZpqRSNtMTB1JIkhbC7X3SUhimpJImPHAghSSpYm7vk5bEMCWVlF/o+7WBSpJUEbf3SUtkmJJK66fhLFC9Ll2KJKnhgjPGDjmSliWVLkDSzG6scZcfSPy+dCmSpEYKxnzm9j5peexMSXXRTwM7VJKkGwrgiUFKWi7DlFQnBipJ0vUFwQ4baad0IVLXuM1PqiO3/EmSrir4mc30P6XLkLrIzpRUR3aoJElXEZwx4S+ly5C6ys6UVGe70Zt1qD4tXYokqWZykPrac1JSOYYpqe4MVJKkdwVjvqCfBqULkbrMbX5S3Z2/h+qX0qVIkmohmNI3SEnl2ZmSmmQ/viPxuHQZkqRiginbbKXt0oVIsjMlNctmegJsk98nIknqFoOUVDN2pqQmyqPT/89zVJLUGQYpqYbsTElNdD46/cfSpUiSFs4gJdWUnSmp6Q7iOfAMv54lqY0MUlKNufiS2sDx6ZLURnlq31baK12IpPdzm5/UBnl8+hcEL3A4hSQ1X/Ca4E8GKane7ExJbXMQ6wTf26WSpIYKzpjwwPdISfVnmJLayrNUktQ0QTCYBalh6WIkfZzb/KS22kjPGfMZwUvc+idJdRcEe0z42iAlNYffsZa6YD8eAs9I9PDrXpLqJoAnbKSd0oVIuh4XVVKXGKpKCIJj4JTE49LFSKqZ4Azos5lOSpci6fpcTEldZKhatCAYAXskjthIx8D8HNvfC9YlqT7yN1omPHJbn9RcLqKkLjNUVS0vjhJHjNmjn0bv/A4DlaTgNYltt/VJzefiSZKh6vbmIeofb7pQlzFQSV01n9b3yLHnUju4aJJ07iDWgW+B+3h/+Ji8lS/xL8a8eG8X6jIGKqlrAthmI22XLkRSdVwsSXrXbvS4y9+Ae3ar3nG9LtRlDFSqRuDXaJ15NkpqMW++ki53EOsEW+RgtUo37xsBDIH9G3WhLmOg0m0EZySekrvJm3Tz67Ou5t1rz0ZJLeZNV9LVHcR9gnt0I1idByg4vnUX6jIGKt1EcPabF7zuRo8VnhN804Gvz7rLW/rG7FT6zRdJteONVtLN5I7VPeArEmuzjzb5nhKzH49J/MSYw6UeEM+B6hnN/jfUsrwdpC7K23TXcahMCW7pkzrGG6yk2ztfvH0FfN6AcBUXfjYATkicMOa46HeR9+Mhie+p77+b6iD4hQkPrrRYz5M6N0ms43W1SPOXc2/78l2pW7yxSqrebqyywhpT1rnDHwh6FwIWLO/eE2/9/5Apx9zhFDhlzKB2W3AMVLpM8DMT/nzt63a+BdBzVVUzREkd5w1V0nLsxirQY4UeU9aAT2ZbkPLH8xmP28mHvUfAgCkj7nBK8CsTBsCwdsHpQwxUelcAvzC+QZC6KIeqx7NzVT28xm7ifEuwIUrqPG+ikupjN3rAKitvgtUq8Z6QlQNTXlCOGQK07nzCbqxxlx9I/L50KSouCPbYTP1KP6sv676u+XS+Hcbste6eI+lGvHlKUl3ls2g/kPi0dCkqJpiyzdYCX/SaX9b9kLwFEFwbvG2+le+QCfuN6XBLWgpvmJJUZwaqLgvgydLeUXQ+SGY+sAK6u07IXSjYAw7dyifpQ7p6k5Sk5sgDPb4H/rd0KVqS4D/Ag2KL+Hy26h6wBbWfzlmVHKCCQxJ7BihJV9H2G6MktYfvouqCmL1D6s+1OZMzD1bBvZZ1rObTPocER9iBknQDbbgZSlJ3HMR9gn+67a+V8qCJCU9qey4nd0nXgXXgS5rVtZpP4RuROJ69XPfftQmtkhqpCTc/SdJFnqNqo+Wej6rKxXfKpVm4+u1rDkqsMy6+lHsEDIETggFTTgxPkqpkmJKkpnLbXxsEwZAJD+inQeliKjEPWPA58CnBH2bj13sf+BPXuX7ffhH3/KM5NAUD4BX5Bd0GJ0kL5wNYkprsINYJvrdL1UhBsMOE7dpu66taPn/VAyDokfgd0zfvUvvkg38u8SvBr0CQeEUQ3OHV7D1zo878+0mqHcOUJDVd7gQ8B77F+3oT5G4U9B14IEnN5kNXktrCLlUTdK8bJUktZpiSpLbxLFUdBTAgeGI3SpLawwetJLVRPpvyHNjEe31JMRvFvd24SX2SpI/yAStJbbYfD4Fns2lq3vOXyy19ktRyPlglqQsMVcsUwCFjnjqaW5LazQeqJHWJoWqRguAY2PZclCR1gw9SSeoiQ1VVYvbjMYYoSeocH6CS1GX78ZDEt8AaPhOuYz5YYocxe27nk6Ru8sEpScrvqIKH5Ol/4PPhQ/KIczhizI6DJSSp23xYSpLOxpm8SQAAAPxJREFU7UaPu6zbrfqN3IWCPeDQrXySpDkfkpKk99uNNVZ4TPBlB89WnZ+FCvaYcmQXSpL0ti49GCVJN5W3Ad4HviR3rKB9z5B5B2pggJIkXUXbHoSSpEU73wr4DcE6idXZrzTtmTLvPg2AExJHjBkYoCRJV9W0B58kqW5y1+pz4CuCtdmWwLm6PGfmwSl3nuCU4JgpJ4YnSdJN1eUhJ0lqi91YZYU1zgNWD+hd6GBB9c+feM/HhuQte68IBkw5pZ8GFf+9kqQOM0xJkpbjPGStMmWNO6wCnxCzkHXe0eq9/xMAOSBlwZDEiCkj4BWJVwSvmXIKjOw4SZIW7b8xLvfX4jJMeQAAAABJRU5ErkJggg==", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "2", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [207.5, 154.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [115, 115, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.858, 0.858, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [115, 115, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.309, 0.309, 0.309], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 46.561, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 63.123, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 79.683, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 96.244, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 112.805, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 130, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 146.561, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 163.123, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 179.683, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 196.244, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.966, 0.966, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 212.805, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 230, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 246.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 263.121, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 279.683, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 296.878, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 313.439, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 330, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 346.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 363.121, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 379.683, "s": [115, 115, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 396.878, "s": [100, 100, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 397, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 413.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 430.121, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 446.683, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 463.878, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 480.439, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 497, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 513.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 530.121, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.966, 0.966, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 546.683, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 563.878, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 580.438, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 596.999, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 613.56, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 630.755, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 647.316, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 663.878, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 680.438, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 696.999, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 713.56, "s": [115, 115, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 730.755, "s": [100, 100, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 731, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 747.561, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 764.756, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 781.318, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 797.879, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 814.439, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 831, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 847.561, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 865, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 881.561, "s": [115, 115, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 899, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 915, "s": [115, 115, 100]}, {"t": 923.000037594561, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 101.5], [-24.5, 153], [-17.5, 160], [-15.832, 158.548], [43.688, 106.707], [44.5, 106]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34, 108], [-24.5, 153], [139, 324], [139, 321.391], [139, 228.271], [139, 227]], "c": true}]}, {"t": 18.000000733155, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34, 108], [-24.5, 153], [141, 332], [438, 27.5], [383.5, -16], [139, 227]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 0, "op": 1281.0000521762, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "1", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 42.062, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [425.5, 425.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [115, 115, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.858, 0.858, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [115, 115, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.309, 0.309, 0.309], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 0.972]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 46.561, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 0.446]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0.028]}, "t": 63.123, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1.852]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0.577]}, "t": 79.683, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, -0.099]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, -0.921]}, "t": 96.244, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 1.313]}, "t": 112.805, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 130, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 146.561, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 163.123, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 179.683, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 196.244, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.966, 0.966, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 212.805, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 230, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 246.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 263.121, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 279.683, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 296.878, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 313.439, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 330, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 346.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 363.121, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 379.683, "s": [115, 115, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 396.878, "s": [100, 100, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 397, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 413.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 430.121, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 446.683, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 463.878, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 480.439, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 497, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 513.56, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 530.121, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.966, 0.966, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 546.683, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 563.878, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 580.438, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 596.999, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 613.56, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 630.755, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 647.316, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 663.878, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 680.438, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 696.999, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 713.56, "s": [115, 115, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 730.755, "s": [100, 100, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 731, "s": [100, 100, 100]}, {"i": {"x": [0.648, 0.648, 0.648], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 747.561, "s": [115, 115, 100]}, {"i": {"x": [0.663, 0.663, 0.663], "y": [1, 1, 1]}, "o": {"x": [0.329, 0.329, 0.329], "y": [0, 0, 0]}, "t": 764.756, "s": [100, 100, 100]}, {"i": {"x": [0.668, 0.668, 0.668], "y": [0.968, 0.968, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 781.318, "s": [115, 115, 100]}, {"i": {"x": [0.672, 0.672, 0.672], "y": [1.014, 1.014, 1]}, "o": {"x": [0.337, 0.337, 0.337], "y": [-0.033, -0.033, 0]}, "t": 797.879, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [0.931, 0.931, 1]}, "o": {"x": [0.342, 0.342, 0.342], "y": [0.015, 0.015, 0]}, "t": 814.439, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 831, "s": [100, 100, 100]}, {"i": {"x": [0.677, 0.677, 0.677], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 847.561, "s": [115, 115, 100]}, {"i": {"x": [0.685, 0.685, 0.685], "y": [1.01, 1.01, 1]}, "o": {"x": [0.349, 0.349, 0.349], "y": [-0.074, -0.074, 0]}, "t": 865, "s": [100, 100, 100]}, {"i": {"x": [0.725, 0.725, 0.725], "y": [1, 1, 1]}, "o": {"x": [0.363, 0.363, 0.363], "y": [0.012, 0.012, 0]}, "t": 881.561, "s": [115, 115, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 899, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 915, "s": [115, 115, 100]}, {"t": 923.000037594561, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 1281.0000521762, "st": 0, "bm": 0}], "markers": []}