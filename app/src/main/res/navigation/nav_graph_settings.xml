<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_host_settings"
    app:startDestination="@id/settingsFragment">

    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.duaag.android.settings.fragments.SettingsFragment"
        android:label="SettingsFragment"
        tools:layout="@layout/fragment_settings">
        <action
            android:id="@+id/action_settingsFragment_to_changePasswordFragment"
            app:destination="@id/changePasswordFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_settingsFragment_to_accountSettingsFragment"
            app:destination="@id/accountSettingsFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_settingsFragment_to_notificationSettingsFragment"
            app:destination="@id/notificationSettingsFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_welcomeScreen2"
            app:destination="@id/welcomeScreen2"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_guidelinesDialogFragment"
            app:destination="@id/guidelinesDialogFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_verifyPhoneFragment"
            app:destination="@id/verifyPhoneFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_settingsFragment_to_verifyEmailFragment"
            app:destination="@id/verifyEmailFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_accountVerificationFragment"
            app:destination="@id/accountVerificationFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_socialMediaFragment"
            app:destination="@id/socialMediaFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_socialMediaFragment2"
            app:destination="@id/socialMediaFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"
            />
        <action
            android:id="@+id/action_settingsFragment_to_loginInfoBottomSheetFragment"
            app:destination="@id/loginInfoBottomSheetFragment" />
        <action
            android:id="@+id/action_settingsFragment_to_premiumSettingsFragment"
            app:destination="@id/premiumSettingsFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_settingsFragment_to_appearanceSettingsFragment"
            app:destination="@id/appearanceSettingsFragment" />
        <action
            android:id="@+id/action_settingsFragment_to_helpScreen"
            app:destination="@id/helpScreen" />
        <action
            android:id="@+id/action_settingsFragment_to_blockContactsFragment"
            app:destination="@id/blockContactsFragment" />
    </fragment>
    <fragment
        android:id="@+id/changePasswordFragment"
        android:name="com.duaag.android.settings.fragments.InputCurrentPasswordFragment"
        android:label="Change password"
        tools:layout="@layout/fragment_input_current_password" >
        <action
            android:id="@+id/action_changePasswordFragment_to_inputNewPasswordFragment"
            app:destination="@id/inputNewPasswordFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/inputNewPasswordFragment"
        android:name="com.duaag.android.settings.fragments.InputNewPasswordFragment"
        android:label="InputNewPasswordFragment"
        tools:layout="@layout/fragment_input_new_password">
        <action
            android:id="@+id/action_inputNewPasswordFragment_to_verifyNewPasswordFragment"
            app:destination="@id/verifyNewPasswordFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/verifyNewPasswordFragment"
        android:name="com.duaag.android.settings.fragments.VerifyNewPasswordFragment"
        android:label="VerifyNewPasswordFragment"
        tools:layout="@layout/fragment_verify_new_password">
        <action
            android:id="@+id/action_verifyNewPasswordFragment_to_settingsFragment"
            app:destination="@id/settingsFragment"
            app:enterAnim="@android:anim/slide_in_left"
            app:exitAnim="@android:anim/slide_out_right"
            app:launchSingleTop="true"
            app:popEnterAnim="@android:anim/slide_in_left"
            app:popExitAnim="@android:anim/slide_out_right"
            app:popUpTo="@id/nav_host_settings"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/accountSettingsFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.AccountSettingsFragment"
        android:label="fragment_account_settings"
        tools:layout="@layout/fragment_account_settings" >
        <action
            android:id="@+id/action_accountSettingsFragment_to_deleteAccountPermanently"
            app:destination="@id/deleteAccountPermanently" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_change_Login_InputCurrentPasswordFragment"
            app:destination="@id/change_Login_InputCurrentPasswordFragment" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_lookingForFriends"
            app:destination="@id/lookingForFriends" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_downloadDataFragment"
            app:destination="@id/downloadDataFragment" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_changeNameFragment"
            app:destination="@id/changeNameFragment" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_changeGenderFragment"
            app:destination="@id/changeGenderFragment" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_changeBirthdayFragment"
            app:destination="@id/changeBirthdayFragment" />
        <action
            android:id="@+id/action_accountSettingsFragment_to_communityFragmentSettings"
            app:destination="@id/communityFragmentSettings" />
    </fragment>
    <fragment
        android:id="@+id/notificationSettingsFragment"
        android:name="com.duaag.android.settings.fragments.notifications.NotificationSettingsFragment"
        android:label="NotificationSettingsFragment"
        tools:layout="@layout/notification_settings_fragment">
        <action
            android:id="@+id/action_notificationSettingsFragment_to_pushNotificationFragment"
            app:destination="@id/pushNotificationFragment" />
    </fragment>
    <fragment
        android:id="@+id/pushNotificationFragment"
        android:name="com.duaag.android.settings.fragments.notifications.pushnotification.PushNotificationFragment"
        android:label="PushNotificationFragment"
        tools:layout="@layout/push_notification_fragment"/>
    <fragment
        android:id="@+id/welcomeScreen2"
        android:name="com.duaag.android.signup.fragment.WelcomeScreenFragment"
        android:label="WelcomeScreen"
        tools:layout="@layout/fragment_welcome_screen"/>

    <fragment
        android:id="@+id/helpScreen"
        android:name="com.duaag.android.settings.fragments.help_center.HelpCenterFragment"
        android:label="HelpScreen"
        tools:layout="@layout/fragment_help_center"/>
    <dialog
        android:id="@+id/guidelinesDialogFragment"
        android:name="com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment"
        android:label="GuidelinesDialogFragment"
        tools:layout="@layout/guidelines_dialog"/>
    <fragment
        android:id="@+id/deleteAccountReasons"
        android:name="com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountReasonsFragment"
        android:label="DeleteAccountReasons"
        tools:layout="@layout/delete_reasons_fragment">
        <action
            android:id="@+id/action_deleteAccountReasons_to_confirmDeletionFragment"
            app:destination="@id/confirmDeletionFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/verifyCodeFragment2"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyAccountCodeFragment"
        android:label="VerifyCodeFragment"
        tools:layout="@layout/fragment_verify_code">
        <action
            android:id="@+id/action_verifyCodeFragment2_to_accountVerificationFragment"
            app:destination="@id/accountVerificationFragment"
            app:popUpTo="@+id/settingsFragment" />
    </fragment>
    <fragment
        android:id="@+id/verifyPhoneFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyPhoneFragment"
        android:label="VerifyPhoneFragment"
        tools:layout="@layout/fragment_verify_phone">
        <action
            android:id="@+id/action_verifyPhoneFragment_to_verifyCodeFragment2"
            app:destination="@id/verifyCodeFragment2" />
    </fragment>
    <fragment
        android:id="@+id/verifyEmailFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyEmailFragment"
        android:label="VerifyEmailFragment"
        tools:layout="@layout/fragment_verify_email">
        <action
            android:id="@+id/action_verifyEmailFragment_to_verifyCodeFragment2"
            app:destination="@id/verifyCodeFragment2" />
    </fragment>
    <fragment
        android:id="@+id/accountVerificationFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.AccountVerificationFragment"
        android:label="AccountVerificationFragment"
        tools:layout="@layout/fragment_account_verification">
        <action
            android:id="@+id/action_accountVerificationFragment_to_settingsFragment"
            app:destination="@id/settingsFragment"
            app:popUpToInclusive="true"/>
        <action
            android:id="@+id/action_accountVerificationFragment_to_verifyEmailFragment"
            app:destination="@id/verifyEmailFragment" />
        <action
            android:id="@+id/action_accountVerificationFragment_to_verifyPhoneFragment"
            app:destination="@id/verifyPhoneFragment" />
    </fragment>
    <fragment
        android:id="@+id/deleteAccountPermanently"
        android:name="com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountPermanentlyFragment"
        android:label="DeleteAccountPermanently">

        <action
            android:id="@+id/action_deleteAccountPermanently_to_deleteAccountReasons"
            app:destination="@id/deleteAccountReasons"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/socialMediaFragment"
        android:name="com.duaag.android.settings.fragments.SocialMediaFragment"
        android:label="socialMediaFragment"
        tools:layout="@layout/social_media_fragment">
    </fragment>
    <dialog
        android:id="@+id/loginInfoBottomSheetFragment"
        android:name="com.duaag.android.login.fragments.LoginInfoBottomSheetFragment"
        android:label="LoginInfoBottomSheetFragment" />

    <fragment
        android:id="@+id/change_Login_InputCurrentPasswordFragment"
        android:name="com.duaag.android.settings.fragments.InputCurrentPasswordFragment"
        android:label="Change LoginMethod"
        tools:layout="@layout/fragment_input_current_password" >
        <argument
            android:name="isChangeLogin"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="isPhone"
            app:argType="boolean"
            android:defaultValue="false"/>
        <action
            android:id="@+id/action_change_Login_InputCurrentPasswordFragment_to_change_Login_VerifyPhoneFragment"
            app:destination="@id/change_Login_VerifyPhoneFragment" />
        <action
            android:id="@+id/action_change_Login_InputCurrentPasswordFragment_to_change_Login_verifyEmailFragment"
            app:destination="@id/change_Login_verifyEmailFragment" />
    </fragment>

    <fragment
        android:id="@+id/change_Login_VerifyPhoneFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyPhoneFragment"
        android:label="Change LoginMethod"
        tools:layout="@layout/fragment_verify_phone">

        <argument
            android:name="isChangeLogin"
            app:argType="boolean"
            android:defaultValue="false" />
        <action
            android:id="@+id/action_change_Login_VerifyPhoneFragment_to_change_Login_verifyCodeFragment"
            app:destination="@id/change_Login_verifyCodeFragment" />
    </fragment>

    <fragment
        android:id="@+id/change_Login_verifyEmailFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyEmailFragment"
        android:label="Change LoginMethod"
        tools:layout="@layout/fragment_verify_email">
        <argument
            android:name="isChangeLogin"
            app:argType="boolean"
            android:defaultValue="false" />
        <action
            android:id="@+id/action_change_Login_verifyEmailFragment_to_change_Login_verifyCodeFragment"
            app:destination="@id/change_Login_verifyCodeFragment" />

    </fragment>

    <fragment
        android:id="@+id/change_Login_verifyCodeFragment"
        android:name="com.duaag.android.settings.fragments.verify_account.VerifyAccountCodeFragment"
        android:label="Change LoginMethod"
        tools:layout="@layout/fragment_verify_code">

        <argument
            android:name="isChangeLogin"
            app:argType="boolean"
            android:defaultValue="false" />
        <action
            android:id="@+id/action_change_Login_verifyCodeFragment_to_accountSettingsFragment"
            app:destination="@id/accountSettingsFragment"
            app:popUpTo="@+id/settingsFragment"/>
    </fragment>
    <fragment
        android:id="@+id/lookingForFriends"
        android:name="com.duaag.android.settings.fragments.account_settings.delete_account.ShowFriendsFragment"
        android:label="LookingForFriends" >
    </fragment>
    <fragment
        android:id="@+id/premiumSettingsFragment"
        android:name="com.duaag.android.settings.fragments.premium.PremiumSettingsFragment"
        android:label="PremiumSettingsFragment"
        tools:layout="@layout/fragment_premium_settings">
        <action
            android:id="@+id/action_premiumSettingsFragment_to_subscriptionAlreadyExistsBottomSheet"
            app:destination="@id/subscriptionAlreadyExistsBottomSheet" />
        <action
            android:id="@+id/action_premiumSettingsFragment_to_redeemCodeBottomSheet"
            app:destination="@id/redeemCodeBottomSheet" />

    </fragment>
    <fragment
        android:id="@+id/downloadDataFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.download_data.DownloadDataFragment"
        android:label="DownloadDataFragment"
        tools:layout="@layout/download_data_fragment"/>
    <fragment
        android:id="@+id/changeNameFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeNameFragment"
        android:label="fragment_change_name"
        tools:layout="@layout/fragment_change_name" />
    <fragment
        android:id="@+id/changeGenderFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeGenderFragment"
        android:label="fragment_change_gender"
        tools:layout="@layout/fragment_change_gender" />
    <fragment
        android:id="@+id/changeBirthdayFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeBirthdayFragment"
        android:label="fragment_change_birthday"
        tools:layout="@layout/fragment_change_birthday" />
    <fragment
        android:id="@+id/appearanceSettingsFragment"
        android:name="com.duaag.android.settings.fragments.appearance.AppearanceSettingsFragment"
        android:label="AppearanceSettingsFragment"
        tools:layout="@layout/fragment_appearance_settings"/>
    <fragment
        android:id="@+id/communityFragmentSettings"
        android:name="com.duaag.android.settings.fragments.CommunitySettingsFragment"
        android:label="CommunityFragmentSettings"
        tools:layout="@layout/fragment_community_settings"/>
    <dialog
        android:id="@+id/subscriptionAlreadyExistsBottomSheet"
        android:name="com.duaag.android.settings.fragments.SubscriptionAlreadyExistsBottomSheet"
        android:label="SubscriptionAlreadyExistsBottomSheet" />
    <dialog
        android:id="@+id/redeemCodeBottomSheet"
        android:name="com.duaag.android.settings.fragments.premium.RedeemCodeBottomSheet"
        android:label="RedeemCodeBottomSheet"
        tools:layout="@layout/fragment_redeem_code_bottom_sheet"/>
    <fragment
        android:id="@+id/blockContactsFragment"
        android:name="com.duaag.android.settings.fragments.block_contacts.presentation.BlockContactsFragment"
        android:label="BlockContactsFragment"
        tools:layout="@layout/fragment_block_contacts">
        <action
            android:id="@+id/action_blockContactsFragment_to_blockAllContactsBottomSheet"
            app:destination="@id/blockAllContactsBottomSheet" />
    </fragment>
    <dialog
        android:id="@+id/blockAllContactsBottomSheet"
        android:name="com.duaag.android.settings.fragments.block_contacts.presentation.BlockAllContactsBottomSheet"
        android:label="BlockAllContactsBottomSheet"
        tools:layout="@layout/block_all_contacts_bottom_sheet"/>
    <fragment
        android:id="@+id/confirmDeletionFragment"
        android:name="com.duaag.android.settings.fragments.account_settings.delete_account.ConfirmDeletionFragment"
        android:label="ConfirmDeletionFragment"
        tools:layout="@layout/confirm_deletion_fragment">
        <action
            android:id="@+id/action_confirmDeletionFragment_to_accountSettingsFragment"
            app:destination="@id/accountSettingsFragment"
            app:popUpTo="@+id/accountSettingsFragment"
            app:popUpToInclusive="true"/>
    </fragment>
</navigation>