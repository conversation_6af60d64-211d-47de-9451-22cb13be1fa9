<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/image_verification"
    app:startDestination="@id/showPoseFragment">

    <fragment
        android:id="@+id/showPoseFragment"
        android:name="com.duaag.android.image_verification.fragments.ShowPoseFragment"
        android:label="ShowPoseFragment"
        tools:layout="@layout/show_pose_fragment">
        <action
            android:id="@+id/action_showPoseFragment_to_cameraImageVerificationFragment"
            app:destination="@id/cameraImageVerificationFragment" />
    </fragment>
    <fragment
        android:id="@+id/cameraImageVerificationFragment"
        android:name="com.duaag.android.image_verification.fragments.CameraImageVerificationFragment"
        android:label="CameraImageVerificationFragment"
        tools:layout="@layout/camera_image_verification_fragment">
        <action
            android:id="@+id/action_cameraImageVerificationFragment_to_isPoseRightDialogFragment"
            app:destination="@id/isPoseRightDialogFragment" />
    </fragment>
    <dialog
        android:id="@+id/isPoseRightDialogFragment"
        android:name="com.duaag.android.image_verification.fragments.IsPoseRightDialogFragment"
        android:label="IsPoseRightDialogFragment"
        tools:layout="@layout/is_pose_right_dialog"/>
</navigation>