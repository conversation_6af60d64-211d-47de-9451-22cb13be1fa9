<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_sign_up"
    app:startDestination="@id/inputPhoneFragment">

    <fragment
        android:id="@+id/signUpNameFragment"
        android:name="com.duaag.android.signup.fragment.SignUpNameFragment"
        android:label="SignUpNameFragment"
        tools:layout="@layout/fragment_sign_up_name">
        <action
            android:id="@+id/action_signUpNameFragment_to_birthdayFragment"
            app:destination="@id/birthdayFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>


    </fragment>
    <fragment
        android:id="@+id/genderFragment"
        android:name="com.duaag.android.signup.fragment.GenderFragment"
        android:label="GenderFragment"
        tools:layout="@layout/fragment_gender_screen">

        <action
            android:id="@+id/action_genderFragment_to_signUpNameFragment2"
            app:destination="@id/signUpNameFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/birthdayFragment"
        android:name="com.duaag.android.signup.fragment.BirthdayFragment"
        android:label="fragment_birthday_screen_"
        tools:layout="@layout/fragment_birthday_screen" >

        <action
            android:id="@+id/action_birthdayFragment_to_notificationPermissionStepFragment2"
            app:destination="@id/notificationPermissionStepFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_birthdayFragment_to_locationPermissionStepFragment"
            app:destination="@id/locationPermissionStepFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"
            app:popUpTo="@id/birthdayFragment"
            app:popUpToInclusive="false" />
        <action
            android:id="@+id/action_birthdayFragment_to_loveStoriesSignUpFragment2"
            app:destination="@id/loveStoriesSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/uploadPhotosFragment"
        android:name="com.duaag.android.signup.fragment.UploadPhotosFragment"
        android:label="UploadPhotosFragment"
        tools:layout="@layout/fragment_upload_photos">
        <action
            android:id="@+id/action_uploadPhotosFragment_to_imageDeniedFragment"
            app:destination="@id/imageDeniedFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_uploadPhotosFragment_to_allSetSignUpFragment"
            app:destination="@id/allSetSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>

    </fragment>
    <fragment
        android:id="@+id/inputPhoneFragment"
        android:name="com.duaag.android.signup.fragment.InputPhoneFragment"
        android:label="InputPhoneFragment"
        tools:layout="@layout/fragment_input_phone_v2">
        <action
            android:id="@+id/action_inputPhoneFragment_to_inputPasswordSignUpFragment"
            app:destination="@id/inputPasswordSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/verifyCodeFragment"
        android:name="com.duaag.android.signup.fragment.VerifyCodeFragment"
        android:label="VerifyCodeFragment"
        tools:layout="@layout/fragment_verify_code">
        <action
            android:id="@+id/action_verifyCodeFragment_to_welcomeNewProfileFragment2"
            app:destination="@id/welcomeNewProfileFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/locationPermissionStepFragment"
        android:name="com.duaag.android.onboarding_permissions.LocationPermissionStepFragment"
        tools:layout="@layout/fragment_location_permission_step" >
        <action
            android:id="@+id/action_locationPermissionStepFragment_to_loveStoriesSignUpFragment"
            app:destination="@id/loveStoriesSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"
            app:popUpTo="@id/birthdayFragment"
            app:popUpToInclusive="false"/>
    </fragment>

    <fragment
        android:id="@+id/notificationPermissionStepFragment"
        android:name="com.duaag.android.onboarding_permissions.NotificationPermissionStepFragment"
        tools:layout="@layout/fragment_notification_permission_step" >
        <action
            android:id="@+id/action_notificationPermissionStepFragment_to_loveStoriesSignUpFragment"
            app:destination="@id/loveStoriesSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_notificationPermissionStepFragment_to_locationPermissionStepFragment2"
            app:destination="@id/locationPermissionStepFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"
            app:popUpTo="@id/notificationPermissionStepFragment"
            app:popUpToInclusive="true"/>
    </fragment>
    <fragment
        android:id="@+id/imageDeniedFragment"
        android:name="com.duaag.android.signup.fragment.ImageDeniedNewFragment"
        android:label="image_denied_fragment"
        tools:layout="@layout/fragment_image_denied_sign_up" >
        <argument
            android:name="arg_image_key"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/welcomeNewProfileFragment"
        android:name="com.duaag.android.signup.fragment.WelcomeNewProfileFragment"
        tools:layout="@layout/fragment_welcome_new_profile" >

        <action
            android:id="@+id/action_welcomeNewProfileFragment_to_genderFragment2"
            app:destination="@id/genderFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_welcomeNewProfileFragment_to_signUpAppendEmailFragment"
            app:destination="@id/signUpAppendEmailFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/verifyProfileWithBadge2PopUp"
        android:name="com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp"
        android:label="VerifyProfileWithBadge2PopUp"
        tools:layout="@layout/fragment_verify_profile_with_badge2_pop_up" >
        <argument
            android:name="pictureKey"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="" />
        <argument
            android:name="userGender"
            app:argType="string"
            app:nullable="true"
           />
        <action
            android:id="@+id/action_verifyProfileWithBadge2PopUp_to_imageDeniedFragmentLiveness"
            app:destination="@id/imageDeniedFragmentLiveness"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
        <action
            android:id="@+id/action_verifyProfileWithBadge2PopUp_to_allSetSignUpFragment"
            app:destination="@id/allSetSignUpFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/verifyAccountFaceLivenessFragment"
        android:name="com.duaag.android.aws_liveness.presentation.fragment.VerifyAccountFaceLivenessFragment"
        android:label="VerifyAccountFaceLivenessFragment" >

        <action
            android:id="@+id/action_verifyAccountFaceLivenessFragment_to_faceMissMatchLivenessFragment"
            app:destination="@id/faceMissMatchLivenessFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"
            app:popUpTo="@id/verifyAccountFaceLivenessFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/faceMissMatchLivenessFragment"
        android:name="com.duaag.android.aws_liveness.presentation.fragment.FaceMissMatchLivenessFragment"
        android:label="fragment_face_miss_match_liveness"
        tools:layout="@layout/fragment_face_miss_match_liveness" >
        <argument
            android:name="faceScanReferenceImage"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="profilePhotoImage"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/action_faceMissMatchLivenessFragment_to_imageDeniedFragmentLiveness"
            app:destination="@id/imageDeniedFragmentLiveness"
            app:popUpTo="@id/faceMissMatchLivenessFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/imageDeniedFragmentLiveness"
        android:name="com.duaag.android.aws_liveness.presentation.fragment.ImageDeniedFragmentLiveness"
        android:label="ImageDeniedFragment"
        tools:layout="@layout/fragment_image_denied_sign_up">
        <argument
            android:name="arg_reason"
            app:argType="string" />
        <argument
            android:name="arg_image_key"
            app:argType="string" />
    </fragment>


    <action
        android:id="@+id/action_global_verifyProfileWithBadge2PopUp"
        app:destination="@id/verifyProfileWithBadge2PopUp"
        app:enterAnim="@anim/enter_from_right"
        app:exitAnim="@anim/exit_to_left"
        app:popEnterAnim="@anim/enter_from_left"
        app:popExitAnim="@anim/exit_to_right"/>


    <action
        android:id="@+id/action_global_livenessProfileVerification"
        app:destination="@id/verifyAccountFaceLivenessFragment"
        app:enterAnim="@anim/enter_from_right"
        app:exitAnim="@anim/exit_to_left"
        app:popEnterAnim="@anim/enter_from_left"
        app:popExitAnim="@anim/exit_to_right"/>
    <fragment
        android:id="@+id/loveStoriesSignUpFragment"
        android:name="com.duaag.android.signup.fragment.LoveStoriesSignUpFragment"
        android:label="LoveStoriesSignUpFragment"
        tools:layout="@layout/fragment_love_stories_sign_up">
        <action
            android:id="@+id/action_loveStoriesSignUpFragment_to_uploadPhotosFragment2"
            app:destination="@id/uploadPhotosFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/allSetSignUpFragment"
        android:name="com.duaag.android.signup.fragment.AllSetSignUpFragment"
        android:label="AllSetSignUpFragment"
        tools:layout="@layout/fragment_all_set_sign_up"/>
    <fragment
        android:id="@+id/signUpAppendEmailFragment"
        android:name="com.duaag.android.signup.fragment.SignUpAppendEmailFragment"
        android:label="SignUpAppendEmailFragment"
        tools:layout="@layout/fragment_sign_up_email_append">
        <action
            android:id="@+id/action_signUpAppendEmailFragment_to_appendEmailVerifyCodeFragment"
            app:destination="@id/appendEmailVerifyCodeFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/appendEmailVerifyCodeFragment"
        android:name="com.duaag.android.signup.fragment.AppendEmailVerifyCodeFragment"
        android:label="AppendEmailVerifyCodeFragment"
        tools:layout="@layout/fragment_verify_code">
        <action
            android:id="@+id/action_appendEmailVerifyCodeFragment_to_genderFragment"
            app:destination="@id/genderFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/inputPasswordSignUpFragment"
        android:name="com.duaag.android.signup.fragment.InputPasswordSignUpFragment"
        android:label="InputPasswordSignUpFragment"
        tools:layout="@layout/fragment_input_password">
    <action
            android:id="@+id/action_inputPasswordSignUpFragment_to_verifyCodeFragment"
            app:destination="@id/verifyCodeFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
</navigation>