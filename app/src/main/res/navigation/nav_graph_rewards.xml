<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_call"
    app:startDestination="@id/getRewardsFragment">

    <fragment
        android:id="@+id/getRewardsFragment"
        android:name="com.duaag.android.rewards.GetRewardsFragment"
        android:label="GetRewardsFragment"
        tools:layout="@layout/get_rewards_from_profile">
        <action
            android:id="@+id/action_getRewardsFragment_to_freeStarterExperienceBottomSheetFragment"
            app:destination="@id/freeStarterExperienceBottomSheetFragment" />
    </fragment>

    <dialog
        android:id="@+id/freeStarterExperienceBottomSheetFragment"
        android:name="com.duaag.android.rewards.FreeStarterExperienceBottomSheetFragment"
        android:label="FreeStarterExperienceBottomSheetFragment"
        tools:layout="@layout/fragment_free_starter_experience_bottomsheet"/>

</navigation>