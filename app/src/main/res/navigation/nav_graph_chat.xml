<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_chat"
    app:startDestination="@id/userFeedFragment">

    <fragment
        android:id="@+id/userFeedFragment"
        android:name="com.duaag.android.user_feed.UserFeedFragment"
        android:label="user_feed_fragment"
        tools:layout="@layout/user_feed_fragment" >
        <action
            android:id="@+id/action_userFeedFragment_to_conversationFragment3"
            app:destination="@id/conversationFragment3" />
        <action
            android:id="@+id/action_userFeedFragment_to_revealHerPopUp"
            app:destination="@id/revealHerPopUp" />
    </fragment>
    <fragment
        android:id="@+id/conversationFragment3"
        android:name="com.duaag.android.chat.fragments.ConversationFragment"
        android:label="ConversationFragment" >
        <argument
            android:name="conversationModel"
            app:argType="com.duaag.android.chat.model.ConversationModel"
            app:nullable="true" />

        <argument
            android:name="rmodModel"
            app:argType="com.duaag.android.recommender.domain.model.RmodModel"
            app:nullable="true" />
    </fragment>
    <dialog
        android:id="@+id/revealHerPopUp"
        android:name="com.duaag.android.chat.fragments.RevealHerPopUp"
        android:label="fragment_reveal_her_pop_up"
        tools:layout="@layout/fragment_reveal_her_pop_up" >
        <action
            android:id="@+id/action_revealHerPopUp_to_conversationFragment3"
            app:destination="@id/conversationFragment3" />
    </dialog>

</navigation>