<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_edit_profile"
    app:startDestination="@id/editProfileFragment">
    <fragment
        android:id="@+id/editProfileFragment"
        android:name="com.duaag.android.profile_new.editprofile.EditProfileFragment"
        android:label="EditProfileFragment"
        tools:layout="@layout/fragment_edit_profile">
        <action
            android:id="@+id/action_editProfileFragment_to_additionalInfoFragment"
            app:destination="@id/lookingForFragment" />
        <action
            android:id="@+id/action_editProfileFragment_to_myInformationFragment"
            app:destination="@id/activitiesFragment" />
        <action
            android:id="@+id/action_editProfileFragment_to_jobEducationFragment"
            app:destination="@id/jobEducationFragment" />
        <action
            android:id="@+id/action_editProfileFragment_to_profileProgressFragment"
            app:destination="@id/profileProgressFragment" />
        <action
            android:id="@+id/action_editProfileFragment_to_zodiacFragment"
            app:destination="@id/zodiacFragment" />
    </fragment>
    <fragment
        android:id="@+id/lookingForFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_lookingForFragment_to_addUserHeightBottomSheet2"
            app:destination="@id/addUserHeightBottomSheet2"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/smokingFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment2"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_smokingFragment_to_petsFragment"
            app:destination="@id/petsFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>

    <fragment
        android:id="@+id/petsFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment3"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_petsFragment_to_religionFragment"
            app:destination="@id/religionFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_petsFragment_to_searchFragmentAdditionalinfo"
            app:destination="@id/searchFragmentAdditionalinfo"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/religionFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment4"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_religionFragment_to_childrenFragment"
            app:destination="@id/haveChildrenFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
        <action
            android:id="@+id/action_religionFragment_to_searchFragmentAdditionalinfo"
            app:destination="@id/searchFragmentAdditionalinfo"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>

    <fragment
        android:id="@+id/haveChildrenFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment5"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_haveChildrenFragment_to_wantChildrenFragment"
            app:destination="@id/wantChildrenFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right" />
    </fragment>
    <fragment
        android:id="@+id/wantChildrenFragment"
        android:name="com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment"
        android:label="AdditionalInfoFragment6"
        tools:layout="@layout/fragment_additional_info">
        <argument
            android:name="Type"
            app:argType="string" />
        <action
            android:id="@+id/action_wantChildrenFragment_to_editProfileFragment"
            app:destination="@id/editProfileFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/nav_graph_edit_profile"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/activitiesFragment"
        android:name="com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment"
        android:label="activitiesFragment"
        tools:layout="@layout/fragment_my_information_searchable">
        <action
            android:id="@+id/action_activitiesFragment_to_hometownFragment"
            app:destination="@id/hometownFragment" />
        <action
            android:id="@+id/action_activitiesFragment_to_searchFragment"
            app:destination="@id/searchFragment" />
        <action
            android:id="@+id/action_activitiesFragment_to_languagesFragment"
            app:destination="@id/languagesFragment" />
    </fragment>
    <fragment
        android:id="@+id/hometownFragment"
        android:name="com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment"
        android:label="hometownFragment"
        tools:layout="@layout/fragment_my_information_searchable">
        <action
            android:id="@+id/action_hometownFragment_to_languagesFragment"
            app:destination="@id/languagesFragment" />
        <action
            android:id="@+id/action_hometownFragment_to_searchFragment"
            app:destination="@id/searchFragment" />
    </fragment>
    <fragment
        android:id="@+id/languagesFragment"
        android:name="com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment"
        android:label="languagesFragment"
        tools:layout="@layout/fragment_my_information_searchable">
        <action
            android:id="@+id/action_languagesFragment_to_descriptionFragment"
            app:destination="@id/descriptionFragment" />
        <action
            android:id="@+id/action_languagesFragment_to_searchFragment"
            app:destination="@id/searchFragment" />
    </fragment>
    <fragment
        android:id="@+id/jobEducationFragment"
        android:name="com.duaag.android.profile_new.editprofile.my_information.MyInformationFragment"
        android:label="jobEducationFragment"
        tools:layout="@layout/fragment_my_information">
        <action
            android:id="@+id/action_jobEducationFragment_to_activitiesFragment"
            app:destination="@id/activitiesFragment" />
    </fragment>
    <fragment
        android:id="@+id/descriptionFragment"
        android:name="com.duaag.android.profile_new.editprofile.my_information.MyInformationFragment"
        android:label="descriptionFragment"
        tools:layout="@layout/fragment_my_information">
        <action
            android:id="@+id/action_descriptionFragment_to_editProfileFragment"
            app:destination="@id/editProfileFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/nav_graph_edit_profile"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/searchFragment"
        android:name="com.duaag.android.profile_new.editprofile.SearchFragment"
        android:label="SearchFragment"
        tools:layout="@layout/fragment_search"/>
    <fragment
        android:id="@+id/searchFragmentAdditionalinfo"
        android:name="com.duaag.android.profile_new.editprofile.SearchFragment"
        android:label="searchFragmentAdditionalinfo"
        tools:layout="@layout/fragment_search"/>
    <fragment
        android:id="@+id/profileProgressFragment"
        android:name="com.duaag.android.profile_new.editprofile.profile_progress.ProfileProgressFragment"
        android:label="ProfileProgressFragment"
        tools:layout="@layout/fragment_profile_progress">
        <action
            android:id="@+id/action_profileProgressFragment_to_lookingForFragment"
            app:destination="@id/lookingForFragment"
            app:launchSingleTop="false"
            app:popUpTo="@id/editProfileFragment"
            app:popUpToInclusive="false" />
        <action
            android:id="@+id/action_profileProgressFragment_to_jobEducationFragment"
            app:destination="@id/jobEducationFragment"
            app:launchSingleTop="false"
            app:popUpTo="@id/editProfileFragment"
            app:popUpToInclusive="false" />
    </fragment>
    <fragment
        android:id="@+id/zodiacFragment"
        android:name="com.duaag.android.profile_new.editprofile.zodiac_sign.ZodiacFragment"
        android:label="fragment_zodiac"
        tools:layout="@layout/fragment_zodiac" />

    <fragment
        android:id="@+id/addUserHeightBottomSheet2"
        android:name="com.duaag.android.profile_new.editprofile.AddUserHeightFragment"
        android:label="AddUserHeightFragment"
        tools:layout="@layout/add_user_height_fragment">
        <action
            android:id="@+id/action_addUserHeightBottomSheet2_to_smokingFragment"
            app:destination="@id/smokingFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

</navigation>