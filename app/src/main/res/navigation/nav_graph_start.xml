<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_start"
    app:startDestination="@id/start2Fragment">

    <fragment
        android:id="@+id/start2Fragment"
        android:name="com.duaag.android.login.fragments.StartFragment"
        android:label="fragment_start2"
        tools:layout="@layout/fragment_start_fragment" >
        <action
            android:id="@+id/action_start2Fragment_to_signInFragment"
            app:destination="@id/signInFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>

    <fragment
        android:id="@+id/forgotPass6DigitCodeFragmentV2"
        android:name="com.duaag.android.login.fragments.forgotpass.ForgotPass6DigitCodeFragment"
        android:label="ForgotPassSetEmailFragmentV2"
        tools:layout="@layout/fragment_forgot_pass_6_digit_code">

    </fragment>

    <fragment
        android:id="@+id/forgotPassNewPassFragmentV2"
        android:name="com.duaag.android.login.fragments.forgotpass.ForgotPassNewPassFragment"
        android:label="ForgotPassSetEmailFragmentV2"
        tools:layout="@layout/fragment_forgotpass_new_password">
        <action
            android:id="@+id/action_forgotPassNewPassFragmentV2_to_forgotPass6DigitCodeFragmentV2"
            app:destination="@id/forgotPass6DigitCodeFragmentV2"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/signInFragment"
        android:name="com.duaag.android.login.fragments.SignInFragment"
        android:label="SignInFragment"
        tools:layout="@layout/fragment_sign_in">
        <action
            android:id="@+id/action_signInFragment_to_forgotPasswordFragment"
            app:destination="@id/forgotPasswordFragment"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
    <fragment
        android:id="@+id/forgotPasswordFragment"
        android:name="com.duaag.android.login.fragments.forgotpass.ForgotPasswordFragment"
        android:label="ForgotPasswordFragment"
        tools:layout="@layout/fragment_forgot_pass">
        <action
            android:id="@+id/action_forgotPasswordFragment_to_forgotPassNewPassFragmentV2"
            app:destination="@id/forgotPassNewPassFragmentV2"
            app:enterAnim="@anim/enter_from_right"
            app:exitAnim="@anim/exit_to_left"
            app:popEnterAnim="@anim/enter_from_left"
            app:popExitAnim="@anim/exit_to_right"/>
    </fragment>
</navigation>