package com.duaag.android.signup.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.LoveStoryCardItemBinding
import com.duaag.android.signup.models.LoveStoryCardItem
import com.duaag.android.utils.bindImage

class LoveStoryCardAdapter(private val items:List<LoveStoryCardItem>) : RecyclerView.Adapter<LoveStoryCardAdapter.LoveStoryCardViewHolder>() {

    inner class LoveStoryCardViewHolder(private var binding: LoveStoryCardItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: LoveStoryCardItem) {
            binding.title.text = item.title
            binding.description.text = item.description
            binding.names.text = item.names

            bindImage(binding.image, item.modeImage)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LoveStoryCardViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = LoveStoryCardItemBinding.inflate(layoutInflater, parent, false)
        return LoveStoryCardViewHolder(binding)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: LoveStoryCardViewHolder, position: Int) {
        holder.bind(items[position])
    }
}