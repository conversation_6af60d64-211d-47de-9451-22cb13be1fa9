package com.duaag.android.signup.fragment


import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentInputPasswordBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.SignUpAuthResult
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.createBlacklistDialog
import com.duaag.android.utils.isPasswordValid
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setTextColorRes
import com.duaag.android.utils.showErrorDialog
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject

class InputPasswordSignUpFragment : Fragment() {

    companion object {
        fun newInstance(): InputPasswordSignUpFragment = InputPasswordSignUpFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentInputPasswordBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentInputPasswordBinding.inflate(inflater)

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        sendNewPasswordScreenViewEvent()

        enableCreateAccountButton(sharedViewModel.password.value ?: "")

        val authMethodIntent = requireActivity().intent.getSerializableExtra(SignUpActivity.AUTH_METHOD_EXTRA) as? AuthMethod
        val isSigningUpWithThirdParty = authMethodIntent == AuthMethod.GOOGLE || authMethodIntent == AuthMethod.FACEBOOK
            val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.PHONE.value

            sendClevertapEvent(
                ClevertapEventEnum.SIGN_UP_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium)
            )

            sendUxCamEvent(
                ClevertapEventEnum.SIGN_UP_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium)
            )

        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.let {

            binding.passwordInput.setText(sharedViewModel.password.value)

            it.passwordInput.onKeyboardDone {
                it.createAccount.performClick()
            }

            it.passwordInput.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                        clearPasswordErrorMessage()
                }

                override fun afterTextChanged(s: Editable?) {
                    val password = s.toString()

                    sharedViewModel.setPassword(it.passwordInput.text.toString())

                    enableCreateAccountButton(password)
                }
            })


            it.passwordInput.setOnFocusChangeListener { _, hasFocus ->
                it.passwordInfoText.isVisible = hasFocus
            }
        }

        binding.createAccount.setOnSingleClickListener(1000L) {
            logSignUpEvent(AuthMethod.PHONE, FirebaseAnalyticsEventsName.SIGN_UP)

            val password = sharedViewModel.password.value ?: ""
            validateInput(password)
        }

        sharedViewModel.signUpResultLiveData.observe(viewLifecycleOwner) {
            when (it) {
                null -> {
                }
                is SignUpAuthResult.Success -> {
                    binding.createAccount.isEnabled = true
                    val action =
                        InputPasswordSignUpFragmentDirections.actionInputPasswordSignUpFragmentToVerifyCodeFragment()
                    findNavController().navigate(action)
                }
                is SignUpAuthResult.UserExists -> {
                    errorDialog()
                    binding.createAccount.isEnabled = true
                    binding.createAccount.isClickable = true
                    sendThisAccountExistEvent()
                }
                is SignUpAuthResult.Loading -> {
                    binding.createAccount.isEnabled = false
                    binding.createAccount.isClickable = false
                }
                is SignUpAuthResult.Error -> {
                    binding.createAccount.isEnabled = true
                    binding.createAccount.isClickable = true
                    val visitorId = sharedViewModel.visitorId
                    when (it.e) {
                        is UserLambdaValidationException -> {
                            when {
                                it.e.message?.contains("account_is_blacklisted") == true -> {
                                    //SIGN UP ERROR
                                    val string =
                                        "Account ID: ${sharedViewModel.phoneEmail.value?.lowercase()} \n\n\nReason (provide reason):\n"
                                    createBlacklistDialog(
                                        requireActivity(),
                                        string
                                    )
                                }
                                it.e.message?.contains("account_device_is_blacklisted") == true -> {
                                    //SIGN UP ERROR
                                    val string =
                                        "Account ID: ${sharedViewModel.phoneEmail.value}\nDevice ID: $visitorId\n\n\nReason (provide reason):\n"
                                    createBlacklistDialog(
                                        requireActivity(),
                                        string
                                    )
                                }
                                it.e.message?.contains("device_is_blacklisted") == true -> {
                                    //SIGN UP ERROR
                                    val string =
                                        "Device ID: $visitorId\n\n\nReason (provide reason):\n"
                                    createBlacklistDialog(
                                        requireActivity(),
                                        string
                                    )
                                }
                                it.e.message?.contains("CustomMessage") == true -> {
                                    val substringMessage = it.e.errorMessage.substringAfter(getString(R.string.limit_delimiter))
                                    showErrorDialog(requireContext(), substringMessage.trim())
                                }
                                it.e.message?.contains("token_not_accepted") == true -> {
                                    blockedDialog()
                                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                                    logError(ErrorStatus.TOKEN_NOT_ACCEPTED)

                                }
                                else -> {
                                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                                    logError(ErrorStatus.CREATE_ACCOUNT)
                                }
                            }
                        }
                        else -> {
                            ToastUtil.toast(getString(R.string.smthg_went_wrong))
                            logError(ErrorStatus.CREATE_ACCOUNT)

                        }
                    }

                }

                is SignUpAuthResult.CustomSmsVerification -> {
                    sharedViewModel.onCustomSmsVerificationUsed(true)

                    val action =
                        InputPasswordSignUpFragmentDirections.actionInputPasswordSignUpFragmentToVerifyCodeFragment()
                    findNavController().navigate(action)
                }
                else -> {}
            }
        }

    }

    private fun validateInput(password: String) {
        if (isPasswordValid(password)) {
            binding.createAccount.isEnabled = false
            (activity as SignUpActivity).executeSignUpReCaptcha(AuthMethod.PHONE)
        } else {
            if (!isPasswordValid(password)) {
                setPasswordError()
            }
        }
    }

    private fun enableCreateAccountButton(password: String) {
        val isEnabled = password.isNotEmpty()
        binding.createAccount.isEnabled = isEnabled
    }

    private fun setPasswordError() {
        binding.passwordInfoText.setTextColorRes(R.color.red_500)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(),  R.drawable.error_corners_12dp)
        sendClevertapEvent(ClevertapEventEnum.WRONG_PASSWORD_FORMAT,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
        sendUxCamEvent(ClevertapEventEnum.WRONG_PASSWORD_FORMAT,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
    }


    private fun clearPasswordErrorMessage() {
        binding.passwordInfoText.setTextColorRes(R.color.gray_200)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }

    @SuppressLint("InflateParams")
    private fun blockedDialog() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.RECAPTCHA_BLOCKED_USER)
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setMessage(getString(R.string.blocked_suspicious_activity))
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }

    @SuppressLint("InflateParams")
    private fun errorDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle(R.string.onboarding_code_error_alert_title)
            setMessage(getString(R.string.account_already_exists_phone))
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }
    private fun sendThisAccountExistEvent() {
        sendClevertapEvent(
            ClevertapEventEnum.THIS_ACCOUNT_EXIST_POP_UP, mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to ClevertapSignUpOrSignInMediumValues.PHONE.value))
        sendUxCamEvent(
            ClevertapEventEnum.THIS_ACCOUNT_EXIST_POP_UP, mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to ClevertapSignUpOrSignInMediumValues.PHONE.value))
    }

    private fun sendNewPasswordScreenViewEvent() {
        val signUpOrSignInMedium = ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName
        val signUpOrSignInmMediumValue = ClevertapSignUpOrSignInMediumValues.PHONE.value

        val onboardingType = ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName
        val onboardingValue = ClevertapOnboardingTypeValues.SIGN_UP.value

        val properties = mutableMapOf<String, Any?>()
        properties[signUpOrSignInMedium] = signUpOrSignInmMediumValue
        properties[onboardingType] = onboardingValue

        sendClevertapEvent(ClevertapEventEnum.NEW_PASSWORD_SCREENVIEW, properties, false)
        sendUxCamEvent(ClevertapEventEnum.NEW_PASSWORD_SCREENVIEW, properties)
    }

    override fun onResume() {
        super.onResume()
        binding.passwordInput.requestFocus()
        binding.passwordInput.showKeyboard()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
    }
}
