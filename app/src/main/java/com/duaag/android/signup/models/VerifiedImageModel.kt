package com.duaag.android.signup.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class VerifiedImageModel(
    @SerializedName("result")
    val images: List<ImageResult>
): Parcelable

@Parcelize
data class ImageResult(
    @SerializedName("isValid")
    var isValid: Boolean,
    @SerializedName("key")
    val key: String,
    @SerializedName("location")
    var location: String? = null,
    @SerializedName("willBadge2BeRemoved")
    var willBadge2BeRemoved: Boolean? = false,
    @SerializedName("invalidReason")
    val invalidReason: String? = null,
    @IgnoredOnParcel
    var isFromFacebook: Boolean = false,
    @IgnoredOnParcel
    var isEmpty: Boolean = false
): Parcelable {
    companion object {
        fun getEmptyItem(): ImageResult {
            return ImageResult(
                isValid = false,
                key = "",
                location = null,
                willBadge2BeRemoved = null,
                isFromFacebook = false,
                invalidReason = null,
                isEmpty = true
            )
        }
    }

    fun getInvalidReason() = InvalidProfileImageReason.fromValue(invalidReason)
}

enum class InvalidProfileImageReason(var value: String?) {
    NO_FACE_DETECTED("no_face_detected"),
    EXPLICIT_NUDITY_DETECTED("explicit_nudity_detected"),
    VIOLENCE_DETECTED("violence_detected"),
    VISUALLY_DISTURBING_DETECTED("visually_disturbing_detected"),
    CHILD_PICTURE_DETECTED("child_picture_detected");

    companion object {
        fun fromValue(value: String?): InvalidProfileImageReason? {
            return values().firstOrNull { it.value == value }

        }
    }
}