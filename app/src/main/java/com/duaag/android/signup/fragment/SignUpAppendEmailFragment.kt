package com.duaag.android.signup.fragment


import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapLogOutTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentSignUpEmailAppendBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.DisablePasteAction
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getHourFromErrorMessage
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showErrorDialog
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import kotlinx.coroutines.launch
import javax.inject.Inject

class SignUpAppendEmailFragment : Fragment(), AccountCreatedDialogFragment.AccountCreatedInterface {

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentSignUpEmailAppendBinding? = null
    private val binding get() = _binding!!

    var beforeTextChanged = ""

    private val pressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true /* enabled by default */) {
        override fun handleOnBackPressed() {
            val dialog = AccountCreatedDialogFragment.newInstance()
            dialog.show(childFragmentManager, "accountCreated")

            sendLogOutInitiatedEvent()
        }
    }

    private val emailTextWatcher = object: TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            if(s == null || _binding == null || s.toString() == beforeTextChanged)
                return

            val email = s.toString()

            sharedViewModel.setAppendEmail(email)

            toggleNextButton(email)

            clearEmailValidationError()
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            beforeTextChanged = s?.toString().orEmpty()
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }
    }

    private fun toggleNextButton(email: String) {
        val isEmpty = email.isEmpty()
        binding.nextBtn.isEnabled = !isEmpty
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        requireActivity().onBackPressedDispatcher.addCallback(this, pressedCallback)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentSignUpEmailAppendBinding.inflate(inflater)

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        binding.nextBtn.setOnSingleClickListener {
            val appendedEmail = sharedViewModel.appendedEmail
            val isValid = isEmailValid(appendedEmail)

            if(isValid) {
                sharedViewModel.addEmailAddress(appendedEmail)
            }
        }

        // this used for disable copy paste
        binding.editTextEmail.customInsertionActionModeCallback = DisablePasteAction()

        binding.editTextEmail.onKeyboardNext {
            binding.nextBtn.performClick()
        }

        binding.editTextEmail.addTextChangedListener(emailTextWatcher)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                binding.editTextEmail.setText(sharedViewModel.appendedEmail)
                toggleNextButton(sharedViewModel.appendedEmail)
            }
        }

        sharedViewModel.addEmailAddress.observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {
                    handleLoadingIndicator(false)
                    findNavController().navigate(R.id.action_signUpAppendEmailFragment_to_appendEmailVerifyCodeFragment)
                }
                is Result.Error -> {
                    handleLoadingIndicator(false)
                    when (it.exception) {
                        is LimitExceededException -> {
                            val message = getHourFromErrorMessage(it.exception.errorMessage)
                            val title = getString(R.string.onboarding_code_limit_alert_title)
                            showErrorDialog(requireContext(), message, title)
                        }
                        is CodeMismatchException -> {
                            val message = getString(R.string.wrong_verification_code)
                            showErrorDialog(requireContext(), message)
                        }
                        else -> {
                            val message = it.exception.message ?: ""
                            showErrorDialog(requireContext(), message)
                        }
                    }
                }
                is Result.Loading -> {
                    clearEmailValidationError()
                    handleLoadingIndicator(true)
                }
                else -> {}
            }
        }

        toggleNextButton(sharedViewModel.appendedEmail)
    }

    private fun handleLoadingIndicator(showLoading: Boolean) {
        if(showLoading) {
            binding.progressBar.visibility = View.VISIBLE
            binding.nextBtn.visibility = View.INVISIBLE
        } else {
            binding.progressBar.visibility = View.GONE
            binding.nextBtn.visibility = View.VISIBLE
        }
    }

    private fun handleEmailValidation(text: String) {
        binding.emailDescription.text = text
        binding.editTextEmail.background = ContextCompat.getDrawable(requireContext(), R.drawable.error_corners_12dp)
        binding.emailDescription.visibility = View.VISIBLE
        binding.editTextEmail.requestFocus()
    }

    private fun clearEmailValidationError() {
        binding.editTextEmail.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
        binding.emailDescription.visibility = View.GONE
    }

    fun isEmailValid(email: String) : Boolean {
        val isEmailValid = com.duaag.android.utils.isEmailValid(email)

        if(!isEmailValid) {
            handleEmailValidation(getString(R.string.email_is_not_valid))
        } else {
            clearEmailValidationError()
        }

        return isEmailValid
    }

    override fun onResume() {
        super.onResume()
        binding.editTextEmail.requestFocus()
        binding.editTextEmail.showKeyboard()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.editTextEmail.removeTextChangedListener(emailTextWatcher)
        _binding = null
    }

    private fun sendLogOutInitiatedEvent() {
        sendClevertapEvent(ClevertapEventEnum.LOG_OUT_INITIATED,
            mapOf(
                ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to ClevertapLogOutTypeValues.BEFORE_CREATING_PROFILE.values,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
        sendUxCamEvent(ClevertapEventEnum.LOG_OUT_INITIATED,
            mapOf(
                ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to ClevertapLogOutTypeValues.BEFORE_CREATING_PROFILE.values,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
    }

    override fun onSignOutClicked() {
        duaAccount.deleteUserDevice { result ->
            if (result) {
                logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.LOG_OUT_BEFORE_CREATING_PROFILE)

                val eventPremiumType = getPremiumTypeEventProperty(sharedViewModel.userRepository.user.value)

                val logOutTypeValues = ClevertapLogOutTypeValues.BEFORE_CREATING_PROFILE.values

                sendClevertapEvent(
                    ClevertapEventEnum.LOG_OUT, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutTypeValues))

                duaAccount.deleteAllData()
                activity?.finish()
            } else {
                ToastUtil.toast(resources.getString(R.string.smthg_went_wrong))
                logError(ErrorStatus.ON_SIGN_OUT_TO_SIGN_UP)
            }
        }
    }
}
