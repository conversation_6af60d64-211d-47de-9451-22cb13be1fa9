package com.duaag.android.signup.fragment


import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserNotFoundException
import com.amazonaws.services.cognitoidentityprovider.model.UsernameExistsException
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ErrorType6DigitCodeValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentVerifyCodeBinding
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.launcher.SplashActivity.Companion.CONFIRMATION_CODE_INTENT_FILTER
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.SignUpAuthResult
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getHourFromErrorMessage
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showErrorDialog
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.PinField
import okhttp3.ResponseBody
import timber.log.Timber
import javax.inject.Inject

class AppendEmailVerifyCodeFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentVerifyCodeBinding? = null
    private val binding get() = _binding!!


    private val deepLinkReceiver = ConfirmCodeBroadcastReceiver()

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
        Timber.tag("VIEWMODEL").d(sharedViewModel.toString())
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentVerifyCodeBinding.inflate(inflater)
        val view = binding.root

        ContextCompat.registerReceiver(
            requireContext(),
            deepLinkReceiver,
            IntentFilter(CONFIRMATION_CODE_INTENT_FILTER),
            ContextCompat.RECEIVER_EXPORTED
        )


        binding.resendBtn.setOnSingleClickListener(1000L) {
            (requireActivity() as SignUpActivity).executeResendCodeReCaptcha()
            sendClevertapEvent(
                ClevertapEventEnum.RESEND_6DIGIT_CODE, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value
                ))
            sendUxCamEvent(
                ClevertapEventEnum.RESEND_6DIGIT_CODE, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value
                ))
        }

        sharedViewModel.elapsedTime.observe(viewLifecycleOwner) {
            val string = "${getString(R.string.onboarding_resend_code)} (${it})"
            binding.timerTextV.text = string
        }
        sharedViewModel.showTimer.observe(viewLifecycleOwner) {
            binding.resendBtn.isVisible = !it
            binding.timerTextV.isVisible = it
        }

        sharedViewModel.doNotShowTimmerIfLimitIsReached.observe(viewLifecycleOwner){
            binding.timerTextV.visibility = View.GONE
            binding.resendBtn.visibility = View.VISIBLE
            sharedViewModel.stopTimer()
        }

        sharedViewModel.verifyDigits.observe(viewLifecycleOwner){
            it?.let {
                binding.modifiedPinView.setText(it)
            }
        }

        sharedViewModel.resendSignUpResultLiveData.observe(viewLifecycleOwner, Observer {
            when (it) {
                is SignUpAuthResult.Success -> {
                    ToastUtil.toast(getString(R.string.verific_code))
                }
                is SignUpAuthResult.Error -> {
                    val errorType = ErrorType6DigitCodeValues.LIMIT_RESEND_EXCEEDED.value
                    sendWrong6DigitCodeEvent(errorType)
                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                    val message = getHourFromErrorMessage(it.e.message!!)
                    showErrorDialog(requireContext(), message)
                    logError(ErrorStatus.RESEND_SIGN_UP_RESULT)
                }
                is SignUpAuthResult.Loading -> return@Observer

                is SignUpAuthResult.CustomSmsVerification -> {
                    ToastUtil.toast(getString(R.string.verific_code))
                    sharedViewModel.onCustomSmsVerificationUsed(true)

                }
                else -> {}
            }
        })

        sharedViewModel.verifyEmailResultLiveData.observe(viewLifecycleOwner) {
            handleResponseEmail(it)
        }

        lifecycleScope.launchWhenResumed {
            binding.modifiedPinView.requestFocus()
            binding.modifiedPinView.showKeyboard()
        }

        send6digitCodeSignUpEvent()


        return view
    }

    private fun handleResponseEmail(it: Result<ResponseBody>) {
        when (it) {
            is Result.Success -> {
                sharedViewModel.saveSignUpPersistStep(
                    SignUpPersistModel(SignUpPersistStepsEnum.EMAIL, sharedViewModel.appendedEmail.lowercase())
                )
                sharedViewModel.sendEmailToClevertap(sharedViewModel.appendedEmail.lowercase())

                findNavController().navigate(R.id.action_appendEmailVerifyCodeFragment_to_genderFragment)
            }
            is Result.Loading -> {
            }

            is Result.Error -> {
                when (it.exception) {
                    is CodeMismatchException -> {
                        showErrorDialog(requireContext(), getString(R.string.code_mismatch_email))
                    }
                    is UserNotFoundException -> {
                        showErrorDialog(requireContext(), getString(R.string.user_can_not_be_found))
                    }
                    is UsernameExistsException -> {
                        showErrorDialog(requireContext(), getString(R.string.email_already_in_use))
                    }
                    is LimitExceededException -> {
                        showErrorDialog(requireContext(), getString(R.string.attempt_limit_reached_try_again_later))
                    }
                    else -> {
                        showErrorDialog(requireContext(), it.exception.message ?: "")
                    }
                }
            }

        }

    }

    private fun sendWrong6DigitCodeEvent(errorType: String?) {
        sendClevertapEvent(
            ClevertapEventEnum.WRONG_6DIGIT_CODE, mapOf(
                ClevertapEventPropertyEnum.ERROR_TYPE.propertyName to errorType,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))
        sendUxCamEvent(
            ClevertapEventEnum.WRONG_6DIGIT_CODE, mapOf(
                ClevertapEventPropertyEnum.ERROR_TYPE.propertyName to errorType,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))
    }

    private fun send6digitCodeSignUpEvent() {
        sendClevertapEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_UP,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName)
        )
        sendUxCamEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_UP,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName)
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val start = getString(R.string.onboarding_code_desc)
        val email = " ${sharedViewModel.appendedEmail.lowercase()}"
        val wordtoSpan: Spannable = SpannableString(start + "\n" + email)
        wordtoSpan.setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.title_primary)), start.length, start.length + email.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.emailDescription.text = wordtoSpan

        //Assign custom listener to our custom pin view
        binding.modifiedPinView.onTextCompleteListener = PinFiledListener()
    }

    inner class PinFiledListener : PinField.OnTextCompleteListener {
        override fun onTextComplete(enteredText: String): Boolean {
            logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.SIGN_UP_CODE)

            sharedViewModel.verifyEmailAddressAPI(enteredText)

            return false
        }

        override fun onTextChange(enteredText: String) {
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            requireActivity().unregisterReceiver(deepLinkReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        _binding = null
    }


    inner class ConfirmCodeBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val code = intent?.getStringExtra(SplashActivity.DEEP_LINK_CONFIRMATION_CODE)
            if(!code.isNullOrEmpty() && code.length == 6)
                binding.modifiedPinView.setText(code)
        }
    }
}
