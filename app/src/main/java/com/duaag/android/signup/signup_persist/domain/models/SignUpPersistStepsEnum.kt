package com.duaag.android.signup.signup_persist.domain.models

import androidx.annotation.StringRes
import com.duaag.android.R

enum class SignUpPersistStepsEnum( @StringRes val step: Int) {
    WELCOME(R.string.pref_dua_sign_up_persist_welcome),
    EMAIL(R.string.pref_dua_sign_up_persist_email),
    GENDER(R.string.pref_dua_sign_up_persist_gender),
    NAME(R.string.pref_dua_sign_up_persist_name),
    BIRTHDAY(R.string.pref_dua_sign_up_persist_birthday),
    LOVE_STORY(R.string.pref_dua_sign_up_persist_love_story),
    UPLOAD_PHOTOS(R.string.pref_dua_sign_up_persist_upload_photos),
    VERIFICATION(R.string.pref_dua_sign_up_persist_verification),
    PROFILE_BUILDER_BE_YOURSELF(R.string.pref_dua_profile_builder_be_yourself),
    PROFILE_BUILDER_CHILDREN_HAVE(R.string.pref_dua_profile_builder_children_have),
    PROFILE_BUILDER_CHILDREN_WANT(R.string.pref_dua_profile_builder_children_want),
    PROFILE_BUILDER_SMOKING(R.string.pref_dua_profile_builder_smoking),
    PROFILE_BUILDER_LANGUAGES(R.string.pref_dua_profile_builder_languages),
    PROFILE_BUILDER_RELIGION(R.string.pref_dua_profile_builder_religion),
    PROFILE_BUILDER_LOOKING_FOR(R.string.pref_dua_profile_builder_looking_for),
    PROFILE_BUILDER_ALL_SET(R.string.pref_dua_profile_builder_all_set);

    companion object {
        fun getAllSteps(): List<SignUpPersistStepsEnum> {
            return entries.filterNot { it.name.startsWith("PROFILE_BUILDER_") }
        }

        fun getAllProfileBuilderSteps(): List<SignUpPersistStepsEnum> {
            return entries.filter { it.name.startsWith("PROFILE_BUILDER_") }
        }

        fun getAllStepsIncludingProfileBuilder(): List<SignUpPersistStepsEnum> {
            return entries.toList()
        }
    }
}