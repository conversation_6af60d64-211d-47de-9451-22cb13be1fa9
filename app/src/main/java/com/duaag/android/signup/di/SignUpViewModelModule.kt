package com.duaag.android.signup.di

import androidx.lifecycle.ViewModel
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.signup.fragment.LoveStorySignUpViewModel
import com.duaag.android.signup.viewmodel.*
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class SignUpViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(SharedSignUpViewModel::class)
    abstract fun bindSharedSignUpViewModel(myViewModel: SharedSignUpViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(ChoosePictureViewModel::class)
    abstract fun bindChoosePicturesViewModel(myViewModel: ChoosePictureViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(LivenessViewModel::class)
    abstract fun bindLivenessViewModelViewModel(myViewModel: LivenessViewModel): ViewModel


    @Binds
    @IntoMap
    @ViewModelKey(LoveStorySignUpViewModel::class)
    abstract fun bindLoveStorySignUpViewModel(myViewModel: LoveStorySignUpViewModel): ViewModel


}