package com.duaag.android.signup.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentWelcomeNewProfileBinding
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.setCustomStyledText
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject


class WelcomeNewProfileFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity } ) { viewModelFactory }

    private var _binding: FragmentWelcomeNewProfileBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sharedSignUpViewModel
        requireActivity().onBackPressedDispatcher.addCallback(this, pressedCallback)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWelcomeNewProfileBinding.inflate(inflater, container, false)

        binding.continueButton.setOnSingleClickListener {
            sharedSignUpViewModel.saveSignUpPersistStep(SignUpPersistModel(SignUpPersistStepsEnum.WELCOME, SignUpPersistStepsEnum.WELCOME.toString()))

            val alreadyHasEmailSet = sharedSignUpViewModel.hasEmailAlreadySet
            val hasPersistedEmail = sharedSignUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.EMAIL)
            if(alreadyHasEmailSet || hasPersistedEmail) {
                findNavController().navigate(R.id.action_welcomeNewProfileFragment_to_genderFragment2)
            } else {
                findNavController().navigate(R.id.action_welcomeNewProfileFragment_to_signUpAppendEmailFragment)
            }
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if ((sharedSignUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.WELCOME))) {
            sharedSignUpViewModel.setSignUpPersistStepAsSkipped(SignUpPersistStepsEnum.WELCOME)

            val alreadyHasEmailSet = sharedSignUpViewModel.hasEmailAlreadySet
            val hasPersistedEmail = sharedSignUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.EMAIL)
            if(alreadyHasEmailSet || hasPersistedEmail) {
                findNavController().navigate(R.id.action_welcomeNewProfileFragment_to_genderFragment2)
            } else {
                findNavController().navigate(R.id.action_welcomeNewProfileFragment_to_signUpAppendEmailFragment)
            }
        }

        sendClevertapEvent(
            ClevertapEventEnum.WELCOME_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
            ))
        sendUxCamEvent(
            ClevertapEventEnum.WELCOME_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
            ))

        binding.firstLine.setCustomStyledText(
            fullText = requireContext().getString(R.string.onboarding_welcome_matches_full),
            targetText = requireContext().getString(R.string.onboarding_welcome_number_matches),
            fontName = "tt_norms_pro_demibold",
            linkColor = ContextCompat.getColor(requireContext(), R.color.red_500),
        )
        binding.secondLine.setCustomStyledText(
            fullText = requireContext().getString(R.string.onboarding_welcome_albanian_full),
            targetText = requireContext().getString(R.string.onboarding_welcome_number_albanian),
            fontName = "tt_norms_pro_demibold",
            linkColor = ContextCompat.getColor(requireContext(), R.color.red_500),
        )

        binding.thirdLine.setCustomStyledText(
            fullText = requireContext().getString(R.string.onboarding_welcome_couples_full),
            targetText = requireContext().getString(R.string.onboarding_welcome_number_couples),
            fontName = "tt_norms_pro_demibold",
            linkColor = ContextCompat.getColor(requireContext(), R.color.red_500),
        )

        binding.fourthLine.setCustomStyledText(
            fullText = requireContext().getString(R.string.onboarding_welcome_weddings_full),
            targetText = requireContext().getString(R.string.onboarding_welcome_number_weddings),
            fontName = "tt_norms_pro_demibold",
            linkColor = ContextCompat.getColor(requireContext(), R.color.red_500),
        )

        binding.fifthLine.setCustomStyledText(
            fullText = requireContext().getString(R.string.onboarding_welcome_reviews_full),
            targetText = requireContext().getString(R.string.onboarding_welcome_number_reviews),
            fontName = "tt_norms_pro_demibold",
            linkColor = ContextCompat.getColor(requireContext(), R.color.red_500),
        )
    }

    private val pressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true /* enabled by default */) {
        override fun handleOnBackPressed() {

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}