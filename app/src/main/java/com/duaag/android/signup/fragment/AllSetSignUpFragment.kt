package com.duaag.android.signup.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.ResourceV2
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentAllSetSignUpBinding
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.TransitionAnimationType
import com.duaag.android.utils.openProfileBuilderActivity
import com.duaag.android.utils.transitionContainers
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AllSetSignUpFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity } ) { viewModelFactory }
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel>({ activity as SignUpActivity }) { viewModelFactory }

    private var _binding: FragmentAllSetSignUpBinding? = null
    private val binding get() = _binding!!

    private var isVerifiedSuccessfully = false

    private val pressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true /* enabled by default */) {
        override fun handleOnBackPressed() {
            if(signUpViewModel.isCreatingProfile.value == true || signUpViewModel.isProfileCreated())
                return
            else
                findNavController().navigateUp()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(this, pressedCallback)

        isVerifiedSuccessfully = arguments?.getBoolean(USER_VERIFIED_SUCCESSFULLY) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAllSetSignUpBinding.inflate(inflater)

        setImageResources()
        handleUIState()

        if(signUpViewModel.isCreatingProfile.value != true && !signUpViewModel.isProfileCreated()) {
            createProfile()
        }

        return binding.root
    }

    private fun setImageResources() {
        val isMale = signUpViewModel.gender.value?.value == GenderType.MAN.value
        if(isMale) {
            binding.verifiedImage.setImageResource(R.drawable.verified_male_illustration)
        } else {
            binding.verifiedImage.setImageResource(R.drawable.verified_female_illustration)
        }
    }

    private fun handleUIState() {
        if(isVerifiedSuccessfully) {
            showVerifiedProfileUI()
            sendVerificationApprovedScreenViewEvent()
        } else {
            showCreatingProfileUI()
            sendProfileCreatingScreenViewEvent()
        }
    }

    private fun showVerifiedProfileUI() {
        binding.profileVerifiedContainer.visibility = View.VISIBLE
        binding.profileCreationContainer.visibility = View.GONE
    }

    private fun showCreatingProfileUI() {
        binding.profileCreationContainer.visibility = View.VISIBLE
        binding.profileVerifiedContainer.visibility = View.GONE
    }


    private fun createProfile() {
        val pictures = choosePicturesViewModel.verifiedPictures.value?.map { it.key }
        pictures?.let {
            signUpViewModel.createProfileAPI(it)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        signUpViewModel.isCreatingProfile.observe(viewLifecycleOwner) { isCreatingProfile ->
            if(isCreatingProfile == true && !signUpViewModel.isProfileCreated()) {
                if(isVerifiedSuccessfully) {
                    transitionContainers(
                        containerToHide = binding.profileVerifiedContainer,
                        containerToShow = binding.profileCreationContainer,
                        animationType = TransitionAnimationType.FADE
                    )
                }
            }
        }

        signUpViewModel.profileCreated.observe(viewLifecycleOwner) {
            when(it) {
                is ResourceV2.Error -> {
                    showCreatingProfileErrorPopUp()

                    sendErrorProfileCreationEvent()

                    logError(ErrorStatus.PROFILE_CREATED)
                }
                is ResourceV2.Success -> {
                    proceedToProfileBuilder()
                }
                null -> {}
            }

        }

        if(signUpViewModel.isProfileCreated()) {
            showCreatingProfileUI()
            proceedToProfileBuilder()
        }
    }

    private fun showCreatingProfileErrorPopUp() {

        val builder = androidx.appcompat.app.AlertDialog.Builder(requireActivity())
        builder.setTitle(R.string.onboarding_code_error_alert_title)
        builder.setMessage(R.string.smthg_went_wrong)
        builder.setPositiveButton(R.string.try_again) { dialogInterface, i ->
            createProfile()
        }
        builder.setCancelable(false)
        builder.show()
    }

    private fun proceedToProfileBuilder() {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            val userModel = signUpViewModel.userRepository.getLoggedInUserModel()
            withContext(Dispatchers.Main) {
                if (userModel != null) {
                    openProfileBuilderActivity(
                        context = <EMAIL>(),
                        userModel = userModel,
                        isSigningUp = true,
                        isSpottedUser = signUpViewModel.isSpottedUser()
                    )
                }
            }
        }
    }

    private fun sendProfileCreatingScreenViewEvent(){
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_CREATING_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
        sendUxCamEvent(ClevertapEventEnum.PROFILE_CREATING_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
    }

    private fun sendVerificationApprovedScreenViewEvent(){
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        sendClevertapEvent(ClevertapEventEnum.VERIFICATION_APPROVED_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
        sendUxCamEvent(ClevertapEventEnum.VERIFICATION_APPROVED_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
    }

    private fun sendErrorProfileCreationEvent(){
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        sendClevertapEvent(ClevertapEventEnum.ERROR_PROFILE_CREATION,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
        sendUxCamEvent(ClevertapEventEnum.ERROR_PROFILE_CREATION,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val USER_VERIFIED_SUCCESSFULLY = "user_verified_successfully"
    }
}