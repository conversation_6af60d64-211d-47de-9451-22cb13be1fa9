package com.duaag.android.signup.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.duaag.android.base.models.TagItem
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class TagDto(
    @SerializedName("tagItemId")
    val tagItemId: Int,
    @SerializedName("tagTypeId")
    val tagTypeId: Int
): Parcelable

fun OnboardingTagModel.Item.toTagDto(): TagDto {
    return TagDto(
        tagItemId = this.tagId,
        tagTypeId = this.tagTypeId
    )
}

fun TagItem.toTagDto(): TagDto {
    return TagDto(
        tagItemId = this.tagItemId,
        tagTypeId = this.tagTypeId
    )
}
