package com.duaag.android.signup.viewmodel

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.UserModel
import com.duaag.android.di.ActivityScope
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.models.ImageModel
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.signup.models.ImageVerificationModel
import com.duaag.android.signup.models.InvalidProfileImageReason
import com.duaag.android.signup.models.LoseBadge2WarningModel
import com.duaag.android.signup.models.PictureModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.signup_persist.domain.use_cases.SaveSignUpPersistStepUseCase
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getFileSize
import com.duaag.android.utils.livedata.SingleLiveData
import com.duaag.android.utils.removeMetadataFromImage
import com.google.gson.Gson
import com.google.mlkit.vision.text.Text
import id.zelory.compressor.Compressor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.Random
import java.util.UUID
import javax.inject.Inject


@ActivityScope
class ChoosePictureViewModel @Inject constructor(
    val userRepository: UserRepository,
    val duaSharedPrefs: DuaSharedPrefs,
    private val saveSignUpPersistStepUseCase: SaveSignUpPersistStepUseCase
) : ViewModel() {

    private var isSignUp: Boolean = false

    private var _selectedItems: MutableLiveData<List<ImageModel>> = MutableLiveData(emptyList())
    val selectedItems: LiveData<List<ImageModel>>
        get() = _selectedItems

    private var _verifiedPictures: MutableLiveData<List<ImageResult>> = MutableLiveData(emptyList())
    val verifiedPictures: LiveData<List<ImageResult>>
        get() = _verifiedPictures

    private val _imageVerificationFailed: SingleLiveData<ImageResult> = SingleLiveData()
    val imageVerificationFailed: LiveData<ImageResult>
        get() = _imageVerificationFailed

    private val _openPremiumActivity: SingleLiveData<Void> = SingleLiveData()
    val openPremiumActivity: LiveData<Void>
        get() = _openPremiumActivity

    private val _uploadProgress = MutableLiveData<Int>()
    val uploadProgress: LiveData<Int>
        get() = _uploadProgress

    private val _uploadFinished: SingleLiveData<Void> = SingleLiveData()
    val uploadFinished: LiveData<Void>
        get() = _uploadFinished

    private val _showBadge2Dialog: SingleLiveData<LoseBadge2WarningModel> = SingleLiveData()
    val showBadge2Dialog: LiveData<LoseBadge2WarningModel>
        get() = _showBadge2Dialog

    private val _imageCropped: SingleLiveData<Uri> = SingleLiveData()
    val imageCropped: LiveData<Uri>
        get() = _imageCropped

    private val _imageChosen: SingleLiveData<Intent> = SingleLiveData()
    val imageChosen: LiveData<Intent>
        get() = _imageChosen

    private val _imageVerified: SingleLiveData<BooleanArray> = SingleLiveData()
    val imageVerified: LiveData<BooleanArray>
        get() = _imageVerified

    private val _imageDetectionIsCompleted: SingleLiveData<Intent> = SingleLiveData()
    val imageDetectionIsCompleted: LiveData<Intent>
        get() = _imageDetectionIsCompleted


    // This livedata contains the first value of UserModel that we fetch from database when initializing the ViewModel!
    // NU -> Not updated / Not updatable
    private var _userProfileNU: MutableLiveData<UserModel> = MutableLiveData()
    val userProfileNU: LiveData<UserModel>
        get() = _userProfileNU


    //this will contain the uri's of images that are queued for detection
    private var faceDetectionQueue = mutableListOf<Uri>()
    private var textDetectionQueue = mutableListOf<Uri>()

    companion object {
        const val UPLOAD_TAG = "S3UPLOAD"
        const val MAX_IMAGES_PREMIUM = 6
        const val MAX_IMAGES_FREEMIUM = 6

        const val MAX_WORDS_ALLOWED = 5

        const val TAG = "ChoosePictureViewModel"
    }

    init {
        getUserFromDb()
    }

    fun setIsSignUp(isSignUp: Boolean) {
        this.isSignUp = isSignUp
    }

    val user = userRepository.user

    fun onNewRecentItemClicked(item: ImageModel) {
        if (item.isSelected) {
            val sItems: ArrayList<ImageModel> =
                _selectedItems.value?.filter { it.url != item.url } as ArrayList<ImageModel>
            _selectedItems.value = sItems
        } else {
            if (selectedItemSize() >= getUploadMaximumImagesCount()) {
                if (DuaApplication.instance.getBillingAvailable() &&
                    DuaApplication.instance.shouldShowPremium &&
                    getUploadMaximumImagesCount() == MAX_IMAGES_FREEMIUM
                ) {
                    _openPremiumActivity.value = null
                } else {
                    val maxImages = getUploadMaximumImagesCount().toString()
                    ToastUtil.toast(
                        DuaApplication.instance.getString(
                            R.string.cant_more_select,
                            maxImages
                        )
                    )
                }
            }
        }
    }

    fun addNewImage(uri: Uri, imageMetaData: ImageMetaDataResultModel?) {
        val item = ImageModel(
            id = Random().nextLong(), uri.toString(),
            isSelected = true,
            photoScreenshot =  imageMetaData?.photoScreenshot ?: false,
            isPhotoNotFromCamera = imageMetaData?.isPhotoNotFromCamera ?: false
        )

        var sItems = ArrayList<ImageModel>()
        if (_selectedItems.value != null) {
            sItems = ArrayList<ImageModel>(_selectedItems.value!!)
        }
        sItems.add(item)
        _selectedItems.value = sItems
    }

    fun addFacebookImageToLists(image: ImageModel) {
        val selectedItems = _selectedItems.value.orEmpty().let { ArrayList<ImageModel>(it) }
        selectedItems.add(image)
        _selectedItems.value = selectedItems
    }

    fun addImageInFaceDetectionQueue(uri: Uri) {
        faceDetectionQueue.add(uri)
    }

    fun addImageInTextDetectionQueue(uri: Uri) {
        textDetectionQueue.add(uri)
    }

    fun setFaceDetected(faceDetected: Boolean) {
        faceDetectionQueue.lastOrNull()?.let { item ->
            _selectedItems.value?.find { it.url == item.toString() }?.apply {
                this.faceDetected = faceDetected
                if(!faceDetected){
                    this.isVerified = true
                    this.isValid = false
                }
            }

            faceDetectionQueue.remove(item)
        }

        checkIfImageDetectionIsCompleted()
    }

    fun setTextDetected(text: Text) {
        val textDetected = text.text.split(" ", "\n").size >= MAX_WORDS_ALLOWED

        Timber.tag("TEXTDETECTION").d("setTextDetected: ${text.text}")
        Timber.tag("TEXTDETECTION").d("setTextDetected-words: ${text.text.split(" ", "\n").size}")

        textDetectionQueue.lastOrNull()?.let { item ->
            _selectedItems.value?.find { it.url == item.toString() }?.textDetected = textDetected
            textDetectionQueue.remove(item)
        }

        checkIfImageDetectionIsCompleted()
    }

    fun checkIfImageDetectionIsCompleted() {
        if(textDetectionQueue.isEmpty() && faceDetectionQueue.isEmpty()) {
            _imageDetectionIsCompleted.call()

            val lastImage = _selectedItems.value?.lastOrNull()
            if(lastImage?.faceDetected == false)
                _imageVerificationFailed.value = ImageResult(
                    isValid = lastImage.isValid,
                    key = lastImage.url!!,
                    location = null,
                    willBadge2BeRemoved = null,
                    invalidReason = InvalidProfileImageReason.NO_FACE_DETECTED.value,
                    isFromFacebook = false,
                    isEmpty = false
                )
        }
    }

    fun removeLastItemFromFaceDetectionQueue() {
        faceDetectionQueue.removeLastOrNull()
    }

    fun removeLastItemFromTextDetectionQueue() {
        textDetectionQueue.removeLastOrNull()
    }

    private fun getUserFromDb() {
        viewModelScope.launch(Dispatchers.IO) {
            _userProfileNU.postValue(userRepository.getLoggedInUserModel())
        }
    }

    fun selectedItemSize(): Int {
        return if (_selectedItems.value != null) {
            _selectedItems.value?.size!!
        } else {
            0
        }
    }


    fun uploadSelectedImages() {
        uploadImage(_selectedItems.value!!.lastIndex)
    }


    private fun uploadImage(imagePosition: Int) {

        val chosenImage = _selectedItems.value!![imagePosition]
        if (chosenImage.alreadyExists || chosenImage.isVerified || !chosenImage.faceDetected) {
            if (imagePosition + 1 == _selectedItems.value!!.size) {
                _uploadProgress.value = 100
                Timber.tag(UPLOAD_TAG).d("Upload Completed!")
                afterUploadImages()
                return
            } else {
                Timber.tag(UPLOAD_TAG).d("Image nr${imagePosition} uploaded")
                uploadImage(imagePosition + 1)
                return
            }
        }

        val image = File(chosenImage.url!!.substringAfter(":"))

        viewModelScope.launch(Dispatchers.IO) {
            try {
                val compressedImageFile = Compressor.compress(DuaApplication.instance, image)
                Timber.tag("COMPRESS").d("Size: ${getFileSize(compressedImageFile.length())}")

                val fileName = AWSInteractor.getResourceUrl(
                    UUID.randomUUID().toString(),
                    compressedImageFile.extension
                )

                removeMetadataFromImage(compressedImageFile.absolutePath)

                AWSInteractor.uploadFile(compressedImageFile, fileName, object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        if (TransferState.COMPLETED === state) {
                            Timber.tag(UPLOAD_TAG).d("State: ${state.name}")
                            //add key to image object to see if they're verified
                            _selectedItems.value!![imagePosition].s3Key = fileName

                            //if all images have been uploaded
                            if (imagePosition + 1 == _selectedItems.value!!.size) {
                                _uploadProgress.postValue(100)
                                Timber.tag(UPLOAD_TAG).d("Upload Completed!")
                                afterUploadImages()
                            } else {
                                Timber.tag(UPLOAD_TAG).d("Image nr${imagePosition} uploaded")
                                uploadImage(imagePosition + 1)
                            }
                        } else if (TransferState.FAILED === state) {
                            _uploadProgress.postValue(-1)
                            ToastUtil.toast("Upload Failed!")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDone =
                            (bytesCurrent.toFloat() / bytesTotal.toFloat() * 100).toInt()
                        _uploadProgress.postValue(((percentDone / selectedItemSize()) + ((100 / selectedItemSize() * imagePosition))))

                        Timber.tag(UPLOAD_TAG).d("ID:$id|bytesCurrent: $bytesCurrent|bytesTotal: $bytesTotal|$percentDone%")
                    }

                    override fun onError(id: Int, ex: Exception) {
                        ex.printStackTrace()
                        _uploadProgress.postValue(-1)
                        ToastUtil.toast("Upload Failed!")
                        ex.printStackTrace()
                    }

                })
            } catch (e: Exception) {
                _uploadProgress.postValue(-1)
                e.printStackTrace()
            }
        }


    }

    fun afterUploadImages() {
        val imagesToVerify = selectedItems.value!!
            .asSequence()
            .filter { !it.alreadyExists && !it.isVerified && it.faceDetected }
            .map { PictureModel(it.textDetected, it.s3Key ?: "", it.photoScreenshot, it.isPhotoNotFromCamera) }.toList()
        if (imagesToVerify.isNotEmpty()) {
            verifyUploadedImages(imagesToVerify)
        } else {
            val verifiedImages = selectedItems.value?.filter { !it.alreadyExists && it.isVerified }?.map { ImageResult(it.isValid, it.s3Key ?: "", it.url) }

            if (!verifiedImages.isNullOrEmpty()) {
                setVerifiedPictures(verifiedImages)
            }

            _uploadFinished.call()
        }
    }

    fun verifyUploadedImages(pictures: List<PictureModel>) {
        Timber.tag(UPLOAD_TAG).d("Key: ${pictures.size}")
        val model = ImageVerificationModel(pictures)

        viewModelScope.launch(Dispatchers.IO) {
            userRepository.verifyUploadedImages(model)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        _uploadFinished.call()
                        ToastUtil.toast(DuaApplication.instance.getString(R.string.an_error_occurred))
                        logError(ErrorStatus.VERIFY_UPLOAD_IMAGES)
                    }
                    ex.printStackTrace()
                }
                .collect { result ->
                    withContext(Dispatchers.Main){
                        when(result) {
                            is Resource.Success -> {
                                if(result.data.images.isNotEmpty())
                                    _imageVerified.value = result.data.images.map { it.isValid }.toBooleanArray()

                                val faceNotDetected = selectedItems.value
                                    ?.filter { !it.faceDetected }
                                    ?.map { ImageResult(false, it.url ?: "", it.url) }

                                val alreadyVerified = selectedItems.value
                                    ?.filter { !it.alreadyExists && it.isVerified && it.faceDetected }
                                    ?.map { ImageResult(it.isValid, it.s3Key ?: "", it.url) }

                                val allPictures = mutableListOf<ImageResult>()
                                alreadyVerified?.let { allPictures.addAll(it) }
                                faceNotDetected?.let { allPictures.addAll(it) }

                                //mark verified pictures as verified
                                selectedItems.value?.forEach { selected ->
                                    if (result.data.images.any { it.key == selected.s3Key })
                                        selected.isVerified = true

                                    if(result.data.images.any { it.key == selected.s3Key && it.isValid})
                                        selected.isValid = true
                                }

                                alreadyVerified?.forEach { verified ->
                                    if (_verifiedPictures.value?.filter { it.isValid }?.map { it.key }?.contains(verified.key) == true)
                                        verified.isValid = true
                                }

                                when {
                                    result.data.images.any { img -> !img.isValid} -> {
                                        _imageVerificationFailed.value = result.data.images[0]
                                    }
                                    result.data.images.any { img -> (img.willBadge2BeRemoved ?: false) } -> {
                                        //make isVerified false so that the pictures are checked again
                                        selectedItems.value?.forEach { it.isVerified = false }

                                        _showBadge2Dialog.value = LoseBadge2WarningModel(result.data, faceNotDetected, alreadyVerified)
                                    }
                                    result.data.images.none { img -> !img.isValid } && faceNotDetected.isNullOrEmpty() -> {
                                        //add new verified keys that came from api
                                        val newImages = result.data.images.toMutableList()
                                        allPictures.addAll(newImages)

                                        setVerifiedPictures(allPictures.filter { it.isValid })
                                    }
                                    else -> {
                                        result.data.images.forEach { verified -> verified.location = selectedItems.value?.firstOrNull { it.s3Key == verified.key }?.url }
                                        allPictures.addAll(result.data.images)

                                        setVerifiedPictures(allPictures.filter { it.isValid })
                                    }
                                }

                                _uploadFinished.call()

                            }
                            is Resource.Loading -> {}
                            is Resource.Error -> {
                                Timber.tag("UPLOAD_IMAGE").d("upload_images ${result.data} | ${result.exception}")
                            }
                        }
                    }
                }
        }
    }

    fun removeVerifiedImages(keys: List<String>) {
        val selectedItemsNew: ArrayList<ImageModel> = ArrayList(_selectedItems.value ?: emptyList())

        selectedItemsNew.removeAll { keys.contains(it.s3Key) }

        _selectedItems.value = selectedItemsNew

        viewModelScope.launch(Dispatchers.IO) {
            userRepository.deleteVerifiedPicture(keys)
                .catch { ex ->
                    ex.printStackTrace()
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {}
                        Resource.Loading -> {}
                        else -> {}
                    }
                }
        }
    }

    fun setVerifiedPictures(images: List<ImageResult>) {
        _verifiedPictures.value = images
        saveSignUpPersistStep(images)
    }

    fun setSelectedPictures(images: List<ImageModel>) {
        _selectedItems.value = images
    }

    fun getUploadMaximumImagesCount(): Int {
        if (!DuaApplication.instance.getBillingAvailable() && !DuaApplication.instance.shouldShowPremium)
            return MAX_IMAGES_FREEMIUM

        val user = user.value
        return if (user?.premiumType != null) {
            MAX_IMAGES_PREMIUM
        } else {
            MAX_IMAGES_FREEMIUM
        }
    }

    fun isUserPremium(): Boolean {
        val user = userRepository.user.value
        return if (user != null)
            user.premiumType != null
        else false
    }

    fun imageCropped(outputUri: Uri) {
        _imageCropped.value = outputUri
    }

    fun imageChosen(data: Intent?) {
        _imageChosen.value = data
    }

    fun clearCurrentSelectedImages() {
        val selectedImages = selectedItems.value?.toMutableList()
        selectedImages?.removeAll{ !it.alreadyExists }

        _selectedItems.value = selectedImages
    }

    fun clearVerifiedImages() {
        _verifiedPictures.value = emptyList()
        saveSignUpPersistStep(null)
    }

    fun clearSpecificVerifiedImage(imageResult: ImageResult) {
        val verifiedImages = _verifiedPictures.value?.toMutableList() ?: mutableListOf()
        verifiedImages.removeIf { it.key == imageResult.key }
        _verifiedPictures.value = verifiedImages
        saveSignUpPersistStep(verifiedImages)
    }

    fun clearNotValidImages() {
        val selectedImages = selectedItems.value?.toMutableList()
        selectedImages?.removeAll{ selected -> !selected.isValid && !selected.alreadyExists}

        _selectedItems.value = selectedImages
        _verifiedPictures.value = emptyList()
        saveSignUpPersistStep(null)
    }


    fun setVerifiedPictureFromLiveness(imagesVerified: List<ImageResult>) {
        if (imagesVerified.isNotEmpty()) {
            val currentPictures = _verifiedPictures.value?.toMutableList() ?: mutableListOf()

            if (currentPictures.isNotEmpty()) {
                currentPictures[0] = imagesVerified[0]
            } else {
                currentPictures.add(imagesVerified[0])
            }

            _verifiedPictures.value = currentPictures
        }
    }

//endregion

   private fun saveSignUpPersistStep(imagesVerified: List<ImageResult>?) {
        viewModelScope.launch {
            if (isSignUp)
            saveSignUpPersistStepUseCase(SignUpPersistModel(SignUpPersistStepsEnum.UPLOAD_PHOTOS,
                imagesVerified?.let { Gson().toJson(imagesVerified) }))
        }
    }
}