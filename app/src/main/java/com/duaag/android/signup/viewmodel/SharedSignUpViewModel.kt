package com.duaag.android.signup.viewmodel

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import androidx.annotation.Nullable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.amazonaws.mobile.client.results.SignInResult
import com.amazonaws.mobile.client.results.SignUpResult
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.services.cognitoidentityprovider.model.AliasExistsException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.amazonaws.services.cognitoidentityprovider.model.UsernameExistsException
import com.applovin.sdk.AppLovinSdk
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.AppsflyerEventsPropertyEnum
import com.duaag.android.appsflyer.domain.AppsFlyerBackendManager
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.aws.FetchUserInfoListener
import com.duaag.android.aws.LogInCallBack
import com.duaag.android.aws.ResendSignUpCallback
import com.duaag.android.aws.SignUpCallback
import com.duaag.android.aws.models.LoginModel
import com.duaag.android.aws.models.SignUpModel
import com.duaag.android.aws.models.VerifyAccountModel
import com.duaag.android.aws_liveness.domain.model.SignUpConfiguration
import com.duaag.android.aws_liveness.domain.usecase.GetUserSignUpConfigurationsUseCase
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapLocationAccessValues
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.clevertap.onLoginCleverTap
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.setUserEmailInClevertap
import com.duaag.android.clevertap.setUserGenderInClevertap
import com.duaag.android.clevertap.updateUserProfileInClevertap
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.ImageUploadFailedException
import com.duaag.android.exceptions.UserDeletedExistException
import com.duaag.android.fingerprint_pro.domain.usecase.VisitorIdUseCase
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.logevents.firebaseanalytics.CommunityChosenTypeEnum
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.logevents.firebaseanalytics.setUserId
import com.duaag.android.login.models.Credentials
import com.duaag.android.login.models.SignUpWithSpottedEntity
import com.duaag.android.login.models.SignUpWithSpottedRawMapper
import com.duaag.android.login.models.ThirdPartyUserData
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationModel
import com.duaag.android.settings.models.AddEmailModel
import com.duaag.android.settings.models.VerifyCodeModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.CreateProfileModel
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.signup.models.ImageVerificationModel
import com.duaag.android.signup.models.LocationModel
import com.duaag.android.signup.models.Picture
import com.duaag.android.signup.models.PictureModel
import com.duaag.android.signup.models.Profile
import com.duaag.android.signup.models.SignUpAuthResult
import com.duaag.android.signup.models.VerifiedImageModel
import com.duaag.android.signup.signup_persist.domain.SignUpPersistManager
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum.*
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.createCalendarFromSpottedBirthday
import com.duaag.android.utils.formatCalendarToUIString
import com.duaag.android.utils.formatUIDateToBackendDate
import com.duaag.android.utils.getFileSize
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.isPostNotificationsPermissionEnabled
import com.duaag.android.utils.livedata.SingleLiveData
import com.duaag.android.utils.location.LocationClientImpl
import com.duaag.android.utils.removeMetadataFromImage
import com.duaag.android.uxcam.sendUxCamEvent
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.common.reflect.TypeToken
import com.google.gson.Gson
import com.uxcam.UXCam
import id.zelory.compressor.Compressor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.util.Locale
import java.util.UUID
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityScope
class SharedSignUpViewModel @Inject constructor(
    val userRepository: UserRepository,
    val notificationRepository: NotificationRepository,
    private val appsFlyerBackendManager: AppsFlyerBackendManager,
    val duaSharedPrefs: DuaSharedPrefs,
    val duaAccount: DuaAccount,
    private val getUserSignUpConfigurationsUseCase: GetUserSignUpConfigurationsUseCase,
    private val visitorIdUseCase: VisitorIdUseCase,
    private val signUpPersistManager: SignUpPersistManager
    ) : ViewModel() {

    companion object {
        const val TAG = "SharedSignUpViewModel"
    }


    var userName: String = UUID.randomUUID().toString()
    val password = MutableLiveData<String>()
    var oldPassword: String? = null

    var phoneEmail = MutableLiveData<String>()
    var oldPhoneEmail: String? = null

    var thirdPartyUserData: ThirdPartyUserData? = null
    var newImagesFromFacebook: List<String>? = null

    var spottedData: SignUpWithSpottedEntity? = null

    private val _spottedProfileLoaded = MutableSharedFlow<SignUpWithSpottedEntity>()
    val spottedProfileLoaded: SharedFlow<SignUpWithSpottedEntity>
        get() = _spottedProfileLoaded.asSharedFlow()

    var firstName: String = ""
        private set

    //This is used for users that we don't have the email yet
    var appendedEmail: String = ""
        private set
    var hasEmailAlreadySet = false
        private set

    var birthday: String? = null
        private set

    var authMethod: AuthMethod? = null
        private set

    var thirdPartyImagePath: File? = null

    var spottedImageFile: File? = null

        private set

    private val _gender = MutableLiveData<GenderType?>()
    val gender: LiveData<GenderType?>
        get() = _gender

    private val _signUpPersistVerifiedImages: MutableStateFlow<List<ImageResult>?> = MutableStateFlow(null)
    val signUpPersistVerifiedImages: StateFlow<List<ImageResult>?>
        get() = _signUpPersistVerifiedImages.asStateFlow()


    private var locationModel: LocationModel? = null

    private var _thirdPartyImageUploaded = SingleLiveData<Result<String>>()
    val thirdPartyImageUploaded: LiveData<Result<String>>
        get() = _thirdPartyImageUploaded

    private var _signUpResultLiveData = SingleLiveData<SignUpAuthResult>()
    val signUpResultLiveData: LiveData<SignUpAuthResult>
        get() = _signUpResultLiveData

    private var _thirdPartyImageResult = SingleLiveData<Result<VerifiedImageModel>>()
    val thirdPartyImageResult: LiveData<Result<VerifiedImageModel>>
        get() = _thirdPartyImageResult

    private var _spottedImageUploaded = SingleLiveData<Result<String>>()
    val spottedImageUploaded: LiveData<Result<String>>
        get() = _spottedImageUploaded

    private var _spottedResult = SingleLiveData<Result<VerifiedImageModel>>()
    val spottedResult: LiveData<Result<VerifiedImageModel>>
        get() = _spottedResult

    private val _useCustomSmsProvider: SingleLiveData<Boolean> = SingleLiveData()

    var selectedCountryCodeAsInt: Int? = null

    private val _elapsedTime: MutableLiveData<String> = MutableLiveData("00:00")
    val elapsedTime: LiveData<String>
        get() = _elapsedTime

    private val _showTimer: MutableLiveData<Boolean> = MutableLiveData(false)
    val showTimer: LiveData<Boolean>
        get() = _showTimer

    private val _profileCreated: MutableLiveData<ResourceV2<Unit>?> = MutableLiveData()
    val profileCreated: LiveData<ResourceV2<Unit>?>
        get() = _profileCreated

    private val _cameraPermission: SingleLiveData<Void> = SingleLiveData()
    val cameraPermission: LiveData<Void>
        get() = _cameraPermission


    private var _resendSignUpResultLiveData = SingleLiveData<SignUpAuthResult>()
    val resendSignUpResultLiveData: LiveData<SignUpAuthResult>
        get() = _resendSignUpResultLiveData

    private val _verifyResultLiveData = SingleLiveData<SignUpAuthResult>()
    val verifyResultLiveData: LiveData<SignUpAuthResult>
        get() = _verifyResultLiveData

    private val _verifyResultCustomSmsLiveData = SingleLiveData<SignUpAuthResult>()
    val verifyResultCustomSmsLiveData: LiveData<SignUpAuthResult>
        get() = _verifyResultCustomSmsLiveData

    private val _verifyDigits = SingleLiveData<String>()
    val verifyDigits: LiveData<String>
        get() = _verifyDigits

    private val _verifyCompleted: SingleLiveData<Void> = SingleLiveData()
    val verifyCompleted: LiveData<Void>
        get() = _verifyCompleted

    private var _doNotShowTimmerIfLimitIsReached: SingleLiveData<Boolean> = SingleLiveData()
    val doNotShowTimmerIfLimitIsReached: LiveData<Boolean>
        get() = _doNotShowTimmerIfLimitIsReached

    private var _onSkipClicked = SingleLiveData<Void>()
    val onSkipClicked: LiveData<Void>
        get() = _onSkipClicked

    private var _isCreatingProfile: MutableLiveData<Boolean> = MutableLiveData(false)
    val isCreatingProfile: LiveData<Boolean?>
        get() = _isCreatingProfile

    private var _signUpConfigurationUiData: MutableStateFlow<SignUpConfigurationUiData> = MutableStateFlow(
        SignUpConfigurationUiData()
    )
    val signUpConfigurationUiData: LiveData<SignUpConfigurationUiData?>
        get() = _signUpConfigurationUiData.asLiveData()

    var visitorId: String = ""
        private set

    private val locationClient by lazy {
        LocationClientImpl(
            DuaApplication.instance,
            LocationServices.getFusedLocationProviderClient(DuaApplication.instance)
        )
    }

    init {
        setupSignUpPersistManager()

        viewModelScope.launch {
            visitorId = visitorIdUseCase.getVisitorId()
        }
    }

    fun isProfileCreated(): Boolean {
        return when(profileCreated.value) {
            is ResourceV2.Error -> false
            is ResourceV2.Success -> true
            null -> false
        }
    }

    fun setResendSignUpResultLiveData(result: SignUpAuthResult) {
        _resendSignUpResultLiveData.value = result
    }

    fun setPhoneNumber(phone: String) {
        phoneEmail.value = phone
    }

    fun setSignUpResultLiveData(result: SignUpAuthResult) {
        _signUpResultLiveData.value = result
    }

    fun setEmailPhone(string: String) {
        phoneEmail.value = string
    }

    fun setPassword(string: String) {
        password.value = string
    }

    fun setAuthMethod(authMethod: AuthMethod) {
        this.authMethod = authMethod
    }

    fun setAuthMethod(authMethod: String) {
        this.authMethod = when(authMethod) {
            AuthMethod.EMAIL.methodName -> AuthMethod.EMAIL
            AuthMethod.PHONE.methodName -> AuthMethod.PHONE
            AuthMethod.FACEBOOK.methodName -> AuthMethod.FACEBOOK
            AuthMethod.GOOGLE.methodName -> AuthMethod.GOOGLE
            else -> null
        }
    }

    fun getClevertapAuthMethodValue(): String? {
        val type = when (authMethod) {
            AuthMethod.EMAIL -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
            AuthMethod.PHONE -> ClevertapSignUpOrSignInMediumValues.PHONE.value
            AuthMethod.GOOGLE -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
            AuthMethod.FACEBOOK -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
            else -> null
        }

        return type
    }

    fun setFirstName(firstName: String) {
        this.firstName = firstName
    }

    fun persistFirstName(firstName: String) {
        saveSignUpPersistStep(SignUpPersistModel(NAME, firstName))
    }

    fun setAppendEmail(email: String) {
        this.appendedEmail = email
    }

    fun setAlreadyEmailSet() {
        this.hasEmailAlreadySet = true
    }

    fun skipTotallyAppendedEmail(email: String) {
        setAppendEmail(email)
        setAlreadyEmailSet()

        val persistModel = SignUpPersistModel(EMAIL, email)
        saveSignUpPersistStep(persistModel)
        setSignUpPersistStepAsSkipped(EMAIL)
    }

    fun setGender(gender: GenderType?) {
        _gender.value = gender
    }

    fun persistGender(gender: GenderType?) {
        saveSignUpPersistStep(SignUpPersistModel(GENDER, gender?.value))
    }

    fun setBirthday(birthDate: String) {
        this.birthday = birthDate
    }

    fun persistBirthday(birthDate: String) {
        saveSignUpPersistStep(SignUpPersistModel(BIRTHDAY, birthDate))
    }

    fun clearThirdPartyGenderPersistence() {
        viewModelScope.launch(Dispatchers.IO) {
            duaSharedPrefs.setThirdPartyAuthGender(null)
        }
    }
    fun clearThirdPartyNamePersistence() {
        viewModelScope.launch(Dispatchers.IO) {
            duaSharedPrefs.setThirdPartyAuthName(null)
        }
    }
    fun clearThirdPartyBirthdayPersistence() {
        viewModelScope.launch(Dispatchers.IO) {
            duaSharedPrefs.setThirdPartyAuthBirthday(null)
        }
    }

    fun setLoveStorySkipped() {
        saveSignUpPersistStep(SignUpPersistModel(LOVE_STORY, "true"))
    }

    fun setHasGrantedCameraPermission(){
        _cameraPermission.call()
    }

    fun signUpUser(reCaptchaToken:String, authMethod: AuthMethod) {
        setAuthMethod(authMethod)

        //generate a neq username only if the email/phone or password has changed
        //otherwise continue to verification code fragment
        if (phoneEmail.value?.lowercase() != oldPhoneEmail?.lowercase() || password.value != oldPassword) {
            userName = UUID.randomUUID().toString()
        } else {
            _signUpResultLiveData.postValue(_signUpResultLiveData.value)
            return
        }

        _signUpResultLiveData.value = SignUpAuthResult.Loading

        val signUpModel = SignUpModel(
            authMethod = authMethod,
            phoneEmail = phoneEmail.value?.lowercase() ?: "",
            username = userName,
            password = password.value ?: "", reCaptchaToken,
            deviceId = visitorId
        )

        AWSInteractor.signUp(signUpModel, object : SignUpCallback {
            override fun onResult(signUpResult: SignUpResult) {
                if (!signUpResult.confirmationState) {
                    oldPhoneEmail = phoneEmail.value?.lowercase()
                    oldPassword = password.value
                    _signUpResultLiveData.postValue(SignUpAuthResult.Success(signUpResult))
                }
            }

            override fun onError(e: Exception) {
                when (e) {
                    is UsernameExistsException -> {
                        _signUpResultLiveData.postValue(SignUpAuthResult.UserExists(e))
                    }
                    is UserLambdaValidationException -> {
                        when {
                            e.message?.contains("custom_sms_verification") == true -> {
                                _signUpResultLiveData.postValue(SignUpAuthResult.CustomSmsVerification)
                            }
                            else -> _signUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                        }
                    }
                    else -> _signUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                }
            }
        })
    }

    fun onCustomSmsVerificationUsed(state: Boolean? = false) {
        _useCustomSmsProvider.value = state
    }

    fun onResendCodeClicked(reCaptchaToken: String) {

        setTimer()
        _resendSignUpResultLiveData.postValue(SignUpAuthResult.Loading)
        AWSInteractor.resendConfirmCode(reCaptchaToken, visitorId, userName, object : ResendSignUpCallback {
            override fun onResult(resendSignUpResult: SignUpResult) {
                _resendSignUpResultLiveData.postValue(SignUpAuthResult.Success(resendSignUpResult))
            }

            override fun onError(e: Exception) {
                when (e) {
                    is UsernameExistsException -> {
                        _resendSignUpResultLiveData.postValue(SignUpAuthResult.UserExists(e))
                    }
                    is UserLambdaValidationException -> {
                        when {
                            e.message?.contains("custom_sms_verification") == true -> {
                                _resendSignUpResultLiveData.postValue(SignUpAuthResult.CustomSmsVerification)
                            }

                            e.errorMessage.contains("Limit reached") ->{
                                _doNotShowTimmerIfLimitIsReached.postValue(true)
                                _resendSignUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                            }

                            else -> _resendSignUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                        }
                    }
                    is LimitExceededException -> {
                        _doNotShowTimmerIfLimitIsReached.postValue(true)
                        _resendSignUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                    }
                    else -> _resendSignUpResultLiveData.postValue(SignUpAuthResult.Error(e))
                }
            }
        })
    }

    private var timer: CountDownTimer? = null
    fun setTimer() {
        if (_showTimer.value == true)
            return
        else {
            createTimer()
            _showTimer.value = true
        }
    }

    private fun createTimer() {
        val triggerTime = System.currentTimeMillis() + 60 * 1000
        timer = object : CountDownTimer(triggerTime - System.currentTimeMillis(), 1000) {
            override fun onTick(p0: Long) {
                var millisUntilFinished = p0
                val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                val string = String.format("%02d:%02d", minutes, seconds)
                _elapsedTime.value = string

                if (millisUntilFinished <= 0) {
                    resetTimer()
                }
            }

            override fun onFinish() {
                resetTimer()
            }


        }
        timer?.start()
    }

    private fun verifyAccountWithCustomSms(code: String) {
        _verifyResultCustomSmsLiveData.postValue(SignUpAuthResult.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val verifyAccountModel = VerifyAccountModel(userName, code)
            userRepository.confirmSignUpCustomSms(verifyAccountModel)
                .catch { exception ->
                    if (exception is Exception)
                        _verifyResultCustomSmsLiveData.postValue(SignUpAuthResult.Error(exception))

                    exception.printStackTrace()
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            val credentials = password.value?.let { Credentials(phone = phoneEmail.value?.lowercase(), password = it) }
                            if (credentials != null) {
                                sendCredentialsBeforeLoginIn(credentials)
                            }
                            //log in the user
                            val loginModel = LoginModel(phoneEmail.value?.lowercase() ?: "", password.value
                                ?: "", authMethod)
                            loginVerifiedUser(loginModel)
                        }
                        is Resource.Loading -> {
                        }
                        else -> {}
                    }
                }
        }
    }

    fun verifyAccount(code: String) {
        if (_useCustomSmsProvider.value == true) {
            verifyAccountWithCustomSms(code)
        } else {
            _verifyResultLiveData.postValue(SignUpAuthResult.Loading)

            val verifyAccountModel = VerifyAccountModel(userName, code)
            if (verifyAccountModel != null) {
                AWSInteractor.confirmSignUp(verifyAccountModel, object : SignUpCallback {
                    override fun onResult(signUpResult: SignUpResult) {
                        if (!signUpResult.confirmationState) {
                            val details = signUpResult.userCodeDeliveryDetails
                            Timber.tag(AWSInteractor.AWS_TAG).d( "Confirm sign-up with: ${details.destination}")
                        } else {
                            Timber.tag(AWSInteractor.AWS_TAG).d("Sign-up done.")

                            val email = if(authMethod == AuthMethod.EMAIL) phoneEmail.value?.lowercase() else null
                            val phone = if(authMethod == AuthMethod.PHONE) phoneEmail.value else null
                            val credentials = password.value?.let { Credentials(email = email, phone = phone, password = it) }
                            if (credentials != null) {
                                sendCredentialsBeforeLoginIn(credentials)
                            }
                            //log in the user
                            val loginModel = LoginModel(phoneEmail.value?.lowercase()
                                ?: "", password.value
                                ?: "", authMethod)
                            loginVerifiedUser(loginModel)

                        }
                        _verifyResultLiveData.postValue(SignUpAuthResult.Success(signUpResult))
                    }

                    override fun onError(e: Exception) {
                        when (e) {
                            is AliasExistsException -> {
                                _verifyResultLiveData.postValue(SignUpAuthResult.UserExists(e))
                            }
                            else -> _verifyResultLiveData.postValue(SignUpAuthResult.Error(e))
                        }
                    }
                })
            }
        }
    }

    private fun loginVerifiedUser(loginModel: LoginModel) {
        AWSInteractor.logIn(loginModel, object : LogInCallBack {
            override fun onResult(signInResult: SignInResult) {

                AWSInteractor.fetchUserInfo(object : FetchUserInfoListener {
                    override fun onResult(cognitoId: String, email: String?) {
                        var auth: AuthMethod? = authMethod
                        var phone: String? = null
                        var email: String? = null
                        when (authMethod) {
                            AuthMethod.EMAIL -> {
                                email = phoneEmail.value?.lowercase()
                            }
                            AuthMethod.PHONE -> {
                                phone = phoneEmail.value?.lowercase()
                            }
                            AuthMethod.GOOGLE -> {
                                auth = AuthMethod.GOOGLE
                            }
                            else -> {
                                auth = AuthMethod.FACEBOOK
                            }
                        }
                        getSpottedProfile()
                        duaSharedPrefs.setHasEverSignedIn(true)
                        duaSharedPrefs.setAuthMethode(authMethod?.methodName)
                        duaSharedPrefs.setHasSeenShadowDialog(false)
                     //   AppsFlyerLib.getInstance().setCustomerUserId(cognitoId)
                        linkCleverTapAppsFlyer()
                        AppLovinSdk.getInstance(DuaApplication.instance.applicationContext).userIdentifier = cognitoId
                        UXCam.setUserIdentity(cognitoId)
                        appsFlyerBackendManager.sendData()

                        onLoginCleverTap(email, phone, cognitoId)

                        sendClevertapEvent(
                            ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                                ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to (email.isNullOrEmpty().not() || appendedEmail.isEmpty().not())
                            )
                        )
                        sendUxCamEvent(
                            ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                                ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to (email.isNullOrEmpty().not() || appendedEmail.isEmpty().not())
                            )
                        )

                        logSignUpEvent(authMethod, FirebaseAnalyticsEventsName.CREATE_ACCOUNT)

                        _verifyCompleted.postValue(null)
                        getNotifications()
                    }

                    override fun onError(e: java.lang.Exception?) {
                        _verifyResultLiveData.postValue(SignUpAuthResult.Error(e
                            ?: java.lang.Exception("Failed getting logged in user cognitoID")))
                    }
                })

            }

            override fun onError(e: Exception) {}
        })
    }

    private fun getSpottedProfile() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getSpottedProfile()
                .catch { e ->
                    e.printStackTrace()
                }
                .collect { resource ->
                    when (resource) {
                        is Result.Success -> {
                            val signUpWithSpottedEntity = SignUpWithSpottedRawMapper.map(resource.data)

                            withContext(Dispatchers.Main){
                            signUpWithSpottedEntity.let {
                                setFirstName(firstName = signUpWithSpottedEntity.name.orEmpty())
                                val genderType = when (it.gender) {
                                    "male" -> GenderType.MAN
                                    "female" -> GenderType.WOMAN
                                    else -> null
                                }
                                genderType.let { gt -> setGender(gt) }
                                it.birthday?.let { spottedBirthday ->
                                    createCalendarFromSpottedBirthday(spottedBirthday, isSpottedProfile = true)?.let { birthday ->
                                        setBirthday(
                                            formatCalendarToUIString(birthday)
                                        )
                                    }
                                }

                                spottedData = signUpWithSpottedEntity
                            }
                                _spottedProfileLoaded.emit(signUpWithSpottedEntity)
                            }
                        }
                        else -> {}
                    }
                }
        }
    }

    private fun getNotifications() {
       viewModelScope.launch {
           notificationRepository.getSettingsNotificationsFlow()
               .catch { e->
                   e.printStackTrace()
               }
               .collect { resource->
                  when(resource){
                      is Resource.Success -> {
                          val userProperties = resource.data.settings.convertToClevertapUserProperties()
                          updateUserProfileInClevertap(userProperties)
                          duaSharedPrefs.setPushNotifications(
                              PushNotificationModel.convertToPushNotificationModelList(resource.data.settings,
                                  DuaAccount.user
                              ))

                      }
                      else -> {}
                  }
               }
       }
    }

    fun checkThirdPartyImageVerification(key: String) {
        val model = ImageVerificationModel(listOf(PictureModel(false, key, false, false)))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getImageVerificationResults(model)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        _thirdPartyImageResult.value = Result.Error(Exception(ex))
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                val model = it.data
                                model.images[0].location = thirdPartyImagePath?.absolutePath
                                _thirdPartyImageResult.value = Result.Success(it.data)
                            }
                            else -> {}
                        }
                    }
                }
        }
    }

    fun downloadThirdPartyPhoto(url: String) {
        val context = DuaApplication.instance.applicationContext
        Glide.with(context)
            .asBitmap()
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .listener(object : RequestListener<Bitmap?> {
                override fun onLoadFailed(@Nullable e: GlideException?, model: Any, target: Target<Bitmap?>, isFirstResource: Boolean): Boolean {
                    Timber.tag("GlideAppListener").d("error")
                    _thirdPartyImageUploaded.value = Result.Error(ImageUploadFailedException("Download Failed"))
                    // important to return false so the error placeholder can be placed
                    return false
                }

                override fun onResourceReady(resource: Bitmap?, model: Any, target: Target<Bitmap?>, dataSource: DataSource, isFirstResource: Boolean): Boolean {
                    val file = File(context?.cacheDir, UUID.randomUUID().toString())
                    file.createNewFile()

                    //Convert bitmap to byte array
                    val bos = ByteArrayOutputStream()
                    resource?.compress(Bitmap.CompressFormat.PNG, 0 /*ignored for PNG*/, bos)
                    val bitmapData = bos.toByteArray()

                    //write the bytes in file
                    val fos = FileOutputStream(file)
                    fos.write(bitmapData)
                    fos.flush()
                    fos.close()
                    thirdPartyImagePath = file

                    //progressDialog?.dismiss()
                    uploadThirdPartyImage(file)
                    return false
                }
            }).into(object : CustomTarget<Bitmap>() {
                override fun onLoadCleared(placeholder: Drawable?) {
                    Timber.tag("GlideAppListener").d("error")
                }

                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    Timber.tag("GlideAppListener").d(resource.byteCount.toString())
                }
            })
    }

    fun uploadThirdPartyImage(image: File) {
        viewModelScope.launch {
            try {
                val compressedImageFile = Compressor.compress(DuaApplication.instance, image)
                Timber.tag("COMPRESS").d("Size: ${getFileSize(compressedImageFile.length())}")

                val fileName = AWSInteractor.getResourceUrl(UUID.randomUUID().toString(), compressedImageFile.extension)

                removeMetadataFromImage(compressedImageFile.absolutePath)

                AWSInteractor.uploadFile(compressedImageFile, fileName, object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        if (TransferState.COMPLETED === state) {
                            Timber.tag(TAG).d("State: ${state.name}")
                            //add key to image object to see if they're verified
                            _thirdPartyImageUploaded.value = Result.Success(fileName)
                        } else if (TransferState.FAILED === state) {
                            _thirdPartyImageUploaded.value = Result.Error(ImageUploadFailedException(state.name))
                            ToastUtil.toast("Upload Failed!")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDone = (bytesCurrent.toFloat() / bytesTotal.toFloat() * 100).toInt()
                        Timber.tag(TAG).d("ID:$id|bytesCurrent: $bytesCurrent|bytesTotal: $bytesTotal|$percentDone%")
                    }

                    override fun onError(id: Int, ex: Exception) {
                        ex.printStackTrace()
                        _thirdPartyImageUploaded.value = Result.Error(
                            ImageUploadFailedException(ex.message
                            ?: "Error")
                        )
                        ToastUtil.toast("Upload Failed!")
                        ex.printStackTrace()
                    }
                })
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun checkSpottedImageVerification(key: String) {
        val model = ImageVerificationModel(listOf(PictureModel(false, key, false, false)))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getImageVerificationResults(model)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        _spottedResult.value = Result.Error(Exception(ex))
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                val model = it.data
                                model.images[0].location = spottedImageFile?.absolutePath
                                _spottedResult.value = Result.Success(it.data)
                            }
                            else -> {}
                        }
                    }
                }
        }
    }

    fun downloadSpottedPhoto(url: String) {
        val context = DuaApplication.instance.applicationContext
        Glide.with(context)
            .asBitmap()
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .listener(object : RequestListener<Bitmap?> {
                override fun onLoadFailed(@Nullable e: GlideException?, model: Any, target: Target<Bitmap?>, isFirstResource: Boolean): Boolean {
                    Timber.tag("GlideAppListener").d("error")
                    _spottedImageUploaded.value = Result.Error(ImageUploadFailedException("Download Failed"))
                    // important to return false so the error placeholder can be placed
                    return false
                }

                override fun onResourceReady(resource: Bitmap?, model: Any, target: Target<Bitmap?>, dataSource: DataSource, isFirstResource: Boolean): Boolean {
                    val file = File(context?.cacheDir, UUID.randomUUID().toString())
                    file.createNewFile()

                    //Convert bitmap to byte array
                    val bos = ByteArrayOutputStream()
                    resource?.compress(Bitmap.CompressFormat.PNG, 0 /*ignored for PNG*/, bos)
                    val bitmapData = bos.toByteArray()

                    //write the bytes in file
                    val fos = FileOutputStream(file)
                    fos.write(bitmapData)
                    fos.flush()
                    fos.close()
                    spottedImageFile = file

                    //progressDialog?.dismiss()
                    uploadSpottedImage(file)
                    return false
                }
            }).into(object : CustomTarget<Bitmap>() {
                override fun onLoadCleared(placeholder: Drawable?) {
                    Timber.tag("GlideAppListener").d("error")
                }

                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    Timber.tag("GlideAppListener").d(resource.byteCount.toString())
                }
            })
    }

    fun uploadSpottedImage(image: File) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val compressedImageFile = Compressor.compress(DuaApplication.instance, image)
                Timber.tag("COMPRESS").d("Size: ${getFileSize(compressedImageFile.length())}")

                val fileName = AWSInteractor.getResourceUrl(UUID.randomUUID().toString(), compressedImageFile.extension)

                removeMetadataFromImage(compressedImageFile.absolutePath)

                AWSInteractor.uploadFile(compressedImageFile, fileName, object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        if (TransferState.COMPLETED === state) {
                            Timber.tag(TAG).d("State: ${state.name}")
                            //add key to image object to see if they're verified
                            _spottedImageUploaded.value = Result.Success(fileName)
                        } else if (TransferState.FAILED === state) {
                            _spottedImageUploaded.value = Result.Error(ImageUploadFailedException(state.name))
                            ToastUtil.toast("Upload Failed!")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDone = (bytesCurrent.toFloat() / bytesTotal.toFloat() * 100).toInt()
                        Timber.tag(TAG).d("ID:$id|bytesCurrent: $bytesCurrent|bytesTotal: $bytesTotal|$percentDone%")
                    }

                    override fun onError(id: Int, ex: Exception) {
                        ex.printStackTrace()
                        _spottedImageUploaded.value = Result.Error(
                            ImageUploadFailedException(ex.message
                                ?: "Error")
                        )
                        ToastUtil.toast("Upload Failed!")
                        ex.printStackTrace()
                    }
                })
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun createProfileAPI(verifiedPictures: List<String>){
        resetProfileCreatedState()
        val birthday = formatUIDateToBackendDate(birthday)
        val language = ModifiedLingver.getInstance().getLanguage()

        val profilePic = verifiedPictures[0]
        val pictures = arrayListOf<Picture>()
        for (i in 1 until verifiedPictures.size)
            pictures.add(Picture(i, verifiedPictures[i]))

        val gender = gender.value!!.value
        val communityId = "al"
        val communityName = "Albanian"

        duaSharedPrefs.setUserCommunityName(communityName)

        val profile = Profile(
            pictureUrl = profilePic,
            pictures = pictures,
            latitude = locationModel?.latitude,
            longitude = locationModel?.longitude
        )
        val referralId = if (duaSharedPrefs.getRetrieveReferral() != "") duaSharedPrefs.getRetrieveReferral() else null
        val influencerName = if(duaSharedPrefs.getInfluencerName()!= "") duaSharedPrefs.getInfluencerName() else null

        val userModel = CreateProfileModel(
            birthDate = birthday,
            firstName = firstName.capitalize(Locale.ROOT),
            gender = gender,
            profile = profile,
            referralId = referralId,
            influencerName = influencerName,
            language = language,
            community = communityId,
            visitorId = visitorId
        )
        val email = if (authMethod == AuthMethod.EMAIL) phoneEmail.value?.lowercase() else null
        val phone = if (authMethod == AuthMethod.PHONE) phoneEmail.value?.lowercase() else null

        viewModelScope.launch(Dispatchers.IO) {
            delay(2000)
            userRepository.createUserProfileFlow(userModel, authMethod ?: AuthMethod.FACEBOOK, email, phone)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        _profileCreated.value = ResourceV2.Error(message = ex.message ?: "")

                        ex.printStackTrace()
                        _isCreatingProfile.value = false
                        when (ex) {
                            is UserDeletedExistException -> {
                                duaAccount.deleteAllData()
                            }

                            else -> {
                                ToastUtil.toast(DuaApplication.instance.getString(R.string.an_error_occurred))
                            }
                        }
                    }
                }.collect {
                    withContext(Dispatchers.Main){
                        when (it) {
                            is Resource.Success -> { 
                                
                                viewModelScope.launch(Dispatchers.IO) {
                                    if(isSpottedUser().not())
                                        duaSharedPrefs.setShouldShowOnboardingPaywall(true)
                                }

                               handleUserProfileCreation(
                                   it.data,
                                   referralId,
                                   duaSharedPrefs
                               )
                                clearAllSignUpPersistSteps()
                                _profileCreated.value = ResourceV2.Success(Unit)
                                _isCreatingProfile.value = true
                            }
                            is Resource.Loading -> {
                                _isCreatingProfile.value = true
                            }
                            else -> {}
                        }
                    }
                }
        }

    }

    private fun handleUserProfileCreation(
        user: UserModel,
        referralId: String?,
        duaSharedPrefs: DuaSharedPrefs
    ) {
        setUserId(user.cognitoUserId)

        sendCreateProfileEvent(user)

        sendAppsflyerEvent(
            AppsflyerEventsNameEnum.IS_PROFILE_CREATED,
            mapOf(
                AppsflyerEventsPropertyEnum.GENDER.eventsProperty to user.gender,
                AppsflyerEventsPropertyEnum.COGNITO_USER_ID.eventsProperty to user.cognitoUserId
            )
        )
        sendAppsflyerEvent("${AppsflyerEventsNameEnum.GENDER_}${user.gender}")

        DuaApplication.instance.sendTokenToServerAndSave()

        duaSharedPrefs.setUserGender(user.gender)

        if (referralId != null) {
            sendClevertapEvent(ClevertapEventEnum.FRIEND_ACCEPTED_THE_INVITATION)
        }
    }


    private fun sendCreateProfileEvent(user: UserModel){
        val areNotificationsOn = if(!DuaApplication.instance.applicationContext.isPostNotificationsPermissionEnabled()){
            false
        } else {
            val isDeviceNotificationOn =
                NotificationHelper.isMessageNotificationsEnables(DuaApplication.instance.applicationContext)
            val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
            (isDeviceNotificationOn && isSettingsNotificationOn)
        }

        val locationAccess = if(DuaApplication.instance.applicationContext.isLocationPermissionEnabled())
            ClevertapLocationAccessValues.ALLOWED.value
        else
            ClevertapLocationAccessValues.DENIED.value

        if (authMethod != null) {
            val parameterName: String = when (authMethod!!) {
                AuthMethod.EMAIL -> {
                    FirebaseAnalyticsParameterName.EMAIL.value
                }
                AuthMethod.PHONE -> {
                    FirebaseAnalyticsParameterName.PHONE.value
                }
                AuthMethod.FACEBOOK -> {
                    FirebaseAnalyticsParameterName.FACEBOOK.value
                }
                AuthMethod.GOOGLE -> {
                    FirebaseAnalyticsParameterName.GOOGLE.value
                }
            }


            firebaseLogEvent(FirebaseAnalyticsEventsName.CREATE_PROFILE,
                mapOf(parameterName to 1L,
                    FirebaseAnalyticsParameterName.ARE_NOTIFICATIONS_ON.value to areNotificationsOn,
                    FirebaseAnalyticsParameterName.LOCATION_ACCESS.value to areNotificationsOn))
        }

        sendClevertapEvent(
            ClevertapEventEnum.CREATE_PROFILE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOn,
                ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess,
                ClevertapEventPropertyEnum.GENDER.propertyName to user.gender
            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.CREATE_PROFILE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOn,
                ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess,
                ClevertapEventPropertyEnum.GENDER.propertyName to user.gender
            )
        )

        sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_ACCOUNT_CREATED)

    }

    fun getCommunitiesList() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getCommunities()
        }
    }

    // Variable to track the email address that code was sent to
    private var codeSentTo: String? = null

    // LiveData to track the result of adding an email address
    private val _addEmailAddress = SingleLiveData<Result<String>>()
    val addEmailAddress: LiveData<Result<String>>
        get() = _addEmailAddress

    private val _verifyEmailResultLiveData = SingleLiveData<Result<ResponseBody>>()
    val verifyEmailResultLiveData: LiveData<Result<ResponseBody>>
        get() = _verifyEmailResultLiveData

    fun addEmailAddress(email: String) {
        if (codeSentTo != email) {
            _addEmailAddress.postValue(Result.Loading)

            resetTimer()
            setTimer()
            viewModelScope.launch {
                try {
                    val model = AddEmailModel(email)
                    val result = userRepository.addEmailAddressV2(model)

                    when (result) {
                        is ResourceV2.Success -> {
                            Timber.tag("ADDEMAILADDRESS").d("Sent Code")
                            codeSentTo = email
                            _addEmailAddress.postValue(Result.Success(""))
                        }
                        is ResourceV2.Error -> {
                            codeSentTo = null
                            _addEmailAddress.postValue(Result.Error(Exception(result.message)))
                        }
                    }
                } catch (ex: Exception) {
                    codeSentTo = null
                    _addEmailAddress.postValue(Result.Error(ex))
                }
            }
        } else {
            Timber.tag("ADDEMAILADDRESS").d("Code already sent!")
            _addEmailAddress.postValue(Result.Success(""))
        }
    }

    fun verifyEmailAddressAPI(code: String) {
        _verifyEmailResultLiveData.postValue(Result.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val model = VerifyCodeModel(code)
            userRepository.verifyEmailCode(model)
                .catch { exception ->
                    if (exception is Exception)
                        _verifyEmailResultLiveData.postValue(Result.Error(exception))
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            _verifyEmailResultLiveData.postValue(Result.Success(value.data))
                        }
                        is Resource.Loading -> {
                        }
                        is Resource.Error -> {
                        }
                    }
                }
        }
    }

    fun clearDigits() {
        _verifyDigits.postValue("")
    }

    private fun resetTimer() {
        timer?.cancel()
        _elapsedTime.value = "00:00"
        _showTimer.value = false
    }


    fun clearValues() {
        phoneEmail.value = ""
        password.value = ""
        _signUpResultLiveData.value = null
    }

    override fun onCleared() {
        super.onCleared()

        timer?.cancel()
        timer = null
    }

    fun stopTimer(){
        timer?.cancel()
        timer = null
    }

    fun sendCredentialsBeforeLoginIn(credentials: Credentials) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.sendEmailCredentials(credentials)
                .catch { exception ->
                    if (exception is Exception)
                        Resource.Error(exception)
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            // handle success case
                        }
                        is Resource.Loading -> {
                            // handle loading case
                        }
                        is Resource.Error -> {
                            // handle error case
                        }
                    }
                }
        }
    }

    fun onSkipClicked() {
        _onSkipClicked.call()
    }

    fun isSpottedUser(): Boolean = spottedData != null

    fun getUserSignUpConfigurations() {
        viewModelScope.launch {
            val result = getUserSignUpConfigurationsUseCase()
            withContext(Dispatchers.Main){
                _signUpConfigurationUiData.update {
                    it.copy(signUpConfiguration = result.data)
                }
            }
        }
    }

    fun setDestinationId(id: Int) {
        _signUpConfigurationUiData.update {
            it.copy(destinationId = id)
        }
    }

    fun resetProfileCreatedState() {
        _profileCreated.value = null
    }

    private fun setupSignUpPersistManager() {
        viewModelScope.launch(Dispatchers.IO) {
           val steps =  signUpPersistManager.getSteps()
            withContext(Dispatchers.Main) {
                steps.forEach {
                    when(it.type) {
                        NAME -> {
                            if (it.value.isNullOrEmpty().not()) {
                                setFirstName(it.value!!)
                            }
                        }
                        EMAIL -> {
                            if (it.value.isNullOrEmpty().not()) {
                                skipTotallyAppendedEmail(it.value!!)
                            }
                        }
                        BIRTHDAY -> {
                            if (it.value.isNullOrEmpty().not()) {
                                setBirthday(it.value!!)
                            }
                        }
                        GENDER -> {
                            if (it.value.isNullOrEmpty().not()) {
                                setGender(GenderType.fromValue(it.value!!))
                            }
                        }
                        UPLOAD_PHOTOS -> {
                            if (it.value.isNullOrEmpty().not()) {
                                try {
                                    val photos = Gson().fromJson<List<ImageResult>?>(it.value, object : TypeToken<List<ImageResult>?>() {}.type)
                                    _signUpPersistVerifiedImages.value = photos
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }
                        else -> {}
                    }
                }
            }

        }
    }

    fun saveSignUpPersistStep(signUpPersistModel: SignUpPersistModel) {
        viewModelScope.launch {
            signUpPersistManager.saveStep(signUpPersistModel)
        }
    }

    fun clearAllSignUpPersistSteps() {
        viewModelScope.launch(Dispatchers.IO) {
            signUpPersistManager.clearAllSteps()
            duaSharedPrefs.setThirdPartyAuthGender(null)
            duaSharedPrefs.setThirdPartyAuthName(null)
            duaSharedPrefs.setThirdPartyAuthBirthday(null)
        }
    }

    fun shouldSkipSignUpPersistStep(step: SignUpPersistStepsEnum): Boolean {
        return signUpPersistManager.shouldSkipStep(step) && signUpPersistManager.hasSkippedStep(step).not()
    }

    fun setSignUpPersistStepAsSkipped(step: SignUpPersistStepsEnum) {
        viewModelScope.launch {
            signUpPersistManager.setStepSkipped(step)
        }
    }

    fun getCurrentLocation() {
        locationClient.getCurrentLocation(CancellationTokenSource())
            .catch { e ->
                e.printStackTrace()
            }
            .onEach { location ->
                Timber.tag("ESAT_LOGI").d("onEach location$location")
                locationModel = LocationModel(location.latitude, location.longitude)
            }.flowOn(Dispatchers.IO)
            .launchIn(viewModelScope)
    }

    fun sendEmailToClevertap(email: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val context = DuaApplication.instance.applicationContext
            setUserEmailInClevertap(context, email)
        }
    }

    fun sendGenderToClevertap(userGender: GenderType) {
        viewModelScope.launch(Dispatchers.IO) {
            val context = DuaApplication.instance.applicationContext
            setUserGenderInClevertap(context, userGender)
        }
    }

}

data class SignUpConfigurationUiData(
    val signUpConfiguration: SignUpConfiguration? = null,
    val destinationId: Int? = null
)