package com.duaag.android.signup.fragment


import android.content.Context
import android.os.Bundle
import android.os.Parcel
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentBirthdayScreenBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.getTimestampAfterPeriodOfTime
import com.duaag.android.utils.getUserAge
import com.duaag.android.utils.isBirthdayFormatCorrect
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.isPostNotificationsPermissionEnabled
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.shouldShowLocationPermissionRationale
import com.duaag.android.utils.shouldShowNotificationPermissionRationale
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.DateValidatorPointForward
import com.google.android.material.datepicker.MaterialDatePicker
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject


class BirthdayFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }

    private var _binding: FragmentBirthdayScreenBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentBirthdayScreenBinding.inflate(inflater, container, false)
        val view = binding.root

        sharedViewModel.gender.observe(viewLifecycleOwner) {
            val illustration = when (it) {
                GenderType.WOMAN -> R.drawable.birthday_illustration_female
                else -> R.drawable.birthday_illustration_male
            }
            binding.illustration.setImageResource(illustration)
        }

        if (sharedViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.BIRTHDAY)) {
            sharedViewModel.setSignUpPersistStepAsSkipped(SignUpPersistStepsEnum.BIRTHDAY)

            navigateBasedOnPermissions(requireContext(), findNavController())
        } else {
            sendClevertapEvent(
                ClevertapEventEnum.BIRTHDAY_SCREENVIEW, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))
            sendUxCamEvent(
                ClevertapEventEnum.BIRTHDAY_SCREENVIEW, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))


        }

        sharedViewModel.birthday?.let { setBirthdayUI(it) }
        checkBirthdayValidity(sharedViewModel.birthday ?: "")

        binding.birthdayText.setOnSingleClickListener {
            val dayOfMonth = sharedViewModel.birthday?.split("/")?.get(0)?.toIntOrNull()
            val monthOfYear = sharedViewModel.birthday?.split("/")?.get(1)?.toIntOrNull()
            val year = sharedViewModel.birthday?.split("/")?.get(2)?.toIntOrNull()
            showDatePicker(dayOfMonth, monthOfYear, year)
        }

        binding.nextBtn.setOnClickListener {
            logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.BIRTHDATE)

            navigateBasedOnPermissions(requireContext(), findNavController())
        }

        return view
    }

    private fun navigateBasedOnPermissions(context: Context, navController: NavController) {
        val shouldShowLocationRationale = shouldShowLocationPermissionRationale()
        val shouldShowNotificationRationale = shouldShowNotificationPermissionRationale()

        // Save notification permission step if it's being skipped
        val shouldShowNotificationPermission = RemoteConfigUtils.isNewNotificationOnboardingEnabled() &&
                !context.isPostNotificationsPermissionEnabled() &&
                !shouldShowNotificationRationale

        if (!shouldShowNotificationPermission) {
            // Notification permission screen is being skipped, save the step
            saveNotificationPermissionStep()
        }

        // Save location permission step if it's being skipped
        val shouldShowLocationPermission = RemoteConfigUtils.isNewLocationOnboardingEnabled() &&
                !context.isLocationPermissionEnabled() &&
                !shouldShowLocationRationale

        if (!shouldShowLocationPermission) {
            // Location permission screen is being skipped, save the step
            saveLocationPermissionStep()
        }

        val action = when {
            shouldShowNotificationPermission ->
                BirthdayFragmentDirections.actionBirthdayFragmentToNotificationPermissionStepFragment2()

            shouldShowLocationPermission ->
                BirthdayFragmentDirections.actionBirthdayFragmentToLocationPermissionStepFragment()

            else -> null
        }

        sharedViewModel.birthday?.let { sharedViewModel.persistBirthday(it) }

        if (findNavController().currentDestination?.id == R.id.birthdayFragment) {
            if (action != null) {
                navController.navigate(action)
            } else {
                navController.navigate(BirthdayFragmentDirections.actionBirthdayFragmentToLoveStoriesSignUpFragment2())
            }
        }
    }

    private fun showDatePicker(dayOfMonth: Int?, monthOfYear: Int?, year: Int?) {
        var current: Calendar? = null
        if(dayOfMonth != null && monthOfYear != null && year != null) {
            current = Calendar.getInstance()
            current?.set(Calendar.YEAR, year)
            current?.set(Calendar.MONTH, monthOfYear - 1)
            current?.set(Calendar.DAY_OF_MONTH, dayOfMonth)
        }
        val currentTime = current?.time?.time

        val endDate = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), -18, Calendar.YEAR)
        val maxCalendar = Calendar.getInstance()
        maxCalendar.time = Date(endDate)

        val startDate = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), -100, Calendar.YEAR)
        val minCalendar = Calendar.getInstance()
        minCalendar.time = Date(startDate)


        val constraints: CalendarConstraints = CalendarConstraints.Builder()
            .setOpenAt(currentTime ?: maxCalendar.timeInMillis)
            .setStart(minCalendar.timeInMillis)
            .setEnd(maxCalendar.timeInMillis)
            .setValidator(object : CalendarConstraints.DateValidator{
                override fun describeContents(): Int {
                    return 0
                }

                override fun writeToParcel(p0: Parcel, flags: Int) {
                }

                override fun isValid(date: Long): Boolean {
                    return DateValidatorPointForward.from(startDate).isValid(date)
                            && DateValidatorPointBackward.before(endDate).isValid(date)
                }
            })
            .build()


        val picker = MaterialDatePicker
            .Builder
            .datePicker()
            .setTheme(R.style.MaterialCalendarTheme)
            .setCalendarConstraints(constraints)
            .setSelection(currentTime ?: maxCalendar.timeInMillis)
            .build()

        picker.addOnPositiveButtonClickListener {
            val chosenDate = Calendar.getInstance()
            chosenDate.time = Date(it)

            val day = chosenDate.get(Calendar.DAY_OF_MONTH)
            val month = chosenDate.get(Calendar.MONTH) + 1
            val year = chosenDate.get(Calendar.YEAR)

            val birthday = "$day/${month}/$year"
            sharedViewModel.setBirthday(birthday)
            sharedViewModel.persistBirthday(birthday)
            sharedViewModel.clearThirdPartyBirthdayPersistence()

            setBirthdayUI(birthday)

            checkBirthdayValidity(birthday)
        }

        picker.show(childFragmentManager, "DATE_PICKER")
    }

    private fun setBirthdayUI(birthday: String) {
        try {
            val inputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(birthday)

            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val formattedBirthday = outputFormat.format(date)

            val birthdayCalendar = Calendar.getInstance()
            birthdayCalendar.time = date

            val birthdateUIText = getString(R.string.x_years_old, getUserAge(birthdayCalendar).toString())
            binding.userAgeText.text = birthdateUIText

            binding.birthdayText.text = formattedBirthday
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun checkBirthdayValidity(date: String){
        binding.nextBtn.isEnabled = isBirthdayFormatCorrect(date)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
