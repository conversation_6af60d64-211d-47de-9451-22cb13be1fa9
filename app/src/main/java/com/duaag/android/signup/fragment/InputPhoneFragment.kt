package com.duaag.android.signup.fragment


import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentInputPhoneV2Binding
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.coutry.detectPrefixCountry
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setWidth
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class InputPhoneFragment : Fragment() {

    companion object {
        fun newInstance(): InputPhoneFragment = InputPhoneFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentInputPhoneV2Binding? = null
    private val binding get() = _binding!!
    private var hasScreenViewEventBeenSent = false


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        requireActivity().onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true /* enabled by default */) {
            override fun handleOnBackPressed() {
                requireActivity().finish()
            }
        })

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentInputPhoneV2Binding.inflate(inflater)

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        enableCreateAccountButton(sharedViewModel.phoneEmail.value ?: "")

        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.let {
            it.ccPicker.detectPrefixCountry()
            sharedViewModel.selectedCountryCodeAsInt?.let {selectedCountryCodeAsInt ->
                it.ccPicker.setCountryForPhoneCode(selectedCountryCodeAsInt)
            }

            it.ccPicker.enableHint(false)
            it.ccPicker.registerPhoneNumberTextView(it.phoneNumberInput)

            it.phoneNumberInput.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    try {
                        val phone = it.ccPicker.selectedCountryCodeWithPlus + it.ccPicker.phoneNumber.nationalNumber

                        sharedViewModel.setPhoneNumber(phone)

                        enableCreateAccountButton(phone)
                    } catch (e: Exception) {
                        sharedViewModel.setPhoneNumber("")
                    }
                }

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    clearPhoneErrorMessage()
                }
            })

            it.ccPicker.setPhoneNumberInputValidityListener { _, isValid ->
                val phone = sharedViewModel.phoneEmail.value ?: ""

                enableCreateAccountButton(phone)
            }

            it.ccPicker.setOnCountryChangeListener { _ ->
                sharedViewModel.selectedCountryCodeAsInt = it.ccPicker.selectedCountryCodeAsInt

                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        val phone = sharedViewModel.phoneEmail.value ?: ""

                        enableCreateAccountButton(phone)
                    }
                }

            }

            binding.phoneNumberInput.setOnFocusChangeListener { _, hasFocus ->
                setContainerBackground(hasFocus)
            }

            it.phoneNumberInput.onKeyboardNext {
                goToPasswordScreen()
            }
        }

        binding.continueBtn.setOnSingleClickListener(1000L) {
            validateInput()
        }
    }

    private fun validateInput() {
        if (binding.ccPicker.isValid) {
            binding.continueBtn.isEnabled = false
            goToPasswordScreen()

//            (activity as SignUpActivity).executeSignUpReCaptcha(AuthMethod.PHONE)
        } else {
            if (!binding.ccPicker.isValid) {
                setPhoneError()
            }
        }
    }

    private fun enableCreateAccountButton(phone: String) {
        val isEnabled = phone.isNotEmpty()
        binding.continueBtn.isEnabled = isEnabled
    }


    private fun setPhoneError() {
        binding.phoneErrorText.visibility = View.VISIBLE
        binding.phoneErrorText.setText(R.string.phone_number_is_not_valid)
        binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.error_corners_12dp)
        binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.red_500)
        binding.phoneInputSeparator.setWidth(1f)

        sendClevertapEvent(ClevertapEventEnum.WRONG_PHONE_NUMBER_FORMAT)
        sendUxCamEvent(ClevertapEventEnum.WRONG_PHONE_NUMBER_FORMAT)
    }

    private fun clearPhoneErrorMessage() {
        binding.phoneErrorText.visibility = View.GONE
        setContainerBackground(binding.phoneNumberInput.hasFocus())
    }

    private fun setContainerBackground(hasFocus: Boolean) {
        if (hasFocus) {
            binding.phoneInputSeparator.setWidth(2f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.bg_input_focus)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_focused)
        } else {
            binding.phoneInputSeparator.setWidth(1f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.border)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_unfocused)
        }
    }

    private fun goToPasswordScreen() {
        val action = InputPhoneFragmentDirections.actionInputPhoneFragmentToInputPasswordSignUpFragment()
        findNavController().navigate(action)
    }

    override fun onResume() {
        super.onResume()
        binding.phoneNumberInput.requestFocus()
        binding.phoneNumberInput.showKeyboard()

        // Send screenview events only when the fragment is actually visible to the user
        if (!hasScreenViewEventBeenSent) {
            hasScreenViewEventBeenSent = true
            sendClevertapEvent(ClevertapEventEnum.ENTER_YOUR_NUMBER_SCREENVIEW)
            sendUxCamEvent(ClevertapEventEnum.ENTER_YOUR_NUMBER_SCREENVIEW)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
