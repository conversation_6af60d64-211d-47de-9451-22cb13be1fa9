package com.duaag.android.signup.fragment

import android.Manifest
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Paint
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.MimeTypeMap
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.duaag.android.R
import com.duaag.android.api.Result.Error
import com.duaag.android.api.Result.Loading
import com.duaag.android.api.Result.Success
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.fragment.ImagePickerFragment
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.UploadImageStatusValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentUploadPhotosBinding
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.REQUEST_CODE_PERMISSIONS
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.SignUpActivity.Companion.TAG
import com.duaag.android.signup.adapters.ImageVerificationStatusAdapter
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.models.ImageModel
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.IMAGE_METADATA_TAG
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getPictureMetaData
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.openSettingsScreen
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.ProgressDialog
import com.duaag.android.views.SpinningCircleDialog
import com.duaag.android.vision.ImageVisionInteractor
import com.duaag.android.vision.VisionImageProcessor
import com.duaag.android.vision.facedetector.FaceDetectorProcessor
import com.duaag.android.vision.textdetector.TextRecognitionProcessor
import com.google.mlkit.vision.text.Text
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import kotlin.random.Random


class UploadPhotosFragment : Fragment(), DialogClickListener,
    KeepFacebookPhotoDialog.KeepFacebookPhotoListener {

    companion object {
        fun newInstance(): UploadPhotosFragment {
            return UploadPhotosFragment()
        }
    }

    private var _binding: FragmentUploadPhotosBinding? = null
    private val binding get() = _binding!!

    protected var isCamera: Boolean = false
    protected var currentPhotoPath: String? = null

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel>({ if (activity is ManagePicturesActivity) activity as ManagePicturesActivity else activity as SignUpActivity }) { viewModelFactory }
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({ if (activity is ManagePicturesActivity) activity as ManagePicturesActivity else activity as SignUpActivity }) { viewModelFactory }
    private val livenessViewModel: LivenessViewModel? by lazy {
        if (activity is SignUpActivity) {
            ViewModelProvider(activity as SignUpActivity, viewModelFactory)[LivenessViewModel::class.java]
        } else {
            null // Return null if the activity is  HomeActivity
        }
    }
    @Inject
    lateinit var duaAccount: DuaAccount
    private var spinningCircleDialog: SpinningCircleDialog? = null

    private lateinit var pickMedia: ActivityResultLauncher<PickVisualMediaRequest>
    private val permissions = arrayOf(Manifest.permission.CAMERA)


    var currentPictureMetadata: ImageMetaDataResultModel? = null
    private var imageVisionInteractor: ImageVisionInteractor? = null
    private var faceDetectionCallback: FaceDetectorProcessor.FaceDetectionCallback? = null
    private var textDetectionCallback: TextRecognitionProcessor.TextDetectionCallback? = null
    private var uploadProgressDialog: ProgressDialog? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        if (activity is SignUpActivity) {
            (requireActivity() as SignUpActivity).signUpComponent.inject(this)

            pickMedia = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
                if (uri != null) {
                    Timber.tag("IMAGE_PICKER").d("Selected URI: $uri")

                    lifecycleScope.launch(Dispatchers.IO) {
                        currentPictureMetadata = getPictureMetaData(requireContext(), uri)
                    }
                    beginUCrop(uri)
                } else {
                    Timber.tag("IMAGE_PICKER").d("No media selected")
                }
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUploadPhotosBinding.inflate(inflater, container, false)

        if (sharedSignUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.UPLOAD_PHOTOS)) {
            handlePersistStep()
        }

        imageVisionInteractor = createImageProcessors()

        binding.guidelinesBtnSignUp.paintFlags =
            binding.guidelinesBtnSignUp.paintFlags or Paint.UNDERLINE_TEXT_FLAG

        val signUpOrSignInMediumValue = if(activity is SignUpActivity) sharedSignUpViewModel.authMethod?.methodName else ClevertapSignUpOrSignInMediumValues.NULL.value

        sendClevertapEvent(
            ClevertapEventEnum.UPLOAD_PHOTOS_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.UPLOAD_PHOTOS_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )

        if(activity is SignUpActivity) {
            choosePicturesViewModel.imageDetectionIsCompleted.observe(viewLifecycleOwner) {
                showUploadProgressDialog()
            }
        }

        binding.guidelinesBtnSignUp.setOnClickListener {
            if(activity is SignUpActivity) logSignUpEvent(sharedSignUpViewModel.authMethod, FirebaseAnalyticsEventsName.IMAGE_GUIDELINES)

            onSeeGuidelinesClicked()
        }

        binding.continueBtnSignUp.setOnClickListener {
            onContinueClicked()
        }


        choosePicturesViewModel.verifiedPictures.observe(viewLifecycleOwner) {
            showVerificationImagesResult(it)

            // This part is commented based on DA-3213 TASK
//            if (activity is SignUpActivity) {
//                binding.continueBtn.isEnabled = true
//
//                val isFacebookPicture = sharedSignUpViewModel.thirdPartyUserData?.picture.isNullOrBlank().not()
//                val isSpottedPicture = sharedSignUpViewModel.spottedData?.profilePicture.isNullOrBlank().not()
//                if (isAnyImageValid && (isSpottedPicture || isFacebookPicture )) {
//                    spinningCircleDialog?.dismiss()
//                    if(!sharedSignUpViewModel.hasSkippedUploadImagesOnce) {
//                        binding.continueBtn.performClick()
//                        sharedSignUpViewModel.hasSkippedUploadImagesOnce = true
//                    }
//                }
//            }

            val minimumRequiredPhotos = sharedSignUpViewModel.signUpConfigurationUiData.value?.signUpConfiguration?.minimumRequiredPhotos ?: 1
            val validImagesCount = it.count { image -> image.isValid }
            binding.continueBtnSignUp.isEnabled = validImagesCount >= minimumRequiredPhotos
        }

        choosePicturesViewModel.imageCropped.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                handleCroppedImage(uri)
            }
        }

        choosePicturesViewModel.imageChosen.observe(viewLifecycleOwner) { data ->
            val imageResultUri: Uri? = getPickImageResultUri(data)
            imageResultUri?.let {
                beginUCrop(imageResultUri)
                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), imageResultUri)
                }
            } ?: run {
                ToastUtil.toast(R.string.smthg_went_wrong)
            }
        }

        choosePicturesViewModel.uploadProgress.observe(viewLifecycleOwner) {
            uploadProgressDialog?.updateProgress(it)
            if (it == -1) {
                uploadProgressDialog?.dismiss()
            }
        }

        choosePicturesViewModel.uploadFinished.observe(viewLifecycleOwner) {
            uploadProgressDialog?.dismiss()
        }

        choosePicturesViewModel.imageVerificationFailed.observe(viewLifecycleOwner) {
            val bundle = bundleOf(
                ImageDeniedNewFragment.ARG_IMAGE_KEY to it.key
            )
            findNavController().navigateSafer(R.id.action_uploadPhotosFragment_to_imageDeniedFragment, bundle)
        }

        choosePicturesViewModel.imageVerified.observe(viewLifecycleOwner) { data ->
            sendUploadImageEvent(data)
        }

        if(requireActivity() is SignUpActivity){

            // This part is commented based on DA-3213 TASK
//            if (!sharedSignUpViewModel.hasSkippedUploadImages) {
//                sharedSignUpViewModel.thirdPartyImageUploaded.observe(viewLifecycleOwner) {
//                    when (it) {
//                        is Success -> {
//                            sharedSignUpViewModel.checkThirdPartyImageVerification(it.data)
//                        }
//                        is Error -> {
//                            spinningCircleDialog?.dismiss()
//                            ToastUtil.toast(getString(R.string.an_error_occurred))
//                        }
//                        is Loading -> {}
//                    }
//                }
//
//                sharedSignUpViewModel.thirdPartyImageResult.observe(viewLifecycleOwner) {
//                    when (it) {
//                        is Success -> {
//                            spinningCircleDialog?.dismiss()
//
//                            if (it.data.images.none { img -> !img.isValid }) {
//
//                                //add new verified keys that came from api
//                                val newImages = it.data.images
//                                newImages.firstOrNull()?.let { it.isFromFacebook = true }
//                                choosePicturesViewModel.setVerifiedPictures(newImages)
//                            } else {
//                                val allPictures = mutableListOf<ImageResult>()
//                                it.data.images.firstOrNull()?.let {
//                                    it.location =
//                                        sharedSignUpViewModel.thirdPartyImagePath?.absolutePath
//                                    it.isFromFacebook = true
//                                }
//                                allPictures.addAll(it.data.images)
//                                spinningCircleDialog?.dismiss()
//                                choosePicturesViewModel.setVerifiedPictures(allPictures)
//                            }
//                        }
//                        is Error -> {
//                            spinningCircleDialog?.dismiss()
//                            ToastUtil.toast(getString(R.string.an_error_occurred))
//                        }
//                        is Loading -> {}
//                    }
//                }
//                sharedSignUpViewModel.thirdPartyUserData?.picture?.let {
//                    sharedSignUpViewModel.hasSkippedUploadImages = true
//                    spinningCircleDialog = context?.let { context -> SpinningCircleDialog(context) }
//                    spinningCircleDialog?.show()
//
//                    sharedSignUpViewModel.downloadThirdPartyPhoto(it)
//                }
//
//                handledSpottedImages()
//
//            }

            sharedSignUpViewModel.cameraPermission.observe(viewLifecycleOwner) {
                openCamera(true)
            }
        }

        livenessViewModel?.uiData?.asLiveData()?.observe(viewLifecycleOwner) {uiData->
            if(uiData.verificationPassedSuccessfully){
                livenessViewModel?.onVerificationPassedSuccessfully()
                findNavController().navigate(R.id.action_uploadPhotosFragment_to_allSetSignUpFragment, bundleOf(AllSetSignUpFragment.USER_VERIFIED_SUCCESSFULLY to true))

//                navigateBasedOnPermissions(requireContext(), findNavController())
            }
        }

        return binding.root
    }

    private fun handledSpottedImages() {

        sharedSignUpViewModel.spottedImageUploaded.observe(viewLifecycleOwner) {
            when (it) {
                is Success -> {
                    sharedSignUpViewModel.checkSpottedImageVerification(it.data)
                }
                is Error -> {
                    spinningCircleDialog?.dismiss()
                    ToastUtil.toast(getString(R.string.an_error_occurred))
                }
                is Loading -> {}
            }
        }

        sharedSignUpViewModel.spottedResult.observe(viewLifecycleOwner) {
            when (it) {
                is Success -> {
                    spinningCircleDialog?.dismiss()

                    if (it.data.images.none { img -> !img.isValid }) {

                        //add new verified keys that came from api
                        val newImages = it.data.images
                        choosePicturesViewModel.setVerifiedPictures(newImages)
                    } else {
                        val allPictures = mutableListOf<ImageResult>()
                        it.data.images.firstOrNull()?.let {
                            it.location = sharedSignUpViewModel.spottedImageFile?.absolutePath
                        }
                        allPictures.addAll(it.data.images)
                        spinningCircleDialog?.dismiss()
                        choosePicturesViewModel.setVerifiedPictures(allPictures)

                        firebaseLogEvent(FirebaseAnalyticsEventsName.SPOTTED_PROFILE_PICTURE_NOT_VERIFIED)
                        sendClevertapEvent(ClevertapEventEnum.SPOTTED_PROFILE_PICTURE_NOT_VERIFIED)
                    }
                }
                is Error -> {
                    spinningCircleDialog?.dismiss()
                    ToastUtil.toast(getString(R.string.an_error_occurred))
                    logError(ErrorStatus.UPLOAD_PHOTO_SPOTTED_RESULT)
                }
                is Loading -> {}
            }
        }
        sharedSignUpViewModel.spottedData?.profilePicture?.let {
            spinningCircleDialog = context?.let { context -> SpinningCircleDialog(context) }
            spinningCircleDialog?.show()

            sharedSignUpViewModel.downloadSpottedPhoto(it)
        }
    }

    private fun onContinueClicked() {
        val pictures = choosePicturesViewModel.verifiedPictures.value?.filter { it.isValid }?.map { it.key }
        val minimumRequiredPhotos = sharedSignUpViewModel.signUpConfigurationUiData.value?.signUpConfiguration?.minimumRequiredPhotos ?: 1
        if ((pictures?.size ?: 0) < minimumRequiredPhotos) {
            return
        }


        pictures?.let {
            val eventPremiumType = getPremiumTypeEventProperty(livenessViewModel?.user?.value)
            val data = bundleOf(
                "pictureKey" to pictures.firstOrNull(),
                "userGender" to sharedSignUpViewModel.gender.value?.value,
                EVENT_SOURCE to ClevertapVerificationSourceValues.ONBOARDING
            )
            sendVerifyYourProfileInitiatedAnalyticsEvent(
                ClevertapVerificationSourceValues.ONBOARDING.value,
                eventPremiumType,
                sharedSignUpViewModel.authMethod?.methodName
            )
            //skip verification if already verified
            if(livenessViewModel?.uiData?.value?.hasAlreadyBeenVerified != true) {
                findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,data)
            } else {
                findNavController().navigate(R.id.action_uploadPhotosFragment_to_allSetSignUpFragment, bundleOf(AllSetSignUpFragment.USER_VERIFIED_SUCCESSFULLY to true))
            }
        }
    }

    private fun onSeeGuidelinesClicked() {
        val dialog = GuidelinesDialogFragment.newInstance(
            methodName = if(activity is SignUpActivity) sharedSignUpViewModel.authMethod?.methodName else "",
            premiumTypeEventProperty = getPremiumTypeEventProperty(livenessViewModel?.user?.value)
        )
        dialog.show(childFragmentManager, "dialog")
    }

    private fun showVerificationImagesResult(images: List<ImageResult>) {
        binding.userImagesContainer.visibility = View.VISIBLE

        val allImages = mutableListOf<ImageResult>()
        allImages.addAll(images.take(3))
        while (allImages.size < 3) {
            allImages.add(ImageResult.getEmptyItem())
        }

        binding.imagesList.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.imagesList.adapter = ImageVerificationStatusAdapter(allImages, object: ImageVerificationStatusAdapter.ClickListener {
            override fun onAddClick() {
                sendStartUploadImageEvent()
                openImagePicker()
            }
            override fun onRemoveClick(item: ImageResult) {
                choosePicturesViewModel.removeVerifiedImages(listOf(item.key))
                choosePicturesViewModel.clearSpecificVerifiedImage(item)

                sendImageRemovedEvent()
            }
        })

    }

    override fun onDestroyView() {
        super.onDestroyView()
        spinningCircleDialog = null

        imageVisionInteractor?.stopImageProcessors()
        imageVisionInteractor = null
        uploadProgressDialog?.dismiss()
        uploadProgressDialog = null

        _binding = null
    }

    override fun onButtonClicked() {
        if(activity is SignUpActivity) {
            logSignUpEvent(sharedSignUpViewModel.authMethod, FirebaseAnalyticsEventsName.START_UPLOAD_IMAGE)
            sendStartUploadImageEvent()
        }

        openImagePicker()
    }

    private fun openImagePicker() {
        pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
    }

    override fun onKeepClicked() {
        val verified = choosePicturesViewModel.verifiedPictures.value!![0]
        val imageModel = ImageModel(
            id = Random.nextLong(),
            url = verified.location,
            isSelected = true,
            s3Key = verified.key,
            isVerified = true,
            isValid = true,
            canClick = true,
            alreadyExists = false,
            faceDetected = true,
            textDetected = false
        )

        choosePicturesViewModel.addFacebookImageToLists(imageModel)

        openImagePicker()
    }

    override fun onUploadNewClicked() {
        choosePicturesViewModel.clearVerifiedImages()

        openImagePicker()
    }

    private fun handleCroppedImage(outputUri: Uri) {
        Timber.tag(IMAGE_METADATA_TAG).d("$currentPictureMetadata")
        choosePicturesViewModel.addNewImage(outputUri ,currentPictureMetadata)

        choosePicturesViewModel.addImageInTextDetectionQueue(outputUri)
        imageVisionInteractor?.detectInImage(outputUri, requireActivity().contentResolver)
        sendUploadImageInitiatedEvent()
    }

    private fun sendUploadImageInitiatedEvent() {
        val signUpOrSignInMediumValue = sharedSignUpViewModel.authMethod?.methodName

        sendClevertapEvent(
            ClevertapEventEnum.UPLOAD_IMAGE_INITIATED, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,

                )
        )
        sendUxCamEvent(
            ClevertapEventEnum.UPLOAD_IMAGE_INITIATED, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            )
        )
    }

    private fun createImageProcessors(): ImageVisionInteractor? {
        faceDetectionCallback = object : FaceDetectorProcessor.FaceDetectionCallback {
            override fun onSuccess(faceDetected: Boolean) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: faceDetected $faceDetected")
                choosePicturesViewModel.setFaceDetected(faceDetected)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: faceDetected exception $exception")
                exception.printStackTrace()

                choosePicturesViewModel.removeLastItemFromFaceDetectionQueue()
                choosePicturesViewModel.checkIfImageDetectionIsCompleted()
            }
        }

        textDetectionCallback = object : TextRecognitionProcessor.TextDetectionCallback {
            override fun onSuccess(text: Text) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: textDetected ${text.text}")
                choosePicturesViewModel.setTextDetected(text)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: textDetected exception $exception")
                exception.printStackTrace()

                choosePicturesViewModel.removeLastItemFromTextDetectionQueue()
                choosePicturesViewModel.checkIfImageDetectionIsCompleted()
            }
        }

        val imageProcessors = mutableListOf<VisionImageProcessor>()
        try {
            //Text Detection
            val textDetector = TextRecognitionProcessor(requireContext(), textDetectionCallback!!)
            imageProcessors.add(textDetector)
        } catch (e: Exception) {
            Timber.tag("IMAGE_DETECTION").e(e, "Can not create image processor: ${e.message}")
            return null
        }

        return ImageVisionInteractor(imageProcessors)
    }

    private fun beginUCrop(uri: Uri) {
        try {
            var randomFileName = UUID.randomUUID().toString()
            val extension: String?
            val cr = requireContext().contentResolver
            val type = cr.getType(uri)
            if (uri.toString().contains("file:")) extension = findExtension(uri)
            else extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(type)

            if (extension != null) randomFileName = "$randomFileName.$extension"
            Timber.tag("URITYPE").d("fileName: \$fileName")

            val destination = Uri.fromFile(File(requireContext().cacheDir, randomFileName))
            val options = UCrop.Options()
            options.setHideBottomControls(true)
            options.setCropGridRowCount(5)
            options.setCropGridColumnCount(4)
            options.setAllowedGestures(UCropActivity.ALL, UCropActivity.SCALE, UCropActivity.ROTATE)
            options.setToolbarTitle("")
            options.setDimmedLayerColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.background
                )
            )

            UCrop.of(uri, destination)
                .withAspectRatio(3f, 4f)
                .withOptions(options)
                .start(requireContext(), this,23)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        //  super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == 23 && data != null) {
            UCrop.getOutput(data)?.let { outputUri ->
                livenessViewModel?.retryVerification()
                Timber.tag(TAG).d("outputUri: $outputUri")
                if (!NetworkChecker.isNetworkConnected(requireContext())) {
                    ToastUtil.toast(getString(R.string.no_internet_string))
                    return
                }
                choosePicturesViewModel.imageCropped(outputUri)
            } ?: Timber.tag(TAG).d("outputUri: null")
        }

    }
    fun findExtension(uri: Uri): String? {
        val stringUri = uri.toString()
        val lastIndex = stringUri.lastIndexOf('.')
        if (lastIndex == -1) {
            return null
        }
        return stringUri.substring(lastIndex + 1)
    }

    private fun showUploadProgressDialog() {
        uploadProgressDialog = ProgressDialog(requireContext(), resources.getString(R.string.uploading_images))
        uploadProgressDialog?.show()
        choosePicturesViewModel.uploadSelectedImages()

        if (activity is SignUpActivity) {
            logSignUpEvent(authMethod = sharedSignUpViewModel.authMethod,
                FirebaseAnalyticsEventsName.UPLOADING_IMAGES_LOADER)
        }else {
            firebaseLogEvent(FirebaseAnalyticsEventsName.UPLOADING_IMAGES_LOADER)
        }
    }

    fun showPhotoSourceDialog() {
        val builder = AlertDialog.Builder(requireContext())

        val galleryItem = getString(R.string.upload_photos)
        val cameraItem = getString(R.string.take_photo)

        val list =arrayOf(galleryItem, cameraItem)

        builder.setItems(list) { _, which ->
            when (which) {
                0 -> {
                    openImagePicker()
                }
                1 -> {
                    onCameraItemClick()
                }
            }
        }

        val dialog = builder.create()
        dialog.show()

        sendStartUploadImageEvent()
    }

    fun onCameraItemClick() {
        checkPermissionForImages()
    }

    private fun checkPermissionForImages() {
        val resultCamera = ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.CAMERA
        )

        when {
            (resultCamera == PackageManager.PERMISSION_GRANTED) -> {
                openCamera(true )
            }
            (shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) -> {
                showSettingsPermissionsDialog(getString(R.string.allow_gallery_camera))
            }
            else -> {
                requireActivity().requestPermissions(permissions, REQUEST_CODE_PERMISSIONS)
            }
        }
    }

    private fun showSettingsPermissionsDialog(description: String) {
        val builder = AlertDialog.Builder(requireContext())
            .setMessage(description)
            .setPositiveButton(R.string.settings) { _, _ ->
                try {
                    openSettingsScreen(requireContext())
                } catch (e: java.lang.Exception) {
                    Timber.tag("error").d(e.toString())
                }
            }
            .setNegativeButton(R.string.cancel) { _, _ -> }
            .setCancelable(false)

        val dialog = builder.create()
        dialog.setOnShowListener {
            dialog.getButton(AlertDialog.BUTTON_POSITIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_500))
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.red_500))
        }
        dialog.show()
    }

    fun openCamera(isCamera: Boolean) {
        this.isCamera = isCamera
        val intent: Intent = getPickImageOrCamera(isCamera)
        if (intent.resolveActivity(requireContext().packageManager) != null) {
            requireActivity().startActivityForResult(intent, ImagePickerFragment.REQUEST_IMAGE_CAPTURE)
        }
    }

    private fun getPickImageOrCamera(isCamera: Boolean): Intent {
        var intent = Intent()
        if (!isCamera) {
            intent.setAction(Intent.ACTION_GET_CONTENT)
            intent.setType("image/*")
        } else {
            intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            // Ensure that there's a camera activity to handle the intent
            // Create the File where the photo should go
            var photoFile: File? = null
            try {
                photoFile = createImageFile()
            } catch (ex: IOException) {
                // Error occurred while creating the File
            }
            // Continue only if the File was successfully created
            if (photoFile != null) {
                val photoURI = FileProvider.getUriForFile(
                    requireActivity(),
                    requireActivity().packageName + ".fileprovider",
                    photoFile
                )
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
            }
        }

        return intent
    }

    @Throws(IOException::class)
    private fun createImageFile(): File {
        // Create an image file name
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir = requireActivity().getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val image = File.createTempFile(
            imageFileName,  /* prefix */
            ".jpg",  /* suffix */
            storageDir /* directory */
        )

        // Save a file: path for use with ACTION_VIEW intents
        currentPhotoPath = image.absolutePath

        return image
    }

    fun getPickImageResultUri(data: Intent?): Uri? {
        return if (isCamera) Uri.parse("file://$currentPhotoPath") else ((if ((data != null && data.data != null)) data.data else null))
    }

    private fun handlePersistStep() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedSignUpViewModel.signUpPersistVerifiedImages.collect{
                    if (sharedSignUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.UPLOAD_PHOTOS) && it.isNullOrEmpty().not()) {
                        sharedSignUpViewModel.setSignUpPersistStepAsSkipped(SignUpPersistStepsEnum.UPLOAD_PHOTOS)
                        choosePicturesViewModel.setVerifiedPictures(it ?: mutableListOf())
                        onContinueClicked()
                    }
                }
            }
        }

    }

    private fun sendUploadImageEvent(verified: BooleanArray) {
        val anyVerified = verified.any { it }
        val signUpOrSignInMediumValue = sharedSignUpViewModel.authMethod?.methodName
        sendClevertapEvent(
            ClevertapEventEnum.UPLOAD_IMAGE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.UPLOAD_IMAGE_STATUS.propertyName to if(anyVerified) UploadImageStatusValues.GUIDELINES_MET.value else UploadImageStatusValues.GUIDELINES_NOT_MET.value,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,

            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.UPLOAD_IMAGE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.UPLOAD_IMAGE_STATUS.propertyName to if(anyVerified) UploadImageStatusValues.GUIDELINES_MET.value else UploadImageStatusValues.GUIDELINES_NOT_MET.value,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
                )
        )
    }

    private fun sendStartUploadImageEvent() {
        sendClevertapEvent(ClevertapEventEnum.START_UPLOAD_IMAGE,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )
        sendUxCamEvent(ClevertapEventEnum.START_UPLOAD_IMAGE,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )
    }

    private fun sendImageRemovedEvent() {
        sendClevertapEvent(ClevertapEventEnum.IMAGE_REMOVED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )
        sendUxCamEvent(ClevertapEventEnum.IMAGE_REMOVED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
            )
        )
    }

}
