package com.duaag.android.signup.fragment.guidelines

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.GuidelinesDialogBinding
import com.duaag.android.image_verification.fragments.ShowPoseFragment
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.tabs.TabLayoutMediator
import kotlin.properties.Delegates

class GuidelinesDialogFragment : BottomSheetDialogFragment() {

    private var isForImageVerification by Delegates.notNull<Boolean>()
    private var userGender by Delegates.notNull<String>()
    private var methodName by Delegates.notNull<String>()
    private var premiumTypeEventProperty by Delegates.notNull<String>()

    private var tabLayoutMediator: TabLayoutMediator? = null

    companion object {
        const val IS_FOR_IMAGE_VERIFICATION = "is_for_image_verification"
        const val USER_GENDER = "user_gender"
        const val AUTH_METHOD_NAME = "auth_method_name"
        const val PREMIUM_TYPE_EVENT_PROPERTY = "premium_type_event_property"


        fun newInstance(isForImageVerification: Boolean = false,
                        userGender: String = GenderType.WOMAN.value,
                        methodName: String?= "",
                        premiumTypeEventProperty: String?): GuidelinesDialogFragment {
            val fragment = GuidelinesDialogFragment()
            val args = Bundle()
            args.putBoolean(IS_FOR_IMAGE_VERIFICATION, isForImageVerification)
            args.putString(USER_GENDER, userGender)
            args.putString(AUTH_METHOD_NAME,methodName?:"")
            args.putString(PREMIUM_TYPE_EVENT_PROPERTY,premiumTypeEventProperty)
            fragment.arguments = args
            return fragment
        }
    }

    private val drawables by lazy {
        if (isForImageVerification) {
            when (userGender) {
                GenderType.WOMAN.value -> intArrayOf(
                        R.drawable.female_first_wrong,
                        R.drawable.female_first_right,
                        R.drawable.female_second_wrong,
                        R.drawable.female_second_right,
                        R.drawable.female_third_wrong,
                        R.drawable.female_first_wrong
                )
                else -> intArrayOf(
                        R.drawable.male_first_wrong,
                        R.drawable.male_first_right,
                        R.drawable.male_second_wrong,
                        R.drawable.male_second_right,
                        R.drawable.male_thid_wrong,
                        R.drawable.male_third_right
                )
            }

        } else {
            intArrayOf(
                    R.drawable.first_page_first_cover,
                    R.drawable.first_page_second_face,
                    R.drawable.second_page_first_gun,
                    R.drawable.second_page_second_nogun,
                    R.drawable.third_page_first_nude,
                    R.drawable.third_page_second_beach,
                    R.drawable.fourth_page_first_celeb,
                    R.drawable.fourth_page_second_real,
                    R.drawable.fifth_page_first_baby,
                    R.drawable.fifth_page_second_adult

            )
        }

    }

    val items by lazy {
        if (isForImageVerification) {
            listOf(
                    GuidelineItem(getString(R.string.make_sure_to_pose_correctly), getString(R.string.please_follow_the_instructions_in_order_to_verify_your_profile_successfully),
                            drawables[0],
                            drawables[1]),
                    GuidelineItem(getString(R.string.dont_hide_or_cover_your_face), getString(R.string.hiding_or_covering_your_face_will_automatically_fail_the_verification_process),
                            drawables[2],
                            drawables[3]),
                    GuidelineItem(getString(R.string.uploading_other_images), getString(R.string.you_can_lose_your_verified_badge_if_you_use_other_images_which_doesn_show_your_face),
                            drawables[4],
                            drawables[5]))
        } else {
            listOf(
                    GuidelineItem(getString(R.string.first_page_title), getString(R.string.first_page_guideline),
                            drawables[0],
                            drawables[1]),
                    GuidelineItem(getString(R.string.second_page_title), getString(R.string.second_page_guideline),
                            drawables[2],
                            drawables[3]),
                    GuidelineItem(getString(R.string.third_page_title), getString(R.string.third_guidelines),
                            drawables[4],
                            drawables[5]),
                    GuidelineItem(getString(R.string.fourth_page_title), getString(R.string.fourth_guideline_an,getString(R.string.dua_support)),
                            drawables[6],
                            drawables[7]),
                    GuidelineItem(getString(R.string.fifth_page_title), getString(R.string.fifth_guideline),
                            drawables[8],
                            drawables[9]))
        }
    }
    private var _binding: GuidelinesDialogBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isForImageVerification = arguments?.getBoolean(IS_FOR_IMAGE_VERIFICATION)!!
        userGender = arguments?.getString(USER_GENDER)!!
        methodName = arguments?.getString(AUTH_METHOD_NAME)!!
        premiumTypeEventProperty = arguments?.getString(PREMIUM_TYPE_EVENT_PROPERTY) ?: ""
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = GuidelinesDialogBinding.inflate(inflater)
        binding.lifecycleOwner = this

        val pager = binding.pagerGuidelines


        val adapter = GuidelinesAdapter(items)

        setupViewPager(adapter, pager)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
            sendClevertapEvent(
                ClevertapEventEnum.SEE_GUIDELINES_BUTTONCLICK, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to methodName,
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeEventProperty
                )
            )
            sendUxCamEvent(
                ClevertapEventEnum.SEE_GUIDELINES_BUTTONCLICK, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to methodName,
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeEventProperty
                )
            )

    }

    private fun setupViewPager(adapter: GuidelinesAdapter, pager: ViewPager2) {
        pager.adapter = adapter
        binding.tabLayout.let {
            pager.let { it1 ->
                tabLayoutMediator = TabLayoutMediator(it, it1) { _, _ ->}
                tabLayoutMediator?.attach()
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        binding.pagerGuidelines.adapter = null
        _binding = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
    }

}