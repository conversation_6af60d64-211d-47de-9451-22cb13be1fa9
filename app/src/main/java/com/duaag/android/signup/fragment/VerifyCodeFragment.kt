package com.duaag.android.signup.fragment


import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazonaws.services.cognitoidentityprovider.model.AliasExistsException
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.ExpiredCodeException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.duaag.android.R
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.broadcasts.SMSReceiver
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.ErrorType6DigitCodeValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentVerifyCodeBinding
import com.duaag.android.exceptions.UserAlreadyExistException
import com.duaag.android.exceptions.UserNotExistException
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.launcher.SplashActivity.Companion.CONFIRMATION_CODE_INTENT_FILTER
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.SignUpAuthResult
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getHourFromErrorMessage
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.PinField
import com.google.android.gms.auth.api.phone.SmsRetriever
import timber.log.Timber
import javax.inject.Inject

class VerifyCodeFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentVerifyCodeBinding? = null
    private val binding get() = _binding!!


    private val deepLinkReceiver = ConfirmCodeBroadcastReceiver()
    private var smsIntentFilter: IntentFilter? = null
    private var smsReceiver: SMSReceiver? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
        Timber.tag("VIEWMODEL").d(sharedViewModel.toString())
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentVerifyCodeBinding.inflate(inflater)
        val view = binding.root

        send6digitCodeSignUpEvent()

        // Set soft input mode to adjust resize to prevent keyboard from covering content
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireActivity().registerReceiver(deepLinkReceiver, IntentFilter(CONFIRMATION_CODE_INTENT_FILTER), RECEIVER_NOT_EXPORTED)
        } else {
            requireActivity().registerReceiver(deepLinkReceiver, IntentFilter(CONFIRMATION_CODE_INTENT_FILTER))
        }

        initSmsListener()
        initSmsBroadCast()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireActivity().registerReceiver(smsReceiver,smsIntentFilter, RECEIVER_NOT_EXPORTED)
        } else {
            requireActivity().registerReceiver(smsReceiver,smsIntentFilter)
        }

        binding.resendBtn.setOnSingleClickListener(1000L) {
            (requireActivity() as SignUpActivity).executeResendCodeReCaptcha()
            sendClevertapEvent(ClevertapEventEnum.RESEND_6DIGIT_CODE,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value
                )
            )
            sendUxCamEvent(ClevertapEventEnum.RESEND_6DIGIT_CODE,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value
                )
            )
        }

        val signUpOrSignInmMedium =   if(sharedViewModel.authMethod == AuthMethod.PHONE) {
            ClevertapSignUpOrSignInMediumValues.PHONE.value
        }else{
            ClevertapSignUpOrSignInMediumValues.EMAIL.value
        }

        sharedViewModel.verifyResultLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is SignUpAuthResult.Loading -> {
//                    binding.progressBar.visibility = View.VISIBLE
                }

                is SignUpAuthResult.Error -> {
//                    binding.progressBar.visibility = View.GONE
                    when (it.e) {

                        is CodeMismatchException, is ExpiredCodeException -> {
//                            errorDialog(getString(R.string.wrong_verification_code))

                            //set error UI wrong code instead of showing dialog
                            binding.errorText.visibility = View.VISIBLE
                            binding.errorText.text = getString(R.string.onboarding_code_error)
                            setBorderColorPinView(R.color.fields_required)
                        }
                        is UserLambdaValidationException -> {
                            when {
                                it.e.message?.contains("CustomMessage") == true -> {
                                    val message = getHourFromErrorMessage(it.e.errorMessage)
                                    errorDialog(message)
                                }
                                else -> {
                                    errorDialog(getString(R.string.smthg_went_wrong))
                                    logError(ErrorStatus.VERIFY_CODE_ERROR)
                                }
                            }

                        }
                        is LimitExceededException -> {
                            errorDialog(getString(R.string.attempt_limit_reached_try_again_later))
                        }
                    }
                }
                is SignUpAuthResult.UserExists -> {
                    binding.modifiedPinView.apply {
                        isEnabled = true
                        showKeyboard()
                    }
//                    binding.progressBar.visibility = View.GONE
                    if (it.e is AliasExistsException) {
//                        handleVerifyError(R.string.user_exists, View.VISIBLE)
                        errorDialog(getString(R.string.account_already_exists_phone))
                        sendThisAccountExistEvent(signUpOrSignInmMedium)
                    }
                }
                else -> {}
            }
        }
        sharedViewModel.elapsedTime.observe(viewLifecycleOwner) {
            val firstPart = getString(R.string.onboarding_resend_code)
            val secondPart = " (${it})"
            val fullString = firstPart + secondPart

            val spannableString = SpannableString(fullString)

            // Set description_primary color for the first part
            spannableString.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_description_secondary)),
                0,
                firstPart.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // Set description_secondary color for the second part (timer)
            spannableString.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_description_primary)),
                firstPart.length,
                fullString.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            binding.timerTextV.text = spannableString
        }
        sharedViewModel.showTimer.observe(viewLifecycleOwner) {
            binding.resendBtn.isVisible = !it
            binding.timerTextV.isVisible = it
        }

        sharedViewModel.doNotShowTimmerIfLimitIsReached.observe(viewLifecycleOwner){
            binding.timerTextV.visibility = View.GONE
            binding.resendBtn.visibility = View.VISIBLE
            sharedViewModel.stopTimer()
        }

        sharedViewModel.verifyDigits.observe(viewLifecycleOwner){
            it?.let {
                binding.modifiedPinView.setText(it)
            }
        }
        sharedViewModel.verifyResultCustomSmsLiveData.observe(viewLifecycleOwner, Observer {
            when (it) {
                is SignUpAuthResult.Loading -> {
                    binding.modifiedPinView.apply {
                        isEnabled = false
                    }

//                    binding.progressBar.visibility = View.VISIBLE
                }
                is SignUpAuthResult.Error -> {
                    binding.modifiedPinView.apply {
                        isEnabled = true
                        showKeyboard()
                    }
//                    binding.progressBar.visibility = View.GONE
                    when (it.e) {
                        is CodeMismatchException, is ExpiredCodeException -> {
                            val errorType = ErrorType6DigitCodeValues.WRONG_CODE_WRITTEN.value
                            sendWrong6DigitCodeEvent(errorType)

                            //set error UI wrong code instead of showing dialog
                            binding.errorText.visibility = View.VISIBLE
                            binding.errorText.text = getString(R.string.onboarding_code_error)
                            setBorderColorPinView(R.color.fields_required)
                        }
                        is LimitExceededException -> {
                            errorDialog(getString(R.string.attempt_limit_reached_try_again_later))
                            val errorType = ErrorType6DigitCodeValues.LIMIT_TRY_EXCEEDED.value
                            sendWrong6DigitCodeEvent(errorType)
                        }
                        is UserNotExistException -> {
                            errorDialog(getString(R.string.user_does_not_exist))
                        }
                        is UserAlreadyExistException ->{
                            errorDialog(getString(R.string.account_already_exists_phone))
                            sendThisAccountExistEvent(signUpOrSignInmMedium)
//                            handleVerifyError(R.string.user_exists,View.VISIBLE)
                        }
                        else -> {
                            errorDialog(getString(R.string.smthg_went_wrong))
                            logError(ErrorStatus.VERIFY_CODE_SMS_ERROR)
                        }
                    }
                }
                else -> {}
            }
        })

        sharedViewModel.resendSignUpResultLiveData.observe(viewLifecycleOwner, Observer {
            when (it) {
                is SignUpAuthResult.Success -> {
                    ToastUtil.toast(getString(R.string.verific_code))
                }
                is SignUpAuthResult.Error -> {
                    val errorType = ErrorType6DigitCodeValues.LIMIT_RESEND_EXCEEDED.value
                    sendWrong6DigitCodeEvent(errorType)

                    val message = getHourFromErrorMessage(it.e.message!!)
                    errorDialog(message)

                    logError(ErrorStatus.RESEND_SIGN_UP_RESULT)
                }
                is SignUpAuthResult.Loading -> return@Observer

                is SignUpAuthResult.CustomSmsVerification -> {
                    ToastUtil.toast(getString(R.string.verific_code))
                    sharedViewModel.onCustomSmsVerificationUsed(true)

                }
                else -> {}
            }
        })

        lifecycleScope.launchWhenResumed {
            binding.modifiedPinView.requestFocus()
            binding.modifiedPinView.showKeyboard()
        }
        sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_ACCOUNT_CREATION_INITIATED)
        return view
    }

    private fun sendWrong6DigitCodeEvent(errorType: String?) {
        sendClevertapEvent(ClevertapEventEnum.WRONG_6DIGIT_CODE,
            mapOf(
                ClevertapEventPropertyEnum.ERROR_TYPE.propertyName to errorType,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
        sendUxCamEvent(ClevertapEventEnum.WRONG_6DIGIT_CODE,
            mapOf(
                ClevertapEventPropertyEnum.ERROR_TYPE.propertyName to errorType,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.SIGN_UP.value,
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName
            )
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val start = getString(R.string.we_ve_sent_a_6_digit_code)
        val email = " ${sharedViewModel.phoneEmail.value?.lowercase()}"
        val wordtoSpan: Spannable = SpannableString(start + email)

        // Set foundation_description_primary color for the first part
        wordtoSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_description_primary)),
            0,
            start.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // Set title_secondary color for the second part (email/phone)
        wordtoSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_title_primary)),
            start.length,
            start.length + email.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        binding.emailDescription.text = wordtoSpan
        //Assign custom listener to our custom pin view
        binding.modifiedPinView.onTextCompleteListener = PinFiledListener()
    }

    private fun setBorderColorPinView(color: Int) {
        binding.modifiedPinView.fieldColor = ContextCompat.getColor(requireContext(), color)
    }

    private fun initSmsBroadCast() {
        smsIntentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
        smsReceiver = SMSReceiver()
        smsReceiver?.setOTPListener(object : SMSReceiver.OTPReceiveListener {
            override fun onOTPReceived(otp: String?) {
                if(!otp.isNullOrEmpty() && otp.length == 6)
                    binding.modifiedPinView.setText(otp)
            }
        })
    }

    private fun initSmsListener() {
        val client = SmsRetriever.getClient(requireActivity())
        client.startSmsRetriever()
    }

    inner class PinFiledListener : PinField.OnTextCompleteListener {
        override fun onTextComplete(enteredText: String): Boolean {
            logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.SIGN_UP_CODE)

            sharedViewModel.verifyAccount(enteredText)

            return false
        }

        override fun onTextChange(enteredText: String) {
            binding.errorText.visibility = View.GONE
            setBorderColorPinView(R.color.bg_input)
        }
    }

    @SuppressLint("InflateParams")
    private fun errorDialog(errorMessage:String) {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle("oopps!")
            setMessage(errorMessage)
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }

    private fun sendThisAccountExistEvent(signUpOrSignInmMedium: String?) {
        sendClevertapEvent(
            ClevertapEventEnum.THIS_ACCOUNT_EXIST_POP_UP, mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium))
        sendUxCamEvent(
            ClevertapEventEnum.THIS_ACCOUNT_EXIST_POP_UP, mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        requireActivity().unregisterReceiver(deepLinkReceiver)
        requireActivity().unregisterReceiver(smsReceiver)
        smsReceiver = null
        smsIntentFilter = null
        _binding = null
    }

    private fun send6digitCodeSignUpEvent() {
        sendClevertapEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_UP,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName)
        )
        sendUxCamEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_UP,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName)
        )
    }

    inner class ConfirmCodeBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val code = intent?.getStringExtra(SplashActivity.DEEP_LINK_CONFIRMATION_CODE)
            if(!code.isNullOrEmpty() && code.length == 6)
                binding.modifiedPinView.setText(code)
        }
    }
}
