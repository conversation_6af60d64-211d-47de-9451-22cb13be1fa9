package com.duaag.android.signup.signup_persist.data

import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.IoDispatcher
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.signup_persist.domain.SignUpPersistRepository
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SignUpPersistRepositoryImpl @Inject constructor(
    private val duaSharedPrefs: DuaSharedPrefs,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @ApplicationScope private val externalScope: CoroutineScope
) : SignUpPersistRepository {
    override suspend fun saveStep(step: SignUpPersistModel) {
        withContext(ioDispatcher) {
            externalScope.launch {
                duaSharedPrefs.saveSignUpPersistStep(step.type.step, step.value)
            }.join()
        }
    }

    override suspend fun getSignUpSteps(): List<SignUpPersistModel> {
        return SignUpPersistStepsEnum.getAllSignUpSteps().map {
            SignUpPersistModel(it, duaSharedPrefs.getSignUpPersistStep(it.step))
        }
    }

    override suspend fun getProfileBuilderSteps(): List<SignUpPersistModel> {
        return SignUpPersistStepsEnum.getAllProfileBuilderSteps().map {
            SignUpPersistModel(it, duaSharedPrefs.getSignUpPersistStep(it.step))
        }
    }

    override suspend fun getLastStep(): SignUpPersistStepsEnum? {
        val signUpSteps = getSignUpSteps()
        val profileBuilderSteps = getProfileBuilderSteps()

        // Check if any signup steps have values (signup is in progress)
        val hasSignUpProgress = signUpSteps.any { !it.value.isNullOrEmpty() }

        if (hasSignUpProgress) {
            // User is in signup phase - return first incomplete signup step
            return signUpSteps.firstOrNull { it.value.isNullOrEmpty() }?.type
        }

        // Check if profile builder is completed (user is on "You're all Set" screen)
        val isProfileBuilderCompleted = profileBuilderSteps
            .find { it.type == SignUpPersistStepsEnum.PROFILE_BUILDER_ALL_SET }
            ?.value?.equals("true") == true

        if (isProfileBuilderCompleted) {
            // Profile builder is completed - return the all set step to show final screen
            return SignUpPersistStepsEnum.PROFILE_BUILDER_ALL_SET
        }

        // Check if any profile builder question steps have values
        val questionSteps = profileBuilderSteps.filter {
            it.type != SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF &&
            it.type != SignUpPersistStepsEnum.PROFILE_BUILDER_ALL_SET
        }
        val hasProfileBuilderProgress = questionSteps.any { !it.value.isNullOrEmpty() }

        if (hasProfileBuilderProgress) {
            // User is in profile builder phase - return first incomplete profile builder step
            return questionSteps.firstOrNull { it.value.isNullOrEmpty() }?.type
        }

        // Check if user has reached the "Be Yourself" screen
        val beYourselfStep = profileBuilderSteps
            .find { it.type == SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF }

        if (!beYourselfStep?.value.isNullOrEmpty()) {
            // User has seen the "Be Yourself" screen, start with first question
            return questionSteps.firstOrNull()?.type ?: SignUpPersistStepsEnum.PROFILE_BUILDER_CHILDREN_HAVE
        }

        // Check if user should be in profile builder mode (based on isProfileBuildingInProgress)
        // This handles the case where user is on "Be Yourself" screen but hasn't clicked the button yet
        if (duaSharedPrefs.isProfileBuildingInProgress()) {
            // User should be in profile builder but no steps are saved yet - return "Be Yourself" screen
            return SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF
        }

        // No progress in either phase - return first signup step to start from beginning
        return signUpSteps.firstOrNull()?.type
    }

    override suspend fun getProfileBuilderLastStep(): SignUpPersistStepsEnum? {
        val steps = getProfileBuilderSteps()
        if (steps.all { it.value == null }) {
            return null
        }
        return steps.firstOrNull { it.value.isNullOrEmpty() }?.type
    }

    override suspend fun clear() {
        withContext(ioDispatcher) {
            externalScope.launch {
                SignUpPersistStepsEnum.getAllSteps().map {
                    duaSharedPrefs.saveSignUpPersistStep(it.step, null)
                }
            }.join()
        }
    }
}