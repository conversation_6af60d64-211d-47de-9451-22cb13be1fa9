package com.duaag.android.signup.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.duaag.android.databinding.EmptyAddImageItemBinding
import com.duaag.android.databinding.ImageVerificationStatusItemBinding
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.setOnSingleClickListener


class ImageVerificationStatusAdapter(
    private val items: List<ImageResult>,
    private val listener: ClickListener) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    companion object {
        const val SMALL = 0
        const val EMPTY = 3
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            EMPTY -> {
                val binding: EmptyAddImageItemBinding =
                    EmptyAddImageItemBinding.inflate(layoutInflater, parent, false)
                EmptyViewHolder(binding)

            }
            else -> {
                val binding: ImageVerificationStatusItemBinding =
                    ImageVerificationStatusItemBinding.inflate(layoutInflater, parent, false)
                SmallViewHolder(binding)

            }
        }
    }

    class SmallViewHolder(private var binding: ImageVerificationStatusItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ImageResult, listener: ClickListener) {
            if(bindingAdapterPosition == 0)
                binding.firstImageLabel.visibility = View.VISIBLE
            else
                binding.firstImageLabel.visibility = View.GONE

            Glide.with(binding.image)
                .load(item.location ?: getS3Url(item.key))
                .transform(
                    CenterCrop(),
                    RoundedCorners(convertDpToPixel(20F, binding.root.context).toInt())
                )
                .into(binding.image)

            binding.removeButton.setOnSingleClickListener {
                listener.onRemoveClick(item)
            }
        }
    }

    inner class EmptyViewHolder(private var binding: EmptyAddImageItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

            init {
                binding.root.setOnSingleClickListener {
                    listener.onAddClick()
                }
            }
        fun bind(item: ImageResult) {
            if (!item.isEmpty) {
                Glide.with(binding.photo)
                    .load(item.location ?: getS3Url(item.key))
                    .transform(
                        CenterCrop(),
                        RoundedCorners(convertDpToPixel(20F, binding.root.context).toInt())
                    )
                    .into(binding.photo)
            } else {
                if (bindingAdapterPosition == 0) {
                    binding.firstImageLabel.visibility = View.VISIBLE
                } else {
                    binding.firstImageLabel.visibility = View.GONE
                }
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return if(items[position].isEmpty)
            EMPTY
        else SMALL
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            EMPTY -> (holder as EmptyViewHolder).bind(items[position])
            SMALL -> (holder as SmallViewHolder).bind(items[position], listener)
        }
    }

    interface ClickListener {
        fun onAddClick()
        fun onRemoveClick(item: ImageResult)
    }
}