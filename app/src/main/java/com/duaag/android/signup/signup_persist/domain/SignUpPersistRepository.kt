package com.duaag.android.signup.signup_persist.domain

import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum

interface SignUpPersistRepository {
   suspend fun saveStep(step: SignUpPersistModel)
   suspend fun getSignUpSteps(): List<SignUpPersistModel>
   suspend fun getProfileBuilderSteps(): List<SignUpPersistModel>
   suspend fun getLastStep(): SignUpPersistStepsEnum?
   suspend fun getProfileBuilderLastStep(): SignUpPersistStepsEnum?
   suspend fun clear()
}