package com.duaag.android.signup.signup_persist.domain.use_cases

import com.duaag.android.signup.signup_persist.domain.SignUpPersistRepository
import javax.inject.Inject

class ClearAllPersistStepsIncludingProfileBuilderUseCase @Inject constructor(
    private val signUpPersistRepository: SignUpPersistRepository
) {
    suspend operator fun invoke() = signUpPersistRepository.clearAllIncludingProfileBuilder()
}
