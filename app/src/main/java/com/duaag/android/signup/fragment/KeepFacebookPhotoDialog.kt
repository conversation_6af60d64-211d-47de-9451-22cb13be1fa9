package com.duaag.android.signup.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.databinding.KeepFacebookPhotoDialogBinding

class KeepFacebookPhotoDialog : DialogFragment() {


    private  var _binding: KeepFacebookPhotoDialogBinding? = null
    private  val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = KeepFacebookPhotoDialogBinding.inflate(layoutInflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

        binding.btnKeep.setOnClickListener {
            (parentFragment as KeepFacebookPhotoListener).onKeepClicked()
            dismiss()
        }

        binding.uploadNewBtn.setOnClickListener {
            (parentFragment as KeepFacebookPhotoListener).onUploadNewClicked()
            dismiss()
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface KeepFacebookPhotoListener {
        fun onKeepClicked()
        fun onUploadNewClicked()
    }
}