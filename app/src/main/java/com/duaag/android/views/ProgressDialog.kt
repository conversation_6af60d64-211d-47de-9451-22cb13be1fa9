package com.duaag.android.views

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.ContextMenu
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.duaag.android.R
import com.duaag.android.databinding.UploadFileDialogBinding
import com.duaag.android.utils.convertDpToPixel
import org.w3c.dom.Text

class ProgressDialog(context: Context, var string: String) : Dialog(context) {


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setContentView(R.layout.upload_file_dialog)
        val uploadText = findViewById<TextView>(R.id.upload_text)
        val progressBar = findViewById<CircularProgressView>(R.id.progress_bar)
        uploadText.text = string
        setCanceledOnTouchOutside(false)
        setCancelable(false)

        progressBar.setProgress(0f)
        progressBar.setRounded(true)
        progressBar.setProgressColor(ContextCompat.getColor(context, R.color.pink_500))
        progressBar.setProgressBackgroundColor(ContextCompat.getColor(context, R.color.border))
        progressBar.setProgressWidth(convertDpToPixel(4f, context))
    }


    fun updateProgress(progress: Int?) {
        val uploadPercentage = findViewById<CircularProgressView>(R.id.progress_bar)
        uploadPercentage.setProgress(progress?.toFloat() ?: 0f)
        val progressPercentage = findViewById<TextView>(R.id.progress_percentage)
        progressPercentage.text = "$progress%"
        if (progress == -1)
            dismiss()
    }
}