package com.duaag.android.views

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.clevertap.*
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import kotlinx.parcelize.Parcelize
import timber.log.Timber
import javax.inject.Inject

class OutOfImpressionsDialog : DialogFragment() {


    companion object {
        const val INTERACTION_TYPE = "interaction_type"
        const val INVITE_TYPE = "invite_type"

        fun newInstance(interactionType: InteractionType): OutOfImpressionsDialog {
            val fragment = OutOfImpressionsDialog()
            val args = Bundle()
            args.putParcelable(INTERACTION_TYPE, interactionType)
            fragment.arguments = args
            return fragment
        }

        fun newInstance(inviteType:InviteType? = InviteType.OTHER): OutOfImpressionsDialog {
            val fragment = OutOfImpressionsDialog()
            val args = Bundle()
            fragment.arguments = args
            args.putParcelable(INVITE_TYPE, inviteType?:InviteType.OTHER)
            return fragment
        }
    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ if(activity is HomeActivity) activity as HomeActivity else activity as ChangeLocationActivity }) { viewModelFactory }
    private lateinit var interactionType: InteractionType
    private var inviteType: InviteType? = InviteType.OTHER


    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (requireActivity() is HomeActivity) {
            (requireActivity() as HomeActivity).homeComponent.inject(this)
        } else {
            (requireActivity() as ChangeLocationActivity).changeLocationComponent.inject(this)
        }
        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        interactionType = arguments?.getParcelable(INTERACTION_TYPE) ?: InteractionType.LIKE
        inviteType = arguments?.getParcelable(INVITE_TYPE) ?: InviteType.OTHER
        sendInviteAFriendScreenView()
    }

    @SuppressLint("StringFormatMatches")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder: AlertDialog.Builder? = this.let { AlertDialog.Builder(requireContext()) }
        val customLayout: View = layoutInflater.inflate(R.layout.out_impression_dialog, null)
        val impressionsText = customLayout.findViewById<TextView>(R.id.impressions_txt)
        val image = customLayout.findViewById<ImageView>(R.id.image)
        val countDownTimer = customLayout.findViewById<TextView>(R.id.count_down_timer)
        val impressionDesc = customLayout.findViewById<TextView>(R.id.txt_impression_desc)
        val referButton = customLayout.findViewById<Button>(R.id.refer_friend_button)
        val notNowButton = customLayout.findViewById<TextView>(R.id.notNowButton)


//        impressionsText.text = when (interactionType) {
//            InteractionType.LIKE, InteractionType.DISLIKE -> {
//                getString(R.string.invite_tx, "100")
//            }
//            InteractionType.SUPER_LIKE -> {
//                getString(R.string.invite_tx, "100")
//            }
//            InteractionType.INSTA_CHAT -> {
//                getString(R.string.insta_chat_dialog_title, "100")
//            }
//            InteractionType.UNDO -> {
//                getString(R.string.undo_dialog_title, "100")
//            }
//            InteractionType.IS_FLYING -> {
//                getString(R.string.invite_a_friend_and_get_1_flight,"100","1","1","1")
//            }
//        }

//        impressionDesc.let {
//            when (interactionType) {
//                InteractionType.LIKE, InteractionType.DISLIKE -> {
//                    it.visibility = View.VISIBLE
//                    it.text = getString(R.string.or_wait)
//                }
//                InteractionType.SUPER_LIKE -> {
//                    it.text = getString(R.string.or_wait)
//                }
//                InteractionType.INSTA_CHAT -> {
//                    it.visibility = View.GONE
//                }
//                InteractionType.IS_FLYING -> {
//                    it.visibility = View.GONE
//                }
//                InteractionType.UNDO -> {
//                    setVisibility(impressionDesc, (homeViewModel.getUndoLimit() > 0))
//                }
//            }
//        }

//        if(interactionType == InteractionType.INSTA_CHAT) {
//            val resetTime = homeViewModel.userProfile.value?.instachatCounterResetTime
//            val lessThan24Hours = isLessThan24HoursFromNow(resetTime?: (getTimeStampAfter12Hours()))
//            countDownTimer.visibility = if (lessThan24Hours) View.VISIBLE else View.GONE
//        }

//        GlideApp.with(requireContext())
//                .load(when (interactionType) {
//                    InteractionType.INSTA_CHAT -> R.drawable.out_of_instachats
//                    InteractionType.UNDO -> R.drawable.out_of_undo
//                    InteractionType.IS_FLYING -> R.drawable.out_of_flights
//                    else -> R.drawable.out_of_interactions
//                })
//                .centerInside()
//                .into(image)

//        customLayout.findViewById<TextView>(R.id.txt_impression_desc).apply {
//            val description = when (interactionType) {
//                InteractionType.LIKE, InteractionType.DISLIKE -> {
//                    R.string.out_of_superlikes_an
//                }
//                InteractionType.SUPER_LIKE -> {
//                    R.string.out_of_superlikes_an
//                }
//            }
//            addDifferentTypeface(this, description)
//        }

//        noThanksBtn.setOnClickListener {
//            dialog?.dismiss()
//        }
        referButton.let {
//            it.text = when (interactionType) {
//                InteractionType.LIKE, InteractionType.DISLIKE, InteractionType.INSTA_CHAT, InteractionType.UNDO ,InteractionType.IS_FLYING,InteractionType.PROFILE-> {
//                    getString(R.string.invite_a_friend)
//
//                }
//                InteractionType.SUPER_LIKE -> {
//                    getString(R.string.refer_a_friend)
//
//                }
//            }
            it.setOnClickListener {
                if (interactionType==InteractionType.IS_FLYING){
                    (requireActivity() as ChangeLocationActivity).checkShareCompat()

                }else{
                (requireActivity() as HomeActivity).checkShareCompat()
                when (interactionType) {
                    InteractionType.LIKE, InteractionType.DISLIKE -> {
                        firebaseLogEvent(
                            FirebaseAnalyticsEventsName.REFER_FRIENDS_BUTTONCLICK_IMPRESSIONS,
                            mapOf(
                                FirebaseAnalyticsParameterName.REFER_FRIENDS_IMPRESSIONS_COUNT.value to 1L
                            )
                        )

                    }
                    InteractionType.SUPER_LIKE -> {
                    }

                    InteractionType.INSTA_CHAT -> {
                        firebaseLogEvent(
                            FirebaseAnalyticsEventsName.REFER_FRIENDS_BUTTONCLICK_INSTACHAT, mapOf(
                                FirebaseAnalyticsParameterName.REFER_FRIENDS_BUTTONCLICK_INSTACHAT_COUNT.value to 1L
                            )
                        )

                    }

                    InteractionType.UNDO -> {

                    }
                    else -> {}
                }

                }

                sendInviteAFriendsInitiatedEvent()

                dialog?.dismiss()
            }

        }

        builder?.setView(customLayout)
        val dialog = builder?.create()?.apply {
            this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }


        notNowButton.setOnClickListener {
            checkForWatchAd()
            dialog?.dismiss()
        }

//        countDown(countDownTimer)

        return dialog as Dialog
    }

    override fun onCancel(dialog: DialogInterface) {
        checkForWatchAd()
        super.onCancel(dialog)
    }
    private fun sendInviteAFriendScreenView() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.INVITE_A_FRIEND_SCREENVIEW,
            mapOf(FirebaseAnalyticsParameterName.BILLING_AVAILABLE.value to DuaApplication.instance.getBillingAvailable().toString(),
                FirebaseAnalyticsParameterName.INVITE_TYPE.value to (inviteType?.value
                    ?: InviteType.OTHER.value)))

        sendClevertapEvent(ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW,
            mapOf(ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName to DuaApplication.instance.getBillingAvailable(),
                ClevertapEventPropertyEnum.INVITE_TYPE.propertyName to (inviteType?.value
                    ?: InviteType.OTHER.value),
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    homeViewModel.userProfile.value),
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapEventSourceValues.IMPRESSIONS.value))
    }

    private fun sendInviteAFriendsInitiatedEvent() {

        firebaseLogEvent(FirebaseAnalyticsEventsName.INVITE_A_FRIEND_INITIATED,
            mapOf(FirebaseAnalyticsParameterName.BILLING_AVAILABLE.value to DuaApplication.instance.getBillingAvailable().toString(),
                FirebaseAnalyticsParameterName.INVITE_TYPE.value to (inviteType?.value
                    ?: InviteType.OTHER.value)))

        sendClevertapEvent(
            ClevertapEventEnum.INVITE_A_FRIEND_INITIATED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    homeViewModel.userProfile.value),
                ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName to DuaApplication.instance.getBillingAvailable(),
                ClevertapEventPropertyEnum.INVITE_TYPE.propertyName to (inviteType?.value
                    ?: InviteType.OTHER.value),
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapEventSourceValues.IMPRESSIONS.value))
    }
    private fun checkForWatchAd() {
        if (activity is HomeActivity && (interactionType == InteractionType.LIKE || interactionType == InteractionType.DISLIKE)) {
            homeViewModel.checkForWatchAd()
        }
    }

    @Parcelize
    enum class InviteType(val value:String) : Parcelable {
        OTHER("other"),DEFAULT("default")
    }

//    private fun countDown(timestamp: TextView) {
//        val resetTime = when (interactionType) {
//            InteractionType.LIKE, InteractionType.DISLIKE -> homeViewModel.userProfile.value?.interactionCounterResetTime
//                    ?: (getTimeStampAfterXHours(homeViewModel.userProfile.value?.settings?.limits?.hourInterval ?: 12))
//            InteractionType.SUPER_LIKE -> homeViewModel.userProfile.value?.superLikeCounterResetTime
//                    ?: (getTimeStampAfterXHours(12))
//            InteractionType.INSTA_CHAT -> homeViewModel.userProfile.value?.instachatCounterResetTime
//                    ?: (getTimeStampAfterXHours(homeViewModel.userProfile.value?.settings?.limits?.instachatHourInterval ?: 24))
//            InteractionType.UNDO -> {
//                if (homeViewModel.getUndoLimit() > 0) {
//                    homeViewModel.userProfile.value?.undoCounterResetTime
//                        ?: (getTimeStampAfterXHours(homeViewModel.userProfile.value?.settings?.limits?.hourInterval ?: 12))
//                } else return
//            }
//            InteractionType.IS_FLYING -> homeViewModel.userProfile.value?.flyingCounterResetTime
//                    ?: (getTimeStampAfterXHours(homeViewModel.userProfile.value?.settings?.limits?.hourInterval ?: 12))
//
//        }
//        val countDownTimer = object : CountDownTimer(resetTime - System.currentTimeMillis(), 1000) {
//            override fun onTick(p0: Long) {
//                var millisUntilFinished = p0
//
//                val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
//                millisUntilFinished -= TimeUnit.HOURS.toMillis(hours)
//
//                val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
//                millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)
//
//                val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)
//
//                timestamp.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
//            }
//
//            override fun onFinish() {
//                dismissAllowingStateLoss()
//            }
//        }
//        countDownTimer.start()
//    }
}