package com.duaag.android.views

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.ViewTreeObserver.*
import android.widget.Button
import android.widget.ImageView
import androidx.appcompat.app.AlertDialog
import androidx.core.animation.doOnEnd
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.clevertap.*
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.HomeActivity.Companion.INTERACTION_LIMIT
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import javax.inject.Inject

class EnvelopeDialog : DialogFragment() {

    companion object {
        const val EVENT_SOURCE = "event_source"
        fun newInstance(interactionLimit:String,eventSource: String?): EnvelopeDialog {
            val fragment= EnvelopeDialog()
            val args=Bundle()
            args.putString(INTERACTION_LIMIT,interactionLimit)
            args.putString(EVENT_SOURCE,eventSource)
            fragment.arguments=args
            return fragment
        }
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ if (activity is HomeActivity) activity as HomeActivity else activity as ChangeLocationActivity }) { viewModelFactory }
    private lateinit var customLayout: View
    private var interactionLimit: String? = "+50"
    private var eventSource: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (requireActivity() is HomeActivity) {
            (requireActivity() as HomeActivity).homeComponent.inject(this)
        } else {
            (requireActivity() as ChangeLocationActivity).changeLocationComponent.inject(this)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // Use the Builder class for convenient dialog construction
        val builder = AlertDialog.Builder(requireContext())
        customLayout = layoutInflater.inflate(R.layout.envelope_dialog_layout, null)

        customLayout.findViewById<Button>(R.id.got_it_button).setOnClickListener {
            dismissAllowingStateLoss()

            sendSuccessfullyInvitedAFriendsEvent()
        }

        val body = customLayout.findViewById<ImageView>(R.id.envelope_body)
        val head = customLayout.findViewById<ImageView>(R.id.envelope_head)
        val letter = customLayout.findViewById<ImageView>(R.id.envelope_letter)
        body.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                body.viewTreeObserver.removeOnGlobalLayoutListener(this)
                head.pivotY = (head.measuredHeight).toFloat()
                head.pivotX = (head.measuredWidth / 2).toFloat()

                val animation1 = ObjectAnimator.ofFloat(letter, View.TRANSLATION_Y, body.y - letter.y).apply {
                    startDelay = 500
                    duration = 300
                    doOnEnd {
                        head.elevation = 0f
                    }

                }
                val animation2 = ObjectAnimator.ofFloat(head, View.ROTATION_X, 0f, -180f).apply {
                    duration = 400
                }
                AnimatorSet().apply {
                    playSequentially(animation1, animation2)
                    start()
                }
            }
        })
        builder.setView(customLayout)
        // Create the AlertDialog object and return it
        val dialog = builder.create().apply {
            this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    private fun sendSuccessfullyInvitedAFriendsEvent() {
        val premiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val isBillingAvailable = DuaApplication.instance.getBillingAvailable()
        val inviteType = if(eventSource == ClevertapEventSourceValues.HOME.value) OutOfImpressionsDialog.InviteType.DEFAULT.value else OutOfImpressionsDialog.InviteType.OTHER

        sendClevertapEvent(ClevertapEventEnum.SUCCESSFULLY_INVITED_A_FRIEND, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName to isBillingAvailable,
            ClevertapEventPropertyEnum.INVITE_TYPE.propertyName to inviteType
        ))

        firebaseLogEvent(FirebaseAnalyticsEventsName.SUCCESSFULLY_INVITED_A_FRIEND, mapOf(
            FirebaseAnalyticsParameterName.BILLING_AVAILABLE.value to DuaApplication.instance.getBillingAvailable().toString()
        ))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if(arguments !=null){
            val args=arguments
            interactionLimit=args?.getString(INTERACTION_LIMIT)
            eventSource = args?.getString(EVENT_SOURCE)
        }
        firebaseLogEvent(
                FirebaseAnalyticsEventsName.GOT_IT_BUTTONCLICK, mapOf(
                FirebaseAnalyticsParameterName.SENT_INVITATION_LINK_COUNT.value to 1L))
    }
}
