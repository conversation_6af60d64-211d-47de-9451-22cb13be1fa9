package com.duaag.android.views

import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.Log


internal class CustomProgressDrawable(private val mForeground: Int, private val mBackground: Int) : Drawable() {
    var mPaint: Paint = Paint()
    override fun onLevelChange(level: Int): <PERSON><PERSON><PERSON> {
        invalidateSelf()
        return true
    }

   override fun draw(canvas: Canvas) {
        val level = level
        val b: Rect = copyBounds()
        val width = b.width()
        for (i in 0 until NUM_RECTS) {
            val left = width * i / NUM_RECTS
            val right = left + 0.98f * width / NUM_RECTS
            mPaint.color = if ((i + 1) * 10000 / NUM_RECTS <= level) mForeground else mBackground
            when (i+1) {
                1 -> {
                    canvas.drawRoundRect(left.toFloat(), b.top.toFloat(), right, b.bottom.toFloat(),20f,20f,mPaint)
                    canvas.drawRect(right,b.top.toFloat(),right/2f,b.bottom.toFloat(),mPaint)
                }
                NUM_RECTS -> {
                    canvas.drawRoundRect(left.toFloat(), b.top.toFloat(), right, b.bottom.toFloat(),20f,20f,mPaint)
                    canvas.drawRect(left.toFloat(),b.top.toFloat(),(left+(right-left)/2),b.bottom.toFloat(),mPaint)
                }
                else -> {
                    canvas.drawRect(left.toFloat(), b.top.toFloat(), right, b.bottom.toFloat(), mPaint)

                }
            }
        }
    }

    override fun setAlpha(alpha: Int) {}
    override fun setColorFilter(cf: ColorFilter?) {}
    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }

    companion object {
        private const val NUM_RECTS = 6
    }
}