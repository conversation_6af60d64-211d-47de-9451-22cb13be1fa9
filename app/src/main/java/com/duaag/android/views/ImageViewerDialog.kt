package com.duaag.android.views

import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.databinding.ImageViewerDialogBinding
import com.duaag.android.utils.CustomTypefaceSpan


class ImageViewerDialog : DialogFragment() {

    companion object {
        const val IMAGES_INTENT = "images_intent"
        const val IMAGES_INDEX_INTENT = "images_index_intent"

        fun newInstance(images: List<String>, index: Int): ImageViewerDialog {
            bundleOf(IMAGES_INTENT to images, IMAGES_INDEX_INTENT to index)
            val fragment = ImageViewerDialog()
            val args = Bundle()
            args.putStringArrayList(IMAGES_INTENT, ArrayList(images))
            args.putInt(IMAGES_INDEX_INTENT, index)
            fragment.arguments = args
            return fragment
        }
    }

    private var _binding: ImageViewerDialogBinding? = null
    private val binding get() = _binding!!

    lateinit var images: ArrayList<String>
    var imagesIndex: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //set style in order to open dialog on full screen
        setStyle(STYLE_NO_TITLE, R.style.full_screen_black_dialog)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        _binding = ImageViewerDialogBinding.inflate(inflater)


        arguments?.let {
            images = it.getStringArrayList(IMAGES_INTENT) ?: arrayListOf()
            imagesIndex = it.getInt(IMAGES_INDEX_INTENT, 0)
            images.let {
                val adapter = ImagesViewerAdapter()
                binding.imagesPager.adapter = adapter
                adapter.setData(images)

                binding.imagesPager.setCurrentItem(imagesIndex, false)
            }
        }

        binding.closeButton.setOnClickListener { dismissAllowingStateLoss() }

        binding.imagesPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                setTextSpan(position)
            }
        })
        setTextSpan(binding.imagesPager.currentItem ?: 0)

        return binding.root
    }

    fun setTextSpan(position: Int) {
        val string = "${position + 1} of ${images.size}"

        val SS = SpannableStringBuilder("${position + 1} of ${binding.imagesPager.adapter?.itemCount}")

        val start = string.indexOf("of")
        val end = string.length

        SS.setSpan(ForegroundColorSpan(resources.getColor(R.color.gray_50)), 0, start, Spanned.SPAN_EXCLUSIVE_INCLUSIVE)
        SS.setSpan(ForegroundColorSpan(resources.getColor(R.color.others)), start, end, Spanned.SPAN_EXCLUSIVE_INCLUSIVE)

        binding.index.text = SS
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}