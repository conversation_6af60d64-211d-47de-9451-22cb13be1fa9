package com.duaag.android.clevertap

import android.content.Context
import com.appsflyer.AppsFlyerLib
import com.clevertap.android.sdk.CleverTapAPI
import com.duaag.android.BuildConfig
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.AccountModel
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.APPLANGUAGE
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.BOOST_COUNTER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.CHECKED_IN_SUNNY_HILL
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.COMMUNITY
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.COUNTRY
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.DISLIKES_COUNTER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.DOB
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.EMAIL
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.GENDER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.HAS_LOCATION_SERVICES_ENABLED
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.IDENTITY
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.INSTACHAT_COUNTER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.ISPREMIUM
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.IS_PROFILE_HIDDEN
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.JOINED_SUNNY_HILL_GIVEAWAY
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.LIKES_COUNTER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.LOCATION_SERVICES_STATUS
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.LOGGED_OUT
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.MSG_EMAIL
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.MSG_PUSH
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.MSG_SMS
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.MSG_WHATSAPP
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.NAME
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.OPERATING_SYSTEM
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.PHONE
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.PREMIUM_TYPE
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.PROFILE_COMPLETION_PERCENTAGE
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.THEME
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.USER_ACCOUNT_CREATED_AT
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.USER_GENDER
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.USER_PROFILE_CREATED_AT
import com.duaag.android.clevertap.ClevertapUserPropertyEnum.USER_SOURCE
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.FilterType
import com.duaag.android.home.models.GooglePlayPackage
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.convertClevertapDateToString
import com.duaag.android.utils.convertISODateToString
import com.duaag.android.utils.createDateFromUTCString
import com.duaag.android.utils.createDateObjectFromBirthday
import com.duaag.android.utils.getLocationServicesStatus
import com.duaag.android.utils.getNotificationsOnClevertapValue
import com.duaag.android.utils.isDarkModeEnabled
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.isPostNotificationsPermissionEnabled
import com.duaag.android.uxcam.sendUxCamEvent
import java.util.Date

fun onLoginCleverTap(email: String?, phone: String?, cognitoUserId: String) {

    val profileUpdate = HashMap<String, Any?>()
    profileUpdate[IDENTITY.value] = cognitoUserId
    profileUpdate[EMAIL.value] = email
    profileUpdate[PHONE.value] = phone
    profileUpdate[OPERATING_SYSTEM.value] = "android"
    profileUpdate[USER_SOURCE.value] = DuaSharedPrefs(DuaApplication.instance).getInfluencerName().ifEmpty { "Organic" }


    val hasEmail = email != null
    profileUpdate[MSG_PUSH.value] = true           // push notifications
    profileUpdate[MSG_EMAIL.value] = hasEmail      // email notifications
    profileUpdate[MSG_SMS.value] = false           // SMS notifications
    profileUpdate[MSG_WHATSAPP.value] = false      // WhatsApp notifications

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.onUserLogin(profileUpdate)
}

fun pushUserProfileClevertap(
    userModel: UserModel,
    email: String?,
    phone: String?,
    accountCreatedAt: String?,
    notificationSettings: Map<String, Boolean>? = null,
) {

    val profileUpdate = HashMap<String, Any?>()
    profileUpdate[IDENTITY.value] = userModel.cognitoUserId
    profileUpdate[NAME.value] = userModel.firstName
    profileUpdate[PREMIUM_TYPE.value] = userModel.premiumType

    profileUpdate[EMAIL.value] = email
    profileUpdate[PHONE.value] = phone

    profileUpdate[GENDER.value] = userModel.gender
    profileUpdate[USER_GENDER.value] = userModel.gender
    profileUpdate[COUNTRY.value] = getUsersCountry(userModel)
    profileUpdate[OPERATING_SYSTEM.value] = "android"
    profileUpdate[ISPREMIUM.value] = userModel.premiumType != null
    profileUpdate[LOGGED_OUT.value] = false

    profileUpdate[USER_SOURCE.value] = DuaSharedPrefs(DuaApplication.instance).getInfluencerName().ifEmpty { "Organic" }
    profileUpdate[DOB.value] = createDateObjectFromBirthday(userModel.birthDate)
    profileUpdate[IS_PROFILE_HIDDEN.value] = userModel.profile.isInvisible
    profileUpdate[PROFILE_COMPLETION_PERCENTAGE.value] = userModel.profilePercentage

    val userProfileCreatedAt: Date? = userModel.profile.createdAt?.let { createDateFromUTCString(it) }
    val userAccountCreatedAt: Date? = accountCreatedAt?.let { createDateFromUTCString(it) }
    profileUpdate[USER_PROFILE_CREATED_AT.value] = userProfileCreatedAt
    profileUpdate[USER_ACCOUNT_CREATED_AT.value] = userAccountCreatedAt

    profileUpdate[COMMUNITY.value] = userModel.communityInfo?.id
    profileUpdate[APPLANGUAGE.value] = userModel.language

    val hasEmail = email != null
    profileUpdate[MSG_PUSH.value] = true           // push notifications
    profileUpdate[MSG_EMAIL.value] = hasEmail      // email notifications
    profileUpdate[MSG_SMS.value] = false           // SMS notifications
    profileUpdate[MSG_WHATSAPP.value] = false      // WhatsApp notifications

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate + (notificationSettings ?: emptyMap()))
}

private fun getUsersCountry(userModel: UserModel): String {
    val userAddress = (userModel.profile.actualAddress ?: userModel.profile.address ?: "")
        .substringAfter(",")
        .trim()

    return userAddress
}

fun setJoinGiveawayFestival() {
    val profileUpdate = HashMap<String, Any?>()

    profileUpdate[JOINED_SUNNY_HILL_GIVEAWAY.value] = true

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate)
}

fun setCheckedInSunnyHill() {
    val profileUpdate = HashMap<String, Any?>()

    profileUpdate[CHECKED_IN_SUNNY_HILL.value] = true

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate)
}

fun setHasLocationServicesEnabled(hasLocationEnabled: Boolean) {
    val profileUpdate = HashMap<String, Any?>()

    profileUpdate[HAS_LOCATION_SERVICES_ENABLED.value] = hasLocationEnabled

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate)
}

fun setLocationServicesStatus(hasLocationEnabled: Boolean, hasBackgroundLocationEnabled: Boolean) {
    val locationServiceStatus = if (hasLocationEnabled) {
        if (hasBackgroundLocationEnabled) ClevertapLocationServiceStatusValues.ALWAYS
        else ClevertapLocationServiceStatusValues.WHILE_USING_THE_APP
    } else {
        ClevertapLocationServiceStatusValues.NOT_ALLOWED
    }
    val profileUpdate = HashMap<String, Any?>()

    profileUpdate[LOCATION_SERVICES_STATUS.value] = locationServiceStatus.value

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate)
}

fun updateUserProfileInClevertap(userProperties: Map<String, Any?>) {
    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(userProperties)
}

fun sendClevertapEvent(event: ClevertapEventEnum, properties: Map<String, Any?>, includeCommunity: Boolean = true) {
    val propertiesWithCommunityId = mutableMapOf<String, Any?>()
    propertiesWithCommunityId.putAll(properties)

    if(includeCommunity && !propertiesWithCommunityId.containsKey(ClevertapEventPropertyEnum.COMMUNITY.propertyName))
        propertiesWithCommunityId[ClevertapEventPropertyEnum.COMMUNITY.propertyName] = DuaApplication.instance.getUserCommunityId()

    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushEvent(event.eventName, propertiesWithCommunityId)
}

//fun sendClevertapEventWithoutCommunity(event: ClevertapEventEnum, properties: Map<String, Any?>) {
//    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushEvent(event.eventName, properties)
//}

fun sendClevertapEvent(event: ClevertapEventEnum) {
    val properties = mapOf(ClevertapEventPropertyEnum.COMMUNITY.propertyName to DuaApplication.instance.getUserCommunityId() )
    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushEvent(event.eventName,properties)
}

fun sendLogOutEventCleverTap() {
    val profileUpdate = HashMap<String, Any>()
    profileUpdate[LOGGED_OUT.value] = true
    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushProfile(profileUpdate)
}

fun sendAppOpenEvent(
    userModel: UserModel?,
    event: ClevertapEventEnum,
    context: Context,
    duaSharedPrefs: DuaSharedPrefs,
    lastPersistStep: SignUpPersistStepsEnum?,
) {

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val actualProfileKey = ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName
    val isBadge2VerifiedKey = ClevertapEventPropertyEnum.IS_BADGE2_VERIFIED.propertyName
    val locationAccessKey = ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName
    val areNotificationsOnKey = ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName
    val persistUserProgressStepKey = ClevertapEventPropertyEnum.PERSIST_USER_PROGRESS_STEP.propertyName

    val locationAccess = getLocationServicesStatus(
        context.isLocationPermissionEnabled(),
        context.checkBackgroundLocationPermission()
    )
    val areNotificationsOn = getNotificationsOnClevertapValue(context, duaSharedPrefs)

    val persistUserProgressStepValues = mapLastPersistStepToClevertapStep(lastPersistStep)

    if (userModel != null) {
        val premiumTypeValue = getPremiumTypeEventProperty(userModel)
        val params = mapOf(
            premiumTypeKey to premiumTypeValue,
            actualProfileKey to userModel.profilePercentage,
            isBadge2VerifiedKey to (userModel.badge2 == Badge2Status.APPROVED.status),
            locationAccessKey to locationAccess,
            areNotificationsOnKey to areNotificationsOn,
            persistUserProgressStepKey to persistUserProgressStepValues
        )

        sendClevertapEvent(event, params)
        sendUxCamEvent(event, params)
    } else {
        val params = mapOf(
            premiumTypeKey to null,
            locationAccessKey to locationAccess,
            areNotificationsOnKey to areNotificationsOn,
            persistUserProgressStepKey to persistUserProgressStepValues
        )

        sendClevertapEvent(event, params)
        sendUxCamEvent(event, params)
    }
}

private fun mapLastPersistStepToClevertapStep(lastPersistStep: SignUpPersistStepsEnum?): String? {
    return when (lastPersistStep) {
        SignUpPersistStepsEnum.WELCOME -> "welcome"
        SignUpPersistStepsEnum.EMAIL -> "email"
        SignUpPersistStepsEnum.NAME -> ClevertapPersistUserProgressStepValues.NAME.step
        SignUpPersistStepsEnum.BIRTHDAY -> ClevertapPersistUserProgressStepValues.BIRTHDAY.step
        SignUpPersistStepsEnum.GENDER -> ClevertapPersistUserProgressStepValues.GENDER.step
        SignUpPersistStepsEnum.LOVE_STORY -> "love_story"
        SignUpPersistStepsEnum.UPLOAD_PHOTOS -> ClevertapPersistUserProgressStepValues.UPLOAD_PHOTOS.step
        SignUpPersistStepsEnum.VERIFICATION -> ClevertapPersistUserProgressStepValues.VERIFICATION.step

        SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF -> "be_yourself"
        SignUpPersistStepsEnum.PROFILE_BUILDER_LOOKING_FOR -> ClevertapPersistUserProgressStepValues.LOOKING_FOR.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_CHILDREN_HAVE -> ClevertapPersistUserProgressStepValues.DO_YOU_HAVE_CHILDREN.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_CHILDREN_WANT -> ClevertapPersistUserProgressStepValues.DO_YOU_WANT_CHILDREN.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_SMOKING -> ClevertapPersistUserProgressStepValues.DO_YOU_SMOKE.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_RELIGION -> ClevertapPersistUserProgressStepValues.RELIGION.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_LANGUAGES -> ClevertapPersistUserProgressStepValues.LANGUAGES.step
        SignUpPersistStepsEnum.PROFILE_BUILDER_ALL_SET -> "all_set"
        else -> null
    }
}

fun sendAppClosedEvent(userModel: UserModel?, event: ClevertapEventEnum) {
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName

    if (userModel != null) {
        val premiumTypeValue = getPremiumTypeEventProperty(userModel)

        sendClevertapEvent(event, mapOf(premiumTypeKey to premiumTypeValue))
    } else {
        sendClevertapEvent(event)
    }
}

fun sendLogInEventCleverTapUxCam(
    authMethod: AuthMethod?,
    premiumType: String?
) {
    val signUpOrSignInmMedium = ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName

    val signUpOrSignInmMediumValue = when (authMethod) {
        AuthMethod.EMAIL -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
        AuthMethod.PHONE -> ClevertapSignUpOrSignInMediumValues.PHONE.value
        AuthMethod.FACEBOOK -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
        AuthMethod.GOOGLE -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
        else -> null
    }

    val properties = mutableMapOf<String, Any?>()
    properties[signUpOrSignInmMedium] = signUpOrSignInmMediumValue
    properties[premiumTypeKey] = premiumType

    sendClevertapEvent(ClevertapEventEnum.SIGN_IN_SUCCESSFULLY, properties, false)
    sendUxCamEvent(ClevertapEventEnum.SIGN_IN_SUCCESSFULLY, properties)
}

fun getPremiumTypeEventProperty(userModel: UserModel?): String? {
    if(userModel == null) return ClevertapPremiumTypeValues.NULL.value
    return if (userModel?.premiumType != null)
        ClevertapPremiumTypeValues.PREMIUM.value
    else if (userModel.hasFreeStarterExperience()) ClevertapPremiumTypeValues.FREE_STARTER_EXPERIENCE.value
    else ClevertapPremiumTypeValues.FREEMIUM.value
}

fun getPremiumTypeEventProperty(isPremium: Boolean): String {
    val premiumType = if (isPremium)
        ClevertapPremiumTypeValues.PREMIUM.value
    else
        ClevertapPremiumTypeValues.FREEMIUM.value

    return premiumType!!
}

fun sendEmailOrPhoneVerificationButtonClickEvent(userModel: UserModel?, verificationType: String) {
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumType = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.EMAIL_OR_PHONE_VERIFICATION_BUTTON_CLICK,
        mapOf(
            premiumTypeKey to premiumType,
            ClevertapEventPropertyEnum.VERIFICATION_TYPE.propertyName to verificationType
        )
    )
}

fun sendEmailOrPhoneVerificationScreenViewEvent(userModel: UserModel?, verificationType: String) {
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumType = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.EMAIL_OR_PHONE_VERIFICATION_SCREENVIEW,
        mapOf(
            premiumTypeKey to premiumType,
            ClevertapEventPropertyEnum.VERIFICATION_TYPE.propertyName to verificationType
        )
    )
}

fun sendEmailOrPhoneVerificationCodeEvent(userModel: UserModel?, verificationType: String) {
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumType = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.EMAIL_OR_PHONE_VERIFICATION_CODE,
        mapOf(
            premiumTypeKey to premiumType,
            ClevertapEventPropertyEnum.VERIFICATION_TYPE.propertyName to verificationType
        )
    )
}


fun sendVerifiedEmailOrPhoneEvent(userModel: UserModel?, verificationType: String) {
    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumType = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.VERIFIED_EMAIL_OR_PHONE,
        mapOf(
            premiumTypeKey to premiumType,
            ClevertapEventPropertyEnum.VERIFICATION_TYPE.propertyName to verificationType
        )
    )
}


fun sendChangeProfilePhotoEvent(
    verificationSource: String,
    premiumType: String?,
    signUpSignInMedium: String? = null,
    eventSource: String? = null
) {
    val properties = hashMapOf(
        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to verificationSource,
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
    )

    properties[ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName] = verificationSource
    properties[ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName] = premiumType

    signUpSignInMedium?.let {
        properties[ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName] = it
    }
    eventSource?.let {
        properties[ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName] = it
    }



    sendClevertapEvent(
        ClevertapEventEnum.CHANGE_PROFILE_PHOTO,
        properties
    )
    sendUxCamEvent(
        ClevertapEventEnum.CHANGE_PROFILE_PHOTO,
        properties
    )
}

fun sendUploadImageVerificationEvent(userModel: UserModel?, eventSource: String) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumTypeValue = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.UPLOAD_IMAGE_VERIFICATION, mapOf(
            premiumTypeKey to premiumTypeValue,
            eventSourceKey to eventSource
        )
    )
}

fun sendSeeGuidelinesVerificationEvent(userModel: UserModel?, eventSource: String) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumTypeValue = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.SEE_GUIDELINES_VERIFICATION, mapOf(
            premiumTypeKey to premiumTypeValue,
            eventSourceKey to eventSource
        )
    )
}

fun sendSentImageVerificationEvent(userModel: UserModel?, eventSource: String) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumTypeValue = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.SENT_IMAGE_VERIFICATION, mapOf(
            premiumTypeKey to premiumTypeValue,
            eventSourceKey to eventSource
        )
    )
}

fun sendImageSentSuccessfullyEvent(userModel: UserModel?, eventSource: String) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumTypeValue = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.IMAGE_SENT_SUCCESSFULLY, mapOf(
            premiumTypeKey to premiumTypeValue,
            eventSourceKey to eventSource
        )
    )
}

fun sendTryPoseAgainImageVerificationEvent(userModel: UserModel?, eventSource: String) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName

    val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
    val premiumTypeValue = getPremiumTypeEventProperty(userModel)

    sendClevertapEvent(
        ClevertapEventEnum.TRY_POSE_AGAIN_IMAGE_VERIFICATION, mapOf(
            premiumTypeKey to premiumTypeValue,
            eventSourceKey to eventSource
        )
    )
}

fun sendGoPremiumInitiatedEvent(productId: String,
                                eventSource: String? = null,
                                placementId: String? = null,
                                promoCode: String? = null) {
    val productIdKey = ClevertapEventPropertyEnum.PRODUCT_ID.propertyName
    val productIdGA = productId.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")

    sendClevertapEvent(
        ClevertapEventEnum.GO_PREMIUM_INITIATED, mapOf(
            productIdKey to productIdGA,
            ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
            ClevertapEventPropertyEnum.PROMO_CODE.propertyName to promoCode,
            ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName to placementId
        )
    )

    firebaseLogEvent(
        FirebaseAnalyticsEventsName.GO_PREMIUM_INITIATED, mapOf(
            FirebaseAnalyticsParameterName.PRODUCT_ID.value to productIdGA,
            FirebaseAnalyticsParameterName.EVENT_SOURCE.value to eventSource,
            FirebaseAnalyticsParameterName.PROMO_CODE.value to promoCode,
            FirebaseAnalyticsParameterName.PLACEMENT_ID.value to placementId
    )
    )
}

fun sendGoPremiumScreenViewEvent(eventSource: String,
                                 placementId: String?,
                                 promoCode: String?) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName
    val promoCodeKey = ClevertapEventPropertyEnum.PROMO_CODE.propertyName
    val placementIdKey = ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName

    sendClevertapEvent(
        ClevertapEventEnum.GO_PREMIUM_SCREENVIEW, mapOf(
            eventSourceKey to eventSource,
            placementIdKey to placementId,
            promoCodeKey to promoCode
        )
    )
}

fun sendConsumableScreenViewEvent(eventSource: String, productType: String, placementId: String?) {
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName
    val productTypeKey = ClevertapEventPropertyEnum.PRODUCT_TYPE.propertyName
    val placementKey = ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName

    sendClevertapEvent(
        ClevertapEventEnum.CONSUMABLE_PAYWALL, mapOf(
            eventSourceKey to eventSource,
            placementKey to placementId,
            productTypeKey to productType
        )
    )

    firebaseLogEvent(
        FirebaseAnalyticsEventsName.CONSUMABLE_PAYWALL, mapOf(
            FirebaseAnalyticsParameterName.EVENT_SOURCE.value to eventSource,
            FirebaseAnalyticsParameterName.PRODUCT_TYPE.value to productType
        )
    )
}

fun sendPremiumPurchasedEvent(
    productId: String,
    priceValue: Float,
    priceCurrency: String,
    placementId: String?,
    eventSource: String? = null
) {
    val productIdKey = ClevertapEventPropertyEnum.PRODUCT_ID.propertyName
    val priceValueKey = ClevertapEventPropertyEnum.PRICE_VALUE.propertyName
    val priceCurrencyKey = ClevertapEventPropertyEnum.PRICE_CURRENCY.propertyName
    val eventSourceKey = ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName
    val placementIdKey = ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName
    val productIdGA = productId.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")

    sendClevertapEvent(
        ClevertapEventEnum.PREMIUM_PURCHASED, mapOf(
            productIdKey to productIdGA,
            priceValueKey to priceValue,
            priceCurrencyKey to priceCurrency,
            placementIdKey to placementId,
            eventSourceKey to eventSource
        )
    )

    firebaseLogEvent(
        FirebaseAnalyticsEventsName.PREMIUM_PURCHASED, mapOf(
            productIdKey to productIdGA,
            priceValueKey to priceValue,
            priceCurrencyKey to priceCurrency,
            placementIdKey to placementId,
            eventSourceKey to eventSource
        )
    )
}

fun getPlanTypeFromPackage(product: String): ClevertapPlanTypeValues {
    return when (product) {
        GooglePlayPackage.PREMIUM_PACKAGE_6_MONTHS.productId -> ClevertapPlanTypeValues.BEST_DEAL
        GooglePlayPackage.PREMIUM_PACKAGE_3_MONTHS.productId -> ClevertapPlanTypeValues.POPULAR
        else -> ClevertapPlanTypeValues.BASIC
    }
}

fun getPlanValueFromPackage(product: String): ClevertapPlanValueValues {
    return when (product) {
        GooglePlayPackage.PREMIUM_PACKAGE_6_MONTHS.productId -> ClevertapPlanValueValues.SIX_MONTHS
        GooglePlayPackage.PREMIUM_PACKAGE_3_MONTHS.productId -> ClevertapPlanValueValues.THREE_MONTHS
        else -> ClevertapPlanValueValues.ONE_MONTH
    }
}

fun sendRestorePremiumEvent(productId: String, communityId: String?) {
    val productIdKey = ClevertapEventPropertyEnum.PRODUCT_ID.propertyName
    val communityIdKey = ClevertapEventPropertyEnum.COMMUNITY.propertyName
    val productIdGA = productId.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")

    sendClevertapEvent(ClevertapEventEnum.RESTORE_PREMIUM, mapOf(
        productIdKey to productIdGA,
        communityIdKey to communityId
    ))
}

fun sendCancelSubscriptionInitiatedEvent(productId: String) {
    val productIdKey = ClevertapEventPropertyEnum.PRODUCT_ID.propertyName
    sendClevertapEvent(ClevertapEventEnum.CANCEL_SUBSCRIPTION_INITIATED, mapOf(productIdKey to productId))
}

fun sendClevertapCardResultEvent(duaSharedPrefs: DuaSharedPrefs, isCardWithResult:Boolean, userProfile:UserModel?) {
    val eventPremiumType = getPremiumTypeEventProperty(userProfile)
    val minAge = userProfile?.filter?.minAge
    val maxAge = userProfile?.filter?.maxAge
    val locationFilter = userProfile?.profile?.address
    val rangeRadiusFiltered = userProfile?.filter?.radius


    val communities = userProfile?.filter?.communitiesList
    val lookingForFiltered = userProfile?.filter?.lookingFor
    val languagesFiltered = userProfile?.filter?.languages
    val religionsFiltered = userProfile?.filter?.religions

    val isRadiusExtended = userProfile?.filter?.areFiltersExtended ?: false
    val heightExtended = userProfile?.filter?.heightExtended ?: false
    val lookingForIfIRunOut = userProfile?.filter?.lookingForExtended ?: false
    val languagesIfIRunOut = userProfile?.filter?.languagesExtended ?: false
    val religionsIfIRunOut = userProfile?.filter?.religionsExtended ?: false
    val communitiesIfRunOut = userProfile?.filter?.isGlobal ?: false

    val communitiesFilteredCount = userProfile?.filter?.communitiesList?.size ?: 0
    val minHeightFiltered = userProfile?.filter?.minHeight
    val maxHeightFiltered = userProfile?.filter?.maxHeight
    val verifiedUsersFiltered = userProfile?.filter?.verified == true
    val areNotificationsOnEventProperty =
        getNotificationsOnClevertapValue(DuaApplication.instance.applicationContext, duaSharedPrefs)
    val locationAccess = if(DuaApplication.instance.applicationContext.isLocationPermissionEnabled()) ClevertapLocationAccessValues.ALLOWED.value else ClevertapLocationAccessValues.DENIED.value


    val params = mapOf(
        ClevertapEventPropertyEnum.IS_CARD_WITH_RESULTS.propertyName to isCardWithResult,
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
        ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to isRadiusExtended,
        ClevertapEventPropertyEnum.RANGE_RADIUS_FILTERED.propertyName to rangeRadiusFiltered,
        ClevertapEventPropertyEnum.LOCATION_FILTERED.propertyName to locationFilter,
        ClevertapEventPropertyEnum.MIN_AGE_FILTERED.propertyName to minAge,
        ClevertapEventPropertyEnum.MAX_AGE_FILTERED.propertyName to maxAge,
        ClevertapEventPropertyEnum.COMMUNITIES_FILTERED.propertyName to communities,
        ClevertapEventPropertyEnum.COMMUNITIES_FILTERED_COUNT.propertyName to communitiesFilteredCount,
        ClevertapEventPropertyEnum.COMMUNITIES_IF_RUN_OUT.propertyName to communitiesIfRunOut,
        ClevertapEventPropertyEnum.MIN_HEIGHT_FILTERED.propertyName to minHeightFiltered,
        ClevertapEventPropertyEnum.MAX_HEIGHT_FILTERED.propertyName to maxHeightFiltered,
        ClevertapEventPropertyEnum.HEIGHT_IF_RUN_OUT.propertyName to heightExtended,
        ClevertapEventPropertyEnum.VERIFIED_USERS_FILTERED.propertyName to verifiedUsersFiltered,
        ClevertapEventPropertyEnum.LOOKING_FOR_FILTERED.propertyName to lookingForFiltered,
        ClevertapEventPropertyEnum.LOOKING_FOR_IF_RUN_OUT.propertyName to lookingForIfIRunOut,
        ClevertapEventPropertyEnum.LANGUAGES_FILTERED.propertyName to languagesFiltered,
        ClevertapEventPropertyEnum.LANGUAGES_IF_RUN_OUT.propertyName to languagesIfIRunOut,
        ClevertapEventPropertyEnum.RELIGION_FILTERED.propertyName to religionsFiltered,
        ClevertapEventPropertyEnum.RELIGION_IF_RUN_OUT.propertyName to religionsIfIRunOut,
        ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOnEventProperty,
        ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess
    )
    sendClevertapEvent(ClevertapEventEnum.CARD_RESULTS, params)
    sendUxCamEvent(ClevertapEventEnum.CARD_RESULTS, params)
}

fun setAppThemeInClevertap(context: Context) {
    val isDarkModeEnabled = isDarkModeEnabled(context)
    val clevertapTheme = CleverTapAPI.getDefaultInstance(context)?.getProperty(THEME.value)

    if(isDarkModeEnabled) {
        if(clevertapTheme != "dark") {
            CleverTapAPI.getDefaultInstance(context)?.pushProfile(mapOf(THEME.value to "dark"))
        }
    } else {
        if(clevertapTheme != "light") {
            CleverTapAPI.getDefaultInstance(context)?.pushProfile(mapOf(THEME.value to "light"))
        }
    }
}

fun setUserGenderInClevertap(context: Context, userGender: GenderType) {
    val userGenderKey = USER_GENDER.value
    val genderKey = GENDER.value

    val genderValue = when (userGender) {
        GenderType.MAN -> "Male"
        GenderType.WOMAN -> "Female"
    }
    val clevertapUserProperties = mapOf(userGenderKey to userGender.value, genderKey to genderValue)

    CleverTapAPI.getDefaultInstance(context)?.pushProfile(clevertapUserProperties)
}

fun setUserEmailInClevertap(context: Context, email: String) {
    val emailKey = EMAIL.value
    val clevertapUserProperties = mapOf(emailKey to email)

    CleverTapAPI.getDefaultInstance(context)?.pushProfile(clevertapUserProperties)
}

fun sendRmodEvent(
    view: RmodViewValues,
    status: RmodRemovedValues,
    premiumType: String? = null,
    compatibilityScore: Int? = 0
) {
    sendClevertapEvent(ClevertapEventEnum.RMOD,
        mapOf(
            ClevertapEventPropertyEnum.VIEW.propertyName to view.value,
            ClevertapEventPropertyEnum.STATUS.propertyName to status.value,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.COMPATIBILITY_SCORE.propertyName to compatibilityScore,
        )
    )
    firebaseLogEvent(FirebaseAnalyticsEventsName.RMOD,
        mapOf(
            FirebaseAnalyticsParameterName.VIEW.value to view.value,
            FirebaseAnalyticsParameterName.STATUS.value to status.value
        )
    )
}

fun sendRmodRemovedEvent(
    premiumType: String? = null,
    compatibilityScore: Int? = 0
) {
    sendClevertapEvent(ClevertapEventEnum.RMOD_REMOVED,
        mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.COMPATIBILITY_SCORE.propertyName to compatibilityScore,
        )
    )
}

fun setCountersLeftUserProperties(
    likesCounter: Int,
    dislikesCounter: Int,
    instachatCounter: Int,
    boostCounter: Int
){
    CleverTapAPI.getDefaultInstance(DuaApplication.instance.applicationContext)?.pushProfile(
        mapOf(
            LIKES_COUNTER.value to likesCounter,
            DISLIKES_COUNTER.value to dislikesCounter,
            INSTACHAT_COUNTER.value to instachatCounter,
            BOOST_COUNTER.value to boostCounter,
        )
    )
}

fun linkCleverTapAppsFlyer(deLink: Boolean= false) {
    CleverTapAPI.getDefaultInstance(DuaApplication.instance.applicationContext)?.getCleverTapID {clevertapId->
        val value = if(deLink) null else clevertapId
        AppsFlyerLib.getInstance().setCustomerUserId(value)
    }
}

/**
 * Updates the CleverTap profile with the user's notification permission status if necessary.
 *
 * @param context The context used for checking notification settings.
 */
fun updateNotificationPermissionStatusInCleverTap(context: Context,duaSharedPrefs: DuaSharedPrefs) {
    val notificationPermissionKey = ClevertapUserPropertyEnum.HAS_NOTIFICATIONS_ENABLED.value

    // Check if post notifications permission is enabled and combine the result with device and settings notifications status.
    val hasNotificationEnabled = if (context.isPostNotificationsPermissionEnabled()) {
        NotificationHelper.isMessageNotificationsEnables(context) && duaSharedPrefs.isPushMessageNotificationsEnables()
    } else {
        false
    }

    val clevertapUserProperties = mapOf(notificationPermissionKey to hasNotificationEnabled)
    updateUserProfileInClevertap(clevertapUserProperties)
}

fun sendMessageSentClevertapEvent(
    premiumType: String?,
    conversationId: String
) {
    sendClevertapEvent(ClevertapEventEnum.MESSAGES_SENT,
            mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                    ClevertapEventPropertyEnum.CONVERSATION_ID.propertyName to conversationId
            )
    )
}

fun sendRepliesBundleSentClevertapEvent(
    premiumType: String?,
    conversationId: String
) {
    sendClevertapEvent(ClevertapEventEnum.REPLIES_BUNDLE_SENT,
            mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                    ClevertapEventPropertyEnum.CONVERSATION_ID.propertyName to conversationId
            )
    )
}

fun sendMessageSentClevertapEvents(eventPremiumType: String?,
                                   conversationId: String,
                                   isReplyMessage: Boolean = false) {
    sendMessageSentClevertapEvent(eventPremiumType, conversationId)

    if (isReplyMessage) {
        sendRepliesBundleSentClevertapEvent(eventPremiumType, conversationId)
    }
}

fun getClevertapUserProperty(key: ClevertapUserPropertyEnum): Any? {
    val value = CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.getProperty(key.value)
    return value
}

fun areUserPropertiesSynced(userModel: UserModel, accountModel: AccountModel): Boolean {
    var isSynced = true

    try {

        val cognitoId = userModel.cognitoUserId
        val userName = userModel.firstName
        val premiumType = userModel.premiumType
        val gender = userModel.gender
        val isPremium = userModel.premiumType != null
        val dob = userModel.birthDate
        val isHidden = userModel.profile.isInvisible
        val profilePercentage = userModel.profilePercentage
        val community = userModel.communityInfo?.id
        val appLanguage = userModel.language
        val userCountry = getUsersCountry(userModel)
        val email = accountModel.email
        val phone = accountModel.phone
        val accountCreatedAt = convertISODateToString(accountModel.createdAt)


        val clevertapIdentity = getClevertapUserProperty(IDENTITY)
        val clevertapName = getClevertapUserProperty(NAME)
        val clevertapPremiumType = getClevertapUserProperty(PREMIUM_TYPE)
        val clevertapEmail = getClevertapUserProperty(EMAIL)
        val clevertapPhone = getClevertapUserProperty(PHONE)
        val clevertapGender = getClevertapUserProperty(GENDER)
        val clevertapOS = getClevertapUserProperty(OPERATING_SYSTEM)
        val clevertapLoggedOut = getClevertapUserProperty(LOGGED_OUT)
        val clevertapIsPremium = getClevertapUserProperty(ISPREMIUM)
        val clevertapDOB = convertClevertapDateToString(getClevertapUserProperty(DOB) as? String)
        val clevertapIsProfileHidden = getClevertapUserProperty(IS_PROFILE_HIDDEN)
        val clevertapProfileCompletedPercentage = getClevertapUserProperty(PROFILE_COMPLETION_PERCENTAGE)
        val clevertapUserAccountCreatedAt = convertClevertapDateToString(getClevertapUserProperty(USER_ACCOUNT_CREATED_AT) as? String)
        val clevertapCommunity = getClevertapUserProperty(COMMUNITY)
        val clevertapAppLanguage = getClevertapUserProperty(APPLANGUAGE)
        val clevertapCountry = getClevertapUserProperty(COUNTRY)

        isSynced = (
                cognitoId == clevertapIdentity &&
                        userName == clevertapName ?: "" &&
                        premiumType == clevertapPremiumType &&
                        email ?: "" == clevertapEmail ?: "" &&
                        phone ?: "" == clevertapPhone ?: "" &&
                        gender ?: "" == clevertapGender ?: "" &&
                        clevertapOS ?: "" == "android" &&
                        clevertapLoggedOut != true &&
                        isPremium == clevertapIsPremium &&
                        dob == clevertapDOB &&
                        isHidden == clevertapIsProfileHidden &&
                        profilePercentage == clevertapProfileCompletedPercentage &&
                        accountCreatedAt == clevertapUserAccountCreatedAt &&
                        community == clevertapCommunity &&
                        appLanguage == clevertapAppLanguage &&
                        userCountry == clevertapCountry
                )

        return isSynced

    } catch (e: Exception) {
        e.printStackTrace()
        return isSynced
    }
}
fun sendAddInfoInitiatedEvent(infoType: String, userModel: UserModel?) {
    val eventPremiumType = getPremiumTypeEventProperty(userModel)


    val params = mapOf(
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
        ClevertapEventPropertyEnum.SOURCE.propertyName to ClevertapEventSourceValues.CARDS.value,
        ClevertapEventPropertyEnum.INFO_TYPE.propertyName to infoType,
    )

    sendClevertapEvent(ClevertapEventEnum.ADD_INFORMATION_INITIATED, params)
    sendUxCamEvent(ClevertapEventEnum.ADD_INFORMATION_INITIATED, params)
}

fun sendAddInfoInitiatedEvent(filterType: FilterType, userModel: UserModel?) {
    val eventPremiumType = getPremiumTypeEventProperty(userModel)

    val infoType = when(filterType) {
        FilterType.HEIGHT -> InfoTypeValues.HEIGHT.value
        FilterType.LOOKING_FOR -> InfoTypeValues.LOOKING_FOR.value
        FilterType.LANGUAGES -> InfoTypeValues.LANGUAGES.value
        FilterType.RELIGION -> InfoTypeValues.RELIGION.value
        else -> ""
    }

    val params = mapOf(
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
        ClevertapEventPropertyEnum.SOURCE.propertyName to ClevertapSourceValues.ADVANCED_FILTERS.value,
        ClevertapEventPropertyEnum.INFO_TYPE.propertyName to infoType,
    )

    sendClevertapEvent(ClevertapEventEnum.ADD_INFORMATION_INITIATED, params)
    sendUxCamEvent(ClevertapEventEnum.ADD_INFORMATION_INITIATED, params)
}

fun sendAddInfoAddedEvent(filterType: FilterType,
                          userModel: UserModel?,
                          source: ClevertapVerificationSourceValues) {
    val eventPremiumType = getPremiumTypeEventProperty(userModel)

    val infoType = when(filterType) {
        FilterType.HEIGHT -> InfoTypeValues.HEIGHT.value
        FilterType.LOOKING_FOR -> InfoTypeValues.LOOKING_FOR.value
        FilterType.LANGUAGES -> InfoTypeValues.LANGUAGES.value
        FilterType.RELIGION -> InfoTypeValues.RELIGION.value
        else -> ""
    }

    val params = mapOf(
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
        ClevertapEventPropertyEnum.SOURCE.propertyName to source.value,
        ClevertapEventPropertyEnum.INFO_TYPE.propertyName to infoType,
    )

    sendClevertapEvent(ClevertapEventEnum.INFORMATION_ADDED, params)
    sendUxCamEvent(ClevertapEventEnum.INFORMATION_ADDED, params)
}

fun sendCrossPathLocationToUsePopupEvent(
    eventSource: String,
    userModel: UserModel?
) {
    val eventPremiumType = getPremiumTypeEventProperty(userModel)

    val eventProperties = mapOf(
        FirebaseAnalyticsParameterName.COMMUNITY.value to userModel?.communityInfo?.id,
        FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
        FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSource
    )

    sendClevertapEvent(ClevertapEventEnum.CROSS_PATH_LOCATION_TO_USE_POPUP, eventProperties)
}

fun sendClevertapProfileVisit(
    recommendedUserModel: RecommendedUserModel,
    userProfile: UserModel?,
    eventSource: String
) {
    fun sendProfileVisitAnalyticsEvents(
        premiumType: String?,
        eventSource: String?,
        areDetailsLocked: Boolean,
        model: CrossPath?,
        isPhotoHidden: Boolean?,
        premiumBadge: String?,
    ) {
        val interactionReceived = if(model?.counterPartInteraction != "no_interaction") model?.counterPartInteraction else null
        val interactedBefore = if(model?.currentUserInteraction != "no_interaction") model?.currentUserInteraction else null
        val spottedTimes = model?.count
        val spottedLocation = model?.address
        val recommSource = "dua"
        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_DETAILS_LOCKED.propertyName to areDetailsLocked,
                ClevertapEventPropertyEnum.INTERACTION_RECEIVED.propertyName to interactionReceived,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore,
                ClevertapEventPropertyEnum.TIMES_SPOTTED.propertyName to spottedTimes,
                ClevertapEventPropertyEnum.SPOTTED_LOCATION.propertyName to spottedLocation,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

            )
        )

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.PROFILE_VISIT_CONVERSATION, mapOf(
                FirebaseAnalyticsParameterName.PROFILE_VISIT_CONVERSATION_COUNT.value to 1L
            )
        )
    }

    val premiumType = getPremiumTypeEventProperty(userProfile)
   // val eventSource = ClevertapEventSourceValues.CROSS_PATH.value
    val areDetailsLocked = userProfile?.tags.isNullOrEmpty()
    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

    sendProfileVisitAnalyticsEvents(premiumType, eventSource, areDetailsLocked,recommendedUserModel.crossPath,isPhotoHidden, premiumBadge)
}