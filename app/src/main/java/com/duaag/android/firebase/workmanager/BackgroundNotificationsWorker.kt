package com.duaag.android.firebase.workmanager

import android.content.Context
import android.os.Bundle
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.duaag.android.ads.likedyou.models.NotificationRewardModel
import com.duaag.android.ads.likedyou.models.RewardType
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.base.models.Badge2VerificationState
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.chat.ChatRepository
import com.duaag.android.chat.model.*
import com.duaag.android.chat.repositories.LikedYouRepository
import com.duaag.android.clevertap.*
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.clevertap.offers.saveOfferInPreferences
import com.duaag.android.counters.domain.GetAllCountersUseCase
import com.duaag.android.counters.domain.GetUserCounterByConfNameUseCase
import com.duaag.android.counters.domain.UpdateUserCounterUseCase
import com.duaag.android.crosspath.domain.usecase.CancelCrossPathWorkersUseCase
import com.duaag.android.crosspath.domain.usecase.ScheduleOneTimeFetchAndSyncLocationUseCase
import com.duaag.android.firebase.DuaFirebaseMessagingService
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.*
import com.duaag.android.home.models.*
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsUnblureSourceValutes
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.catch
import timber.log.Timber
import java.lang.Exception
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class BackgroundNotificationsWorker(val context: Context, val params: WorkerParameters) :
    CoroutineWorker(context, params) {
    val title: String?
        get() {
            return params.inputData.getString(DuaFirebaseMessagingService.TITLE)
        }

    val body: String?
        get() {
            return params.inputData.getString(DuaFirebaseMessagingService.BODY)
        }

    val jsonData: String?
        get() {
            return params.inputData.getString(DuaFirebaseMessagingService.JSON_DATA)
        }

    val action: String?
        get() {
            return params.inputData.getString(DuaFirebaseMessagingService.ACTION)
        }

    @Inject
    lateinit var chatRepository: ChatRepository

    @Inject
    lateinit var likedYouRepository: LikedYouRepository

    @Inject
    lateinit var userRepository: UserRepository

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var getAllCountersUseCase: GetAllCountersUseCase

    @Inject
    lateinit var getUserCounterUseCase: GetUserCounterByConfNameUseCase

    @Inject
    lateinit var updateUserCounterUseCase: UpdateUserCounterUseCase

    @Inject
    lateinit var scheduleOneTimeFetchAndSyncLocationUseCase: ScheduleOneTimeFetchAndSyncLocationUseCase

    @Inject
    lateinit var cancelCrossPathWorkersUseCase: CancelCrossPathWorkersUseCase


    override val coroutineContext: CoroutineDispatcher
        get() = Dispatchers.IO

    override suspend fun doWork(): Result {
        (context as DuaApplication).appComponent.backgroundNotificationsWorkerComponent().create()
            .inject(this)
        val extras = Bundle().apply {
            putString(DuaFirebaseMessagingService.ACTION, action)
            putString(DuaFirebaseMessagingService.JSON_DATA, jsonData)
        }
        val notificationModel = NotificationModel(extras)
        when (notificationModel.type) {
            NotificationType.MESSAGE -> {
                val conversationModel =
                    (notificationModel.data as ConversationData).conversationMember
                if (conversationModel.type != ConversationType.INSTANT_CHAT.value) {

                    firebaseLogEvent(FirebaseAnalyticsEventsName.NEW_MESSAGE_NOTIFICATION_RECEIVED,
                        mapOf(FirebaseAnalyticsParameterName.NEW_MESSAGE_COUNT.value to 1L))

                } else {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.NEW_INSTACHAT_NOTIFICATION_RECEIVED,
                        mapOf(FirebaseAnalyticsParameterName.NEW_INSTACHAT_COUNT.value to 1L))

                    val premiumTypeValue = getPremiumTypeEventProperty(userRepository.user.value)
                    sendClevertapEvent(ClevertapEventEnum.INSTACHAT_RECEIVED,
                        mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue))
                }

                //user has allowed calls
                if ((notificationModel.data!! as ConversationData).message.type == MessageType.CALLS_ALLOWED.value)
                    conversationModel.areOutgoingCallsAllowed = true
                else if ((notificationModel.data!! as ConversationData).message.type == MessageType.CALLS_DISALLOWED.value)
                    conversationModel.areOutgoingCallsAllowed = false

                chatRepository.insertConversation(conversationModel)
                chatRepository.insertMessage((notificationModel.data as ConversationData).message)
            }

            NotificationType.MATCH -> {
                @Suppress("UNCHECKED_CAST")
                val matchesAndLikeYouModelNotification =
                    (notificationModel.data as ArrayList<UserMatchNotificationResponse>)[0]
                val userMatchesModel =
                    UserMatchesModel.convertToUserMatchesModel(matchesAndLikeYouModelNotification.data)
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.NEW_MATCH_NOTIFICATION_RECEIVED, mapOf(
                        FirebaseAnalyticsParameterName.NEW_MATCH_COUNT.value to 1L))
                userMatchesModel?.let {
                    chatRepository.insertMatch(userMatchesModel)
                }
            }

            NotificationType.NEW_LIKE_RECEIVED -> {
                @Suppress("UNCHECKED_CAST")
                val item =
                    (notificationModel.data as UserLikedYouNotificationResponse)

                val userLikesModel =
                    if (DuaApplication.instance.shouldShowPremium && userRepository.user.value?.premiumType == null) {
                        UserLikesModel.convertToUserLikesModelUnblur(item.data)
                    } else {
                        UserLikesModel.convertToUserLikesModel(item.data)
                    }

                if (userLikesModel?.type == InteractionType.LIKE.value) {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.NEW_LIKE_NOTIFICATION_RECEIVED,
                        mapOf(FirebaseAnalyticsParameterName.LIKED_YOU_COUNT.value to 1L))
                }
                userLikesModel?.let {
                    likedYouRepository.insertLike(userLikesModel)
                }
            }

            NotificationType.NONE -> {
            }

            NotificationType.UN_MATCHED -> {
                val item = (notificationModel.data as UserUnMatchedModel)
                chatRepository.deleteMatchByUserId(item.userId)
                chatRepository.deleteConversationByUserId(item.userId)
            }

            NotificationType.COUNTER_RESET -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.INTERACTION_COUNTER_RESET_NOTIFICATION,
                    mapOf(FirebaseAnalyticsParameterName.COUNTER_RESET_COUNT.value to 1L))
                if (NotificationHelper.isApplicationInTheBackground()) {
                    userRepository.getLoggedInUserModel()?.let {
                        getAllCountersUseCase.invoke(it)
                            .catch {
                                Timber.e(it)
                            }
                            .collect { counters ->
                                Timber.tag("COUNTERS").d("COUNTERS: $counters")
                            }
                    }
                }
            }

            NotificationType.USER_DELETED -> {
                duaAccount.deleteOnlyData()
            }
            NotificationType.PROFILE_COMPLETION_REMINDER -> {
            }
            NotificationType.VERIFY_USER_ATTRIBUTE -> {
            }
            NotificationType.NEW_REFERRAL_REWARD -> {
            }
            NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> {
            }
            NotificationType.UPDATE_REMOTE_CONFIG -> {
                duaSharedPrefs.settRemoteConfigState(true)
            }
            NotificationType.DISLIKE_INSTACHAT -> {
            }
            NotificationType.BADGE_2 -> {
                val badge2ApprovalModel = notificationModel.data as Badge2ApprovalModel
                if (badge2ApprovalModel.badge2 == Badge2Status.APPROVED.status) {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_VERIFIED2_NOTIFICATION)
                    val user = userRepository.getLoggedInUserModel()
                    if (user != null) {
                        if (user.isDisabled) {
                            AWSInteractor.refresh()
                            DuaApplication.instance.isLoggedInUserDisabled = false
                            userRepository.updateUser(user.copy(isDisabled = false))
                        }
                    }
                }
            }
            NotificationType.VERIFY_YOUR_IMAGE -> {
            }
            NotificationType.DOWNLOAD_DATA -> {

            }
            NotificationType.NEW_LOCAL_SUBSCRIPTION -> {
                val newLocalSubscriptionModel = notificationModel.data as NewLocalSubscriptionModel
                userRepository.makeUserPremium(newLocalSubscriptionModel)
                userRepository.getUserProfile()
            }

            NotificationType.IMPRESSIONS_REWARDED -> {
                val item = (notificationModel.data as ImpressionsRewarded)
                userRepository.getLoggedInUserModel()?.let {
                    getUserCounterUseCase.invoke(it.counterConfigurationNames.likeCounterCN)
                        .collect { counterEntity ->
                            counterEntity?.let { counter ->
                                val newInteractionCount = counter.total - (item.count ?: 0)
                                updateUserCounterUseCase.invoke(counter.copy(total = newInteractionCount))
                            }
                        }
                }
            }

            NotificationType.DISABLED_STATE_CHANGED -> {
                val disableStateChanged = notificationModel.data as DisableStateChanged

                val user = userRepository.getLoggedInUserModel()
                if (user != null) {
                    AWSInteractor.refresh()
                    DuaApplication.instance.isLoggedInUserDisabled = disableStateChanged.isDisabled
                    userRepository.updateUser(user.copy(isDisabled = disableStateChanged.isDisabled,
                        hasRequestedReview = false))
                    if (!disableStateChanged.isDisabled) {
                        firebaseLogEvent(FirebaseAnalyticsEventsName.ENABLED_PROFILE_NOTIFICATION)
                    }  else cancelCrossPathWorkersUseCase(context)
                }
            }

            NotificationType.OPEN_PREMIUM_PAYWALL -> {
                duaSharedPrefs.setShouldClearPaywallCache()
            }

            NotificationType.PREMIUM_SPECIAL_OFFER, NotificationType.PREMIUM_PAY_1_GET_1 -> {
                duaSharedPrefs.setShouldClearPaywallCache()

                val specialOfferDataModel = notificationModel.data as SpecialOfferDataModel
                val user = userRepository.getLoggedInUserModel()
                val sharedPrefsOffer = duaSharedPrefs.getPremiumSpecialOfferData()
                if (user?.premiumType == null && specialOfferDataModel != sharedPrefsOffer) {
                    duaSharedPrefs.setPremiumSpecialOfferData(specialOfferDataModel)
                    duaSharedPrefs.setHasOfferBeenShownFromCards(false)
                }

            }

            NotificationType.BOOST_SUCCESS -> {

                val boostResultModel = notificationModel.data as BoostResultModel
                if(isUIEnabled()) {
                    duaSharedPrefs.setBoostResultData(boostResultModel)
                    duaSharedPrefs.setBoostResultDialogShown(false)
                }
                firebaseLogEvent(FirebaseAnalyticsEventsName.SUCCESSFUL_BOOST,
                    mapOf(FirebaseAnalyticsParameterName.VIEWS.value to boostResultModel.boostResult.toLong()))

                val premiumTypeValue = getPremiumTypeEventProperty(userRepository.user.value)
                val boostPerformanceStatus = CleverTapBoostPerformanceStatus.SUCCESSFUL.value
                val boostPerformanceValue = boostResultModel.boostResult

                sendClevertapEvent(
                    ClevertapEventEnum.BOOST_PERFORMANCE, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                        ClevertapEventPropertyEnum.BOOST_PERFOMANCE_STATUS.propertyName to boostPerformanceStatus,
                        ClevertapEventPropertyEnum.BOOST_PERFOMANCE_VALUE.propertyName to boostPerformanceValue,
                        ClevertapEventPropertyEnum.BOOST_ACTIVATION_SOURCE.propertyName to duaSharedPrefs.getLastActiveBoostPlaceForEvent()
                    )
                )
            }

            NotificationType.BOOST_FAILED -> {
                val boostResultModel = notificationModel.data as BoostResultModel
                if(isUIEnabled()) {
                    duaSharedPrefs.setBoostResultData(boostResultModel)
                    duaSharedPrefs.setBoostResultDialogShown(false)
                }
                firebaseLogEvent(FirebaseAnalyticsEventsName.UNSUCCESSFUL_BOOST)

                val premiumTypeValue = getPremiumTypeEventProperty(userRepository.user.value)
                val boostPerformanceStatus = CleverTapBoostPerformanceStatus.UNSUCCESSFUL.value
                val boostPerformanceValue = boostResultModel.boostResult

                sendClevertapEvent(
                    ClevertapEventEnum.BOOST_PERFORMANCE, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                        ClevertapEventPropertyEnum.BOOST_PERFOMANCE_STATUS.propertyName to boostPerformanceStatus,
                        ClevertapEventPropertyEnum.BOOST_PERFOMANCE_VALUE.propertyName to boostPerformanceValue,
                        ClevertapEventPropertyEnum.BOOST_ACTIVATION_SOURCE.propertyName to duaSharedPrefs.getLastActiveBoostPlaceForEvent()
                    )
                )
            }

            NotificationType.PUSH_BADGE2_VERIFICATION -> {
                if(isUIEnabled()) {
                    val pushBadge2VerificationModel =
                        notificationModel.data as PushBadge2VerificationModel
                    duaSharedPrefs.setVerifyProfileBadge2DialogModel(pushBadge2VerificationModel)

                }
            }

            NotificationType.INSTACHAT_LASTHOUR -> {
            }

            NotificationType.RMOD_LASTHOUR -> {
            }

/*
            NotificationType.SHOW_DONT_LET_GO_OFFER -> {
                val user = userRepository.getLoggedInUserModel()

                val modelNotification = notificationModel.data as DontLetGoOfferNotificationModel

                val dontLetGoOfferAvailableResponseModel = DontLetGoOfferAvailableResponseModel(
                    CurrentSubscription(
                        cognitoUserId = modelNotification.cognitoUserId,
                        expireTime = System.currentTimeMillis() +  86400000,
                        isAutoRenewable = false,
                        isChecked = false,
                        premiumPackage = user?.premiumType.toString(),
                        provider = "play-store",
                        token = "",
                        transactionId = ""
                    ),
                    offersPackage = OffersPackage(
                        dontLetGoOffer = modelNotification.packageName
                    ), showDontLetGoOffer = true
                )

                if (user?.premiumType != null) {
                    duaSharedPrefs.setDontLetGoOfferData(dontLetGoOfferAvailableResponseModel)
                    duaSharedPrefs.setDontLetGoOfferShown(false)
                }
            }
*/

            NotificationType.NEW_GROUPED_LIKES -> {
            }

            NotificationType.PREMIUM_OFFER -> {
                duaSharedPrefs.setShouldClearPaywallCache()

                // commenting out this method for now as Purchasely doesn't support offers

                /* val offerId: String? = (notificationModel.data as? PremiumIdModel)?.premiumOfferId
                 offerId?.let {
                     withContext(Dispatchers.IO) {
                         duaSharedPrefs.setPremiumOfferIdData(PremiumOfferId(it, false))

                         userRepository.getPremiumOfferById(it)
                             .catch { ex -> ex.printStackTrace() }
                             .collect { response ->
                                 when (response) {
                                     is Resource.Success -> {
                                         val model = response.data
                                         val now = System.currentTimeMillis()

                                         if (now > model.eligibleFrom && now < model.eligibleUntil) {
                                             val endTime = now + TimeUnit.HOURS.toMillis(model.lengthInHours.toLong())
                                             val offer = SpecialOfferDataModel(endTime, model.offerType, model.productId, model.eligibleFrom)
                                             duaSharedPrefs.setPremiumSpecialOfferData(offer)
                                             duaSharedPrefs.setHasOfferBeenShownFromCards(false)
                                             duaSharedPrefs.setPremiumOfferIdData(null)
                                         }

                                         val sendBroadCastData = Intent(PremiumActivity.PREMIUM_OFFER_INTENT)
                                         sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                                         LocalBroadcastManager.getInstance(DuaApplication.instance).sendBroadcast(sendBroadCastData)
                                     }
                                 }
                             }
                     }
                 }*/

            }
            NotificationType.PURCHASELY_PAYWALL -> {
                if(isUIEnabled()) {
                    duaSharedPrefs.setShouldClearPaywallCache()

                    val model = (notificationModel.data as? PurchaselyPaywalllModel)
                    val placementId = model?.placementId
                    duaSharedPrefs.setPurchaselyPaywallId(placementId)

                    val clearExistingOffers = model?.clearExistingOffer
                    if(clearExistingOffers != null && clearExistingOffers == true) {
                        duaSharedPrefs.setRealTimeClevertapPremiumOffer(null)
                        duaSharedPrefs.setRealTimeClevertapShowOffer(null)
                    }
                }
            }
            NotificationType.FORCE_PURCHASELY_PAYWALL -> {
                if(isUIEnabled()) {
                    duaSharedPrefs.setShouldClearPaywallCache()

                    val model = (notificationModel.data as? PurchaselyPaywalllModel)
                    val placementId = model?.placementId
                    duaSharedPrefs.setForcePurchaselyPaywallId(placementId)

                    val clearExistingOffers = model?.clearExistingOffer
                    if(clearExistingOffers != null && clearExistingOffers == true) {
                        duaSharedPrefs.setRealTimeClevertapPremiumOffer(null)
                        duaSharedPrefs.setRealTimeClevertapShowOffer(null)
                    }
                }
            }
            NotificationType.PURCHASELY_BOOST_PAYWALL -> {
                if(isUIEnabled()) {
                    duaSharedPrefs.setShouldClearPaywallCache()

                    val placementId: String? =
                        (notificationModel.data as? PurchaselyPaywalllModel)?.placementId
                    duaSharedPrefs.setBoostPurchaselyPaywallId(placementId)
                }
            }
            NotificationType.SETUP_ACCOUNT_CREDENTIALS -> {
               if(isUIEnabled()) duaSharedPrefs.setShowSetupAccountCredentials(true)
            }
            NotificationType.EXTRA_SWIPES_REWARDED_MALE -> {
            }

            NotificationType.REWARD_VIDEOS -> {

                userRepository.getLoggedInUserModel()?.let {
                    if(!isUIEnabled()) return@let
                    val eventPremiumType = getPremiumTypeEventProperty(it)

                    val rewardType = (notificationModel.data as? NotificationRewardModel)?.rewardType

                    when(rewardType) {
                        RewardType.UNBLUR.type -> {
                            val profilePercentage = userRepository.user.value?.profilePercentage
                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.REWARDED_POPUP, mapOf(
                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                    FirebaseAnalyticsParameterName.REWARD_TYPE.value to ClevertapRewardTypeValues.UNBLUR_PROFILE.value,
                                    FirebaseAnalyticsParameterName.REWARD_SOURCE.value to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

                                ))
                            sendAppsflyerEvent(
                                AppsflyerEventsNameEnum.REWARDED_POPUP, mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.UNBLUR_PROFILE.value,
                                    ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

                                ))
                            sendClevertapEvent(
                                ClevertapEventEnum.REWARDED_POPUP, mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.UNBLUR_PROFILE.value,
                                    ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

                                ))

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.UNBLUR_PROFILE,
                                mapOf(FirebaseAnalyticsParameterName.UNBLUR_SOURCE.value to FirebaseAnalyticsUnblureSourceValutes.ADMOB.value)
                            )
                            sendClevertapEvent(
                                ClevertapEventEnum.UNBLUR_PROFILE, mapOf(
                                    ClevertapEventPropertyEnum.UNBLUR_SOURCE.propertyName to ClevertapUnbureSourceValues.ADMOB.value
                                )
                            )
                        }

                        RewardType.INTERACTION.type -> {
                            val profilePercentage = userRepository.user.value?.profilePercentage
                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.REWARDED_POPUP, mapOf(
                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                    FirebaseAnalyticsParameterName.REWARD_TYPE.value to ClevertapRewardTypeValues.IMPRESSION.value,
                                    FirebaseAnalyticsParameterName.REWARD_SOURCE.value to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
                                ))
                            sendAppsflyerEvent(
                                AppsflyerEventsNameEnum.REWARDED_POPUP, mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.IMPRESSION.value,
                                    ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
                                ))
                            sendClevertapEvent(
                                ClevertapEventEnum.REWARDED_POPUP, mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.IMPRESSION.value,
                                    ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.WATCH_ADS.value,
                                    ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
                                ))
                        }
                    }

                    getUserCounterUseCase.invoke(it.counterConfigurationNames.likeCounterCN)
                        .collect { counterEntity ->
                            Timber.d("counterEntity: $counterEntity")
                        }
                }
            }
            NotificationType.FEATURED_USERS_RESET -> {}
            NotificationType.CONSUMABLE_REWARD_GIVEN -> {
                val model = notificationModel.data as? ConsumableRewardGivenModel
                model?.let {
                if(isUIEnabled()) duaSharedPrefs.setConsumableRewardModel(it)
                }
            }
            NotificationType.NEW_PROFILE_VISIT_RECEIVED -> {}
            NotificationType.SHOW_INSTACHAT_PAYWALL -> {}
            NotificationType.SHOW_BOOST_PAYWALL -> {}
            NotificationType.RMOD_GENERATED -> {
                duaSharedPrefs.setRmodGenerated(true)
            }
            NotificationType.REAL_TIME_PURCHASELY_OFFER -> {
                duaSharedPrefs.setShouldClearPaywallCache()

                try {
                    val model = notificationModel.data as? RealTimeClevertapOfferModel
                    model?.let {
                        if(!isUIEnabled()) return@let
                        val activeUntil = System.currentTimeMillis() + (TimeUnit.HOURS.toMillis(it.durationHours))
                        val offerModel = model.copy(activeUntil = activeUntil)

                        saveOfferInPreferences(offerModel, duaSharedPrefs)
                        duaSharedPrefs.setRealTimeClevertapShowOffer(offerModel)
                    }
                } catch (ex: Exception) {
                    Timber.tag("real_time_offer").d("malformed json ${notificationModel.mRemoteMessage}")
                    ex.printStackTrace()
                }

            }
            NotificationType.CROSS_PATH_SYNC -> {
                scheduleOneTimeFetchAndSyncLocationUseCase(context)
            }
            else -> {}
        }

        return Result.success()
    }

    private fun isUIEnabled() = duaSharedPrefs.getBadge2VerificationState() != Badge2VerificationState.REQUIRED.state &&
            userRepository.user.value?.isDisabled != true
}