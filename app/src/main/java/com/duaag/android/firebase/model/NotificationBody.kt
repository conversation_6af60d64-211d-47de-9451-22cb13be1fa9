package com.duaag.android.firebase.model

import com.google.gson.annotations.SerializedName

class NotificationBody(@SerializedName("version")
                       val version: String, @SerializedName("appVersion")
                       val appVersion: String,
                       @SerializedName("deviceId")
                       val deviceId: String,
                       @SerializedName("deviceType")
                       val deviceType: String,
                       @SerializedName("oldToken")
                       val oldToken: String?,
                       @SerializedName("language")
                       val language: String,
                       @SerializedName("os")
                       val os: String = "android",
                       @SerializedName("appInstanceId")
                       val appInstanceId: String? = null
)