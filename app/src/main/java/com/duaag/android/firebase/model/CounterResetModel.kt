package com.duaag.android.firebase.model

import com.google.gson.annotations.SerializedName

class CounterResetDataModel(@SerializedName("id")
                            val id: Int?,
                            @SerializedName("type")
                            val type: String?,
                            @SerializedName("mode")
                            val mode: String?)

enum class CounterResetDataModelType(val value:String?) {
    INTERACTION("interaction"), SUPER_LIKE("super_like"),INSTA_CHAT("instachat"),UNDO("undo"),FLYING("flying")
}
