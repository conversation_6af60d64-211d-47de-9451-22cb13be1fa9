package com.duaag.android.firebase.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Keep
class EndVideoCallModel(
    @SerializedName("senderUserId")
    val senderUserId: String,
    @SerializedName("receiverUserId")
    val receiverUserId: String,
    @SerializedName("conversationId")
    val conversationId: String,
    @SerializedName("reason")
    val reason: String,
    @SerializedName("forceEnd")
    val forceEnd: String?,
    @SerializedName("duration")
    val duration: String?
):Parcelable