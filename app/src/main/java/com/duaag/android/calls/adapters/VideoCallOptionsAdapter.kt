package com.duaag.android.calls.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.calls.models.AudioOutputDevice
import com.duaag.android.calls.models.VideoCallOptionModel
import com.duaag.android.databinding.VideoCallOptionItemBinding

class VideoCallOptionsAdapter(
    val items: MutableList<VideoCallOptionModel>,
    val clickListener: OptionClickListener
) : RecyclerView.Adapter<VideoCallOptionsAdapter.VideoCallOptionItemVideHolder>() {

    companion object {
        const val LOCAL_VIDEO = "localVideoAction"
        const val SWITCH_CAMERA = "switchCameraAction"
        const val AUDIO_ACTION = "audioAction"
        const val MUTE_ACTION = "mute_action"
        const val END_CALL = "end_call"
        const val BLUETOOTH = "bluetooth"
    }


    override fun onCreateViewHolder(
        parent: <PERSON>G<PERSON>,
        viewType: Int
    ): VideoCallOptionItemVideHolder {
        val binding: VideoCallOptionItemBinding =
            VideoCallOptionItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return VideoCallOptionItemVideHolder(binding)
    }

    override fun onBindViewHolder(holder: VideoCallOptionItemVideHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    inner class VideoCallOptionItemVideHolder(val binding: VideoCallOptionItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.btn.setOnClickListener {
                val model = items[adapterPosition]
                model.enabled = !model.enabled

                clickListener.onItemClick(model)
                bind(model)
            }
        }

        fun bind(model: VideoCallOptionModel) {
            if (model.enabled) {
                binding.btn.setImageResource(model.enabledIcon)
                binding.btn.setBackgroundResource(model.enabledBackground)
            } else {
                binding.btn.setImageResource(model.disabledIcon)
                binding.btn.setBackgroundResource(model.disabledBackground)
            }
        }
    }

    interface OptionClickListener {
        fun onItemClick(model: VideoCallOptionModel)
    }

    fun updateAudioDevice(name: String) {
        val audioItem = items.find { it.type == AUDIO_ACTION }
        audioItem?.let {
            val position = items.indexOf(it)

            when (name) {
                AudioOutputDevice.EARPIECE.type -> {
                    it.enabled = false
                    it.disabledIcon = R.drawable.ic_volume_1
                    it.disabledBackground = R.drawable.background_call_option_black
                    notifyItemChanged(position)
                }
                AudioOutputDevice.SPEAKERPHONE.type -> {
                    it.enabled = true
                    notifyItemChanged(position)
                }
                else -> {
                    it.enabled = false
                    it.disabledIcon = R.drawable.ic_bluetooth_on
                    it.disabledBackground = R.drawable.background_call_option_white
                    notifyItemChanged(position)
                }
            }
        }
    }

    fun updateBluetooth(name: String) {
        val audioItem = items.find { it.type == BLUETOOTH }
        audioItem?.let {
            val position = items.indexOf(it)

            when (name) {
                AudioOutputDevice.EARPIECE.type,
                AudioOutputDevice.SPEAKERPHONE.type -> {
                    it.enabled = false
                    it.disabledIcon = R.drawable.ic_bluetooth_off
                    it.disabledBackground = R.drawable.background_call_option_black
                    notifyItemChanged(position)
                }
                else -> {
                    it.enabled = true
                    it.disabledIcon = R.drawable.ic_bluetooth_on
                    it.disabledBackground = R.drawable.background_call_option_white
                    notifyItemChanged(position)
                }
            }
        }
    }

    fun replaceAudioWithCameraSwitch(cameraSwitch: VideoCallOptionModel) {
        items.add(1, cameraSwitch)
        items.removeAll { it.type == AUDIO_ACTION }
        notifyDataSetChanged()
    }

    fun replaceCameraSwitchWithAudio(audioOption: VideoCallOptionModel) {
        items.add(1, audioOption)
        items.removeAll { it.type == SWITCH_CAMERA }
        notifyDataSetChanged()
    }

    fun replaceAudioWithBluetoothButton(bluetoothOption: VideoCallOptionModel) {
        items.add(3, bluetoothOption)
        items.removeAll { it.type == AUDIO_ACTION }
        notifyItemRangeChanged(1, 3)
    }

    fun replaceBluetoothWithAudioButton(audioOption: VideoCallOptionModel) {
        val bluetooth = items.find { it.type == BLUETOOTH }
        bluetooth?.let {
            items.remove(it)
            items.add(1, audioOption)
            notifyItemRangeChanged(1, 3)
        }
    }

    fun removeBluetoothOption() {
        val bluetooth = items.find { it.type == BLUETOOTH }
        bluetooth?.let {
            items.remove(it)
            notifyItemRemoved(3)
        }
    }

    fun addBluetoothOption(bluetoothOption: VideoCallOptionModel) {
        val bluetooth = items.find { it.type == BLUETOOTH }
        if (bluetooth == null) {
            items.add(3, bluetoothOption)
            notifyItemInserted(3)
        }
    }

    fun disableCameraIcon() {
        val video = items.find { it.type == LOCAL_VIDEO }
        video?.let {
            it.enabled = false
            notifyItemChanged(0)
        }
    }
}