package com.duaag.android.calls.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class StartCallErrorBody(
    @SerializedName("type")
    val type: String?,
    @SerializedName("message")
    val message: String?
)

enum class StartCallErrorType(val value: String) {
    USER_VIDEO_CALLS_LIMIT_REACHED("user_video_calls_limit_reached"),
    USER_VIDEO_CALLS_NOT_ALLOWED("USER_VIDEO_CALLS_NOT_ALLOWED"),
    USER_BUSY("user_busy")
}
