package com.duaag.android.calls.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.core.app.NotificationCompat
import com.duaag.android.R
import com.duaag.android.calls.CallActivity
import com.duaag.android.calls.fragments.IncomingCallFragment
import com.duaag.android.calls.models.CallType
import com.duaag.android.calls.services.CallService

private const val VIDEO_SERVICE_CHANNEL = "VIDEO_SERVICE_CHANNEL"
const val ONGOING_NOTIFICATION_ID = 1

class CallNotification(private val context: Context, bundle: Bundle) {

    private val pendingIntent
        get() =
            Intent(context, CallActivity::class.java).let { notificationIntent ->
                notificationIntent.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
                notificationIntent.putExtra(
                    IncomingCallFragment.CALL_TYPE,
                    listOf(CallType.AUDIO_CALL.type, CallType.VIDEO_CALL.type).random()
                )
                notificationIntent.putExtra(
                    CallService.USER_IMAGE,
                    "https://i.ytimg.com/vi/2MtDS81XSoE/maxresdefault.jpg"
                )
                PendingIntent.getActivity(context, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
            }

    init {
        createOngoingCallNotificationChannel(VIDEO_SERVICE_CHANNEL, "Notification channel", context)
    }

    fun buildNotification(userName: String, callType: CallType): Notification =
        NotificationCompat.Builder(context, VIDEO_SERVICE_CHANNEL)
            .setContentTitle(
                if (callType == CallType.AUDIO_CALL) context.getString(R.string.call) else context.getString(R.string.video_call)
            )
            .setContentText(userName)
            .setContentIntent(pendingIntent)
            .setUsesChronometer(true)
            .setSmallIcon(R.drawable.ic_dua_notification)
            .setTicker("Ticker message")
            .build()

    private fun createOngoingCallNotificationChannel(
        channelId: String,
        channelName: String,
        context: Context
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationChannel = NotificationChannel(
                channelId,
                channelName,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(notificationChannel)
        }
    }
}
