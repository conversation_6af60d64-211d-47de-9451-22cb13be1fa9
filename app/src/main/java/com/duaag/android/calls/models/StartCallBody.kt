package com.duaag.android.calls.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Keep
class StartCallBody(
    @SerializedName("receiverUserId")
    val receiverUserId: String,
    @SerializedName("conversationId")
    val conversationId: String,
    @SerializedName("isVoiceCall")
    val isVoiceCall: Boolean
): Parcelable