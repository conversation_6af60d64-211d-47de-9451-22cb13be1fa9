package com.duaag.android.calls.repositories

import com.duaag.android.api.Resource
import com.duaag.android.api.CallsApiService
import com.duaag.android.api.ResourceV2
import com.duaag.android.calls.models.*
import com.google.gson.Gson
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton
import kotlin.coroutines.cancellation.CancellationException

@Singleton
class VideoCallsRepository @Inject constructor(@Named("videoCalls") private val callsApiService: CallsApiService) {

    fun startCall(startCallBody: StartCallBody) = flow {
        emit(Resource.Loading)
        try {
            val response = callsApiService.startCall(startCallBody)
            if (response.isSuccessful) emit(Resource.Success(response.body()!!))
            else {
                when {
                    response.code() == 403 -> {
                        val error = response.errorBody()?.string()
                        val errorObject = Gson().from<PERSON>son(error, StartCallErrorBody::class.java)

                        when (errorObject.type) {

                            StartCallErrorType.USER_VIDEO_CALLS_LIMIT_REACHED.value -> {
                                throw UserVideoCallsLimitReachedException(errorObject.message?.toLong())
                            }

                            StartCallErrorType.USER_VIDEO_CALLS_NOT_ALLOWED.value -> {
                                throw UserVideoCallsNotAllowedException()
                            }

                            StartCallErrorType.USER_BUSY.value -> {
                                throw UserBusyException()
                            }
                        }
                    }

                    else -> {
                        throw Exception(response.errorBody().toString())
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    fun endCall(endCallBody: EndCallBody) = flow {
        emit(Resource.Loading)
        try {
            val response = callsApiService.endCall(endCallBody)
            if (response.isSuccessful) emit(Resource.Success(response.body()!!))
            else {
                throw Exception(response.code().toString())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    suspend fun endCallBackground(endCallBody: EndCallBody): ResourceV2<Unit> {
        return try {
            val response = callsApiService.endCall(endCallBody)
            if(response.isSuccessful) {
                ResourceV2.Success(Unit)
            } else {
                ResourceV2.Error(response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "")
        }
    }

    fun acceptCall(callerUserId: String) = flow {
        emit(Resource.Loading)
        try {
            val response = callsApiService.acceptCall(callerUserId)
            if (response.isSuccessful) emit(Resource.Success(response.body()!!))
            else {
                throw Exception(response.code().toString())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }
}