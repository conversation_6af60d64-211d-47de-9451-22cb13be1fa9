package com.duaag.android.base.models

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@Entity(tableName = "account")
data class AccountModel(
        @SerializedName("id")
        val id: Int?,
        @SerializedName("cognitoUserId")
        @PrimaryKey
        val cognitoUserId: String,
        @SerializedName("appleId")
        val appleId: String?,
        @SerializedName("createdAt")
        val createdAt: String?,
        @SerializedName("email")
        val email: String?,
        @SerializedName("facebookId")
        val facebookId: String?,
        @SerializedName("googleId")
        val googleId: String?,
        @SerializedName("phone")
        val phone: String?
) : Parcelable