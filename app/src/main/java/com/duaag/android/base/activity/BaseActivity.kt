package com.duaag.android.base.activity

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.duaag.android.base.baseinterfaces.BaseInitializations

/** This class is base for Activities which include reused variables and functionality and content Dependency injection (Dagger 2) */

//Todo add Dagger 2 and base interface
abstract class BaseActivity : AppCompatActivity(), BaseInitializations {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
}