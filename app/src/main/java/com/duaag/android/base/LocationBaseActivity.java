package com.duaag.android.base;

import static com.duaag.android.clevertap.ClevertapExtensionsKt.getPremiumTypeEventProperty;
import static com.duaag.android.clevertap.ClevertapExtensionsKt.sendClevertapEvent;
import static com.duaag.android.logevents.firebaseanalytics.DuaFirebaseAnalyticsKt.firebaseLogEvent;

import android.Manifest;
import android.content.Intent;
import android.content.IntentSender;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Looper;
import android.provider.Settings;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import com.duaag.android.BuildConfig;
import com.duaag.android.R;
import com.duaag.android.clevertap.ClevertapEventEnum;
import com.duaag.android.clevertap.ClevertapEventPropertyEnum;
import com.duaag.android.clevertap.ClevertapExtensionsKt;
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName;
import com.duaag.android.user.DuaAccount;
import com.duaag.android.utils.PermissionExtensionsKt;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.ResolvableApiException;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.LocationSettingsResponse;
import com.google.android.gms.location.LocationSettingsStatusCodes;
import com.google.android.gms.location.SettingsClient;
import com.google.android.gms.tasks.Task;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import timber.log.Timber;

public abstract class LocationBaseActivity extends AppCompatActivity {
    private static final String TAG = "LocationBaseActivity";
    public static final int REQUEST_PERMISSIONS_REQUEST_CODE = 34;
    protected static final int REQUEST_CHECK_SETTINGS = 103;
    private static final int REQUEST_LOCATION_SETTINGS = 104;

    private static String LAST_PERMISSION_REQUEST_EVENT_SOURCE = null;

    private static String[] PERMISSIONS_LOCATION = {
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
    };

    private FusedLocationProviderClient mFusedLocationClient;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mFusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
    }

    protected abstract View getMainView();

    protected abstract LocationCallback getLocationCallback();
    protected abstract FastLocation.Companion.FastLocationInterface getFastLocationCallback();


    protected void requestLocation(String eventSource) {
        if (!isLocationPermissionEnabled()) {
            requestPermissions(eventSource);
        } else {
            fetchLastLocation();
        }
    }

    public void fetchLastLocationIfPermissionsEnabled() {
        if (isLocationPermissionEnabled()) {
            fetchLastLocation();
        }
    }

    private void startLocationPermissionRequest(String eventSource) {
        LAST_PERMISSION_REQUEST_EVENT_SOURCE = eventSource;

       sendLocationPermissionPopupEvent(eventSource);

        requestPermissions(PERMISSIONS_LOCATION, REQUEST_PERMISSIONS_REQUEST_CODE);
    }

    public boolean isLocationPermissionEnabled() {
        int fineLocationPermissionState = ActivityCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION);
        int coarseLocationPermissionState = ActivityCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_COARSE_LOCATION);
        return fineLocationPermissionState == PackageManager.PERMISSION_GRANTED || coarseLocationPermissionState == PackageManager.PERMISSION_GRANTED;
    }

    public void requestPermissions(String eventSource) {
        if (!shouldProvideRationale()) {
            Timber.tag(TAG).i("Requesting permission");
            startLocationPermissionRequest(eventSource);
        }
    }


    public void provideRationale(String eventSource) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this)
        .setTitle(R.string.location_access)
        .setCancelable(false)
        .setMessage(R.string.allow_access_in_order_to_use_the_app)
        .setPositiveButton(R.string.allow, (dialogInterface, which) ->
                startLocationPermissionRequest(eventSource))
        .setNegativeButton(R.string.cancel, (dialog, which) -> dialog.dismiss());
            AlertDialog dialog=builder.create();
            dialog.setOnShowListener(dialog1 -> {
                dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
                dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
            });
        dialog.show();
    }

    public boolean shouldProvideRationale() {
        boolean shouldProvideRationale =
                ActivityCompat.shouldShowRequestPermissionRationale(this,
                        Manifest.permission.ACCESS_FINE_LOCATION);

        return shouldProvideRationale;
    }

    private void getLastLocation(LocationRequest locationRequest) {

       if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
           FastLocation.Companion.getFastLocation(this, getFastLocationCallback());
       } else {
           mFusedLocationClient.requestLocationUpdates(locationRequest, getLocationCallback(), Looper.getMainLooper());
       }
    }

    /**
     * This function prompts user to turn on the location when the permission is given but location is turned off
     */
    private void fetchLastLocation() {
        LocationRequest request = LocationRequest.create();
        request.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        request.setNumUpdates(1);
        request.setInterval(0);
        request.setSmallestDisplacement(20000);

        Timber.tag(TAG).d("promptUserToTurnOnLocation: checking if there is a need to prompt user to turn location on");
        LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder()
                .addLocationRequest(request);

        SettingsClient settingsClient = LocationServices.getSettingsClient(this);

        Task<LocationSettingsResponse> task = settingsClient.checkLocationSettings(builder.build());

        task.addOnSuccessListener(locationSettingsResponse -> getLastLocation(request));

        task.addOnFailureListener(this, e -> {
            Timber.tag(TAG).e(e, "checkIfUserHasLocationOn: getting last location failed. ");
            int statusCode = ((ApiException) e).getStatusCode();
            if (statusCode == LocationSettingsStatusCodes
                    .RESOLUTION_REQUIRED) {
                // Location settings are not satisfied, but this can
                // be fixed by showing the user a dialog
                try {
                    // Show the dialog by calling
                    // startResolutionForResult(), and check the
                    // result in onActivityResult()
                    ResolvableApiException resolvable =
                            (ResolvableApiException) e;
                    startIntentSenderForResult(resolvable.getResolution().getIntentSender(), REQUEST_CHECK_SETTINGS,
                            null, 0, 0, 0, null);
                } catch (IntentSender.SendIntentException sendEx) {
                    // Ignore the error
                    sendEx.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        Timber.tag(TAG).i("onRequestPermissionResult");
        if (requestCode == REQUEST_PERMISSIONS_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                fetchLastLocation();

                if(isLocationPermissionEnabled()) {
                    ClevertapExtensionsKt.setHasLocationServicesEnabled(true);
                    ClevertapExtensionsKt.setLocationServicesStatus(true, PermissionExtensionsKt.checkBackgroundLocationPermission(this));
                    sendLocationPermissionAllowedEvent();
                }
            } else {
                showAllowLocationWithOpenSettingsOptionAlertDialog();
            }
        }
    }

    private void sendLocationPermissionAllowedEvent() {
        String eventPremiumType = getPremiumTypeEventProperty(DuaAccount.Companion.getUser());
        String community = Objects.requireNonNull(DuaAccount.Companion.getUser()).getCommunityInfo().getCommunityName();

        Map<String, Object> eventProperties = new HashMap<>();
        eventProperties.put(ClevertapEventPropertyEnum.COMMUNITY.getPropertyName(),community);
        eventProperties.put(ClevertapEventPropertyEnum.PREMIUM_TYPE.getPropertyName(), eventPremiumType);
        eventProperties.put(ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.getPropertyName(), LAST_PERMISSION_REQUEST_EVENT_SOURCE);

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_ALLOWED, eventProperties, true);
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_ALLOWED,eventProperties);

    }

    private void sendLocationPermissionPopupEvent(String eventSource) {
        String eventPremiumType = getPremiumTypeEventProperty(DuaAccount.Companion.getUser());
        String community = Objects.requireNonNull(DuaAccount.Companion.getUser()).getCommunityInfo().getCommunityName();

        Map<String, Object> eventProperties = new HashMap<>();
        eventProperties.put(ClevertapEventPropertyEnum.COMMUNITY.getPropertyName(),community);
        eventProperties.put(ClevertapEventPropertyEnum.PREMIUM_TYPE.getPropertyName(), eventPremiumType);
        eventProperties.put(ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.getPropertyName(), eventSource);

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_POPUP, eventProperties, true);
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_POPUP,eventProperties);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CHECK_SETTINGS) {
            if (resultCode == RESULT_OK) {
                fetchLastLocation();

                if(isLocationPermissionEnabled()) {
                    ClevertapExtensionsKt.setHasLocationServicesEnabled(true);
                    ClevertapExtensionsKt.setLocationServicesStatus(true, PermissionExtensionsKt.checkBackgroundLocationPermission(this));
                }


            }
        } else if (requestCode == REQUEST_LOCATION_SETTINGS) {
            if (resultCode == RESULT_OK) {
                fetchLastLocation();

                if(isLocationPermissionEnabled()) {
                    ClevertapExtensionsKt.setHasLocationServicesEnabled(true);
                    ClevertapExtensionsKt.setLocationServicesStatus(true, PermissionExtensionsKt.checkBackgroundLocationPermission(this));
                }
            }
        }


    }

    @Override
    protected void onDestroy() {
        if (mFusedLocationClient != null) {
            try {
                mFusedLocationClient.removeLocationUpdates(getLocationCallback());
                mFusedLocationClient = null;
            } catch (Exception e) {
                Timber.e(e);
            }
        }
        super.onDestroy();
    }

    /*
    private void showAppDoesNotWorkWithoutLocationAlertDialog(boolean isLocationOn) {
        String title = getString(R.string.location_access);
        String message = isLocationOn ? getString(R.string.location_access) : getString(R.string.turn_on_device_location);
        String positiveButtonTitle = isLocationOn ? getString(R.string.give_permission) : getString(R.string.turn_on);

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(title);
        builder.setMessage(message);
        builder.setCancelable(false);
        builder.setPositiveButton(
                positiveButtonTitle, (dialogInterface, i) -> {
                    requestPermissions();
                }
        );
        builder.setNegativeButton(
                getString(R.string.close_app), (dialogInterface, i) -> {
                    finish();
                    dialogInterface.cancel();
                }
        );
        AlertDialog alertDialog = builder.create();
        alertDialog.setOnShowListener(dialog -> {
            alertDialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
            alertDialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
        });
        alertDialog.show();
    }
    */

    private void showAllowLocationWithOpenSettingsOptionAlertDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.location_access));
        builder.setMessage(getString(R.string.location_permission_needed));
        builder.setCancelable(false);
        builder.setPositiveButton(
                getString(R.string.open_settings), (dialogInterface, i) -> {
                    openSettingsForLocation();
                }
        );
        builder.setNegativeButton(
                getString(R.string.cancel), (dialogInterface, i) -> {
                    dialogInterface.cancel();
                }
        );
        AlertDialog alertDialog = builder.create();
        alertDialog.setOnShowListener(dialog -> {
            alertDialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
            alertDialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(this, R.color.dua_red_color));
        });
        alertDialog.show();
    }

    protected void openSettingsForLocation() {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", BuildConfig.APPLICATION_ID, null);
        intent.setData(uri);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivityForResult(intent, REQUEST_LOCATION_SETTINGS);
    }
}
