package com.duaag.android.db

import androidx.annotation.Keep
import androidx.room.TypeConverter
import com.duaag.android.base.models.TagModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

@Keep
class TagTypeConverter {

    @TypeConverter
    fun tagsFromJson(json: String?): List<TagModel> {
        return Gson().fromJson(json, object : TypeToken<List<TagModel>>(){}.type)
    }

    @TypeConverter
    fun tagsToJson(tags: List<TagModel>): String? {
        return Gson().toJson(tags)
    }
}