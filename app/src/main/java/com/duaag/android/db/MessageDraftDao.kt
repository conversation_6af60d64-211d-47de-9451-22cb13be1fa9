package com.duaag.android.db

import androidx.room.*
import com.duaag.android.chat.model.MessageDraftModel

/**
 * The Data Access Object for the MessageDraftModel class.
 */
@Dao
interface MessageDraftDao {
    @Query("SELECT * FROM draft_messages where conversationId = :conversationId limit 1")
    fun getDraft(conversationId: String): MessageDraftModel

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDraft(draft: MessageDraftModel)

    @Query("DELETE FROM draft_messages where conversationId = :conversationId")
    suspend fun deleteDraft(conversationId: String)

    @Query("DELETE FROM draft_messages")
    suspend fun deleteDrafts()
}