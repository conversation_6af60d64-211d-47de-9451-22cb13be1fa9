package com.duaag.android.db

import androidx.room.*
import com.duaag.android.chat.model.MessageModel

/**
 * The Data Access Object for the MessageModel class.
 */
@Dao
interface MessagesDao {

    @Query("SELECT * FROM messages WHERE conversationId=:conversationId and time < :time order BY time DESC LIMIT 20")
    fun getNextMessages(conversationId: String, time: Long): List<MessageModel>

    @Query("SELECT * FROM messages WHERE conversationId = :conversationId and time > :start and time < :end and hasFailed=1")
    fun getFailedMessages(conversationId: String, start: Long,end: Long): List<MessageModel>

    @Query("SELECT * FROM messages WHERE conversationId=:conversationId and time > :time order BY time DESC LIMIT 20")
    fun getPreviousMessages(conversationId: String, time: Long): List<MessageModel>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessages(messages: List<MessageModel>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: MessageModel)

    @Update(entity = MessageModel::class)
    suspend fun updateModel(message: MessageModel)

    @Query("DELETE FROM messages")
    suspend fun deleteMessages()

    @Query("DELETE FROM messages where conversationId = :conversationId and hasFailed = 0")
    suspend fun deleteMessages(conversationId: String)

    @Query("DELETE FROM messages where id = :id")
    suspend fun deleteMessage(id: String)

    @Query("DELETE FROM messages where localId = :localId")
    suspend fun deleteMessageByLocalId(localId: String)

    @Transaction
    suspend fun updateData(messages: List<MessageModel>) {
        deleteMessages(messages.firstOrNull()?.conversationId ?: "")
        insertMessages(messages)
    }

}
