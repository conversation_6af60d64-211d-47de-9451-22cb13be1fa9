package com.duaag.android.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.duaag.android.chat.blockspamlinks.data.db.models.SpamLinkModel

@Dao
interface SpamLinkDao {

    @Query("SELECT * FROM spam_link WHERE time >=:time")
    suspend fun getSpamLinks(time: Long): List<SpamLinkModel>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSpamLink(spamLinkModel: SpamLinkModel)

    @Query("DELETE FROM spam_link WHERE time < :time")
    suspend fun deleteSpamLinks(time: Long)

    @Query("DELETE FROM spam_link")
    suspend fun deleteAllSpamLinks()

}