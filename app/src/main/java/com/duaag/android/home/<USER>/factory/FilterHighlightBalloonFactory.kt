package com.duaag.android.home.activity_status.factory

import android.content.Context
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LifecycleOwner
import com.duaag.android.R
import com.duaag.android.utils.convertDpToPixel
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.TextForm

class FilterHighlightBalloonFactory : Balloon.Factory() {

  override fun create(context: Context, lifecycle: LifecycleOwner?): Balloon {

    return Balloon.Builder(context)
      .setLayout(R.layout.custom_filters_tooltip_layout)
      .setArrowSize(16)
      .setArrowTopPadding(8)
      .setArrowAlignAnchorPaddingRatio(convertDpToPixel(5f, context))
      .setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
      .setArrowOrientation(ArrowOrientation.BOTTOM)
      .setArrowPosition(0.5F)
      .setPadding(8)
      .setMarginRight(24)
      .setArrowElevation(1)
      .setCornerRadius(8f)
      .setElevation(1)
      .setPreferenceName(this.javaClass.simpleName)
      .setBackgroundColor(ContextCompat.getColor(context, R.color.tooltip_bg_primary))
      .setLifecycleOwner(lifecycle)
      .build()
  }
}