package com.duaag.android.home.adapter


import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.duaag.android.R
import com.duaag.android.databinding.*
import com.duaag.android.home.models.GetInteractionsModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.utils.*
import java.util.concurrent.TimeUnit


class GetInteractionsAdapter(
    private val items: List<GetInteractionsModel>,
    private val viewType: Int = PREMIUM,
    private val homeViewModel: HomeViewModel
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val PROFILE = 0
        const val PREMIUM = 1
        const val PROFILE_SMALL = 2
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            PROFILE -> {
                val binding: GetInteractionsLayoutBinding = GetInteractionsLayoutBinding.inflate(layoutInflater, parent, false)
                InAppItemsViewHolder(binding)
            }
            PROFILE_SMALL -> {
                val binding: InAppBenefitSmallBinding = InAppBenefitSmallBinding.inflate(layoutInflater, parent, false)
                InAppSmallItemsViewHolder(binding)
            }
            else -> {
                val binding: GetPremiumItemsLayoutBinding = GetPremiumItemsLayoutBinding.inflate(layoutInflater, parent, false)
                PremiumItemsViewHolder(binding)
            }
        }
    }
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            PROFILE -> (holder as InAppItemsViewHolder).bind(items[position])
            PROFILE_SMALL -> (holder as InAppSmallItemsViewHolder).bind(items[position])
            else -> {
                (holder as PremiumItemsViewHolder).bind(items[position])
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return viewType
    }

    override fun getItemCount() = items.size

    inner class InAppItemsViewHolder(private var binding: GetInteractionsLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(model: GetInteractionsModel) {
            val context = binding.root.context

            addDifferentTypeface(binding.title, model.title, R.color.title_primary)
            binding.description.text =  model.descriptionText?:context.getString(model.description)
            
            Glide.with(binding.image)
                    .load(model.image)
                    .dontTransform()
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .into(binding.image)
        }
    }

    inner class InAppSmallItemsViewHolder(private var binding: InAppBenefitSmallBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(model: GetInteractionsModel) {
            val context = binding.root.context

            addDifferentTypeface(binding.title, model.title, R.color.title_primary)
            binding.description.text =  model.descriptionText?:context.getString(model.description)

            Glide.with(binding.image)
                .load(model.image)
                .dontTransform()
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(binding.image)
        }
    }

    inner class PremiumItemsViewHolder(private var binding: GetPremiumItemsLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        var countDownTimer: CountDownTimer? = null

        fun bind(model: GetInteractionsModel) {
            val context = binding.root.context

            binding.title.text = model.titleText?:context.getString(model.title)
            binding.description.text = model.descriptionText?:context.getString(model.description)

            if (countDownTimer!= null){
                countDownTimer?.cancel()
                countDownTimer = null
            }

            if (model.timer != null) {
                binding.countDownTimer.visibility = View.VISIBLE
                countDown(binding.countDownTimer,model.timer)
            } else {
                binding.countDownTimer.visibility = View.GONE
            }

            when (model.title) {
                R.string.get_instachats, R.string.get_flights, R.string.get_undos, R.string.ad_free,
                R.string.flights, R.string.unlimited_undo_premium -> {
                    binding.image.elevation = convertDpToPixel(2f, context)
                    val layoutParams = binding.image.layoutParams
                    layoutParams.width = ConstraintLayout.LayoutParams.WRAP_CONTENT
                    layoutParams.height = convertDpToPixel(56f, context).toInt()
                    binding.image.layoutParams = layoutParams
                }
                R.string.five_instachats_daily_premium -> {
                    binding.image.elevation = convertDpToPixel(2f, context)
                    val layoutParams = binding.image.layoutParams
                    layoutParams.width = ConstraintLayout.LayoutParams.WRAP_CONTENT
                    layoutParams.height = convertDpToPixel(56f, context).toInt()
                    binding.image.layoutParams = layoutParams
                    val configurationNames = homeViewModel.userProfile.value?.counterConfigurationNames
                    val lessThan24Hours = isLessThan24HoursFromNow(
                        homeViewModel.userCounters?.firstOrNull { it.configurationName == configurationNames?.instachatCounterCN}?.counter?.resetTime
                            ?: (getTimeStampAfter12Hours())
                    )
                    binding.countDownTimer.visibility =
                        if (lessThan24Hours) View.VISIBLE else View.GONE
                }
                else -> {
                    binding.image.elevation = convertDpToPixel(0f, context)
                    val layoutParams = binding.image.layoutParams
                    layoutParams.width = ConstraintLayout.LayoutParams.WRAP_CONTENT
                    layoutParams.height = convertDpToPixel(64f, context).toInt()
                    binding.image.layoutParams = layoutParams
                }
            }

            Glide.with(binding.image)
                    .load(model.image)
                    .dontTransform()
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .into(binding.image)
        }

        private fun countDown(timestamp: TextView,resetTime:Long) {
             countDownTimer = object : CountDownTimer(resetTime - System.currentTimeMillis(), 1000) {
                override fun onTick(p0: Long) {
                    var millisUntilFinished = p0

                    val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                    millisUntilFinished -= TimeUnit.HOURS.toMillis(hours)

                    val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                    millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                    val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                    timestamp.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
                }

                override fun onFinish() {
                    timestamp.visibility = View.GONE
                }
            }
            countDownTimer?.start()
        }
    }

}
