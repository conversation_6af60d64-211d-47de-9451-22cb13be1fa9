package com.duaag.android.home.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentLocationPermissionBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.NotificationsSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.setOnSingleClickListener
import javax.inject.Inject

class LocationPermissionDialog : DialogFragment() {

    private var  _binding: FragmentLocationPermissionBinding? = null
    private val binding get() = _binding

    private var listener: LocationPermissionDialogListener? = null
    private var hasDeniesPermission : Boolean? = false
    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
        try {
            listener = context as LocationPermissionDialogListener
        } catch (e: ClassCastException) {
            throw ClassCastException("$context must implement LocationPermissionDialogListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreenStyle)

        hasDeniesPermission = arguments?.getBoolean(HAS_DENIED_PERMISSIONS)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): ConstraintLayout? {
        _binding = FragmentLocationPermissionBinding.inflate(inflater)

        binding?.allowLocationBtn?.setOnClickListener {
            listener?.onLocationContinueClicked(hasDeniesPermission == true)
            dismissAllowingStateLoss()
        }
        binding?.btnClose?.setOnSingleClickListener{
            dismissAllowingStateLoss()
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sendScreenViewEvents(duaSharedPrefs, DuaAccount.user)

    }
    private fun sendScreenViewEvents(duaSharedPrefs: DuaSharedPrefs, user: UserModel?) {
        val community = duaSharedPrefs.getUserCommunityName()
        val premiumType = getPremiumTypeEventProperty(user)
        val eventSourceCT = ClevertapEventSourceValues.RETENTION_CLEVERTAP.value
        val eventSourceGA = NotificationsSourceValues.RETENTION_CLEVERTAP.value
        sendClevertapEvent(
            ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_SCREEN_VIEW,
            mapOf(
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to community,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.propertyName to eventSourceCT))

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_SCREEN_VIEW,
            mapOf(
                FirebaseAnalyticsParameterName.COMMUNITY.value to community,
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface LocationPermissionDialogListener {
        fun onLocationContinueClicked(hasDeniedPermission: Boolean)
    }


    companion object {
        const val HAS_DENIED_PERMISSIONS = "has_denied_permissions"
        fun newInstance(hasDeniesPermission: Boolean): LocationPermissionDialog {
            val fragment = LocationPermissionDialog()
            val arguments = bundleOf(HAS_DENIED_PERMISSIONS to hasDeniesPermission)
            fragment.arguments = arguments
            return fragment
        }
    }
}