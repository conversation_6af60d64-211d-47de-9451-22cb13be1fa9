package com.duaag.android.home

import android.Manifest.permission.ACCESS_COARSE_LOCATION
import android.app.Activity
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.Intent.EXTRA_CHOSEN_COMPONENT
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.location.Location
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.format.DateUtils
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.forEach
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.Purchase.PurchaseState.PURCHASED
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.android.billingclient.api.SkuDetailsParams
import com.android.billingclient.api.queryPurchasesAsync
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.MaxReward
import com.applovin.mediation.MaxRewardedAdListener
import com.applovin.mediation.ads.MaxRewardedAd
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.clevertap.android.sdk.CTInboxListener
import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.android.sdk.displayunits.DisplayUnitListener
import com.clevertap.android.sdk.displayunits.model.CleverTapDisplayUnit
import com.clevertap.android.sdk.inbox.CTInboxMessage
import com.clevertap.android.sdk.pushnotification.CTPushNotificationListener
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.ads.RewardAdType
import com.duaag.android.ads.createLikeCustomDataAd
import com.duaag.android.ads.createUnblurCustomDataAd
import com.duaag.android.ads.impressions.persantation.WatchAdBottomSheetFragment
import com.duaag.android.ads.impressions.persantation.routing.WatchAdArgs
import com.duaag.android.ads.impressions.persantation.routing.WatchAdBottomSheetRouting
import com.duaag.android.ads.likedyou.models.NotificationRewardModel
import com.duaag.android.ads.likedyou.persantation.SuccessUnblurProfileByAdRewardDialog
import com.duaag.android.api.ProfileVisitSourceEnum
import com.duaag.android.api.Result
import com.duaag.android.api.socket.model.LikeYouWebSocketModel
import com.duaag.android.api.socket.model.MatchesWebSocketModel
import com.duaag.android.api.socket.model.UserDeletedWebSocketModel
import com.duaag.android.api.socket.model.UserUnmatchedWebSocketModel
import com.duaag.android.application.AppState
import com.duaag.android.application.DuaApplication
import com.duaag.android.application.DuaApplication.Companion.BILLING_AVAILABLE
import com.duaag.android.aws_liveness.presentation.dialog.LivenessVerificationFailedDialog.LivenessVerificationFailedDialogListener
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.base.FastLocation
import com.duaag.android.base.LocationBaseActivity
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.Badge2VerificationState
import com.duaag.android.base.models.PremiumEnum
import com.duaag.android.base.models.UserAuxiliaryData
import com.duaag.android.base.models.UserModel
import com.duaag.android.boost.fragments.BoostResultDialog
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.broadcasts.ConnectivityReceiver
import com.duaag.android.calls.CallActivity
import com.duaag.android.calls.CallActivity.Companion.CALL_REQUEST_CODE
import com.duaag.android.calls.models.CallState
import com.duaag.android.calls.services.CallService
import com.duaag.android.calls.services.CallService.Companion.CALL_DURATION_KEY
import com.duaag.android.calls.services.CallService.Companion.CALL_STATE_FILTER
import com.duaag.android.calls.services.CallService.Companion.CALL_STATUS_KEY
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.chat.fragments.ConversationFragment
import com.duaag.android.chat.fragments.ConversationFragment.Companion.NEW_CONVERSATION_MODEL
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.ConversationWebSocketModel
import com.duaag.android.chat.model.LikeMessageModel
import com.duaag.android.chat.model.MessageType
import com.duaag.android.chat.model.UserLikesModel
import com.duaag.android.chat.viewmodel.InstaChatViewModel
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.chat.viewmodel.MatchesTabViewModel
import com.duaag.android.clevertap.ClevertapAdTypeValues
import com.duaag.android.clevertap.ClevertapAppInboxActionEnum
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapLogOutTypeValues
import com.duaag.android.clevertap.ClevertapNotificationAction
import com.duaag.android.clevertap.ClevertapUnhideSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.PremiumIdModel
import com.duaag.android.clevertap.ProfileVisitedModel
import com.duaag.android.clevertap.PurchaselyPaywalllModel
import com.duaag.android.clevertap.SwipeSourceValues
import com.duaag.android.clevertap.UploadImageSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.clevertap.offers.saveOfferInPreferences
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.setAppThemeInClevertap
import com.duaag.android.clevertap.setHasLocationServicesEnabled
import com.duaag.android.clevertap.setLocationServicesStatus
import com.duaag.android.clevertap.updateNotificationPermissionStatusInCleverTap
import com.duaag.android.crosspath.data.geofence.GeofenceManager
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.usecase.CancelCrossPathWorkersUseCase
import com.duaag.android.crosspath.domain.usecase.RunSyncingLocationBackendUseCase
import com.duaag.android.crosspath.domain.usecase.SaveLocationUseCase
import com.duaag.android.crosspath.domain.usecase.SchedulePeriodicLocationUpdatesUseCase
import com.duaag.android.crosspath.domain.usecase.ScheduleSyncingLocationBackendUseCase
import com.duaag.android.crosspath.presentation.ProfileRecommendationsDialogFragment
import com.duaag.android.crosspath.presentation.fragment.CrossPathFullScreenFragment.Companion.CROSS_PATH_DATA
import com.duaag.android.crosspath.presentation.fragment.CrossPathFullScreenFragment.Companion.SUNNY_HILL_START
import com.duaag.android.crosspath.presentation.viewmodel.CrossPathViewModel
import com.duaag.android.databinding.HomeActivityBinding
import com.duaag.android.databinding.LayoutLocationServicesBinding
import com.duaag.android.disabled.DisabledActivity
import com.duaag.android.disabled.models.DisableUserType
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.Badge2ApprovalModel
import com.duaag.android.firebase.model.ConsumableRewardGivenModel
import com.duaag.android.firebase.model.ConversationDeletedModel
import com.duaag.android.firebase.model.DisableStateChanged
import com.duaag.android.firebase.model.DislikeInstaChatModel
import com.duaag.android.firebase.model.NotificationModel
import com.duaag.android.firebase.model.RewardType
import com.duaag.android.firebase.model.UserDeletedModel
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.model.UserMatchNotificationResponse
import com.duaag.android.firebase.model.UserUnMatchedModel
import com.duaag.android.home.di.HomeComponent
import com.duaag.android.home.fragments.FilterOptionsFragment.Companion.ARG_EVENT_SOURCE
import com.duaag.android.home.fragments.InstaChatDialogFragment
import com.duaag.android.home.fragments.LocationPermissionDialog
import com.duaag.android.home.fragments.NewFeaturesDialogFragment
import com.duaag.android.home.fragments.NotificationPermissionDialog
import com.duaag.android.home.fragments.ShadowBannedDialog
import com.duaag.android.home.fragments.UnhideProfileDialog
import com.duaag.android.home.fragments.UserHeightBottomSheet
import com.duaag.android.home.fragments.UserProfileFragment
import com.duaag.android.home.fragments.VerificationInProgressDialog
import com.duaag.android.home.fragments.VerifyYourProfileDialog
import com.duaag.android.home.interfaces.ChatButtonClickListener
import com.duaag.android.home.models.DeepLinkActionNames
import com.duaag.android.home.models.DeepLinkScreenNames
import com.duaag.android.home.models.GooglePlaySubscriptions
import com.duaag.android.home.models.HomeScreenType
import com.duaag.android.home.models.ImpressionsRewarded
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.LimitReachedModel
import com.duaag.android.home.models.LimitReachedScreenSource
import com.duaag.android.home.models.PaymentType
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.models.VerifyPaymentModel
import com.duaag.android.home.viewmodels.BillingViewModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.home.viewmodels.HomeViewModel.Companion.CACHE_EXPIRE_TIME_MINUTES
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.SHOULD_SKIP_IMAGE_VERIFICATION_API
import com.duaag.android.instagram.InstagramAuthenticationDialog
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.NEW_IMAGES_REQUEST
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.REQUEST_CODE_PERMISSIONS
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.UPLOAD_IMAGE_SOURCE
import com.duaag.android.notification_feed.NotificationFeedViewModel
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PREMIUM_OFFER_ID
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter.PremiumPaywallList
import com.duaag.android.premium_subscription.models.PremiumOfferId
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.premium_subscription.openPremiumPaywallAsync
import com.duaag.android.profile_new.ProfileViewModel
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.EditProfileActivity.Companion.CONNECT_INSTAGRAM_INTENT
import com.duaag.android.rewards.BoostRewardDialog
import com.duaag.android.rewards.ConsumableRewardBaseDialog
import com.duaag.android.rewards.FreeStarterExperienceApprovedDialog.Companion.showFreeStarterExperienceApprovedDialog
import com.duaag.android.rewards.InstachatRewardDialog
import com.duaag.android.rewards.InteractionRewardDialog
import com.duaag.android.rewards.RewardsActivity
import com.duaag.android.rewards.UnblurRewardDialog
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsActivity.Companion.SETTINGS_ACTIVITY_START_DESTINATION_INTENT
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signInWithSpotted.fragments.SetUpAccountDialog
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.DeviceUtil
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.checkAndRequestAlwaysLocationPermission
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.checkIfAppVersionIsSupported
import com.duaag.android.utils.checkLocationAndBackgroundPermission
import com.duaag.android.utils.convertPixelsToDp
import com.duaag.android.utils.getPackageTypeForId
import com.duaag.android.utils.haveMoreThanXMinutesPassedFrom
import com.duaag.android.utils.isCallServiceRunning
import com.duaag.android.utils.isFragmentDialogShowing
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.isPostNotificationsPermissionEnabled
import com.duaag.android.utils.launchWhileAtLeast
import com.duaag.android.utils.location.LocationClientImpl
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.openAppSettings
import com.duaag.android.utils.openCreateProfile
import com.duaag.android.utils.requestAndLaunchReviewFlow
import com.duaag.android.utils.roundToDecimals
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.sunnyhill.SunnyHillUtils
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.EnvelopeDialog
import com.duaag.android.views.InAppPackagesDialog
import com.duaag.android.views.OutOfImpressionsDialog
import com.duaag.android.views.TrophyDialog
import com.giphy.sdk.ui.Giphy
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.ActivityResult
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.AppUpdateType.IMMEDIATE
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.play.core.review.testing.FakeReviewManager
import com.google.firebase.Firebase
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.dynamiclinks.androidParameters
import com.google.firebase.dynamiclinks.dynamicLinks
import com.google.firebase.dynamiclinks.iosParameters
import com.google.firebase.dynamiclinks.shortLinkAsync
import com.google.firebase.dynamiclinks.socialMetaTagParameters
import com.google.gson.Gson
import com.yuyakaido.android.cardstackview.Direction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import sendVerifyYourProfileInitiatedAnalyticsEvent
import sendVerifyYourProfilePopupAnalyticsEvent
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.min
import kotlin.math.pow


class HomeActivity : LocationBaseActivity(),
    ConnectivityReceiver.ConnectivityReceiverListener,
    TrophyDialog.TrophyDialogListener,
    UnhideProfileDialog.UnhideProfile,
    CTPushNotificationListener,
    CTInboxListener,
    DisplayUnitListener,
    ShadowBannedDialog.ShadowBannedDialogListener,
    LocationPermissionDialog.LocationPermissionDialogListener,
    MaxRewardedAdListener,
    WatchAdBottomSheetFragment.WatchAdBottomSheetListener,
    UserProfileFragment.UserProfileInteract,
    VerifyYourProfileDialog.VerifyYourProfileDialogListener,
    ConsumableRewardBaseDialog.ConsumableRewardListener,
    LivenessVerificationFailedDialogListener
{

    companion object {
        private const val TAG = "MainActivity"
        var isActive = false
        const val NORMAL_LOCATION_THRESHOLD_KM = 2
        const val FLY_LOCATION_THRESHOLD_KM = 10
        const val UPDATE_REQUEST_CODE = 300
        const val INTERACTION_LIMIT = "interaction_limit"
        private const val KEY_NOTIFICATION_PERMISSION_DIALOG_SHOWN = "notification_permission_dialog_shown"
        private const val KEY_LOCATION_PERMISSION_DIALOG_SHOWN = "location_permission_dialog_shown"

    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel> { viewModelFactory }
    private val crossPathViewModel by viewModels<CrossPathViewModel> { viewModelFactory }
    private val billingViewModel by viewModels<BillingViewModel> { viewModelFactory }
    private val likeYouViewModel by viewModels<LikedYouViewModel> { viewModelFactory }
    private val matchesTabViewModel by viewModels<MatchesTabViewModel> { viewModelFactory }
    private val instaChatViewModel by viewModels<InstaChatViewModel> { viewModelFactory }
    private val notificationFeedViewModel by viewModels<NotificationFeedViewModel>{viewModelFactory}
    private val profileViewModel by viewModels<ProfileViewModel>{viewModelFactory}
    private val livenessViewModel by viewModels<LivenessViewModel> { viewModelFactory }


    private val reviewManager: ReviewManager by lazy {
        if(BuildConfig.DEBUG) FakeReviewManager(this) else ReviewManagerFactory.create(this)
    }

    private var instaChatLimitDialog: OutOfImpressionsDialog? = null
    private var interactionsOutOfImpressionsLimitDialog: OutOfImpressionsDialog? = null
    private var undoLimitDialog: OutOfImpressionsDialog? = null

    var boostResultDialog: BoostResultDialog? = null

    var hasPurchaeslyStarted: Boolean = false

    var mChatButtonClickListener: ChatButtonClickListener? = null

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var appState: AppState

    lateinit var homeComponent: HomeComponent
    private  var _binding: HomeActivityBinding? = null
    private  val binding get() = _binding!!

    private var webSockedTimer: Timer? = null
    private val webSockedTimerDelay = 10000L

    // Creates instance of the manager.
    private var appUpdateManager: AppUpdateManager? = null
    private val connectivityReceiver: ConnectivityReceiver by lazy { ConnectivityReceiver() }
    private var lastConnectivity: Boolean = true
    private val purchaselyScreensReceiver = PurchaselyBroadcastReceiver()
    private val forcePurchaselyScreensReceiver = ForcePurchaselyBroadcastReceiver()
    private val boostPurchaselyScreensReceiver = BoostPurchaeslyBroadcastReceiver()
    private val deepLinkScreensReceiver = ScreensBroadcastReceiver()
    private val deepLinkSunnyHillReceiver = SunnyHillBroadcastReceiver()
    private val deepLinkActionReceiver = ActionsBroadcastReceiver()
    private val premiumOfferScreensReceiver = PremiumOfferBroadcastReceiver()

     lateinit var navController: NavController

    private var billingClient: BillingClient? = null

    private var authMethod: AuthMethod? = null

    // ads
    //ChatAD
    private var nativeAdChatLoader: MaxNativeAdLoader? = null
    private var nativeAdChatView: MaxNativeAdView? = null
    private var nativeAdChat: MaxAd? = null
    var lastTimeAdChatShowed: Long? = null
    private val nativeAdChatHandler by lazy { Handler(Looper.getMainLooper()) }
    private val nativeAdChatRunnable by lazy { Runnable { addNewChatMobAd(true) } }

    //InstaChatAD
    private var nativeAdInstaChatLoader: MaxNativeAdLoader? = null
    private var nativeAdInstaChatView: MaxNativeAdView? = null
    private var nativeAdInstaChat: MaxAd? = null
    var lastTimeAdInstaChatShowed: Long? = null
    private val nativeAdInstaChatHandler by lazy { Handler(Looper.getMainLooper()) }
    private val nativeAdInstaChatRunnable by lazy { Runnable { addNewInstaChatMobAd(true) } }

    //CardAD
    private var nativeAdCardLoader: MaxNativeAdLoader? = null
    private var nativeAdCardView: MaxNativeAdView? = null
    private var nativeAdCard: MaxAd? = null
    var lastTimeAdCardShowed: Long? = null

    //RewardedAD
    private var rewardedAd: MaxRewardedAd? = null
    private var retryAttempt = 0.0
    var hasProfileCompletedProgressShown = false

    private var hasNotificationPermissionDialogBeenShown: Boolean= false
    private var hasLocationPermissionDialogBeenShown: Boolean = false

    //CrossPath
    private val locationClient by lazy {
        LocationClientImpl(
            this,
            LocationServices.getFusedLocationProviderClient(this)
        )
    }
    private val geofenceManager by lazy { GeofenceManager(this) }

    @Inject
    lateinit var saveLocationUseCase: SaveLocationUseCase
    @Inject
    lateinit var schedulePeriodicLocationUpdatesUseCase: SchedulePeriodicLocationUpdatesUseCase
    @Inject
    lateinit var scheduleSyncingLocationBackendUseCase: ScheduleSyncingLocationBackendUseCase
    @Inject
    lateinit var runSyncingLocationBackendUseCase: RunSyncingLocationBackendUseCase
    @Inject
    lateinit var cancelCrossPathWorkersUseCase: CancelCrossPathWorkersUseCase
    //end

    private var appInboxHandledMessages = mutableListOf<String>()

    private var recheckBillingAvailableAttemps = 0

    private val DELAY_MILLIS = 60000L

    private val premiumOfferAvailableBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
//            val hasOffer = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false) ?: false
//            if (hasOffer) {
//                if(!duaSharedPrefs.isLoggedInUserPremium() && DuaApplication.instance.getBillingAvailable())
//                    showSpecialOffer()
//            }
        }
    }

    private val newConversationCreatedBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Timber.tag(TAG).d("onReceive: newConversationCreatedBroadcastReceiver")
            val extras = intent?.getParcelableExtra<ConversationWebSocketModel>(NEW_CONVERSATION_MODEL)
            extras?.let {
                val newMessage = it.data.message
                val conversationModel = it.data.conversationMember
                homeViewModel.updateConversationListFrom1to1Conversation(
                        ConversationWebSocketModel(
                                "chat.newMessage", ConversationData(
                                newMessage,
                                conversationModel
                        )
                        )
                )

                if (conversationModel.isRMOD) {
                    homeViewModel.rmodItem.value?.isHidden = true
                }
            }
        }
    }
    inner class SunnyHillBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val attendeeId = intent?.getStringExtra(SplashActivity.SUNNY_HILL_ATTENDEE_ID)
            if (!attendeeId.isNullOrEmpty() && SunnyHillUtils.shouldShowSunnyHillViews()) {
                homeViewModel.redeemTicket(attendeeId)
                duaSharedPrefs.setSunnyHillAttendeeId(null)
            }
        }
    }
    inner class ScreensBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val eventSource = intent?.getStringExtra(EVENT_SOURCE) ?: ""
            val screen = intent?.getStringExtra(SplashActivity.DEEP_LINK_SCREENS)
            val jsonData = intent?.getStringExtra("jsonData")
            if (!screen.isNullOrEmpty()) {
                lifecycleScope.launchWhenResumed{
                    openScreenActions(screen, jsonData, eventSource)
                }
            }
        }
    }
    inner class ActionsBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.getStringExtra(SplashActivity.DEEP_LINK_ACTION)
            if (!action.isNullOrEmpty()) {

                lifecycleScope.launchWhenResumed{
                    handleDeepLinkActions(action)
                }
            }
        }
    }
    inner class PurchaselyBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val placementId = intent?.getStringExtra(SplashActivity.DEEP_LINK_PURCHASELY)

            if (!placementId.isNullOrEmpty()) {
                lifecycleScope.launchWhenResumed{
                    openPremiumPaywallAsync(
                        eventSourceClevertap = ClevertapEventSourceValues.DEEPLINK,
                        placementId = placementId,
                        userModel = homeViewModel.userProfile.value
                    )
                }
            }
            duaSharedPrefs.setPurchaselyPaywallId(null)
        }
    }
    inner class ForcePurchaselyBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val placementId = intent?.getStringExtra(SplashActivity.DEEP_LINK_FORCE_PURCHASELY_PAYWALL_ID)

            if (!placementId.isNullOrEmpty()) {
                lifecycleScope.launchWhenResumed{
                    openPremiumPaywallAsync(
                        eventSourceClevertap = ClevertapEventSourceValues.DEEPLINK,
                        placementId = placementId,
                        userModel = homeViewModel.userProfile.value
                    )
                }
            }
            duaSharedPrefs.setForcePurchaselyPaywallId(null)
        }
    }

    inner class BoostPurchaeslyBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val paywallId = intent?.getStringExtra(SplashActivity.DEEP_LINK_BOOST_OFFER_PURCHASELY)

            if (!paywallId.isNullOrEmpty()) {
                lifecycleScope.launchWhenResumed{
                    showBoostOfferPurchasely(paywallId)
                }
            }
            duaSharedPrefs.setBoostPurchaselyPaywallId(null)
        }
    }

    inner class PremiumOfferBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val offerId = intent?.getStringExtra(PREMIUM_OFFER_ID)

            if (!offerId.isNullOrEmpty()) {
                duaSharedPrefs.setPremiumOfferIdData(PremiumOfferId(offerId, true))

                // commenting out this method for now as Purchasely doesn't support offers
//                homeViewModel.getPremiumOfferFromId(offerId = offerId, isFromDeepLink = false)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        homeComponent = (application as DuaApplication).appComponent.homeComponent().create()
        homeComponent.inject(this)

        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
        super.onCreate(savedInstanceState)
        _binding = DataBindingUtil.setContentView(this, R.layout.home_activity)
        binding.lifecycleOwner = this
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED){
                appState.userSignedOut.collect{
                    if(it == true){
                        val intent = Intent(this@HomeActivity, StartActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                        <EMAIL>(intent)
                        appState.setUserSignedOut(null)
                    }
                }
            }
        }
        CleverTapAPI.getDefaultInstance(applicationContext)?.apply {
            enableDeviceNetworkInfoReporting(true)
            ctPushNotificationListener = this@HomeActivity
            //Set the Notification Inbox Listener
            ctNotificationInboxListener = this@HomeActivity
            //Initialize the inbox and wait for callbacks on overridden methods
            initializeInbox()
        }

        if(checkLocationPermission()) {
            val clevertap: CleverTapAPI? = CleverTapAPI.getDefaultInstance(applicationContext)
            val location = clevertap?.location
            if (clevertap != null) {
                clevertap.location = location
            }
        }

        CleverTapAPI.getDefaultInstance(this)?.setDisplayUnitListener(this)
        getScreenDimensions()
        billingClient = BillingClient.newBuilder(this)
                .setListener(PurchasesUpdatedListener { billingResult, purchases ->
                    Timber.tag("PurchasesListener").d("purchases: $purchases")

                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
                        // Activity is at least in the RESUMED state (it is visible and in the foreground)
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            // The BillingClient is ready. You can query purchases here.
                            Timber.tag("BILLING").d("purchases: $purchases")
                            if (!purchases.isNullOrEmpty()) {
                                Timber.tag("BILLING").d("purchases: $purchases")
                                purchases.forEach { purchase ->
                                    purchase?.let {
                                        Timber.tag("BILLING").d("purchaseState: ${purchase.purchaseState}")
                                        val purchaseType = getPackageTypeForId(it.skus[0])

                                        //This listener was being called even when the purchase was being
                                        //made from PremiumActivity.
                                        if (purchaseType != PaymentType.PREMIUM.type)
                                            if (purchase.purchaseState == PURCHASED) {
                                                val model = VerifyPaymentModel(
                                                    "play-store",
                                                    it.skus[0],
                                                    it.orderId!!,
                                                    it.purchaseTime.toString(),
                                                    it.purchaseToken,
                                                    purchaseType,
                                                    null
                                                )
                                                homeViewModel.verifyPayment(model,purchase)
                                            }
                                    }
                                }
                            }
                        }
                    }

                })
                .enablePendingPurchases()
                .build()


        homeViewModel.reloadRecommendedUsers.observe(this) {
            Timber.tag("relaodData").d("reload recommended users")
            homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                fromTouch = false,
                applyCardsImmediately = true
            )
        }

//        homeViewModel.specialOfferReceived.observe(this) {
//            if(!duaSharedPrefs.isLoggedInUserPremium() && DuaApplication.instance.getBillingAvailable())
//                showSpecialOffer()
//        }

        homeViewModel.userProfile.observe(this) {
            Timber.tag("homeactivity").d("observer")
            it?.let {
                DuaAccount.user = it
                DuaApplication.instance.initializeApplovinSdk(it)
                createRewardedAd()
                homeViewModel.syncLocalUserCounters(it)
                homeViewModel.badge2 = when (it.badge2) {
                    Badge2Status.APPROVED.status -> Badge2Status.APPROVED
                    Badge2Status.PROCESSING.status -> Badge2Status.PROCESSING
                    Badge2Status.NOT_APPROVED.status -> Badge2Status.NOT_APPROVED
                    Badge2Status.NULL.status -> Badge2Status.NULL
                    else -> Badge2Status.NULL
                }
                if (!HomeViewModel.ignoreUserUpdating) {
                    DuaAccount.latitude =
                        if (it.isInFlyMode()) it.profile.actualLatitude else it.profile.latitude
                    DuaAccount.longitude =
                        if (it.isInFlyMode()) it.profile.actualLongitude else it.profile.longitude
                    DuaAccount.user = it

                    handleDisableUserView(it)

                    if (it.isInvisible()) {
                        homeViewModel.setUsersRecommended(emptyList())
                    } else if (homeViewModel.recommendedUsers.value.isNullOrEmpty() || duaSharedPrefs.getShouldUpdateCards()) {
                        duaSharedPrefs.setShouldUpdateCards(false)
                        homeViewModel.fetchedRecommendedUsersIds.clear()
                        homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                            fromTouch = false,
                            applyCardsImmediately = true
                        )
                    } else if (duaSharedPrefs.getShouldUpdateCards()) {
                        duaSharedPrefs.setShouldUpdateCards(false)
                        homeViewModel.fetchedRecommendedUsersIds.clear()
                    }
                    lifecycleScope.launch {
                        val visitorId = homeViewModel.getVisitorId()
                        withContext(Dispatchers.Main) {
                            setDeviceId(it, visitorId)
                        }
                    }

                }

                handleBadge2VerificationState(it)

                //initialize Purchasely once
                if(!hasPurchaeslyStarted)
                    PurchaselyManager.startPurchasely(it) {
                        hasPurchaeslyStarted = true

                        Timber.tag("Purchasely").d("Purchasely Started in HomeActivity")

                        if (profileViewModel.arePricesMissing()) {
                            profileViewModel.getPurchaselyPackages()
                        }
                    }
            }
            HomeViewModel.ignoreUserUpdating = false
        }

        homeViewModel.accountModel.observe(this) {
            Timber.tag("User_Account").d("model : $it")
            it?.let {mAccountModel ->
                if ((mAccountModel.phone == null && mAccountModel.email != null) || (mAccountModel.phone == null && mAccountModel.email == null)) {
                    authMethod = AuthMethod.PHONE
                } else if (mAccountModel.email == null && mAccountModel.phone != null) {
                    authMethod = AuthMethod.EMAIL
                }
            }
        }
        homeViewModel.consumableRewardUiState.observe(this){ state->
            if(state.dialog != null && state.dismissReward == true){
                state.dialog.dismissAllowingStateLoss()
                homeViewModel.updateConsumableRewardState(null,null)
            }
        }
        homeViewModel.interactionUndone.observe(this) {
            matchesTabViewModel.removeMatchByUserId(it.cognitoUserId.toString())
            matchesTabViewModel.removeConversationByCognitoId(it.id.toString())
        }

        homeViewModel.limitReached.observe(this) {

            val limitType = it.type

            if (homeViewModel.userProfile.value?.profile?.isShadowBanned == true && (limitType == InteractionType.LIKE || limitType == InteractionType.DISLIKE)) {
                homeViewModel.showShadowBannedDialog()
                return@observe
            }

            val configurationNames = homeViewModel.userProfile.value?.counterConfigurationNames
            if (DuaApplication.instance.getPremiumAvailable() && homeViewModel.userProfile.value?.premiumType == null) {
                when (limitType) {
                    InteractionType.LIKE-> {

                        firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_SWIPERIGHT)

                        if (homeViewModel.userCounters?.firstOrNull
                            { it.configurationName == configurationNames?.likeCounterCN }?.counter?.resetTime == null
                        ) {
                            homeViewModel.setLikesCounterResetTime()
                        }
                    }

                    InteractionType.DISLIKE -> {
                        if (homeViewModel.userCounters?.firstOrNull
                            { it.configurationName == configurationNames?.dislikeCounterCN }?.counter?.resetTime == null
                        ) {
                            homeViewModel.setDislikesCounterResetTime()
                        }
                        sendDislikeLimitationClevertapEvent(it,homeViewModel.userProfile.value)
                        showSwipeLimitationView(binding.swipeLeftLimitationLayout.root)
                        firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_SWIPELEFT)
                    }


                    InteractionType.INSTA_CHAT -> {
                        firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_INSTACHAT)
                        if (homeViewModel.userCounters?.firstOrNull
                            { it.configurationName == configurationNames?.instachatCounterCN }?.counter?.resetTime == null
                        ) {
                            homeViewModel.setInstaChatLimitReachedCounterRestTime()
                        }
                    }
                    InteractionType.UNDO -> {
                        firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_UNDO)
                        if (homeViewModel.userCounters?.firstOrNull
                            { it.configurationName == configurationNames?.undoCounterCN }?.counter?.resetTime == null
                        ) {
                            homeViewModel.setUndoCounterResetTime()
                        }
                    }

                    else -> {
                    }
                }
                if (homeViewModel.specialOfferAvailable()) {
                    if (!duaSharedPrefs.isLoggedInUserPremium() && DuaApplication.instance.getBillingAvailable()) {
//                        showSpecialOffer()
                    }
                } else {
                    if (limitType == InteractionType.INSTA_CHAT && RemoteConfigUtils.showInstachatPaywallPopup()) {

                        if(it.place.isFromProfileVisit) {
                            openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.PROFILE_VISIT_FEATURE,
                                placementId = PurchaselyPlacement.DYNAMIC_INSTACHAT.id,
                                userModel = homeViewModel.userProfile.value
                            )
                        } else {
                            val placementId = if(it.place == LimitReachedScreenSource.CROSS_PATH)
                                PurchaselyPlacement.SPOTTED_INSTACHAT.id
                            else
                                PurchaselyPlacement.DYNAMIC_INSTACHAT.id

                            val eventSource = if(it.place == LimitReachedScreenSource.FOR_YOU)
                                ClevertapEventSourceValues.CARDS
                            else if(it.place == LimitReachedScreenSource.POPULAR)
                                ClevertapEventSourceValues.FEATURED_PROFILE
                            else
                                ClevertapEventSourceValues.CARDS

                            openPremiumPaywall(
                                eventSourceClevertap = eventSource,
                                placementId = placementId,
                                userModel = homeViewModel.userProfile.value,
                            )
                        }



                    } else {

                        val viewPagerStart = when (limitType) {
                            InteractionType.INSTA_CHAT -> PremiumPaywallList.INSTACHATS_ITEM
                            InteractionType.UNDO -> PremiumPaywallList.UNDO_ITEM
                            else -> PremiumPaywallList.IMPRESSIONS_ITEM
                        }
                        val clevertapSource = when {
                            it.place.isFromProfileVisit -> ClevertapEventSourceValues.PROFILE_VISIT_FEATURE
                            limitType == InteractionType.INSTA_CHAT -> ClevertapEventSourceValues.INSTACHAT
                            limitType == InteractionType.UNDO -> ClevertapEventSourceValues.UNDO
                            else -> ClevertapEventSourceValues.IMPRESSIONS
                        }
                        val placement = when (limitType) {
                            InteractionType.INSTA_CHAT -> PurchaselyPlacement.OUT_OF_INSTACHATS
                            InteractionType.UNDO -> PurchaselyPlacement.OUT_OF_UNDOS
                            else -> PurchaselyPlacement.OUT_OF_IMPRESSIONS
                        }

                        val isFromImpressions =
                            limitType == InteractionType.LIKE || limitType == InteractionType.DISLIKE

                         if(limitType != InteractionType.DISLIKE) {
                             openPremiumPaywall(
                                 viewPagerStartPosition = viewPagerStart,
                                 eventSourceClevertap = clevertapSource,
                                 placementId = placement.id,
                                 userModel = homeViewModel.userProfile.value,
                                 isFromImpressions = isFromImpressions
                             )
                         }
                    }
                }
            } else {
                when (limitType) {
                    InteractionType.INSTA_CHAT -> {
                        if (DuaApplication.instance.getBillingAvailable()) {
                            if (!(((homeViewModel.userProfile.value?.premiumType == PremiumEnum.PREMIUM1.type)
                                        || !DuaApplication.instance.shouldShowPremium))
                            ) return@observe

                            if (homeViewModel.userCounters?.firstOrNull
                                { it.configurationName == configurationNames?.instachatCounterCN }?.counter?.resetTime == null
                            ) {
                                homeViewModel.setInstaChatLimitReachedCounterRestTime()
                            }

                            openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.CARDS,
                                placementId = PurchaselyPlacement.DYNAMIC_INSTACHAT.id,
                                userModel = homeViewModel.userProfile.value
                            )

                        } else {
                            if (instaChatLimitDialog != null) {
                                if (homeViewModel.userCounters?.firstOrNull
                                    { it.configurationName == configurationNames?.instachatCounterCN }?.counter?.resetTime == null
                                ) {
                                    homeViewModel.setInstaChatLimitReachedCounterRestTime()
                                }
                                if (!isFragmentDialogShowing(instaChatLimitDialog)) {
                                    instaChatLimitDialog!!.show(
                                        supportFragmentManager,
                                        "superLikeLimitDialog"
                                    )
                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                                            FirebaseAnalyticsParameterName.OUT_OF_IMPRESSIONS_POPUP_COUNT.value to 1L
                                        ))

                                    val premiumTypeValue =
                                        getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                    sendClevertapEvent(
                                        ClevertapEventEnum.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue
                                        )
                                    )

                                    val eventSourceValue =
                                        ClevertapEventSourceValues.INSTACHAT.value

                                    sendClevertapEvent(
                                        ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                            ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue))


                                }
                            }
                        }
                    }
                    InteractionType.LIKE -> {

                        if (DuaApplication.instance.getBillingAvailable()) {

                            if (!(((homeViewModel.userProfile.value?.premiumType == PremiumEnum.PREMIUM1.type)
                                        || !DuaApplication.instance.shouldShowPremium))
                            ) return@observe

                            if (homeViewModel.userCounters?.firstOrNull
                                { it.configurationName == configurationNames?.likeCounterCN }?.counter?.resetTime == null
                            ) {
                                homeViewModel.setLikesCounterResetTime()
                            }

                            openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.CARDS,
                                placementId = PurchaselyPlacement.DYNAMIC_IMPRESSIONS.id,
                                userModel = homeViewModel.userProfile.value
                            )
                        } else {
                            if ((rewardedAd?.isReady == true && homeViewModel.checkForWatchAd()).not()) {
                                if (interactionsOutOfImpressionsLimitDialog != null) {
                                    if (homeViewModel.userCounters?.firstOrNull
                                        { it.configurationName == configurationNames?.likeCounterCN }?.counter?.resetTime == null
                                    ) {
                                        homeViewModel.setLikesCounterResetTime()
                                    }
                                    if (!isFragmentDialogShowing(
                                            interactionsOutOfImpressionsLimitDialog)
                                    ) {
                                        interactionsOutOfImpressionsLimitDialog!!.show(
                                            supportFragmentManager,
                                            "interactionsLimitDialog"
                                        )
                                        firebaseLogEvent(
                                            FirebaseAnalyticsEventsName.OUT_OF_IMPRESSIONS_POPUP,
                                            mapOf(
                                                FirebaseAnalyticsParameterName.OUT_OF_IMPRESSIONS_POPUP_COUNT.value to 1L))

                                        val premiumTypeValue =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendClevertapEvent(
                                            ClevertapEventEnum.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue
                                            )
                                        )

                                        val eventSourceValue =
                                            ClevertapEventSourceValues.IMPRESSIONS.value

                                        sendClevertapEvent(
                                            ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW, mapOf(
                                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue))

                                    }
                                }
                            }
                        }
                    }
                    InteractionType.DISLIKE -> {

                        if (DuaApplication.instance.getBillingAvailable()) {

                            if (!(((homeViewModel.userProfile.value?.premiumType == PremiumEnum.PREMIUM1.type)
                                        || !DuaApplication.instance.shouldShowPremium))
                            ) return@observe

                            if (homeViewModel.userCounters?.firstOrNull
                                { it.configurationName == configurationNames?.dislikeCounterCN }?.counter?.resetTime == null
                            ) {
                                homeViewModel.setDislikesCounterResetTime()
                            }
                            sendDislikeLimitationClevertapEvent(it,homeViewModel.userProfile.value)
                            showSwipeLimitationView(binding.swipeLeftLimitationLayout.root)


                        } else {
                            if ((rewardedAd?.isReady == true && homeViewModel.checkForWatchAd()).not()) {
                                if (homeViewModel.userCounters?.firstOrNull
                                    { it.configurationName == configurationNames?.dislikeCounterCN }?.counter?.resetTime == null
                                ) {
                                    homeViewModel.setDislikesCounterResetTime()
                                }
                                sendDislikeLimitationClevertapEvent(it,homeViewModel.userProfile.value)
                                showSwipeLimitationView(binding.swipeLeftLimitationLayout.root)

                            }
                        }
                    }
                    InteractionType.UNDO -> {
                        if (DuaApplication.instance.getBillingAvailable()) {
                            if (DuaApplication.instance.shouldShowPremium) return@observe

                            if (homeViewModel.userCounters?.firstOrNull
                                { it.configurationName == configurationNames?.undoCounterCN }?.counter?.resetTime == null
                            ) {
                                homeViewModel.setUndoCounterResetTime()
                            }

                            openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.CARDS,
                                placementId = PurchaselyPlacement.DYNAMIC_UNDO.id,
                                userModel = homeViewModel.userProfile.value
                            )

                        } else {
                            if (undoLimitDialog != null) {
                                if (homeViewModel.userCounters?.firstOrNull
                                    { it.configurationName == configurationNames?.undoCounterCN }?.counter?.resetTime == null
                                ) {
                                    homeViewModel.setUndoCounterResetTime()
                                }
                                if (!isFragmentDialogShowing(undoLimitDialog)) {
                                    undoLimitDialog!!.show(
                                        supportFragmentManager,
                                        "interactionsLimitDialog"
                                    )
                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                                            FirebaseAnalyticsParameterName.OUT_OF_IMPRESSIONS_POPUP_COUNT.value to 1L
                                        ))

                                    val premiumTypeValue =
                                        getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                    sendClevertapEvent(
                                        ClevertapEventEnum.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue
                                        )
                                    )

                                    val eventSourceValue = ClevertapEventSourceValues.UNDO.value

                                    sendClevertapEvent(
                                        ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                            ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue))
                                }
                            }
                        }
                    }
                    else -> {
                    }

                }
            }
        }

        homeViewModel.conversationHasChat.observe(this, {
            matchesTabViewModel.onMessageReceives(mutableListOf(it))
            instaChatViewModel.removeInstaChatFromList(it.id ?: "")

        })

        homeViewModel.showFilter.observe(this) { source->
            navController.navigateSafer(R.id.action_global_filterOptionsFragment, bundleOf(ARG_EVENT_SOURCE to source))
        }

        homeViewModel.tags.observe(this, {
            Timber.tag(HomeViewModel.TAG).d("tags collected")
            DuaAccount.tags = it
        })

        homeViewModel.userFilterUpdated.observe(this, {
            //clear allRecommendedUsers list
            homeViewModel.fetchedRecommendedUsersIds.clear()

            homeViewModel.getCardsAndFeaturedProfilesFromAPi(fromTouch = false, applyCardsImmediately = true)
        })

        homeViewModel.deleteUser.observe(this, {
            duaAccount.deleteAllData()
        })

        if (savedInstanceState == null) {
            setupBottomNavigationBar()
        } // Else, need to wait for onRestoreInstanceState
         hasLocationPermissionDialogBeenShown =
            savedInstanceState?.getBoolean(KEY_LOCATION_PERMISSION_DIALOG_SHOWN, false) == true

        homeViewModel.dislikeInstaChat.observe(this, {
            it?.let {
                instaChatViewModel.removeInstaChatFromListByUserId(it.userId, true)
            }
        })

        homeViewModel.consumePurchase.observe(this) {
            consumeProduct(it)
        }

        homeViewModel.instachatsBought.observe(this) {
            checkIfProfileWasVisited()
        }

        homeViewModel.showShadowBannedDialog.observe(this) {
            showShadowBannedDialog()
        }

        billingViewModel.buyPackage.observe(this, Observer { playPackage ->
            playPackage?.let { buyItem(it) }
        })

        lifecycleScope.launch {
            homeViewModel.isTypingStateChange.collect {
                matchesTabViewModel.updateTypingStateChanged(it)
            }
        }

        binding.bottomNav.setOnNavigationItemReselectedListener {}

        checkExtras(intent)
        checkAndShowCrossPathIntro()
        showNewFeaturesOnDuaPopUp()
        checkAndObserverForDotNotification()
        checkForUpdate()
        LocalBroadcastManager.getInstance(this).registerReceiver(
                onAppForegroundedReceiver,
                IntentFilter(DuaApplication.ON_APP_FOREGROUNDED)
        )

        instaChatLimitDialog = OutOfImpressionsDialog.newInstance(InteractionType.INSTA_CHAT)
        interactionsOutOfImpressionsLimitDialog =
                OutOfImpressionsDialog.newInstance(InteractionType.LIKE)
        undoLimitDialog = OutOfImpressionsDialog.newInstance(InteractionType.UNDO)


        LocalBroadcastManager.getInstance(this).registerReceiver(
                onUpdateDataReceiver,
                IntentFilter(NotificationHelper.UPDATE_DATA)
        )

        val durationFilter = IntentFilter()
        durationFilter.addAction(CALL_STATE_FILTER)
        ContextCompat.registerReceiver(
            this,
            callStatusReceiver, 
            durationFilter,
            ContextCompat.RECEIVER_EXPORTED
        )

        val callEndedFilter = IntentFilter()
        callEndedFilter.addAction(CallService.END_CALL_ACTION)
        ContextCompat.registerReceiver(
            this,
            callEndedBroadcastReceiver, 
            callEndedFilter, 
            ContextCompat.RECEIVER_EXPORTED
        )

        val newConversationFilter = IntentFilter()
        newConversationFilter.addAction(ConversationFragment.NEW_CONVERSATION_BROADCAST_FILTER)
        ContextCompat.registerReceiver(
            this,
            newConversationCreatedBroadcastReceiver, 
            newConversationFilter, 
            ContextCompat.RECEIVER_EXPORTED
        )

        val premiumOfferFilter = IntentFilter(PremiumActivity.PREMIUM_OFFER_INTENT)
        LocalBroadcastManager.getInstance(this).registerReceiver(premiumOfferAvailableBroadcastReceiver, premiumOfferFilter)

        homeViewModel.checkIfHasReferralId()
        Giphy.configure(this, BuildConfig.GIPHY_API_KEY)

        binding.callIndicator.container.setOnClickListener {
            if (isCallServiceRunning(this)) {
                val openIntent = Intent(this, CallActivity::class.java)
                openIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivityForResult(openIntent, CALL_REQUEST_CODE)
                overridePendingTransition(R.anim.enter_from_top, R.anim.nothing)
            } else {
                setCallIndicatorVisibility(View.GONE)
            }
        }

        homeViewModel.showWatchAd.observe(this) { rewardVideosAmount ->
            val firstItem = homeViewModel.recommendedUsers.value?.firstOrNull()
            if (rewardedAd?.isReady == true && firstItem?.profile?.pictureUrl != null) {
                val watchAdArgs = WatchAdArgs(imageUrl = firstItem.profile.pictureUrl,
                    xLikes = rewardVideosAmount.toString())
                WatchAdBottomSheetRouting.openWatchAdBottomSheetFragment(supportFragmentManager,
                    watchAdArgs)
            }
        }

        ContextCompat.registerReceiver(
            this,
            boostPurchaselyScreensReceiver, 
            IntentFilter(SplashActivity.PURCHASELY_BOOST_INTENT_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            purchaselyScreensReceiver, 
            IntentFilter(SplashActivity.PURCHASELY_INTENT_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            forcePurchaselyScreensReceiver, 
            IntentFilter(SplashActivity.PURCHASELY_FORCE_PAYWALL_INTENT_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            deepLinkScreensReceiver, 
            IntentFilter(SplashActivity.SCREENS_INTENT_FILTER),
            ContextCompat.RECEIVER_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            deepLinkSunnyHillReceiver, 
            IntentFilter(SplashActivity.SUNNY_HILL_INTENT_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            deepLinkActionReceiver,
            IntentFilter(SplashActivity.DEEP_LINK_ACTION_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ContextCompat.registerReceiver(
            this,
            premiumOfferScreensReceiver, 
            IntentFilter("premium_offer"),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        onNavButtonReselected()

        consumePurchasedGoods()

        deleteOfferDataIfNotActive()

        displayBoostResultDialog()

//        displayDontLetGoOfferPremiumFragment()

//        displaySpecialOfferPremium()

        showBadge2PopUpWhenNotification()

        checkShowSetupAccountCredentials()

        displayPurchaselyPaywall()

        displayBoostPurchaselyOffer()

        setHasLocationServicesEnabled(isLocationPermissionEnabled)
        setLocationServicesStatus(isLocationPermissionEnabled, this.checkBackgroundLocationPermission())

        fetchLastLocationIfPermissionsEnabled()

        checkIfProfileWasVisited()

        checkIfRewardGiven()

        checkOfferAvailable()

        checkSunnyHillAttendeeId()

        checkForPaymentTypeDialog()

        setAppThemeInClevertap(this)

        checkIfAppVersionIsSupported(this, homeViewModel.userProfile.value)

        updateNotificationPermissionStatusInCleverTap(this,duaSharedPrefs)
        manageNotificationPermissionDialog(savedInstanceState,this,duaSharedPrefs)

        lifecycleScope.launch {
            schedulePeriodicLocationUpdatesUseCase(this@HomeActivity)
            scheduleSyncingLocationBackendUseCase(this@HomeActivity)
            runSyncingLocationBackendUseCase(this@HomeActivity)
        }



        binding.locationServicesLayout.let {
            it.settingsButton.setOnSingleClickListener {
                if(!<EMAIL>() && !shouldProvideRationale()) {
                    requestPermissions(ClevertapEventSourceValues.CARDS.value)
                    return@setOnSingleClickListener
                }
                openAppSettings()
            }
        }

        //init the viewmodel to get the crosspaths
        crossPathViewModel
    }

    private fun handleBadge2VerificationState(user: UserModel) {
        val badge2VerificationState = user.getVerificationState()
        badge2VerificationState?.let { state ->
            if (state == Badge2VerificationState.REQUESTED && !DuaAccount.hasShownVerifyPopup) {
                DuaAccount.hasShownVerifyPopup = true
                badge2VerificationPopUp(
                    verificationState = state,
                    eventSource = ClevertapVerificationSourceValues.REGULAR
                )
            } else if(state == Badge2VerificationState.REQUIRED){
                badge2VerificationPopUp(
                    verificationState = state,
                    eventSource = ClevertapVerificationSourceValues.REGULAR
                )
            }

        }
        duaSharedPrefs.setBadge2VerificationState(badge2VerificationState?.state)

    }

    private fun sendDislikeLimitationClevertapEvent(limitReachedModel: LimitReachedModel, userProfile: UserModel?) {
        val premiumType = getPremiumTypeEventProperty(userProfile)
        val eventSource = limitReachedModel.place.name.lowercase()
        sendClevertapEvent(
            ClevertapEventEnum.DISLIKE_LIMIT_REACHED,
            mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource
            )
        )
    }

    private fun showSwipeLimitationView(view: View ) {
        val fadeDuration = 500L
        val visibleDuration = 3000L

        view.visibility = View.VISIBLE
        view.bringToFront()
        view.alpha = 0f

        view.animate()
            .alpha(1f)
            .setDuration(fadeDuration)
            .withEndAction {
                lifecycleScope.launch {
                    delay(visibleDuration)
                    view.animate()
                        .alpha(0f)
                        .setDuration(fadeDuration)
                        .withEndAction {
                            view.visibility = View.GONE
                        }
                }
            }
    }

    private fun checkSunnyHillAttendeeId() {
        val attendeeId = duaSharedPrefs.getSunnyHillAttendeeId()
        if(!attendeeId.isNullOrEmpty() && SunnyHillUtils.shouldShowSunnyHillViews()) {
            homeViewModel.redeemTicket(attendeeId)
                duaSharedPrefs.setSunnyHillAttendeeId(null)
        }
    }

    private fun checkAndShowCrossPathIntro() {
        val DOESNT_EXIST = -1

        val savedVersionCode = duaSharedPrefs.getSavedVersionCode()
        if (!duaSharedPrefs.isCrossPathIntroShown() && savedVersionCode != DOESNT_EXIST) {
            lifecycle.launchWhileAtLeast(Lifecycle.State.STARTED){
                if(!<EMAIL>()){
                    showProfileRecommendationsDialogFragment(homeViewModel.userProfile.value)
                }
            }
        }
        duaSharedPrefs.showCrossPathIntro()
    }
     private fun manageLocationPermissions() {
         hasLocationPermissionDialogBeenShown = true
         if (!isLocationPermissionEnabled() && !shouldProvideRationale() ) {
                requestPermissions(ClevertapEventSourceValues.CARDS.value)
                return
            }
        checkAndRequestAlwaysLocationPermission(ClevertapEventSourceValues.CROSS_PATH.value!!, homeViewModel.userProfile.value)
    }

    private fun openOnboardingPlacement() {
        lifecycleScope.launch {
            delay(1000)

            val billingAvailable = DuaApplication.instance.getBillingAvailable()
            val billingAvailableKey = ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName
            sendClevertapEvent(
                ClevertapEventEnum.BEFORE_ONBOARDING_PAYWALL_EXPOSURE,
                mapOf(billingAvailableKey to billingAvailable)
            )

            Timber.tag(PremiumActivity.PURCHASELY_TAG).d("openPremiumPaywallAsync")
            openPremiumPaywallAsync(
                PremiumPaywallList.UNBLURRED_ITEM,
                ClevertapEventSourceValues.ONBOARDING,
                placementId = PurchaselyPlacement.ONBOARDING.id,
                userModel = homeViewModel.userProfile.value
            )

            withContext(Dispatchers.IO) {
                duaSharedPrefs.setShouldShowOnboardingPaywall(false)
            }
        }
    }
    private fun manageNotificationPermissionDialog(
        savedInstanceState: Bundle?,
        context: Context,
        sharedPreferences: DuaSharedPrefs
    ) {

        val hasNotificationPermissionDialogBeenShown =
            savedInstanceState?.getBoolean(KEY_NOTIFICATION_PERMISSION_DIALOG_SHOWN, false) == true

        if (!hasNotificationPermissionDialogBeenShown) {
            lifecycle.launchWhileAtLeast(Lifecycle.State.STARTED){
                showNotificationPermissionDialog(context, sharedPreferences)
            }
        }
    }

    fun checkIfProfileWasVisited() {
        lifecycleScope.launchWhenResumed {
            delay(1000)
            val cognitoId = duaSharedPrefs.getProfileVisitedCognitoId()
            if (!cognitoId.isNullOrEmpty()) {
                showProfileVisited(cognitoId)
                duaSharedPrefs.setProfileVisitedCognitoId(null)
            }
        }
    }

    fun checkIfRewardGiven() {
        lifecycleScope.launchWhenResumed {
            delay(1000)
            val model = duaSharedPrefs.getConsumableRewardModel()
            if(model != null){
              showRewardDialog(model)
            }
        }
    }

    private fun checkOfferAvailable() {
        val model = duaSharedPrefs.getRealTimeClevertapShowOffer()
        model?.let {
            lifecycleScope.launchWhenResumed {
                delay(1000)

                if(model.activeUntil != null && System.currentTimeMillis() < model.activeUntil) {
                    openPremiumPaywall(
                            eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                            placementId = model.placementId,
                            userModel = homeViewModel.userProfile.value
                    )
                }

                duaSharedPrefs.setRealTimeClevertapShowOffer(null)
            }
        }

    }

     fun checkForPaymentTypeDialog() {
        lifecycleScope.launchWhenResumed {
            delay(1000)
            val paymentType = duaSharedPrefs.getPaymentTypePaywall()
                ?.let {
                    if(it.isNotEmpty()){
                        PaymentType.valueOf(it)
                    } else null
                }

            if(paymentType != null) {
                val placementId = when(paymentType) {
                    PaymentType.IMPRESSIONS -> PurchaselyPlacement.DYNAMIC_IMPRESSIONS
                    PaymentType.INSTACHATS -> PurchaselyPlacement.DYNAMIC_INSTACHAT
                    PaymentType.UNDO -> PurchaselyPlacement.DYNAMIC_UNDO
                    PaymentType.FLYING -> PurchaselyPlacement.DYNAMIC_FLIGHT
                    PaymentType.BOOST -> PurchaselyPlacement.DYNAMIC_BOOST
                    PaymentType.BOOST_OFFER -> PurchaselyPlacement.DYNAMIC_BOOST
                    else -> null
                }
                placementId?.let {
                    openPremiumPaywallAsync(
                        eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                        placementId = it.id,
                        userModel = homeViewModel.userProfile.value
                    )
                }

                duaSharedPrefs.setPaymentTypePaywall(null)
            }
        }
    }

    private fun showProfileVisited(cognitoId: String) {
        val userProfileFragment = UserProfileFragment.newInstance(
            RecommendedUserModel.getEmptyRecommendedUserModel().copy(cognitoUserId = cognitoId),
            _isFromHome = true,
            _isFeaturedProfile = true,
            _profileVisitSource = ProfileVisitSourceEnum.PROFILE_VISIT.source)
        if (supportFragmentManager.findFragmentByTag("userProfile") == null) {
            userProfileFragment.show(supportFragmentManager, "userProfile")
        }
    }

    override fun onVerifyYouProfileClicked(
        openedFrom: VerifyYourProfileDialog.VerifyProfileFromEnum,
        eventName: String?
    ) {
        when(openedFrom) {
            VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.INSTACHAT_VERIFY_PROFILE_TIER2)

                val eventPremiumType =
                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                sendVerifyYourProfileInitiatedAnalyticsEvent(
                    ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value,
                    eventPremiumType
                )

                navController.navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
                    bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value)
                )
            }
            else -> {/*not used*/}
        }
    }


    override fun onUserInteracted(
        direction: Direction,
        user: RecommendedUserModel,
        isFeaturedProfile: Boolean
    ) {
        val interactionType = when(direction){
            Direction.Right -> InteractionType.LIKE
            Direction.Left -> InteractionType.DISLIKE
            Direction.Top -> InteractionType.INSTA_CHAT
            else -> null
        }
        if(isFeaturedProfile) {
            interactionType?.let { interactVisitedProfileUser(it, user) }
            return
        }
    }

    fun interactVisitedProfileUser(
        interactionType: InteractionType,
        recommendedUserModel: RecommendedUserModel
    ) {
        when (interactionType) {
            InteractionType.INSTA_CHAT -> {
                recommendedUserModel.let {
                    if (recommendedUserModel.cardUserGuideType == null) {
                        if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                            if (!it.isMobAd && !it.isProfileInfo && !it.isProfileActivities && !it.isBrillionAd && !it.isIncognitoMode && !it.isDontLetGoItem) {
                                if (homeViewModel.remainingInstaChatInteractions()) {
                                    val dialog = getInstaChatDialogFragment(
                                        recommendedUserModel,
                                        isFeaturedProfile = true
                                    )
                                    dialog.show(supportFragmentManager, "InstaChatDialogFragment")

                                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                    val eventSourceValue =
                                        SwipeSourceValues.PROFILE_VISIT_FEATURE

                                    val eventActivityType = recommendedUserModel.activityType
                                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                            FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                            FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                        )
                                    )
                                    sendClevertapEvent(
                                        ClevertapEventEnum.INSTACHAT_INITIATED,
                                        mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                            ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                            ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                        )
                                    )

                                } else {
                                    duaSharedPrefs.setProfileVisitedCognitoId(recommendedUserModel.cognitoUserId)
                                    val limitReachedSource = LimitReachedScreenSource.getAndSetProfileVisit(LimitReachedScreenSource.POPULAR)
                                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, limitReachedSource))
                                }
                            } else {

                                if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                                    if (homeViewModel.remainingInstaChatInteractions()) {
                                        val dialog = getInstaChatDialogFragment(
                                            recommendedUserModel,
                                            isFeaturedProfile = true
                                        )
                                        dialog.show(supportFragmentManager, "InstaChatDialogFragment")


                                    } else {
                                        duaSharedPrefs.setProfileVisitedCognitoId(recommendedUserModel.cognitoUserId)
                                        val limitReachedSource = LimitReachedScreenSource.getAndSetProfileVisit(LimitReachedScreenSource.POPULAR)
                                        homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, limitReachedSource))
                                    }
                                } else {
                                    if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                        VerificationInProgressDialog.showVerificationInProgressDialog(
                                            supportFragmentManager
                                        )
                                    } else {
                                        val eventPremiumType =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendVerifyYourProfilePopupAnalyticsEvent(
                                            eventPremiumType,
                                            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                        )

                                        VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                            supportFragmentManager,
                                            VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                        )
                                    }
                                }

                            }
                        } else {
                            if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                VerificationInProgressDialog.showVerificationInProgressDialog(
                                    supportFragmentManager
                                )
                            } else {
                                val eventPremiumType =
                                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                sendVerifyYourProfilePopupAnalyticsEvent(
                                    eventPremiumType,
                                    ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                )

                                VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                    supportFragmentManager,
                                    VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                )
                            }

                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                            val eventSourceValue =
                                SwipeSourceValues.PROFILE_VISIT_FEATURE

                            val eventActivityType = recommendedUserModel.activityType
                            val recommSource = recommendedUserModel.recommSource ?: "dua"
                            val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                            val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                    FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                )
                            )
                            sendClevertapEvent(
                                ClevertapEventEnum.INSTACHAT_INITIATED,
                                mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                    ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                    ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                    ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                    ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                    ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                    ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                )
                            )
                        }
                    } else {
                        val dialog = getInstaChatDialogFragment(
                            recommendedUserModel,
                            isFeaturedProfile = true
                        )
                        dialog.isCancelable = false
                        dialog.show(supportFragmentManager, "InstaChatDialogFragment")
                    }
                }
            }

            InteractionType.DISLIKE -> {
                homeViewModel.swipeCard(recommendedUserModel, InteractionType.DISLIKE)
                val eventActivityType = recommendedUserModel.activityType
                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.SWIPE_LEFT,
                        mapOf(
                            FirebaseAnalyticsParameterName.SWIPE_LEFT_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.SOURCE.value to SwipeSourceValues.POPULAR.value,
                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                        )

                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val gender =
                        if (homeViewModel.userProfile.value?.gender == GenderType.WOMAN.value) {
                            "female"
                        } else {
                            "male"
                        }
                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                  val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                sendClevertapEvent(
                        ClevertapEventEnum.SWIPE_LEFT, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                            ClevertapEventPropertyEnum.GENDER.propertyName to gender,
                            ClevertapEventPropertyEnum.SOURCE.propertyName to SwipeSourceValues.POPULAR.value,
                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                        )
                    )

            }
            else -> {}
        }
    }

    private fun getInstaChatDialogFragment(recommendedUserModel: RecommendedUserModel, isFeaturedProfile: Boolean = false): InstaChatDialogFragment {
        val dialog = InstaChatDialogFragment.newInstance()
        val arguments = Bundle()
        arguments.putString(InstaChatDialogFragment.USER_NAME, recommendedUserModel.firstName)
        if (recommendedUserModel.cardUserGuideType != null) {
            arguments.putString(
                InstaChatDialogFragment.USER_GUIDE_TEXT,
                getString(R.string.instachat_user_guide_text,"72")
            )
        }
        dialog.arguments = arguments
        dialog.listener = object : InstaChatDialogFragment.InstaChatDialogListener {
            override fun onSendButtonClicked(dialog: InstaChatDialogFragment, message: String?) {
                dialog.dismissAllowingStateLoss()
                if (recommendedUserModel.cardUserGuideType == null) {
                    homeViewModel.swipeCard(
                        recommendedUserModel = recommendedUserModel,
                        interactionType = InteractionType.INSTA_CHAT,
                        message = message,
                        swipeSource = SwipeSourceValues.PROFILE_VISIT_FEATURE
                    )
                }
            }
        }
        return dialog
    }


    private fun checkLocationPermission(): Boolean {
        val result = ContextCompat.checkSelfPermission(this, ACCESS_COARSE_LOCATION)
        return result == PackageManager.PERMISSION_GRANTED
    }

    private fun displayBoostResultDialog() {
        if (!duaSharedPrefs.getHasBoostResultDialogShown()) {
            val boostResult = duaSharedPrefs.getBoostResultData()
            if (boostResult != null) {
                val boostDialog = BoostResultDialog.newInstance(boostResult)
                boostResultDialog = boostDialog
                boostDialog.show(supportFragmentManager, "BoostResultDialog")
                duaSharedPrefs.setBoostResultDialogShown(true)
            }
        } else {
            return
        }
    }


//    private fun displayDontLetGoOfferPremiumFragment() {
//        if (DuaApplication.instance.getBillingAvailable()
//            && duaSharedPrefs.getDontLetGoOfferData() != null
//            && !duaSharedPrefs.getDontLetGoOfferShown()
//            && homeViewModel.userProfile.value?.premiumType != null) {
//
//            openPremiumActivity()
//        }
//    }

//    private fun displaySpecialOfferPremium() {
//        lifecycleScope.launch(Dispatchers.Main) {
//            delay(1000)
//            val remoteConfigOffer = RemoteConfigUtils.getSpecialOfferData()
//            val sharedPrefsOffer = duaSharedPrefs.getPremiumSpecialOfferData()
//
//            Timber.tag("SPECIAL_OFFER").d("HOME remoteConfigOffer: $remoteConfigOffer")
//            Timber.tag("SPECIAL_OFFER").d("HOME sharedPrefsOffer: $sharedPrefsOffer")
//
//            if (DuaApplication.instance.getBillingAvailable()
//                && remoteConfigOffer != null
//                && remoteConfigOffer != sharedPrefsOffer) {
//
//                duaSharedPrefs.setPremiumSpecialOfferData(remoteConfigOffer)
//
//                val currentTime = System.currentTimeMillis()
//                if(remoteConfigOffer.startTime < currentTime && remoteConfigOffer.endTime > currentTime) {
//                    openPremiumActivity()
//                }
//            }
//        }
//    }

    private fun displayPurchaselyPaywall() {
        val placementId = duaSharedPrefs.getPurchaselyPaywallId()
        if(!placementId.isNullOrEmpty() && homeViewModel.userProfile.value?.premiumType == null) {
            lifecycleScope.launchWhenResumed {
                openPremiumPaywallAsync(
                    eventSourceClevertap = ClevertapEventSourceValues.DEEPLINK,
                    placementId = placementId,
                    userModel = homeViewModel.userProfile.value
                )
            }
        }

        duaSharedPrefs.setPurchaselyPaywallId(null)

        //force open paywall even if the user is premium
        val forcePlacementId = duaSharedPrefs.getForcePurchaselyPaywallId()
        if(!forcePlacementId.isNullOrEmpty()) {
            lifecycleScope.launchWhenResumed {
                openPremiumPaywallAsync(
                    eventSourceClevertap = ClevertapEventSourceValues.DEEPLINK,
                    placementId = forcePlacementId,
                    userModel = homeViewModel.userProfile.value
                )
            }
        }

        duaSharedPrefs.setForcePurchaselyPaywallId(null)
    }

    private fun displayBoostPurchaselyOffer() {
        val placementId = duaSharedPrefs.getBoostPurchaselyPaywallId()
        if(!placementId.isNullOrEmpty()) {
            lifecycleScope.launchWhenResumed {
                showBoostOfferPurchasely(placementId)
            }
        }

        duaSharedPrefs.setBoostPurchaselyPaywallId(null)
    }



    private fun openOrCloseWebSocket(isOpen: Boolean) {
        if (isOpen) {
            webSockedTimer?.cancel()
            homeViewModel.connectWithWebSocket()
        } else {
            webSockedTimer?.cancel()
            webSockedTimer = Timer()
            val timerTask: TimerTask = object : TimerTask() {
                override fun run() {
                    homeViewModel.closeWithWebSocket()
                }
            }
            webSockedTimer?.schedule(timerTask, webSockedTimerDelay)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        // Now that BottomNavigationBar has restored its instance state
        // and its selectedItemId, we can proceed with setting up the
        // BottomNavigationBar with Navigation
        setupBottomNavigationBar()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        checkExtras(intent, true)
    }

    private fun checkExtras(intent: Intent?, isUpdateIntent: Boolean = false) {
        val extras = intent?.extras
        if (extras != null) {
            if (extras.containsKey("action") && extras.containsKey("jsonData"))
                onPushNotificationClick(NotificationModel(extras))
        } else if (intent?.getStringExtra("action") != null && intent.getStringExtra("jsonData") != null) {
            Bundle().apply {
                putString("action", intent.getStringExtra("action"))
                putString("jsonData", intent.getStringExtra("jsonData"))
                onPushNotificationClick(NotificationModel(this))
            }
        }
    }

    private fun onPushNotificationClick(notificationModel: NotificationModel) {
        if (notificationModel.type != NotificationType.MESSAGE) {
            homeViewModel.setOnNewPushNotificationsClick()
        }
        when (notificationModel.type) {
            NotificationType.MESSAGE -> {

                if (homeViewModel.userProfile.value?.isDisabled == true) {
                    return
                }

                val conversationModel = (notificationModel.data as ConversationData).conversationMember

                //user has allowed calls
                if((notificationModel.data!! as ConversationData).message.type == MessageType.CALLS_ALLOWED.value)
                    conversationModel.areOutgoingCallsAllowed = true
                else if ((notificationModel.data!! as ConversationData).message.type == MessageType.CALLS_DISALLOWED.value)
                    conversationModel.areOutgoingCallsAllowed = false

                if (!(ConversationFragment.isInConversationFragment && ConversationFragment.isChattingWithId == conversationModel.userId)) {
                    homeViewModel.setOnNewPushNotificationsClick()
                    if (conversationModel.type != ConversationType.INSTANT_CHAT.value) {
                        homeViewModel.setNavigateToChat(conversationModel)
                    } else {
                        if(conversationModel.isRMOD) {
                            homeViewModel.setNavigateToChat(conversationModel)
                        } else if (conversationModel.gender != GenderType.MAN.value && !duaSharedPrefs.isLoggedInUserPremium()) {
                            homeViewModel.setNavigateToInstaChatList(true)
                        } else {
                            homeViewModel.setNavigateToInstaChat(conversationModel)
                        }
                    }
                    navigateTo(R.id.nav_graph_chat)
                }
            }
            NotificationType.MATCH -> {
                @Suppress("UNCHECKED_CAST")
                val matchesModelNotification =
                    (notificationModel.data as ArrayList<UserMatchNotificationResponse>)[0].data
                if (!matchesModelNotification.seen) {
                    matchesTabViewModel.seenMatch(matchesModelNotification.id!!).observe(
                        this,
                        Observer {
                            when (it) {
                                is Result.Success -> {
                                    matchesTabViewModel.updateSeenMatchAdapter(
                                            matchesModelNotification.id
                                    )
                                }
                                is Result.Error -> {
                                }
                                is Result.Loading -> {
                                }
                            }
                        })
                }
                val conversationModel = ConversationModel(
                    null,
                        matchesModelNotification.user.cognitoUserId,
                    "",
                    "",
                    "",
                        matchesModelNotification.time,
                        matchesModelNotification.user.firstName,
                     "",
                        matchesModelNotification.user.profile.pictureUrl,
                    null,
                    null,
                    1,
                    if (matchesModelNotification.receivedSuperMatch) 1 else 0,
                    ConversationType.DEFAULT.value,
                        matchesModelNotification.user.hasBadge1,
                        matchesModelNotification.user.badge2,
                    false,
                    false,
                    false,
                    0,
                    false,
                    0
                )
                conversationModel.isFromMatchView = true

                homeViewModel.setNavigateToChat(conversationModel)
                navigateTo(R.id.nav_graph_chat)
            }
            NotificationType.NEW_LIKE_RECEIVED -> {
                @Suppress("UNCHECKED_CAST")
                val userLikesModel = UserLikesModel.convertToUserLikesModel(
                    (notificationModel.data as UserLikedYouNotificationResponse).data
                )
                userLikesModel?.let {
                    homeViewModel.setNavigateToLikedYou(
                        userLikesModel
                    )
                }

                navigateTo(R.id.nav_graph_likes)
            }
            NotificationType.NONE -> {
            }
            NotificationType.UN_MATCHED -> {
            }
            NotificationType.COUNTER_RESET -> {
                homeViewModel.userProfile.value?.let {
                    homeViewModel.getUserCounters(it)
                }
                navigateTo(R.id.nav_graph_home)
            }
            NotificationType.USER_DELETED -> {
                duaAccount.deleteAllData()
            }
            NotificationType.PROFILE_COMPLETION_REMINDER -> {
            }
            NotificationType.VERIFY_USER_ATTRIBUTE -> {

            }
            NotificationType.NEW_REFERRAL_REWARD -> {
                homeViewModel.userProfile.value?.let {
                    homeViewModel.getUserCounters(it)
                }
                navigateTo(R.id.nav_graph_home)
                homeViewModel.showTrophyDialog()
            }
            NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> {
            }
            NotificationType.UPDATE_REMOTE_CONFIG -> {
            }
            NotificationType.DISLIKE_INSTACHAT -> {
            }
            NotificationType.VERIFY_YOUR_IMAGE -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.POPUP_NOTIFICATION_VERIFY_PROFILE_TIER2)
                val eventPremiumType =
                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                sendVerifyYourProfileInitiatedAnalyticsEvent(
                    ClevertapVerificationSourceValues.SETTINGS.value,
                    eventPremiumType
                )

                navController.navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
                    bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.SETTINGS.value)
                )
            }
            NotificationType.DOWNLOAD_DATA -> {
                val intent = Intent(this, SettingsActivity::class.java)
                intent.putExtra(
                SETTINGS_ACTIVITY_START_DESTINATION_INTENT, "Download_Data_Notification")
                startActivity(intent)
            }

            NotificationType.IMPRESSIONS_REWARDED ->{}

            NotificationType.OPEN_PREMIUM_PAYWALL -> {
                PurchaselyManager.clearCache()

                if (DuaApplication.instance.getPremiumAvailable() && homeViewModel.userProfile.value?.premiumType == null) {
                    openPremiumPaywall(
                        eventSourceClevertap = ClevertapEventSourceValues.BE_NOTIFICATION_OPEN_PAYWALL,
                        placementId = PurchaselyPlacement.PROFILE_GO_PREMIUM_BUTTON.id,
                        userModel = homeViewModel.userProfile.value
                    )
                }
            }

            NotificationType.PREMIUM_SPECIAL_OFFER, NotificationType.PREMIUM_PAY_1_GET_1 -> {
                PurchaselyManager.clearCache()

//                val specialOfferDataModel = notificationModel.data as SpecialOfferDataModel
//                handleSpecialOfferNotification(specialOfferDataModel)
            }

            NotificationType.BOOST_SUCCESS -> {
                val boostResult = notificationModel.data as BoostResultModel
                homeViewModel.setUserBoostFinishes()
                homeViewModel.setBoostStatusChanged()
                BoostResultDialog.newInstance(boostResult).show(supportFragmentManager, "BoostResultDialog")
                if (isFragmentDialogShowing(boostResultDialog)) {
                    boostResultDialog?.dismissAllowingStateLoss()
                }
            }

            NotificationType.BOOST_FAILED -> {
                val boostResult = notificationModel.data as BoostResultModel
                homeViewModel.decreaseBoost()
                homeViewModel.setUserBoostFinishes()
                homeViewModel.setBoostStatusChanged()
                BoostResultDialog.newInstance(boostResult).show(supportFragmentManager, "BoostResultDialog")
                if (isFragmentDialogShowing(boostResultDialog)) {
                    boostResultDialog?.dismissAllowingStateLoss()
                }
            }

            NotificationType.PUSH_BADGE2_VERIFICATION-> {
                if(duaSharedPrefs.getVerifyProfileBadge2DialogModel()?.isBadge2Needed == true){
                    badge2VerificationPopUp(
                        isFromNotification = true,
                        eventSource = ClevertapVerificationSourceValues.RETENTION_JOURNEY
                    )
                }else{
                    badge2VerificationPopUp(
                        isFromNotification = true,
                        eventSource = ClevertapVerificationSourceValues.RETENTION_JOURNEY
                    )
                    duaSharedPrefs.setVerifyProfileBadge2DialogModel(null)
                }
            }

            NotificationType.INSTACHAT_LASTHOUR -> {
                navigateTo(R.id.nav_graph_chat)
                homeViewModel.setNavigateToInstaChatList(true)
            }

            NotificationType.RMOD_LASTHOUR -> {
                navigateTo(R.id.nav_graph_chat)
            }

            NotificationType.SHOW_DONT_LET_GO_OFFER -> {
//                val modelNotification = notificationModel.data as DontLetGoOfferNotificationModel
//                val dontLetGoOfferAvailableResponseModel = DontLetGoOfferAvailableResponseModel(
//                    CurrentSubscription(
//                        cognitoUserId = modelNotification.cognitoUserId,
//                        expireTime = System.currentTimeMillis() + 86400000,
//                        isAutoRenewable = false,
//                        isChecked = false,
//                        premiumPackage = homeViewModel.userProfile.value?.premiumType.toString(),
//                        provider = "play-store",
//                        token = "",
//                        transactionId = ""
//                    ),
//                    offersPackage = OffersPackage(
//                        dontLetGoOffer = modelNotification.packageName
//                    ), showDontLetGoOffer = true
//                )
//
//                val user = homeViewModel.userProfile.value
//
//                if (user?.premiumType != null) {
//                    duaSharedPrefs.setDontLetGoOfferShown(false)
//                    duaSharedPrefs.setDontLetGoOfferData(dontLetGoOfferAvailableResponseModel)
//                }
//                if (DuaApplication.instance.getPremiumAvailable() && homeViewModel.userProfile.value?.premiumType != null)
//                    openPremiumActivity()
            }

            NotificationType.NEW_GROUPED_LIKES -> {
                likeYouViewModel.initData()
                homeViewModel.setNavigateToLikedYou()
                navigateTo(R.id.nav_graph_likes)
            }

            NotificationType.SETUP_ACCOUNT_CREDENTIALS -> {}
            NotificationType.REWARD_VIDEOS -> {}
            NotificationType.FEATURED_USERS_RESET -> {
                navigateTo(R.id.nav_graph_home)
                homeViewModel.getCardsAndFeaturedProfilesFromAPi(fromTouch = false, applyCardsImmediately = true)
                homeViewModel.setHomeScreenType(HomeScreenType.FEATURED_PROFILE)
            }
            NotificationType.RMOD_GENERATED -> {
                navigateTo(R.id.nav_graph_chat)
            }
            else -> {}
        }

        NotificationHelper.clareAllNotifications()
    }

    private fun onDataUpdateFromNotification(notificationModel: NotificationModel) {
        if(navController.currentDestination?.id != R.id.notificationFeedFragment) {
            homeViewModel.showNotificationFeedTabDotNotification(true)

        }
        notificationFeedViewModel.updateLastReceivedNotificationTime(duaSharedPrefs.getLastReceivedNotificationTime())
        when (notificationModel.type) {
            NotificationType.MATCH -> {
                val model = (notificationModel.data as ArrayList<UserMatchNotificationResponse>)[0]

                @Suppress("UNCHECKED_CAST")
                homeViewModel.newMatch(
                    MatchesWebSocketModel(
                        "users.newMatch",
                            model
                    )
                )
                crossPathViewModel.matchReceived(model.data.user.cognitoUserId)
            }

            NotificationType.NEW_LIKE_RECEIVED -> {
                val model = (notificationModel.data as UserLikedYouNotificationResponse)

                @Suppress("UNCHECKED_CAST")
                val likesModel = LikeYouWebSocketModel(
                    "users.newLike",
                        model
                )
                homeViewModel.newLike(likesModel)

                likesModel.data.data?.user?.cognitoUserId?.let {
                    crossPathViewModel.newLikeReceived(it)
                }
            }

            NotificationType.UN_MATCHED -> {
                val userId = (notificationModel.data as UserUnMatchedModel).userId
                homeViewModel.setUserRemovedYou(userId)
                matchesTabViewModel.setUserDeleted(userId)

                homeViewModel.userUnmatched(
                    UserUnmatchedWebSocketModel(
                        "users.unmatched",
                        (notificationModel.data as UserUnMatchedModel)
                    )
                )
            }

            NotificationType.MESSAGE -> {
                val conversationData = notificationModel.data as ConversationData
                homeViewModel.newFcmMessage(
                    ConversationWebSocketModel(
                        "chat.newMessage",
                            conversationData
                    )
                )

                if(conversationData.message.type == ConversationType.INSTANT_CHAT.value)
                    crossPathViewModel.newInstachatReceived(conversationData.conversationMember.userId)
            }

            NotificationType.COUNTER_RESET -> {
                homeViewModel.userProfile.value?.let {
                    homeViewModel.getUserCounters(it)
                }
            }

            NotificationType.USER_DELETED -> {
                homeViewModel.userDeleted(
                    UserDeletedWebSocketModel(
                        "users.deleted",
                        (notificationModel.data as UserDeletedModel)
                    )
                )
            }

            NotificationType.NONE -> {
            }

            NotificationType.PROFILE_COMPLETION_REMINDER -> {
            }

            NotificationType.VERIFY_USER_ATTRIBUTE -> {
            }

            NotificationType.NEW_REFERRAL_REWARD -> {
                homeViewModel.showTrophyDialog()
                homeViewModel.userProfile.value?.let {
                    homeViewModel.getUserCounters(it)
                }
            }

            NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> {
                homeViewModel.likeMessage(notificationModel.data as LikeMessageModel)
            }

            NotificationType.UPDATE_REMOTE_CONFIG -> {
            }

            NotificationType.DISLIKE_INSTACHAT -> {
                homeViewModel.dislikeInstaChat(notificationModel.data as DislikeInstaChatModel)
            }

            NotificationType.BADGE_2 -> {
                val badge2ApprovalModel = notificationModel.data as Badge2ApprovalModel
                homeViewModel.userProfile.value?.badge2 = badge2ApprovalModel.badge2
                if(badge2ApprovalModel.badge2 == Badge2Status.NOT_APPROVED.status){
                    homeViewModel.fetchUserProfile()
                }
                if(badge2ApprovalModel.badge2 == Badge2Status.APPROVED.status &&
                    homeViewModel.userProfile.value?.userAuxiliaryData?.firstTimeVerifiedDate == null)
                    homeViewModel.userProfile.value?.userAuxiliaryData = UserAuxiliaryData(1, null, false, System.currentTimeMillis() / 1000, 0)

                homeViewModel.setUserBadge2StatusOnDb(badge2ApprovalModel.badge2)
                likeYouViewModel.initData(false)
            }

            NotificationType.NEW_LOCAL_SUBSCRIPTION -> {
                //set the value manually as the save in db might not happen before the code gets here
                val gender = homeViewModel.userProfile.value?.gender
                val premiumType = if(gender == GenderType.MAN.value) PremiumEnum.PREMIUM1.type else PremiumEnum.PREMIUM2.type
                homeViewModel.userProfile.value?.premiumType = premiumType

                val sendBroadCastData = Intent(PremiumActivity.PREMIUM_INTENT)
                sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(sendBroadCastData)

                homeViewModel.fetchUserProfile(isAfterPremium = true)
            }

            NotificationType.VERIFY_YOUR_IMAGE ->{
            }

            NotificationType.DOWNLOAD_DATA ->{

            }

            NotificationType.IMPRESSIONS_REWARDED ->{
                val item = (notificationModel.data as ImpressionsRewarded)
                homeViewModel.userProfile.value?.let {
                    homeViewModel.getUserCounters(it, it.counterConfigurationNames.likeCounterCN)
                }

            }

            NotificationType.DISABLED_STATE_CHANGED -> {
                val item = (notificationModel.data as DisableStateChanged)

                homeViewModel.userProfile.value?.let {
                    it.apply {
                        isDisabled = item.isDisabled
                        hasRequestedReview = false
                    }
                    homeViewModel.updateUser(it)
                    handleDisableUserView(it)
                   if(item.isDisabled){
                       cancelCrossPathWorkersUseCase(this)
                   } else {
                       lifecycleScope.launch {
                           schedulePeriodicLocationUpdatesUseCase(this@HomeActivity)
                           scheduleSyncingLocationBackendUseCase(this@HomeActivity)
                           runSyncingLocationBackendUseCase(this@HomeActivity)
                       }
                   }
                }
            }

            NotificationType.OPEN_PREMIUM_PAYWALL -> {
                PurchaselyManager.clearCache()
            }

            NotificationType.PREMIUM_SPECIAL_OFFER, NotificationType.PREMIUM_PAY_1_GET_1  -> {
                PurchaselyManager.clearCache()

//                val specialOfferDataModel = notificationModel.data as SpecialOfferDataModel
//                handleSpecialOfferNotification(specialOfferDataModel)
            }

            NotificationType.BOOST_SUCCESS -> {
                val boostResult = notificationModel.data as BoostResultModel
                homeViewModel.setUserBoostFinishes()
                homeViewModel.setBoostStatusChanged()
                lifecycle.launchWhileAtLeast(Lifecycle.State.RESUMED) {
                        BoostResultDialog.newInstance(boostResult)
                            .show(supportFragmentManager, "BoostResultDialog")
                    }
            }

            NotificationType.BOOST_FAILED -> {
                val boostResult = notificationModel.data as BoostResultModel
                homeViewModel.decreaseBoost()
                homeViewModel.setUserBoostFinishes()
                homeViewModel.setBoostStatusChanged()
                lifecycle.launchWhileAtLeast(Lifecycle.State.RESUMED) {
                        BoostResultDialog.newInstance(boostResult)
                            .show(supportFragmentManager, "BoostResultDialog")
                }
            }


            NotificationType.PUSH_BADGE2_VERIFICATION-> {
            }

            NotificationType.INSTACHAT_LASTHOUR -> {
            }

            NotificationType.SHOW_DONT_LET_GO_OFFER -> {
                PurchaselyManager.clearCache()
            }

            NotificationType.NEW_GROUPED_LIKES -> {
                likeYouViewModel.initData()
            }

            NotificationType.SETUP_ACCOUNT_CREDENTIALS ->{
            }

            NotificationType.REWARD_VIDEOS -> {
                val notificationRewardModel = notificationModel.data as NotificationRewardModel

                when (notificationRewardModel.rewardType) {
                    RewardType.INTERACTION.value -> {
                        homeViewModel.userProfile.value?.let {
                            homeViewModel.getUserCounters(
                                it,
                                it.counterConfigurationNames.likeCounterCN
                            )
                        }
                    }

                    RewardType.UNBLUR.value -> {
                        homeViewModel.fetchUserProfile()
                        likeYouViewModel.initData()
                        lifecycleScope.launch {
                            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                                SuccessUnblurProfileByAdRewardDialog.newInstance().show(
                                    supportFragmentManager,
                                    "SuccessUnblurProfileByAdRewardDialog"
                                )
                                this.cancel()
                            }
                            homeViewModel.refreshLikedYouHeader()
                        }
                    }
                }

            }

            NotificationType.FEATURED_USERS_RESET-> {
            }

            NotificationType.RMOD_GENERATED -> {
                homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                    fromTouch = false,
                    applyCardsImmediately = false,
                    fetchCards = false,
                    fetchFeaturedProfiles = true
                )
            }

            NotificationType.RMOD_REMOVED -> {
                homeViewModel.setRmodDeleted()

                homeViewModel.rmodItem.value?.partner?.cognitoUserId?.let {
                    homeViewModel.setUserRemovedYou(it)
                }
            }

            NotificationType.CONVERSATION_DELETED -> {
                val userId = (notificationModel.data as ConversationDeletedModel).userId

                matchesTabViewModel.setUserDeleted(userId)

                homeViewModel.setUserRemovedYou(userId)
            }

            NotificationType.CROSS_PATH_CREATED -> {
                lifecycle.launchWhileAtLeast(Lifecycle.State.RESUMED) {
                    navigateTo(R.id.nav_graph_cross_path)
                }

            }

            else -> {}
        }
    }

    // commenting out this method for now as Purchasely doesn't support offers
//    private fun handleSpecialOfferNotification(specialOfferDataModel: SpecialOfferDataModel) {
//        val user = homeViewModel.userProfile.value
//        if(user?.premiumType == null) {
//            duaSharedPrefs.setPremiumSpecialOfferData(specialOfferDataModel)
//            duaSharedPrefs.setHasOfferBeenShownFromCards(false)
//            homeViewModel.setSpecialOfferReceived()
//        }
//
//        if(DuaApplication.instance.getPremiumAvailable() && user?.premiumType == null && DuaApplication.instance.getBillingAvailable())
//            showSpecialOffer()
//    }

    private val onUpdateDataReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val extras = intent.extras
            if (extras != null) {
                if (extras.containsKey("action") && extras.containsKey("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(extras))
            } else if (intent.getStringExtra("action") != null && intent.getStringExtra("jsonData") != null) {
                Bundle().apply {
                    putString("action", intent.getStringExtra("action"))
                    putString("jsonData", intent.getStringExtra("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(this))
                }
            }
        }
    }

    private val onAppForegroundedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val extras = intent.extras
            if (extras != null) {
                if (extras.containsKey(DuaApplication.IS_ON_APP_FOREGROUNDED))
                    updateDataAfterOnAppForegrounded(extras)
            } else {
                Bundle().apply {
                    putBoolean(
                        DuaApplication.IS_ON_APP_FOREGROUNDED, intent.getBooleanExtra(
                            DuaApplication.IS_ON_APP_FOREGROUNDED,
                            false
                        )
                    )
                    updateDataAfterOnAppForegrounded(this)
                }
            }
        }
    }

    private val callStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val duration = intent.getLongExtra(CALL_DURATION_KEY, 0L)
            val callState = intent.getStringExtra(CALL_STATUS_KEY)

            Timber.tag(TAG).d("onReceive: CALL_DURATION_KEY $duration")
            Timber.tag(TAG).d("onReceive: CALL_STATUS_KEY $callState")

            if (callState != null) {
                when (callState) {
                    CallState.NO_ANSWER.type -> {
                        binding.callIndicator.duration.text = getString(R.string.no_answer)
                    }
                    CallState.CALL_ENDED.type -> {
                        if (binding.callIndicator.duration.text != getString(R.string.no_answer))
                            binding.callIndicator.duration.text = getString(R.string.call_ended)
                    }
                }
                setCallIndicatorVisibility(View.VISIBLE)
                return
            }

            if (duration <= 0) {
                setCallIndicatorVisibility(View.GONE)
            } else {
                binding.callIndicator.duration.text = DateUtils.formatElapsedTime(duration)
                setCallIndicatorVisibility(View.VISIBLE)
            }
        }
    }

    private val callEndedBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Timber.tag(TAG).d("onReceive: callEndedBroadcastReceiver")
            homeViewModel.setCallEnded()
            setCallIndicatorVisibility(View.GONE)
        }
    }

    fun setCallIndicatorVisibility(visibility: Int) {
        binding.callIndicator.container.visibility = visibility
    }

    override fun onNetworkConnectionChanged(isConnected: Boolean) {
        if (isConnected != lastConnectivity && isConnected) {
            refreshUserProfile()
            homeViewModel.notifyForNetworkConnectionChanged(isConnected)
            matchesTabViewModel.getPreviousConversations()
            matchesTabViewModel.getPreviousMatches()
            likeYouViewModel.initData()
            if (findViewById<BottomNavigationView>(R.id.bottom_nav)?.selectedItemId != R.id.nav_graph_chat) {
                openOrCloseWebSocket(true)
            }

            consumePurchasedGoods()
        }
        lastConnectivity = isConnected
    }

    fun updateDataAfterOnAppForegrounded(extras: Bundle) {
        if (extras.getBoolean(DuaApplication.IS_ON_APP_FOREGROUNDED)) {

            if(haveMoreThanXMinutesPassedFrom(homeViewModel._lastTimeCurrentUserUpdated, CACHE_EXPIRE_TIME_MINUTES))
                homeViewModel.fetchUserProfile()

            if(haveMoreThanXMinutesPassedFrom(matchesTabViewModel._lastTimeConversationsAreUpdated, CACHE_EXPIRE_TIME_MINUTES))
                matchesTabViewModel.getPreviousConversations()

            if(haveMoreThanXMinutesPassedFrom(matchesTabViewModel._lastTimeMatchesAreUpdated, CACHE_EXPIRE_TIME_MINUTES))
                matchesTabViewModel.getPreviousMatches()

            if(haveMoreThanXMinutesPassedFrom(likeYouViewModel._lastTimeLikesAreUpdated, CACHE_EXPIRE_TIME_MINUTES))
                likeYouViewModel.initData()

            if(haveMoreThanXMinutesPassedFrom(notificationFeedViewModel.getLastRefreshTime(),
                    CACHE_EXPIRE_TIME_MINUTES))
                notificationFeedViewModel.getNotificationFeedData()

            if(haveMoreThanXMinutesPassedFrom(crossPathViewModel._lastTimeDiscoveredProfilesUpdated, CACHE_EXPIRE_TIME_MINUTES))
                crossPathViewModel.loadDiscoveredProfiles()

            if(haveMoreThanXMinutesPassedFrom(crossPathViewModel._lastTimeGroupedPinsUpdated, CACHE_EXPIRE_TIME_MINUTES)) {
                if(crossPathViewModel.lastDistance != null && crossPathViewModel.lastLatitude != null && crossPathViewModel.lastLongitude != null)
                    crossPathViewModel.getGroupedCrossPaths(
                            distance = crossPathViewModel.lastDistance!!,
                            latitude = crossPathViewModel.lastLatitude!!,
                            longitude = crossPathViewModel.lastLongitude!!
                    )
            }

            //purchasely presentation cache logic
            if(homeViewModel.userProfile.value?.premiumType == null)
                PurchaselyManager.reFetchPaywallsIfExpired()

            if(duaSharedPrefs.getShouldClearPaywallCache()) {
                PurchaselyManager.clearCache()
                duaSharedPrefs.setShouldClearPaywallCache(false)
            }

            NotificationHelper.clareAllNotifications()

            if(duaSharedPrefs.getRmodGenerated())
                homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                    fromTouch = false,
                    applyCardsImmediately = false,
                    fetchCards = false,
                    fetchFeaturedProfiles = true
                )
        }
    }

    fun navigateTo(@IdRes itemId: Int) {
        val bottomNavigationView = findViewById<BottomNavigationView>(R.id.bottom_nav)
        bottomNavigationView.selectedItemId = itemId
    }

    private fun checkForUpdate() {

        appUpdateManager = AppUpdateManagerFactory.create(this)
        // Returns an intent object that you use to check for an update.
        val appUpdateInfoTask = appUpdateManager?.appUpdateInfo

// Checks that the platform will allow the specified type of update.
        appUpdateInfoTask?.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                // For a flexible update, use AppUpdateType.FLEXIBLE
                && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)
            ) {
                // Request the update.

                appUpdateManager?.startUpdateFlowForResult(
                        // Pass the intent that is returned by 'getAppUpdateInfo()'.
                        appUpdateInfo,
                        // Or 'AppUpdateType.FLEXIBLE' for flexible updates.
                        IMMEDIATE,
                        // The current activity making the update request.
                        this,
                        // Include a request code to later monitor this update request.
                        UPDATE_REQUEST_CODE
                )

                Timber.tag(TAG).d("No Update available")

            } else {
                Timber.tag(TAG).d("No Update available")
            }
        }
    }

    private fun checkIfUpdateIsNotInstalled() {
        appUpdateManager
            ?.appUpdateInfo
            ?.addOnSuccessListener { appUpdateInfo ->
                if (appUpdateInfo.updateAvailability()
                    == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS
                ) {
                    // If an in-app update is already running, resume the update.
                    appUpdateManager?.startUpdateFlowForResult(
                        appUpdateInfo,
                        IMMEDIATE,
                        this,
                        UPDATE_REQUEST_CODE
                    );
                }
            }
    }

    /**
     * Called on first creation and when restoring state.
     */
    private fun setupBottomNavigationBar() {
        val navHostFragment = supportFragmentManager.findFragmentById(
            R.id.nav_host_container
        ) as NavHostFragment
        navController = navHostFragment.navController

        // Setup the bottom navigation view with navController
        val bottomNavigationView = findViewById<BottomNavigationView>(R.id.bottom_nav)

        bottomNavigationView.menu.findItem(R.id.nav_graph_profile).setOnMenuItemClickListener {
            openOrCloseWebSocket(false)
            return@setOnMenuItemClickListener false
        }

        bottomNavigationView.menu.findItem(R.id.nav_graph_home).setOnMenuItemClickListener {
            openOrCloseWebSocket(false)
            return@setOnMenuItemClickListener false
        }

        bottomNavigationView.menu.findItem(R.id.nav_graph_chat).setOnMenuItemClickListener {
            openOrCloseWebSocket(true)
            return@setOnMenuItemClickListener false
        }

        bottomNavigationView.setupWithNavController(navController)
        setDestinationChangeListener()
    }

    override fun onSupportNavigateUp(): Boolean {
        return navController.navigateUp()
    }

    fun showHideBottomNavigation(show: Boolean) {
        binding.bottomNav.visibility = if (show) View.VISIBLE else View.GONE
    }

    override fun getMainView(): View {
        return binding.root
    }

    private fun refreshUserProfile(){
        homeViewModel.fetchUserProfile()
    }

    private fun checkAndObserverForDotNotification() {
        matchesTabViewModel.matches.observe(this) {
            it?.let {
                if (it.isNotEmpty()) {
                    homeViewModel.checkForDotNotification(it.first())
                }
            }
        }

        matchesTabViewModel.conversations.observe(this) {
            it?.let {
                if (it.isNotEmpty()) {
                    homeViewModel.checkForDotNotification(it.first())
                }
            }
        }

        likeYouViewModel.likeItemsMutableLiveData.observe(this) {
            it?.let {
                if (it.isNotEmpty()) {
                    homeViewModel.checkForDotNotification(it.first())
                }
            }
        }

        homeViewModel.showChatDotNotification.observe(this) {
            val badge = binding.bottomNav.getOrCreateBadge(R.id.nav_graph_chat)
            badge.backgroundColor = ContextCompat.getColor(this, R.color.pink_500)
            badge.isVisible = it
        }

        homeViewModel.showLikesDotNotification.observe(this) {
            showLikedYouBadge(it)
        }

        likeYouViewModel.likesCountBadge.observe(this) {
            showLikedYouBadge(it)
        }

        homeViewModel.showEnvelopeDialog.observe(this) {
            if (it) {
                lifecycleScope.launchWhenResumed {
                    if (supportFragmentManager.findFragmentByTag("EnvelopeDialog") == null)
                        EnvelopeDialog.newInstance(
                            homeViewModel.getInteractionLimit().toString(),
                            ClevertapEventSourceValues.HOME.value
                        ).show(supportFragmentManager, "EnvelopeDialog")
                }
            }
        }

        homeViewModel.openCreateProfile.observe(this) {
            openCreateProfile(this)
        }

        homeViewModel.purchaselySetUp.observe(this) {
            Timber.tag(PremiumActivity.PURCHASELY_TAG).d("purchaselySetUp called")

            lifecycleScope.launch(Dispatchers.IO) {
                val shouldShowOnboardingPaywall = duaSharedPrefs.shouldShowOnboardingPaywall()

                withContext(Dispatchers.Main) {
                    if(shouldShowOnboardingPaywall &&
                        homeViewModel.userProfile.value?.profile?.isShadowBanned != true) {

                        openOnboardingPlacement()
                    }
                }
            }

        }


        homeViewModel.showInstagramReconnectDialog.observe(this) {
            showInstagramReconnectDialog()
        }

        homeViewModel.showTrophyDialog.observe(this) {
            if (it) {
                if (supportFragmentManager.findFragmentByTag("TrophyDialog") == null)
                    Handler(Looper.getMainLooper()).postDelayed({
                        val trophyDialog =
                            TrophyDialog.newInstance(homeViewModel.getInteractionLimit().toString())
                        trophyDialog.listener = this
                        trophyDialog.show(supportFragmentManager, "TrophyDialog")
                    }, 150)
            }
        }
    }

    override fun getLocationCallback(): LocationCallback {
        return object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                if (locationResult == null) {
                    Timber.tag(TAG).d("onLocationResult: locationResult is null")
                    return
                }
                var lastLocation: Location? = null
                for (location in locationResult.locations) {
                    Timber.tag(TAG).d("onLocationResult: getting new location")
                    lastLocation = location
                }
                if (lastLocation != null) {
                    if (lastLocation.latitude == 0.0 || lastLocation.longitude == 0.0) {
                        return
                    }
                    if (calculateIfAllowedToUpdate(lastLocation)) {
                        homeViewModel.updateLocation(lastLocation)
                    }
                } else {
                    if (!DeviceUtil.isEmulator()) {
                        requestLocation(ClevertapEventSourceValues.CARDS.value)
                    }
                }

            }
        }
    }

    override fun getFastLocationCallback(): FastLocation.Companion.FastLocationInterface {
        return object : FastLocation.Companion.FastLocationInterface {
            override fun onLocationChanged(location: Location) {
                if (location.latitude == 0.0 || location.longitude == 0.0) {
                    return
                }
                if (calculateIfAllowedToUpdate(location)) {
                    homeViewModel.updateLocation(location)
                }
            }

        }
    }

    fun setClickableBottomNavigationView(isClickable:Boolean) {
        val bottomNavigationView = findViewById<BottomNavigationView>(R.id.bottom_nav)
        bottomNavigationView.menu.forEach {
            it.isEnabled = isClickable
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            CALL_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    homeViewModel.setCallEnded()
                    setCallIndicatorVisibility(View.GONE)
                }
            }
            PremiumActivity.PREMIUM_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    when (data?.getSerializableExtra(PremiumActivity.PURCHASE_TYPE)) {
                        PremiumActivity.InAppPurchase.SUBSCRIPTION -> {
                            val sendBroadCastData = Intent(PremiumActivity.PREMIUM_INTENT)
                            sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                            LocalBroadcastManager.getInstance(this).sendBroadcast(sendBroadCastData)

                            homeViewModel.fetchUserProfile(isAfterPremium = true)
                            homeViewModel.afterPremiumVerified()

                            lifecycleScope.launch {
                                delay(2000)

                                likeYouViewModel.initData()
                                homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                                    fromTouch = false,
                                    applyCardsImmediately = false,
                                    fetchCards = false,
                                    fetchFeaturedProfiles = true
                                )
                            }

                            checkIfProfileWasVisited()
                        }
                        PremiumActivity.InAppPurchase.CONSUMABLE -> {
                            homeViewModel.fetchUserProfile(notifyCountersFetched = true)
                        }
                    }
                } else if (resultCode == Activity.RESULT_CANCELED &&
                    data?.getBooleanExtra(PremiumActivity.IS_FROM_IMPRESSIONS,false) == true ) {
                    homeViewModel.checkForWatchAd()
                }

            }

            ChangeLocationActivity.CHANGE_LOCATION_SUCCESS -> {
                if (resultCode == Activity.RESULT_OK) {
                    Timber.tag(TAG).d("reloading data")
                    homeViewModel.fetchedRecommendedUsersIds.clear()
                    homeViewModel.getCardsAndFeaturedProfilesFromAPi(fromTouch = false, applyCardsImmediately = true)
                }
            }
            UPDATE_REQUEST_CODE -> {
                when (resultCode) {
                    Activity.RESULT_OK -> {
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.UPDATE_AVAILABLE_POPUP,
                                mapOf(
                                        FirebaseAnalyticsParameterName.UPDATE_POPUP_COUNT.value to 1L,
                                        FirebaseAnalyticsParameterName.GETIT_BUTTON_COUNT.value to 1L
                                )
                        )
                        Timber.tag(TAG).d("" + "Result Ok")
                    }
                    Activity.RESULT_CANCELED -> {
                        Timber.tag(TAG).d("" + "Result Cancelled")
                        //  handle user's rejection  }
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.UPDATE_AVAILABLE_POPUP,
                                mapOf(
                                        FirebaseAnalyticsParameterName.UPDATE_POPUP_COUNT.value to 1L,
                                        FirebaseAnalyticsParameterName.DISMISS_BUTTON_COUNT.value to 1L
                                )
                        )
                    }
                    ActivityResult.RESULT_IN_APP_UPDATE_FAILED -> {
                        //if you want to request the update again just call
                        checkForUpdate()
                        Timber.tag(TAG).d("" + "Update Failure")
                        //  handle update failure
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.UPDATE_AVAILABLE_POPUP,
                                mapOf(
                                        FirebaseAnalyticsParameterName.UPDATE_POPUP_COUNT.value to 1L,
                                        FirebaseAnalyticsParameterName.RESULT_IN_APP_UPDATE_FAILED.value to 1L
                                )
                        )
                    }
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CODE_PERMISSIONS -> {
                // If request is cancelled, the result arrays are empty.
                if (grantResults.isNotEmpty()
                    && grantResults[0] == PackageManager.PERMISSION_GRANTED
                    && ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) || (grantResults[1] == PackageManager.PERMISSION_GRANTED))
                ) {
                    Timber.tag("debug").d("Permission Granted")
                    homeViewModel.setHasGrantedReadWritePermission()
                } else {
                    Timber.tag("debug").d("Permission Denied")
                }
                return
            }

            ConversationFragment.CALL_CODE_PERMISSIONS -> {
                // If request is cancelled, the result arrays are empty.
                if (grantResults.isNotEmpty()
                    && grantResults[0] == PackageManager.PERMISSION_GRANTED
                    && grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    Timber.tag("debug").d("Permission Granted")
                    homeViewModel.setOnRequestPermissionsResultForVideoCall(true)
                } else {
                    Timber.tag("debug").d("Permission Denied")
                    homeViewModel.setOnRequestPermissionsResultForVideoCall(false)
                }
                return
            }

            else -> {
                // Ignore all other requests.
            }
        }

    }

    fun calculateIfAllowedToUpdate(location: Location): Boolean {
        if (DuaAccount.latitude == null || DuaAccount.longitude == null) {
            return true
        }
        fun distance(): Float? {
            val results = FloatArray(1)
            Location.distanceBetween(
                location.latitude,
                location.longitude,
                DuaAccount.latitude ?: 0.0,
                DuaAccount.longitude ?: 0.0,
                results
            )
            return results[0]
        }

        val distance = distance()
        return if (distance != null && distance > 1000) {
            (distance / 1000).toDouble().roundToDecimals(1) > getDistanceThreshold()
        } else {
            false
        }
    }

    private fun getDistanceThreshold() =
        if (homeViewModel.isUserInFlyMode()) FLY_LOCATION_THRESHOLD_KM else NORMAL_LOCATION_THRESHOLD_KM

    fun checkShareCompat() {
        homeViewModel.getReferral()
        homeViewModel.referralIdGenerated.observe(this, {
            if (NetworkChecker.isNetworkConnected(this)) {
                generateInvitationLink(it)
            } else ToastUtil.toast(getString(R.string.no_internet_connection))
        })

        homeViewModel.showReferralFriendsShare.observe(this, { uri ->
            // check if the activity resolves
            if (null == getShareIntent(uri).resolveActivity(packageManager)) {
                ToastUtil.toast("Your device doesn't support this action")
            } else shareDua(uri)
        })

    }

    private fun shareDua(referralId: Uri) {
        // Use custom action only for your app to receive the broadcast
        val shareAction = "$packageName.share.SHARE_ACTION"
        val receiver = Intent(shareAction)
        val flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else  PendingIntent.FLAG_UPDATE_CURRENT
        val pi = PendingIntent.getBroadcast(
            this, 0, receiver, flag
        )

        val shareIntent = Intent.createChooser(getShareIntent(referralId), null, pi.intentSender)
        ContextCompat.registerReceiver(
            this,
            chosenComponentReceiver, 
            IntentFilter(shareAction),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
        startActivity(shareIntent)
    }

    private fun getShareIntent(referralId: Uri): Intent {
        val text = "${resources.getString(R.string.invite_text)}\n$referralId"
        return Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            // (Optional) Here we're setting the title of the content
//            putExtra(Intent.EXTRA_TITLE, "Introducing dua.com")
            type = "text/plain"
        }
    }

    private fun generateInvitationLink(referralId: String) {

        val invitationLink = "${BuildConfig.INVITATION_LINK}$referralId"
        Firebase.dynamicLinks.shortLinkAsync {
            link = Uri.parse(invitationLink)
            domainUriPrefix = BuildConfig.DOMAIN_URI_PREFIX
            androidParameters(packageName) {
                minimumVersion = BuildConfig.DYNAMIC_LINKS_ANDROID_MINIMUM_VERSION
            }
            iosParameters(BuildConfig.IOS_ID) {
                appStoreId = BuildConfig.DYNAMIC_LINKS_APP_STORE_ID
                minimumVersion = BuildConfig.DYNAMIC_LINKS_IOS_MINIMUM_VERSION
            }
            socialMetaTagParameters {
                title = getString(R.string.refer_your_friend_to_join)
                description = getString(R.string.install_dua_to_join_our_wonderful)
                imageUrl =
                    Uri.parse(BuildConfig.DYNAMIC_LINKS_IMAGE_URL)
            }
        }.addOnSuccessListener { shortDynamicLink ->
            shortDynamicLink.shortLink?.let {
                homeViewModel.showReferralFriendsShare(it)
            }

        }
    }

    private val chosenComponentReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            unregisterReceiver(this)

            val clickedComponent: ComponentName? = intent?.getParcelableExtra(
                EXTRA_CHOSEN_COMPONENT
            );

            clickedComponent?.let {
                homeViewModel.showEnvelopeDialog()
            }

        }

    }
    override fun onSaveInstanceState(outState: Bundle) {
        outState.putBoolean(KEY_NOTIFICATION_PERMISSION_DIALOG_SHOWN,hasNotificationPermissionDialogBeenShown)
        outState.putBoolean(KEY_LOCATION_PERMISSION_DIALOG_SHOWN,hasLocationPermissionDialogBeenShown)
        super.onSaveInstanceState(outState)
    }
    override fun onDestroy() {
        super.onDestroy()
        webSockedTimer?.cancel()
        isActive = false
        billingClient?.endConnection()
        billingClient = null

        LocalBroadcastManager.getInstance(this).unregisterReceiver(onAppForegroundedReceiver)
        LocalBroadcastManager.getInstance(this).unregisterReceiver(onUpdateDataReceiver)
        LocalBroadcastManager.getInstance(this).unregisterReceiver(premiumOfferAvailableBroadcastReceiver)

        try {
            this.unregisterReceiver(purchaselyScreensReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            this.unregisterReceiver(boostPurchaselyScreensReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            this.unregisterReceiver(forcePurchaselyScreensReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            this.unregisterReceiver(deepLinkScreensReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            this.unregisterReceiver(deepLinkSunnyHillReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            this.unregisterReceiver(deepLinkActionReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            this.unregisterReceiver(premiumOfferScreensReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            this.unregisterReceiver(callEndedBroadcastReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            this.unregisterReceiver(newConversationCreatedBroadcastReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            this.unregisterReceiver(callStatusReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        destroyAds()
        _binding = null
        instaChatLimitDialog = null
        interactionsOutOfImpressionsLimitDialog = null
        undoLimitDialog = null
    }

    override fun onStart() {
        super.onStart()
        isActive = true
    }

    override fun onStop() {
        super.onStop()
        isActive = false
    }

    override fun onResume() {
        super.onResume()
        isActive = true
        if(navController.currentDestination?.id != R.id.crossPathFragment) {
            binding.locationServicesLayout.root.visibility = View.GONE
        }
        checkIfUpdateIsNotInstalled()

        lifecycleScope.launchWhenResumed {
            openScreenActions(duaSharedPrefs.getActionLink())
        }

        @Suppress("DEPRECATION")
        ContextCompat.registerReceiver(
            this,
            connectivityReceiver,
            IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
        connectivityReceiver.connectivityReceiverListener = this

        lifecycleScope.launch(Dispatchers.IO) {
            delay(1000)
            withContext(Dispatchers.Main){
                setCallIndicatorVisibility(if (isCallServiceRunning(this@HomeActivity)) View.VISIBLE else View.GONE)
            }
        }

        if ((lastTimeAdChatShowed != null && (System.currentTimeMillis() >= lastTimeAdChatShowed!! + DELAY_MILLIS))) addNewChatMobAd(true)
        if (lastTimeAdInstaChatShowed != null && ((System.currentTimeMillis() >= lastTimeAdInstaChatShowed!! + DELAY_MILLIS))) addNewInstaChatMobAd(true)
        if ((lastTimeAdCardShowed != null && (System.currentTimeMillis() >= lastTimeAdCardShowed!! + DELAY_MILLIS)) || (homeViewModel.recommendedUsers.value?.any { it.isMobAd } == true && getAdCardView() == null)) addNewCardMobAd()

        storeCrossPathLocation()

        if (DuaAccount.shouldShowPremiumLiteApprovedDialog)
            showFreeStarterExperienceApprovedDialog(supportFragmentManager)

    }

    fun recheckIfBillingIsAvailable() {
        if(!DuaApplication.instance.getPlayBillingAvailable() &&
                recheckBillingAvailableAttemps < 3) {
            recheckBillingAvailableAttemps++
            consumePurchasedGoods()
        }
    }

    override fun onPause() {
        super.onPause()
        unregisterReceiver(connectivityReceiver)
        connectivityReceiver.connectivityReceiverListener = null
    }

    private fun showNewFeaturesOnDuaPopUp() {
        val DOESNT_EXIST = -1
        val currentVersionCode = BuildConfig.VERSION_CODE
        val ForUpdateVersionCode = BuildConfig.FOR_UPDATE_VERSION_CODE

        val savedVersionCode = duaSharedPrefs.getSavedVersionCode()
        if (ForUpdateVersionCode > savedVersionCode && savedVersionCode != DOESNT_EXIST) {
            val dialogFragment = NewFeaturesDialogFragment()
            val ft = supportFragmentManager.beginTransaction()
            dialogFragment.show(ft, "dialog")
        }

        //show add height bottom-sheet
        if (ForUpdateVersionCode > savedVersionCode && savedVersionCode != DOESNT_EXIST) {
            val user = homeViewModel.userProfile.value
            if (user != null && user.profile.height == null) {
                val bottomSheet = UserHeightBottomSheet()
                bottomSheet.show(supportFragmentManager, bottomSheet.tag)
            }
        }

        duaSharedPrefs.setSavedVersionCode(currentVersionCode)
    }

    // commenting out this method for now as Purchasely doesn't support offers
//    fun shouldShowSpecialOfferWhenSwiping():Boolean {
//        val isOfferActive = homeViewModel.anyOfferAvailable()
//        val hasOfferBeenShown = duaSharedPrefs.getHasOfferBeenShownFromCards()
//
//        return isOfferActive && !hasOfferBeenShown
//    }

    // commenting out this method for now as Purchasely doesn't support offers
//    fun showSpecialOffer(){
//        val intent = Intent(this, PremiumActivity::class.java)
//        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
//        intent.putExtra(PremiumActivity.EVENT_SOURCE_VALUE, ClevertapEventSourceValues.SPECIAL_OFFER)
//        startActivityForResult(intent, PremiumActivity.PREMIUM_REQUEST_CODE)
//        firebaseLogEvent(FirebaseAnalyticsEventsName.SPECIAL_OFFER_POPUP)
//    }

     fun checkShowSetupAccountCredentials() {
        lifecycleScope.launchWhenResumed {
            val isSetupAccountCredentialsShown = duaSharedPrefs.showSetupAccountCredentials()
            if (isSetupAccountCredentialsShown && DuaApplication.instance.isCredentialUser == false) {
                SetUpAccountDialog.newInstance().show(supportFragmentManager, "SetUpAccountDialog")
                duaSharedPrefs.setShowSetupAccountCredentials(false)
            }
        }
    }

    private fun deleteOfferDataIfNotActive() {
        lifecycleScope.launch(Dispatchers.Main) {
            delay(3000)
            val specialOffer = duaSharedPrefs.getPremiumSpecialOfferData()

            if (specialOffer != null && System.currentTimeMillis() > specialOffer.endTime) {
                Timber.tag("SPECIAL_OFFER").d("Setting special Offer NULL")
                duaSharedPrefs.setPremiumSpecialOfferData(null)
            }
        }
    }

    override fun onTrophyDialogDismissed(dismissed: Boolean) {
    }

    fun setChatButtonClickListener(listener: ChatButtonClickListener) {
        mChatButtonClickListener = listener
    }

    fun removeChatButtonClickListener() {
        mChatButtonClickListener = null
    }

    fun onNavButtonReselected() {
        val bottomNavigationView = findViewById<BottomNavigationView>(R.id.bottom_nav)
        bottomNavigationView.setOnNavigationItemReselectedListener {
            if (it.itemId == R.id.nav_graph_chat) {
                mChatButtonClickListener?.chatButtonClickListener()
            }
        }
    }

     fun badge2VerificationPopUp(
         isFromNotification: Boolean = false,
         eventSource: ClevertapVerificationSourceValues,
         verificationState: Badge2VerificationState? = null,
     ) {
         lifecycle.launchWhileAtLeast(Lifecycle.State.RESUMED) {
             if(navController.currentDestination?.id != R.id.verifyProfileWithBadge2PopUp && navController.currentDestination?.id != R.id.mandatoryVerificationFragment) {
             val shouldSkipImageVerificationApi = !isFromNotification

              when(verificationState){
                  Badge2VerificationState.REQUESTED -> {
                      navController.navigateSafer(
                          R.id.action_global_verifyProfileWithBadge2PopUp,
                          bundleOf(
                              SHOULD_SKIP_IMAGE_VERIFICATION_API to shouldSkipImageVerificationApi,
                              EVENT_SOURCE to eventSource
                          )
                      )
                  }
                  Badge2VerificationState.REQUIRED ->  {
                      navController.navigateSafer(R.id.action_global_mandatoryVerification)
                  }
                  null ->  {}
              }

             }
         }
     }

    private fun showBadge2PopUpWhenNotification() {
        if (duaSharedPrefs.getVerifyProfileBadge2DialogModel() != null && homeViewModel.userProfile.value?.badge2.toString() == Badge2Status.NULL.status) {
            if(duaSharedPrefs.getVerifyProfileBadge2DialogModel()?.isBadge2Needed == true){
                badge2VerificationPopUp(
                    isFromNotification = true,
                    eventSource = ClevertapVerificationSourceValues.RETENTION_JOURNEY
                )
            }else{
                badge2VerificationPopUp(
                    isFromNotification = true,
                    eventSource = ClevertapVerificationSourceValues.RETENTION_JOURNEY
                )
                duaSharedPrefs.setVerifyProfileBadge2DialogModel(null)
            }
        }
    }


    fun showInstagramReconnectDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setMessage(R.string.instagram_token_refresh_error)

        builder.setPositiveButton(R.string.connect) { dialogInterface, i ->
            val dialog = InstagramAuthenticationDialog(this) {
                homeViewModel.reconnectInstagram(it)
            }
            dialog.show()
        }

        builder.setNegativeButton(R.string.cancel) { dialogInterface, i ->
            homeViewModel.disconnectInstagram()
        }

        val dialog = builder.create()
        dialog.setOnShowListener { dialog1: DialogInterface? ->
            dialog.getButton(AlertDialog.BUTTON_POSITIVE)
                .setTextColor(ContextCompat.getColor(this, R.color.dua_red_color))
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE)
                .setTextColor(ContextCompat.getColor(this, R.color.dua_red_color))
        }
        dialog.show()
    }

    //region Play Billing
    private fun buyItem(item: String) {
        billingClient?.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                Timber.tag("BILLING").d("result: ${billingResult.responseCode}")
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.
                    querySkuDetails(item)
                } else if (billingResult.responseCode == BillingClient.BillingResponseCode.BILLING_UNAVAILABLE) {
                    ToastUtil.toast("Billing not available")
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        })
    }

    private fun querySkuDetails(productId: String) {
        val params = QueryProductDetailsParams.newBuilder()
        val productList = QueryProductDetailsParams.Product.newBuilder()
            .setProductType(BillingClient.ProductType.INAPP)
            .setProductId(productId)
            .build()
        params.setProductList(listOf(productList))

        billingClient?.queryProductDetailsAsync(params.build()) { billingResult, productDetails ->
            // Process the result.
            Timber.tag("BIILING").d("billingResult: $billingResult billingResult: $productDetails")

            val productDetailsParamsList = listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails.first())
                    .build()
            )

            val billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .build()

            // Launch the billing flow
            val billingResult = billingClient?.launchBillingFlow(this@HomeActivity, billingFlowParams)
            Timber.tag(TAG).d("launchBillingFlow: responseCode: $billingResult")
        }

    }

    private fun consumeProduct(purchaseToken: String) {
        val params = ConsumeParams.newBuilder()
            .setPurchaseToken(purchaseToken)
            .build()

        billingClient?.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.

                    billingClient?.consumeAsync(params) { billingResult, outToken ->
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            Timber.tag("BILLING").d("CONSUMED: $outToken")
                            // Handle the success of the consume operation.
                            billingViewModel.dismissDialog()
                        }
                    }
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        })
    }


    private fun consumePurchasedGoods() {
        // Connect to Google Play
        billingClient?.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.
                    checkPlayBillingAvailability()

                    // Query subscriptions
                    if (homeViewModel.userProfile.value != null &&
                        homeViewModel.userProfile.value?.premiumType == null) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            val params = QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS).build()
                            val results = billingClient?.queryPurchasesAsync(params)
                            results?.let {
                                if (results.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                                    val purchases = results.purchasesList
                                    if (!purchases.isNullOrEmpty()) {
                                        Timber.tag("BILLING").d("subscription Purchases: " + purchases)
                                        purchases.forEach { purchase ->
                                            purchase?.let {
                                                if (purchase.purchaseState == PURCHASED) {
                                                    val model = VerifyPaymentModel(
                                                        "play-store",
                                                        it.skus.first(),
                                                        it.orderId!!,
                                                        it.purchaseTime.toString(),
                                                        it.purchaseToken,
                                                        getPackageTypeForId(it.skus.first()),
                                                        null
                                                    )
                                                    if(duaSharedPrefs.getPremiumOrderIdRetried() != model.transactionId)
                                                        homeViewModel.verifySubscription(
                                                            model = model,
                                                            purchase = purchase,
                                                            isRestoring = true
                                                        )
                                                }
                                            }
                                        }
                                    }
                                }
                                Log.d(TAG, " Subscription queryPurchases: $results")
                            }
                        }
                    }

                    // Query in-app purchases
                    lifecycleScope.launch(Dispatchers.IO) {
                        val params = QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.INAPP).build()
                        val results = billingClient?.queryPurchasesAsync(params)
                        results?.let {
                            if (results.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                                val purchases = results.purchasesList
                                if (!purchases.isNullOrEmpty()) {
                                    Log.d("BILLING", "consumable purchases: $purchases")
                                    purchases.forEach { purchase ->
                                        purchase?.let {
                                            if (purchase.purchaseState == PURCHASED) {
                                                val model = VerifyPaymentModel(
                                                    "play-store",
                                                    it.skus.first(),
                                                    it.orderId!!,
                                                    it.purchaseTime.toString(),
                                                    it.purchaseToken,
                                                    getPackageTypeForId(it.skus.first()),
                                                    null
                                                )
                                                homeViewModel.verifyPayment(model, purchase)
                                            }
                                        }
                                    }
                                }
                            }
                            Log.d(TAG, "Consumable queryPurchases: $results")
                        }
                    }
                } else {
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.BILLING_UNAVAILABLE || billingResult.responseCode == BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE) {
                        Timber.tag("BILLING").d("BILLING_UNAVAILABLE")
                        DuaApplication.instance.setPlayBillingAvailability(false)
                    }
                }
            }

            override fun onBillingServiceDisconnected() {
                // Handle the disconnection
                Timber.tag("BILLING").d("onBillingServiceDisconnected")
            }
        })
    }

    private fun checkPlayBillingAvailability() {
        val params = SkuDetailsParams.newBuilder()
        params.setSkusList(listOf(
                GooglePlaySubscriptions.PACKAGE_1M.productId,
                GooglePlaySubscriptions.PACKAGE_2M.productId,
                GooglePlaySubscriptions.PACKAGE_3M.productId,
                GooglePlaySubscriptions.PACKAGE_F_12M_G2.productId,
                GooglePlaySubscriptions.PACKAGE_F_6M_G2.productId,
                GooglePlaySubscriptions.PACKAGE_F_3M_G2.productId,
                GooglePlaySubscriptions.PACKAGE_F_1M_G2.productId,
                GooglePlaySubscriptions.PACKAGE_F_12M_G1.productId,
                GooglePlaySubscriptions.PACKAGE_F_6M_G1.productId,
                GooglePlaySubscriptions.PACKAGE_F_3M_G1.productId,
                GooglePlaySubscriptions.PACKAGE_F_1M_G1.productId,
        ))
            .setType(BillingClient.SkuType.SUBS)
        billingClient?.querySkuDetailsAsync(params.build()) { billingResult, skuDetailsList ->
            // Process the result.
            Timber.tag("BIILING").d("billingResult: ${billingResult} billingResult: ${skuDetailsList.toString()}")

            if (skuDetailsList.isNullOrEmpty() && NetworkChecker.isNetworkConnected(this@HomeActivity)) {
                Timber.tag("BILLING").d("isNullOrEmpty BILLING_UNAVAILABLE")
                DuaApplication.instance.setPlayBillingAvailability(false)
                homeViewModel.updateIsBillingEnabledOnUser(false)
            } else {
                if(!DuaApplication.instance.getBillingAvailable()) {
                    val sendBroadCastData = Intent(PremiumActivity.BILLING_AVAILABLE_INTENT)
                    sendBroadCastData.putExtra(BILLING_AVAILABLE, true)
                    LocalBroadcastManager.getInstance(this).sendBroadcast(sendBroadCastData)
                }

                DuaApplication.instance.setPlayBillingAvailability(true)
                homeViewModel.updateIsBillingEnabledOnUser(true)
            }
        }
    }
    //endregion

    private fun handleDeepLinkActions(action: String?=null) {
        when(action) {
         DeepLinkActionNames.RATE_APP.value -> {
             reviewManager.requestAndLaunchReviewFlow(this) { reviewFlowTask ->
                 if (reviewFlowTask.isSuccessful) {
                     // Review flow completed successfully
                     if(BuildConfig.DEBUG) ToastUtil.toast("AppReview success")
                 } else {
                     // Handle review flow completion failure
                     if(BuildConfig.DEBUG) ToastUtil.toast("AppReview failure")

                 }
             }
         }
         }
        }

    private fun openScreenActions(
        screen: String? = null,
        jsonData: String? = null,
        eventSource: String? = null
    ) {

        val badge2statusValue = homeViewModel.userProfile.value?.badge2.toString()

        when (screen) {
            DeepLinkScreenNames.VERIFY_IMAGE.value -> {
                if (badge2statusValue == Badge2Status.NULL.status || badge2statusValue == Badge2Status.NOT_APPROVED.status) {
                    navController.navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
                        bundleOf(EVENT_SOURCE to eventSource)
                    )
                }
            }
            DeepLinkScreenNames.EDIT_PROFILE.value -> {
                val intent = Intent(this, EditProfileActivity::class.java)
                startActivity(intent)
            }
            DeepLinkScreenNames.MATCHES.value -> {
                navigateTo(R.id.nav_graph_chat)
                homeViewModel.deepLinkToMatches()
            }
            DeepLinkScreenNames.LIKED_YOU.value -> {
                navigateTo(R.id.nav_graph_likes)
            }
            DeepLinkScreenNames.INSTACHATS.value -> {
                navigateTo(R.id.nav_graph_chat)
                homeViewModel.deepLinkToInstachat()
            }
            DeepLinkScreenNames.CHANGE_LOCATION.value -> {
                val intent = Intent(this, ChangeLocationActivity::class.java)
                intent.putExtra("UserModel", homeViewModel.userProfile.value)
                startActivity(intent)
            }
            DeepLinkScreenNames.PREMIUM_POPUP.value -> {
                if (DuaApplication.instance.getPremiumAvailable() && homeViewModel.userProfile.value?.premiumType == null) {
                    openPremiumPaywall(
                        eventSourceClevertap = ClevertapEventSourceValues.DEEPLINK,
                        placementId = PurchaselyPlacement.PROFILE_GO_PREMIUM_BUTTON.id,
                        userModel = homeViewModel.userProfile.value
                    )
                }
            }
            DeepLinkScreenNames.LOCATION_PICKER.value -> {
                val intent = Intent(this, ChangeLocationActivity::class.java)
                intent.putExtra("UserModel", homeViewModel.userProfile.value)
                startActivity(intent)
            }
            DeepLinkScreenNames.PROFILE.value -> {
                navigateTo(R.id.nav_graph_profile)
            }
            DeepLinkScreenNames.INVITE_A_FRIEND.value -> {
                val outOfImpressionsDialog = OutOfImpressionsDialog.newInstance()
                outOfImpressionsDialog.show(supportFragmentManager, "outOfImpressionsDialog")
            }
            DeepLinkScreenNames.INVITE_FRIEND.value-> {
                val outOfImpressionsDialog = OutOfImpressionsDialog.newInstance()
                outOfImpressionsDialog.show(supportFragmentManager, "outOfImpressionsDialog")
            }
            DeepLinkScreenNames.BOOST.value-> {
                displayBoostPurchaselyOffer()
            }
            DeepLinkScreenNames.LANGUAGES.value-> {
                openSettingsActivity("Languages")
            }
            DeepLinkScreenNames.NOTIFICATIONS.value-> {
                openSettingsActivity("Notifications")
            }
            DeepLinkScreenNames.APPEARANCE.value-> {
                openSettingsActivity("Appearance")
            }
            DeepLinkScreenNames.NAME_CHANGE.value-> {
                openSettingsActivity("Name_Change")
            }
            DeepLinkScreenNames.GENDER_CHANGE.value -> {
                openSettingsActivity("Gender_Change")
            }
            DeepLinkScreenNames.BIRTHDAY_CHANGE.value-> {
                openSettingsActivity("Birthday_Change")
            }
            DeepLinkScreenNames.PHONE_EMAIL_VERIFICATION.value-> {
                when(authMethod){
                    AuthMethod.EMAIL -> openSettingsActivity("Email_Verification")
                    AuthMethod.PHONE -> openSettingsActivity("Phone_Verification")
                    else -> {/*not used*/}
                }
            }
            DeepLinkScreenNames.INSTAGRAM.value-> {
                val intent = Intent(this, EditProfileActivity::class.java)
                intent.putExtra(
                    CONNECT_INSTAGRAM_INTENT, true)
                startActivity(intent)
            }
            DeepLinkScreenNames.SPOTTED.value-> {
                navigateTo(R.id.nav_graph_cross_path)
            }
            DeepLinkScreenNames.SUNNY_HILL_PIN.value -> {
                if(!SunnyHillUtils.shouldShowSunnyHillViews()) return
                openCrossPathFullSCreen()
            }
            DeepLinkScreenNames.FILTERS.value -> {
                homeViewModel.showFilter()
            }
            DeepLinkScreenNames.MY_PHOTOS.value -> {
                openManagePicturesActivity()
            }
            DeepLinkScreenNames.INCOGNITO_MODE.value -> {
              openSettingsActivity("Account_Settings")
            }
            ClevertapNotificationAction.NEW_PROFILE_VISIT_RECEIVED.type -> {
                val model = Gson().fromJson(jsonData, ProfileVisitedModel::class.java)
                model.cognitoUserId.let { cognitoId ->
                    showProfileVisited(cognitoId)
                    duaSharedPrefs.setProfileVisitedCognitoId(null)
                }
            }
        }
        duaSharedPrefs.setActionLink(null)
    }
    private fun openManagePicturesActivity() {
        val intent = Intent(this, ManagePicturesActivity::class.java)
        intent.putExtra(UPLOAD_IMAGE_SOURCE, UploadImageSourceValues.PROFILE.value)
        startActivityForResult(intent, NEW_IMAGES_REQUEST)
    }
    private fun openCrossPathFullSCreen() {
        val args = bundleOf(CROSS_PATH_DATA to null, SUNNY_HILL_START to true)
        navController.navigateSafer(R.id.action_global_crossPathFullScreen, args)
    }

    private fun openSettingsActivity(startDestination: String) {
        val intent = Intent(this, SettingsActivity::class.java)
        intent.putExtra(
            SETTINGS_ACTIVITY_START_DESTINATION_INTENT, startDestination)
        startActivity(intent)
    }

    private fun setDestinationChangeListener() {

        navController.addOnDestinationChangedListener { _, destination, _ ->
            //set resize mode
            when (destination.id) {
                R.id.homeFragment, R.id.userFeedFragment -> {
                    this.window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
                }
                else -> {
                    this.window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                }
            }

            //show bottom navigation
            when (destination.id) {
                R.id.conversationFragment3,
                R.id.conversationFragment4,
                R.id.locationPermissionStepFragment,
                R.id.filterOptionsFragment,
                R.id.notificationFeedFragment,
                R.id.crossPathFullScreenFragment,
                R.id.allCrossPathProfilesFragment,
                R.id.verifyProfileWithBadge2PopUp,
                R.id.verifyAccountFaceLivenessFragment,
                R.id.faceMissMatchLivenessFragment,
                R.id.imageDeniedFragmentLiveness,
                R.id.mandatoryVerificationFragment-> {
                    showHideBottomNavigation(false)
                }
                else -> showHideBottomNavigation(true)
            }

            //close the Search in MatchesTab
            when (destination.id) {
                R.id.newProfileFragment, R.id.homeFragment, R.id.conversationFragment3, R.id.conversationFragment4 -> {
                    matchesTabViewModel.setIsSearching(false)
                }
            }

            when (destination.id) {
                R.id.homeFragment -> {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.CARDS)
                }
                R.id.newProfileFragment -> {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.MY_PROFILE)
                }
                R.id.userFeedFragment -> {
                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    sendClevertapEvent(
                        ClevertapEventEnum.CHAT_LIST_SCREENVIEW, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
                }
                R.id.crossPathFragment -> {
                    lifecycle.launchWhileAtLeast(Lifecycle.State.RESUMED){
                        if(!hasLocationPermissionDialogBeenShown) {
                            manageLocationPermissions()
                        }
                    }
                }
            }
            homeViewModel.onMatchFragmentShowed(destination.id == R.id.userFeedFragment)
        }
    }

    private fun getScreenDimensions(){
        binding.root.viewTreeObserver?.addOnGlobalLayoutListener(
            object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    Timber.tag("SCREENLOG").d("getScreenDimensions: SplashActivity")
                    binding.root.viewTreeObserver!!.removeOnGlobalLayoutListener(this)
                    //set offscreen coordinates
                    val displayMetrics = DisplayMetrics()
                    windowManager.defaultDisplay.getMetrics(displayMetrics)
                    DuaApplication.screenHeight = displayMetrics.heightPixels
                    DuaApplication.screenWidth = displayMetrics.widthPixels
                    DuaApplication.screenHeightDp = convertPixelsToDp(displayMetrics.heightPixels.toFloat(), this@HomeActivity)

                    Timber.tag("SCREENINFO").d("displayMetrics.density: ${displayMetrics.density}")
                    Timber.tag("SCREENINFO").d("displayMetrics.densityDpi: ${displayMetrics.densityDpi}")
                    Timber.tag("SCREENINFO").d("displayMetrics.screenHeight: ${displayMetrics.heightPixels}")
                    Timber.tag("SCREENINFO").d("displayMetrics.screenWidth: ${displayMetrics.widthPixels}")
                }
            })
    }

    private fun setDeviceId(model: UserModel, visitorId: String) {

        if (visitorId != homeViewModel.userProfile.value?.lastDeviceId) {
                homeViewModel.updateLastDevice(visitorId).observe(this, Observer {
                    when (it) {
                        is Result.Success -> {
                            val newModel = model.copy(lastDeviceId = visitorId)
                            homeViewModel.updateLastDeviceInDB(newModel)
                        }
                        is Result.Loading -> {
                        }
                        is Result.Error -> {
                        }
                    }
                })
        }
    }

    //ads region

    fun addNewChatMobAd(isRefresh: Boolean) {
        if (!homeViewModel.isUserPremium() && DuaApplication.instance.getIsApplovinInitialized()) {
            lastTimeAdChatShowed = null
            nativeAdChatHandler.removeCallbacks(nativeAdChatRunnable)

            if (isRefresh) {
                matchesTabViewModel.initAdsInChatList()
            }

            if (nativeAdChatView == null || nativeAdChatLoader == null) {
                val binder: MaxNativeAdViewBinder =
                    MaxNativeAdViewBinder.Builder(layoutInflater.inflate(R.layout.chat_applovin_item, null))
                        .setTitleTextViewId(R.id.ad_headline)
                        .setBodyTextViewId(R.id.ad_body)
                        .setIconImageViewId(R.id.ad_app_icon)
                        .setOptionsContentViewGroupId(R.id.info_button)
                        .setCallToActionButtonId(R.id.ad_call_to_action)
                        .build()
                nativeAdChatView = MaxNativeAdView(binder, this)

                nativeAdChatLoader = MaxNativeAdLoader(getString(R.string.applovin_unit_id_chat), this)

                nativeAdChatLoader?.setRevenueListener {
                    lastTimeAdChatShowed = System.currentTimeMillis()
                }

                nativeAdChatLoader?.setNativeAdListener(object : MaxNativeAdListener() {
                    override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {

//                        // Cleanup any pre-existing native ad to prevent memory leaks.
//                        if (nativeAdChat != null) {
//                            nativeAdChatLoader?.destroy(nativeAdChat)
//                        }
//
//                        // Save ad for cleanup.
//                        nativeAdChat = ad
                        matchesTabViewModel.addAdToChatList(nativeAdView){ shouldLoadMore ->
                            if(shouldLoadMore){
                                addNewChatMobAd(false)
                            } else {
                                loadNextNativeAdChat()
                            }

                        }
                    }

                    override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                        val errorMessage = "message: ${error.message}"
                        Timber.tag("NativeAd").e("Failed to load native ad with error $errorMessage")
                        matchesTabViewModel.removeAllShimmerItemsInChat()
                        loadNextNativeAdChat()
                    }

                    override fun onNativeAdClicked(ad: MaxAd) {
                    }
                })
            }
            nativeAdChatLoader?.loadAd(nativeAdChatView)
        }
    }

    private fun loadNextNativeAdChat() {
        nativeAdChatHandler.removeCallbacks(nativeAdChatRunnable)
        nativeAdChatHandler.postDelayed(nativeAdChatRunnable, DELAY_MILLIS)
    }

    fun addNewInstaChatMobAd(isRefresh: Boolean) {
        if (!homeViewModel.isUserPremium() && DuaApplication.instance.getIsApplovinInitialized()) {
            lastTimeAdInstaChatShowed = null
            nativeAdInstaChatHandler.removeCallbacks(nativeAdInstaChatRunnable)

            if (isRefresh) {
                instaChatViewModel.initAdsInInstaChatList()
            }

            if (nativeAdInstaChatView == null || nativeAdInstaChatLoader == null) {
                val binder: MaxNativeAdViewBinder =
                    MaxNativeAdViewBinder.Builder(layoutInflater.inflate(R.layout.chat_applovin_item, null))
                        .setTitleTextViewId(R.id.ad_headline)
                        .setBodyTextViewId(R.id.ad_body)
                        .setIconImageViewId(R.id.ad_app_icon)
                        .setOptionsContentViewGroupId(R.id.info_button)
                        .setCallToActionButtonId(R.id.ad_call_to_action)
                        .build()
                nativeAdInstaChatView = MaxNativeAdView(binder, this)

                nativeAdInstaChatLoader = MaxNativeAdLoader(getString(R.string.applovin_unit_id_chat), this)

                nativeAdInstaChatLoader?.setRevenueListener {
                    lastTimeAdInstaChatShowed = System.currentTimeMillis()
                }

                nativeAdInstaChatLoader?.setNativeAdListener(object : MaxNativeAdListener() {
                    override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {

//                        // Cleanup any pre-existing native ad to prevent memory leaks.
//                        if (nativeAdInstaChat != null) {
//                            nativeAdInstaChatLoader?.destroy(nativeAdInstaChat)
//                        }
//
//                        // Save ad for cleanup.
//                        nativeAdInstaChat = ad

                        instaChatViewModel.addAdToInstaChatList(nativeAdView){ shouldLoadMore ->
                            if(shouldLoadMore){
                                addNewInstaChatMobAd(false)
                            } else {
                                loadNextNativeAdInstaChat()
                            }

                        }
                    }

                    override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                        val errorMessage = "message: ${error.message}"
                        Timber.tag("NativeAd").e("Failed to load native ad with error $errorMessage")
                        instaChatViewModel.removeAllShimmerItemsInChat()
                        loadNextNativeAdInstaChat()
                    }

                    override fun onNativeAdClicked(ad: MaxAd) {
                    }
                })
            }
            nativeAdInstaChatLoader?.loadAd(nativeAdInstaChatView)
        }
    }

    private fun loadNextNativeAdInstaChat() {
        nativeAdInstaChatHandler.removeCallbacks(nativeAdInstaChatRunnable)
        nativeAdInstaChatHandler.postDelayed(nativeAdInstaChatRunnable, DELAY_MILLIS)
    }

    fun addNewCardMobAd() {
        if (!homeViewModel.isUserPremium() && DuaApplication.instance.getIsApplovinInitialized()) {
            lastTimeAdCardShowed = null

            if (nativeAdCardView == null || nativeAdCardLoader == null) {
                val binder: MaxNativeAdViewBinder =
                    MaxNativeAdViewBinder.Builder(layoutInflater.inflate(R.layout.applovin_card_ad_item, null))
                        .setTitleTextViewId(R.id.ad_headline)
                        .setBodyTextViewId(R.id.ad_body)
                        .setMediaContentViewGroupId(R.id.media_container)
                        .setIconImageViewId(R.id.ad_app_icon)
                        .setOptionsContentViewGroupId(R.id.info_button)
                        .setCallToActionButtonId(R.id.ad_call_to_action)
                        .build()
                nativeAdCardView = MaxNativeAdView(binder, this)

                nativeAdCardLoader = MaxNativeAdLoader(getString(R.string.applovin_unit_id_card), this)

                nativeAdCardLoader?.setRevenueListener {
                    lastTimeAdCardShowed = System.currentTimeMillis()
                }

                nativeAdCardLoader?.setNativeAdListener(object : MaxNativeAdListener() {
                    override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {

                        // Cleanup any pre-existing native ad to prevent memory leaks.
                        if (nativeAdCard != null) {
                            nativeAdCardLoader?.destroy(nativeAdCard)
                        }

                        // Save ad for cleanup.
                        nativeAdCard = ad
                        lastTimeAdCardShowed = System.currentTimeMillis()
                        homeViewModel.setCheckIfAdViewIsShowedOnCard()

                    }

                    override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                        val errorMessage = "message: ${error.message}"
                        Timber.tag("NativeAd").e("Failed to load native ad with error $errorMessage")

                    }

                    override fun onNativeAdClicked(ad: MaxAd) {
                        addNewCardMobAd()
                    }
                })
            }
            nativeAdCardLoader?.loadAd(nativeAdCardView)
        }
    }

    fun createRewardedAd()
    {
        if (rewardedAd != null && RemoteConfigUtils.isAdsEnabled().not()) return

        rewardedAd = MaxRewardedAd.getInstance( getString(R.string.applovin_unit_id_rewarded), this )
        rewardedAd?.setListener( this )

        rewardedAd?.loadAd()
    }

    // MAX Ad Listener
    override fun onAdLoaded(maxAd: MaxAd)
    {
        // Rewarded ad is ready to be shown. rewardedAd.isReady() will now return 'true'

        // Reset retry attempt
        retryAttempt = 0.0
       homeViewModel.refreshLikedYouHeader()
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError)
    {
        // Rewarded ad failed to load
        // AppLovin recommends that you retry with exponentially higher delays up to a maximum delay (in this case 64 seconds)

        retryAttempt++
        val delayMillis = TimeUnit.SECONDS.toMillis( 2.0.pow(min(6.0, retryAttempt)).toLong() )
        lifecycleScope.launch {
            delay( delayMillis )
            rewardedAd?.loadAd()
        }
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError)
    {
        // Rewarded ad failed to display. AppLovin recommends that you load the next ad.
        rewardedAd?.loadAd()
    }

    override fun onAdDisplayed(maxAd: MaxAd) {
        sendRewardAdEvent(FirebaseAnalyticsEventsName.AD_VIEW, ClevertapEventEnum.AD_VIEW, maxAd.networkName)
    }

    override fun onAdClicked(maxAd: MaxAd) {
        sendRewardAdEvent(FirebaseAnalyticsEventsName.AD_CLICK, ClevertapEventEnum.AD_CLICK, maxAd.networkName)
    }

    override fun onAdHidden(maxAd: MaxAd)
    {
        // rewarded ad is hidden. Pre-load the next ad
        rewardedAd?.loadAd()
    }

    override fun onRewardedVideoStarted(maxAd: MaxAd) {} // deprecated

    override fun onRewardedVideoCompleted(maxAd: MaxAd) {} // deprecated

    override fun onUserRewarded(maxAd: MaxAd, maxReward: MaxReward)
    {
        // Rewarded ad was displayed and user should receive the reward
        when(homeViewModel.rewardAdTypeShowed) {
            RewardAdType.UN_BLUR_LIKED_YOU -> homeViewModel.increaseLikedYouRewardedAdsWatchCount()
            else ->  homeViewModel.increaseRewardVideoCounter()
        }
    }

    //WatchAdBottomSheetListener
    override fun onContinueInteractionAdClick() {
        homeViewModel.updateRewardAdType(RewardAdType.LIKE)
        rewardedAd?.showAd(null, createLikeCustomDataAd())
    }

    fun onContinueLikedYouAdClick() {
        homeViewModel.updateRewardAdType(RewardAdType.UN_BLUR_LIKED_YOU)
        rewardedAd?.showAd(null, createUnblurCustomDataAd())
    }

    fun getRewardAd(): MaxRewardedAd?{
        return rewardedAd
    }

    fun getAdChatView(): MaxNativeAdView?{
        return nativeAdChatView
    }

    fun getAdInstaChatView(): MaxNativeAdView?{
        return nativeAdInstaChatView
    }

    fun getAdCardView(): MaxNativeAdView?{
        return nativeAdCardView
    }

    fun getNativeAdCard(): MaxAd?{
        return nativeAdCard
    }

    fun hasNativeAdLoaded(): Boolean{
        return nativeAdCard != null
    }

    fun removeAdChat(){
        if (nativeAdChat != null) {
            nativeAdChatLoader?.destroy(nativeAdChat)
        }
        nativeAdChatLoader?.destroy()
        nativeAdChatLoader = null
    }

    fun removeAdInstaChat(){
        if (nativeAdInstaChat != null) {
            nativeAdInstaChatLoader?.destroy(nativeAdInstaChat)
        }
        nativeAdInstaChatLoader?.destroy()
        nativeAdInstaChatLoader = null
    }

    private fun destroyAds(){

        //Destroy ChatAd Object
        // Must destroy native ad or else there will be memory leaks.
        if (nativeAdChat != null) {
            // Call destroy on the native ad from any native ad loader.
            nativeAdChatLoader?.destroy(nativeAdChat)
            nativeAdCard = null
        }

        // Destroy the actual loader itself
        nativeAdChatLoader?.destroy()
        nativeAdChatLoader = null

        nativeAdChatHandler.removeCallbacks(nativeAdChatRunnable)

        //Destroy InstaChatAd Object
        if (nativeAdInstaChat != null) {

            nativeAdInstaChatLoader?.destroy(nativeAdInstaChat)
            nativeAdInstaChat = null
        }

        nativeAdInstaChatLoader?.destroy()
        nativeAdInstaChatLoader = null

        nativeAdInstaChatHandler.removeCallbacks(nativeAdInstaChatRunnable)

        //Destroy CardAd Object
        if (nativeAdCard != null) {

            nativeAdCardLoader?.destroy(nativeAdCard)
            nativeAdCard = null
        }

        nativeAdCardLoader?.destroy()
        nativeAdCardLoader = null

        //Destroy RewardedAd Object
        rewardedAd?.destroy()

    }

    private fun sendRewardAdEvent(firebaseAnalyticsEventsName: FirebaseAnalyticsEventsName, clevertapEventsName: ClevertapEventEnum, networkName: String) {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)


        firebaseLogEvent(firebaseAnalyticsEventsName, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.NETWORK_NAME.value to networkName,
            FirebaseAnalyticsParameterName.AD_TYPE.value to ClevertapAdTypeValues.REWARD_VIDEO.value,
            FirebaseAnalyticsParameterName.COMMUNITY.value to homeViewModel.userProfile.value?.communityInfo?.communityName!!
        ))

        sendClevertapEvent(
            clevertapEventsName, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.NETWORK_NAME.propertyName to networkName,
                ClevertapEventPropertyEnum.AD_TYPE.propertyName to ClevertapAdTypeValues.REWARD_VIDEO.value,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.communityName!!
            ))
    }

    //end region
    private fun handleDisableUserView(userModel: UserModel) {
        if (userModel.isDisabled) {
            val isOtherCommunity = userModel.disabledReasons?.any { it.category == DisableUserType.COMMUNITY_NOT_SUPPORTED.type } ?: false
            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

            if (binding.disableView.disableUserViewContainer.visibility == View.GONE) {
                firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SCREENVIEW)

                sendClevertapEvent(ClevertapEventEnum.PROFILE_DISABLED_POPUP, mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                    ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.id
                ))
            }

            binding.disableView.disableUserViewContainer.visibility = View.VISIBLE
            val hasRequestedReview = userModel.hasRequestedReview
            val underReviewVisibility = if (hasRequestedReview) View.VISIBLE else View.GONE
            val reviewVisibility = if (!hasRequestedReview) View.VISIBLE else View.GONE

            binding.disableView.disableUnderReview.disableUnderReviewContainer.visibility =
                underReviewVisibility
            binding.disableView.disableReview.disableReviewContainer.visibility = reviewVisibility

            binding.disableView.disableReview.reviewBtn.setOnClickListener {
                if(isOtherCommunity) {
                    startActivity(Intent(this, SettingsActivity::class.java))
                } else {
                    startActivity(Intent(this, DisabledActivity::class.java))
                    firebaseLogEvent(FirebaseAnalyticsEventsName.DP_REVIEW_PROFILE)

                    sendClevertapEvent(ClevertapEventEnum.REVIEW_YOUR_PROFILE, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.id
                    ))
                }
            }

            binding.disableView.disableReview.logoutReviewBtn.setOnClickListener {
                createLogOutDialog()
            }

            if(isOtherCommunity) {
                binding.disableView.disableReview.txtTitleReview.setText(R.string.community_disabled_desc)
                binding.disableView.disableReview.txtBodyReview.setText(R.string.community_disabled_heading)
                binding.disableView.disableReview.reviewBtn.setText(R.string.community_disabled_settings_lable)
            } else {
                binding.disableView.disableReview.txtTitleReview.setText(R.string.profile_disabled_title)
                binding.disableView.disableReview.txtBodyReview.setText(R.string.profile_disabled_body)
                binding.disableView.disableReview.reviewBtn.setText(R.string.review_your_profile)
            }
        } else {
            binding.disableView.disableUserViewContainer.visibility = View.GONE
        }
    }

    private fun createLogOutDialog() {
        val builder = AlertDialog.Builder(this,R.style.AlertDialogButtonTheme)
        builder.apply {

            setTitle(getString(R.string.sign_out))
                .setMessage(getString(R.string.sign_out_string))
                .setNegativeButton(getString(R.string.cancel)) { dialog, _ ->
                    dialog.cancel()
                }
                .setPositiveButton(getString(R.string.ok_dialog)) { _, _ ->
                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                    val logOutType = ClevertapLogOutTypeValues.PROFILE_DISABLED.values

                    sendClevertapEvent(ClevertapEventEnum.LOG_OUT_INITIATED,
                        mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutType,
                        )
                    )

                    logOutFromDevice()
                }

        }
        return builder.create().run { show() }
    }

    private fun logOutFromDevice() {
        duaAccount.deleteUserDevice() { result ->
            if (result) {
                duaAccount.deleteAllData()
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.LOG_OUT_REGULAR, mapOf(
                        FirebaseAnalyticsParameterName.LOG_OUT_REGULAR_COUNT.value to 1L))

                val eventPremiumType =getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)

                val logOutTypeValues = ClevertapLogOutTypeValues.PROFILE_DISABLED.values

                sendClevertapEvent(
                    ClevertapEventEnum.LOG_OUT, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutTypeValues))
            } else {
                ToastUtil.toast(getString(R.string.smthg_went_wrong))
                logError(ErrorStatus.LOG_OUT_SETTINGS)
            }
        }
    }

    override fun unHideProfile() {
        val user = homeViewModel.userProfile.value
        user?.let {
            homeViewModel.unHideMyProfile().observe(this) { response ->
                when (response) {
                    is Result.Success -> {
                        firebaseLogEvent(FirebaseAnalyticsEventsName.UNHIDE_TO_BOOST)
                        val newUser = user.copy()
                        newUser.profile.isInvisible = false
                        homeViewModel.updateUserInvisible(newUser)
                        homeViewModel.reloadRecommendedUsers()

                        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                        val unhideSourceValue = ClevertapUnhideSourceValues.UNHIDE_TO_BOOST.values

                        sendClevertapEvent(
                            ClevertapEventEnum.UNHIDE_PROFILE, mapOf(
                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                ClevertapEventPropertyEnum.UNHIDE_SOURCE.propertyName to unhideSourceValue))
                    }
                    is Result.Error -> { }
                    is Result.Loading -> { }
                }
            }
        }
    }

    override fun onNotificationClickedPayloadReceived(payload: HashMap<String, Any>?) {
        Timber.tag("Clevertap_Notification").d("payload: $payload")
        val screen = payload?.get("screen") as String?
        screen?.let { screenKey ->
            lifecycleScope.launchWhenResumed {
                openScreenActions(screenKey)
            }
        }


        val action = payload?.get("action") as String?
        val jsonData = payload?.get("jsonData") as String?
        when(action) {
            ClevertapNotificationAction.PURCHASELY_PAYWALL.type -> {
                PurchaselyManager.clearCache()

                if(homeViewModel.userProfile.value?.premiumType == null) {
                    val model = Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                    model.placementId.let { placementId ->
                        lifecycleScope.launchWhenResumed {
                            openPremiumPaywallAsync(
                                eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                                placementId = model.placementId,
                                userModel = homeViewModel.userProfile.value
                            )
                        }
                    }
                }
                duaSharedPrefs.setPurchaselyPaywallId(null)
            }
            ClevertapNotificationAction.FORCE_PURCHASELY_PAYWALL.type -> {
                PurchaselyManager.clearCache()

                val model = Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                model.placementId.let { placementId ->
                    lifecycleScope.launchWhenResumed {
                        openPremiumPaywallAsync(
                            eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                            placementId = model.placementId,
                            userModel = homeViewModel.userProfile.value
                        )

                        duaSharedPrefs.setForcePurchaselyPaywallId(null)
                    }
                }
            }
            ClevertapNotificationAction.PURCHASELY_BOOST_OFFER.type -> {
                PurchaselyManager.clearCache()

                val model = Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                model.placementId.let { paywallId ->
                    showBoostOfferPurchasely(paywallId)
                }

                duaSharedPrefs.setBoostPurchaselyPaywallId(null)
            }
            ClevertapNotificationAction.NEW_PROFILE_VISIT_RECEIVED.type -> {
                val model = Gson().fromJson(jsonData, ProfileVisitedModel::class.java)
                model.cognitoUserId.let { cognitoId ->
                    showProfileVisited(cognitoId)
                    duaSharedPrefs.setProfileVisitedCognitoId(null)
                }
            }
            ClevertapNotificationAction.CONSUMABLE_REWARD_GIVEN.type -> {
                lifecycleScope.launchWhenResumed {
                    delay(1000)
                    val model = Gson().fromJson(jsonData,ConsumableRewardGivenModel::class.java)
                    model?.let {
                        showRewardDialog(model)
                    }
                }
            }
            ClevertapNotificationAction.SHOW_INSTACHAT_PAYWALL.type -> {
                PurchaselyManager.clearCache()

                lifecycleScope.launchWhenResumed {
                  delay(1000)

                  openPremiumPaywall(
                      eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                      placementId = PurchaselyPlacement.DYNAMIC_INSTACHAT.id,
                      userModel = homeViewModel.userProfile.value
                  )
                   duaSharedPrefs.setPaymentTypePaywall(null)
              }
            }
            ClevertapNotificationAction.SHOW_BOOST_PAYWALL.type -> {
                PurchaselyManager.clearCache()

                lifecycleScope.launchWhenResumed {
                    delay(1000)

                    openPremiumPaywall(
                        eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                        placementId = PurchaselyPlacement.DYNAMIC_BOOST.id,
                        userModel = homeViewModel.userProfile.value
                    )

                    duaSharedPrefs.setPaymentTypePaywall(null)
                }
            }
            ClevertapNotificationAction.REAL_TIME_PURCHASELY_OFFER.type -> {
                PurchaselyManager.clearCache()

                try {
                    val model = Gson().fromJson(jsonData, RealTimeClevertapOfferModel::class.java)
                    model?.let {
                        val activeUntil = System.currentTimeMillis() + (TimeUnit.HOURS.toMillis(it.durationHours))
                        val offerModel = model.copy(activeUntil = activeUntil)

                        saveOfferInPreferences(offerModel, duaSharedPrefs)
                    }

                    lifecycleScope.launchWhenResumed {
                        delay(1000)

                        openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                                placementId = model.placementId,
                                userModel = homeViewModel.userProfile.value
                        )

                        duaSharedPrefs.setRealTimeClevertapShowOffer(null)
                    }
                } catch (ex: Exception) {
                    Timber.tag("real_time_offer").d("malformed json ${jsonData}")
                    ex.printStackTrace()
                }
            }
            ClevertapNotificationAction.SUNNY_HILL_PIN.type -> {
                if(!SunnyHillUtils.shouldShowSunnyHillViews()) return
                openCrossPathFullSCreen()
            }
            else -> {

            }
        }


        Timber.tag("CLEVERTAP_LOG").d("onClicked - payload : $payload")
    }

     fun showRewardDialog(model: ConsumableRewardGivenModel) {
         when (model.getDomainRewardType()) {
            RewardType.BOOST -> {
                duaSharedPrefs.setConsumableRewardModel(null)
                if (supportFragmentManager.findFragmentByTag("BoostRewardDialog") == null) {
                    val dialog = BoostRewardDialog.newInstance(model.rewardAmount)
                    dialog.show(supportFragmentManager, "BoostRewardDialog")
                    homeViewModel.updateConsumableRewardState(dialog,model)
                }
            }

            RewardType.UNBLUR -> {
                duaSharedPrefs.setConsumableRewardModel(null)
                if (supportFragmentManager.findFragmentByTag("UnblurRewardDialog") == null) {
                    val dialog =UnblurRewardDialog.newInstance(model.rewardAmount)
                    dialog.show(supportFragmentManager, "UnblurRewardDialog")
                    homeViewModel.updateConsumableRewardState(dialog,model)
                }
            }

            RewardType.INSTACHAT -> {
                duaSharedPrefs.setConsumableRewardModel(null)
                if (supportFragmentManager.findFragmentByTag("InstachatRewardDialog") == null) {
                    val dialog =InstachatRewardDialog.newInstance(model.rewardAmount)
                    dialog.show(supportFragmentManager, "InstachatRewardDialog")
                    homeViewModel.updateConsumableRewardState(dialog,model)
                }
            }

            RewardType.INTERACTION -> {
                duaSharedPrefs.setConsumableRewardModel(null)
                if (supportFragmentManager.findFragmentByTag("InteractionRewardDialog") == null) {
                    val dialog =InteractionRewardDialog.newInstance(model.rewardAmount)
                    dialog.show(supportFragmentManager, "InteractionRewardDialog")
                    homeViewModel.updateConsumableRewardState(dialog,model)
                }
            }
            null -> {/*not used*/
            }
        }
    }

    fun showBoostOfferPurchasely(paywallId: String) {
        val dialog = InAppPackagesDialog.newInstance(paywallId)
        dialog.setData(PaymentType.BOOST_OFFER)
        dialog.show(supportFragmentManager, "buyBoostOfferDialog")
    }

    override fun inboxDidInitialize() {
        val instanceId = FirebaseAnalytics.getInstance(this).appInstanceId.addOnSuccessListener {
            Timber.tag("instanceId").d("instanceId : $it")
        }

        Timber.tag("ClevertapInbox").d("inboxDidInitialize")
        handleAppInboxMessages()
    }

    private fun handleAppInboxMessages() {
        Timber.tag("ClevertapInbox").d("handleAppInboxMessages")

        val inboxMessageCount = CleverTapAPI.getDefaultInstance(this)?.inboxMessageCount
        val allInboxMessages = CleverTapAPI.getDefaultInstance(this)?.allInboxMessages

        if(!allInboxMessages.isNullOrEmpty()) {
            Timber.tag("ClevertapInbox").d("inboxMessageCount :$inboxMessageCount")
            Timber.tag("ClevertapInbox").d("allInboxMessages :${allInboxMessages.map { it.data }}")
            allInboxMessages.forEach { handleClevertapInAppInboxMessage(it) }
        }
    }

    private fun handleClevertapInAppInboxMessage(message: CTInboxMessage) {
        if(appInboxHandledMessages.contains(message.messageId))
            return



        Timber.tag("ClevertapInbox").d("Handling :${message.data}")

        val clevertap = CleverTapAPI.getDefaultInstance(DuaApplication.instance)

        //delete inbox message when user has a state that forbids them from using the app
        if(duaSharedPrefs.getBadge2VerificationState() == Badge2VerificationState.REQUIRED.state ||
            homeViewModel.userProfile.value?.isDisabled == true
        ) {
            clevertap?.deleteInboxMessage(message.messageId)
            return
        }

        val action = (message.data["msg"] as JSONObject).getJSONArray("content").getJSONObject(0).getJSONObject("title").getString("text")
        val body = (message.data["msg"] as JSONObject).getJSONArray("content").getJSONObject(0).getJSONObject("message").getString("text")

        when(action) {
            ClevertapAppInboxActionEnum.PURCHASELY_PAYWALL.value -> {
                PurchaselyManager.clearCache()
                clevertap?.deleteInboxMessage(message.messageId)

                try {
                    val purchaselyPaywall = Gson().fromJson(body, PurchaselyPaywalllModel::class.java)

                    openPremiumPaywallAsync(
                        eventSourceClevertap = ClevertapEventSourceValues.CL_IN_APP_OPEN_PAYWALL,
                        placementId = purchaselyPaywall.placementId,
                        userModel = homeViewModel.userProfile.value
                    )
                } catch (e: Exception) {
                    Timber.tag("purchaselyPaywall").d("malformed json ${body}")
                    e.printStackTrace()
                }
            }

            ClevertapAppInboxActionEnum.FORCE_PURCHASELY_PAYWALL.value -> {
                PurchaselyManager.clearCache()
                clevertap?.deleteInboxMessage(message.messageId)

                try {
                    val model = Gson().fromJson(body, PurchaselyPaywalllModel::class.java)
                    model.placementId.let { placementId ->
                        lifecycleScope.launchWhenResumed {
                            openPremiumPaywallAsync(
                                eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                                placementId = model.placementId,
                                userModel = homeViewModel.userProfile.value
                            )

                            duaSharedPrefs.setForcePurchaselyPaywallId(null)
                        }
                    }
                } catch (e: Exception) {
                    Timber.tag("purchaselyPaywall").d("malformed json ${body}")
                    e.printStackTrace()
                }
            }

            ClevertapAppInboxActionEnum.PREMIUM_OFFER.value -> {
                PurchaselyManager.clearCache()

                val offerModel = Gson().fromJson(body, PremiumIdModel::class.java)

                // commenting out this method for now as Purchasely doesn't support offers
                /*homeViewModel.getPremiumOfferFromId(offerModel.premiumOfferId, message.messageId)*/

                Timber.tag("ClevertapInbox").d("offerModel :$offerModel")
            }

            ClevertapAppInboxActionEnum.SETUP_ACCOUNT_CREDENTIALS.value -> {
                if (DuaApplication.instance.isCredentialUser == false ) {
                    SetUpAccountDialog.newInstance().show(supportFragmentManager, "SetUpAccountDialog")
                    duaSharedPrefs.setShowSetupAccountCredentials(false)
                }
            }

            ClevertapAppInboxActionEnum.REQUEST_LOCATION_POPUP.value -> {
                lifecycle.launchWhileAtLeast(Lifecycle.State.STARTED){
                    showLocationPermissionDialog()
                }
                clevertap?.deleteInboxMessage(message.messageId)
            }

            ClevertapAppInboxActionEnum.REQUEST_NOTIFICATION_POPUP.value -> {
                lifecycle.launchWhileAtLeast(Lifecycle.State.STARTED){
                    showNotificationPermissionDialog(this@HomeActivity, duaSharedPrefs)
                }
                clevertap?.deleteInboxMessage(message.messageId)
            }

            ClevertapAppInboxActionEnum.PURCHASELY_BOOST_OFFER.value -> {
                PurchaselyManager.clearCache()

                val purchaselyPaywall = Gson().fromJson(body, PurchaselyPaywalllModel::class.java)

                showBoostOfferPurchasely(purchaselyPaywall.placementId)

                clevertap?.deleteInboxMessage(message.messageId)
            }

            ClevertapAppInboxActionEnum.REQUEST_FREE_STARTER_EXPERIENCE_POPUP.value -> {
                startActivity(Intent(this, RewardsActivity::class.java))
                clevertap?.deleteInboxMessage(message.messageId)
            }

            ClevertapAppInboxActionEnum.REAL_TIME_PURCHASELY_OFFER.value -> {
                PurchaselyManager.clearCache()

                try {
                    val model = Gson().fromJson(body, RealTimeClevertapOfferModel::class.java)
                    model?.let {
                        val messageSent = message.date
                        Timber.tag("ClevertapInbox").d("model ${model} : messageSent $messageSent")
                        if(System.currentTimeMillis() < messageSent + TimeUnit.HOURS.toMillis(it.durationHours)) {
                            val activeUntil = System.currentTimeMillis() + (TimeUnit.HOURS.toMillis(it.durationHours))
                            val offerModel = model.copy(activeUntil = activeUntil)

                            saveOfferInPreferences(offerModel, duaSharedPrefs)
                        }
                    }
                } catch (ex: Exception) {
                    Timber.tag("real_time_offer").d("malformed json ${body}")
                    ex.printStackTrace()
                }

                clevertap?.deleteInboxMessage(message.messageId)
            }

/*            ClevertapAppInboxActionEnum.ASK_FOR_ALWAYS_ALLOW_LOCATION.value -> {
                lifecycle.launchWhileAtLeast(Lifecycle.State.STARTED){
                    if(!<EMAIL>()){
                        showProfileRecommendationsDialogFragment(homeViewModel.userProfile.value)
                    }
                    clevertap?.deleteInboxMessage(message.messageId)
                }
            }*/

            else -> {
                clevertap?.deleteInboxMessage(message.messageId)
            }
        }

        appInboxHandledMessages.add(message.messageId)
    }

    private fun showProfileRecommendationsDialogFragment(user: UserModel?) {
        val profileRecommendationsDialogFragment = ProfileRecommendationsDialogFragment.newInstance( premiumType = getPremiumTypeEventProperty(user))
        if (supportFragmentManager.findFragmentByTag("ProfileRecommendations") == null) {
            profileRecommendationsDialogFragment.show(supportFragmentManager, "ProfileRecommendations")
        }
    }

    override fun inboxMessagesDidUpdate() {
        Timber.tag("ClevertapInbox").d("inboxMessagesDidUpdate")

        handleAppInboxMessages()
    }

    override fun onDisplayUnitsLoaded(units: ArrayList<CleverTapDisplayUnit>?) {
        Timber.tag("DisplayUnits").d("units: ${units?.map { it.customExtras }}")
    }

    private fun showShadowBannedDialog() {
        homeViewModel.userProfile.value?.let {
            val dialog = ShadowBannedDialog.newInstance(it.profile.pictureUrl)
            dialog.show(supportFragmentManager, "ShadowBannedDialog")
        }
    }

    private fun showLocationPermissionDialog() {
                if(!isLocationPermissionEnabled){
                    val shouldProvideRationale = shouldProvideRationale()

                    val dialog = LocationPermissionDialog.newInstance(shouldProvideRationale)
                    dialog.show(supportFragmentManager, "LocationPermissionDialog")
                }

    }

    private fun showNotificationPermissionDialog(context: Context, duaSharedPrefs: DuaSharedPrefs) {
        if (!isPostNotificationsPermissionEnabled()) {
            var dialog =
                supportFragmentManager.findFragmentByTag("NotificationPermissionDialog") as? NotificationPermissionDialog?
            if (dialog == null) {
                dialog = NotificationPermissionDialog.newInstance()
                dialog.show(supportFragmentManager, "NotificationPermissionDialog")
                hasNotificationPermissionDialogBeenShown = true
            }

        } else {
            val isDeviceNotificationOn =
                NotificationHelper.isMessageNotificationsEnables(context)
            val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
            if (!(isDeviceNotificationOn && isSettingsNotificationOn)) {
                var dialog =
                    supportFragmentManager.findFragmentByTag("NotificationPermissionDialog") as? NotificationPermissionDialog?
                if (dialog == null) {
                    dialog = NotificationPermissionDialog.newInstance()
                    dialog.show(supportFragmentManager, "NotificationPermissionDialog")
                    hasNotificationPermissionDialogBeenShown = true
                }
                    }
                }
    }

    override fun changePhoto() {
        navigateTo(R.id.nav_graph_profile)
        homeViewModel.navigatedToProfileFromRadar()
    }

    override fun onLocationContinueClicked(hasDeniedPermission: Boolean) {
        if(hasDeniedPermission) {
            openSettingsForLocation()
        } else {
            requestLocation(ClevertapEventSourceValues.CARDS.value)
        }
    }

    private fun showLikedYouBadge(newLike: Boolean = true) {
        val maxCharacterCount = 3
        val likedYouCountNumber = likeYouViewModel.likesCount

        val badge = binding.bottomNav.getOrCreateBadge(R.id.nav_graph_likes)
        val backgroundBadgeColor = if (newLike) R.color.pink_500 else R.color.gray_200
        badge.backgroundColor = ContextCompat.getColor(this, backgroundBadgeColor)
        badge.badgeTextColor = ContextCompat.getColor(this, R.color.background)
        badge.horizontalOffset = 14
        badge.verticalOffset = 10
        badge.isVisible = likedYouCountNumber > 0

        when (likedYouCountNumber) {
            in 1..99 -> {
                badge.number = likedYouCountNumber
            }
            else -> {
                badge.number = likedYouCountNumber
                badge.maxCharacterCount = maxCharacterCount
            }
        }
    }

    override fun onClaimNowClick(dialog: ConsumableRewardBaseDialog) {
        when(dialog){
            is UnblurRewardDialog -> {
                likeYouViewModel.unBlurLatestLike()
                navigateTo(R.id.nav_graph_likes)
            }
            is BoostRewardDialog -> {
                homeViewModel.showBoostHighlightDialog()
                navigateTo(R.id.nav_graph_home)
            }
            else -> {
                dialog.dismissAllowingStateLoss()
                homeViewModel.dismissConsumableRewardDialog()
            }
        }
    }

    private fun storeCrossPathLocation() {
        if (isLocationPermissionEnabled) {
            val geofenceM:GeofenceManager? = if (checkBackgroundLocationPermission()) { geofenceManager} else null

            locationClient.getCurrentLocation(CancellationTokenSource())
                .catch { e ->
                    e.printStackTrace()
                }
                .onEach { location ->
                    saveLocationUseCase(
                        CrossPathLocation(
                            location.longitude,
                            location.latitude,
                            System.currentTimeMillis()
                        )
                    )
                    geofenceM?.removeAndAddGeoFences(location) {
                    Timber.tag(TAG).d("Geofence added: $it")
                    }
                }.flowOn(Dispatchers.IO)
                .launchIn(lifecycleScope)
        }
    }

    fun getLocationServicesLayout(): LayoutLocationServicesBinding = binding.locationServicesLayout

    override fun onTryAgainClicked() {
        val premiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        sendClevertapEvent(ClevertapEventEnum.TRY_AGAIN_TO_POSE, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
        ))
        sendUxCamEvent(ClevertapEventEnum.TRY_AGAIN_TO_POSE, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
        ))
    }
}