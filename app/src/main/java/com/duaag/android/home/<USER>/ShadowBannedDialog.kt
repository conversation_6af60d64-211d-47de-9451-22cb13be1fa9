package com.duaag.android.home.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapAddPhotoSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentShadowBannedBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsAddPhotoSourceValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.utils.imageUrlCircleFullResolution
import com.duaag.android.utils.setOnSingleClickListener
import com.google.firebase.Firebase

class ShadowBannedDialog : DialogFragment(), DialogClickListener {

    private var  _binding: FragmentShadowBannedBinding? = null
    private val binding get() = _binding!!

    private var listener: ShadowBannedDialogListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        try {
            listener = if(arguments?.getBoolean(IS_FROM_FRAGMENT) == true)
                parentFragment as ShadowBannedDialogListener
            else
                context as ShadowBannedDialogListener
        } catch (e: ClassCastException) {
            throw ClassCastException("calling class must implement ShadowBannedDialogListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreenStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentShadowBannedBinding.inflate(inflater)
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }


        if(arguments?.getBoolean(IS_USER_TRYING_TO_VERIFY) == true) {
            binding.changePhotoButton.setText(R.string.change_your_photo)
            binding.description.setText(R.string.verification_denied_shadow)

            val premiumType = arguments?.getString(PREMIUM_TYPE)

            firebaseLogEvent(FirebaseAnalyticsEventsName.HIDEN_PROFILE, mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
            ))

            sendClevertapEvent(ClevertapEventEnum.HIDEN_PROFILE, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
            ))
        }

        binding.changePhotoButton.setOnSingleClickListener {
            listener?.changePhoto()
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_PHOTO)
            sendClevertapEvent(ClevertapEventEnum.CHANGE_PHOTO)
            sendAddPhotoEvents()
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    private fun sendAddPhotoEvents() {
        sendClevertapEvent(
            ClevertapEventEnum.ADD_PHOTO,
            mapOf(ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapAddPhotoSourceValues.FULL_SCREEN.value)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.ADD_PHOTO,
            mapOf(FirebaseAnalyticsParameterName.EVENT_SOURCE.value to FirebaseAnalyticsAddPhotoSourceValues.FULL_SCREEN.value)
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val url = arguments?.getString(PROFILE_IMAGE)?:return
        val premiumType = arguments?.getString(PREMIUM_TYPE)

        imageUrlCircleFullResolution(binding.image,url)

        binding.seeGuidelinesTxt.setOnSingleClickListener{
            val dialog= GuidelinesDialogFragment.newInstance(premiumTypeEventProperty = premiumType)
            dialog.show(childFragmentManager,"GuidelinesDialog")
        }

        binding.closeBtn.setOnSingleClickListener{
            dismissAllowingStateLoss()
        }
    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface ShadowBannedDialogListener {
        fun changePhoto()
    }


    companion object {
        const val PROFILE_IMAGE = "profile_image"
        const val IS_FROM_FRAGMENT = "is_from_fragment"
        private val IS_USER_TRYING_TO_VERIFY = "is_user_trying_to_verify"
        private val PREMIUM_TYPE = "premium_type"

        fun newInstance(
            profileUrl: String,
            premiumType: String? = null,
            isFromFragment: Boolean = false,
            isUserTryingToVerify: Boolean = false
        ): ShadowBannedDialog {
            val args = Bundle()
            args.putString(PROFILE_IMAGE, profileUrl)
            args.putString(PREMIUM_TYPE, premiumType)
            args.putBoolean(IS_FROM_FRAGMENT, isFromFragment)
            args.putBoolean(IS_USER_TRYING_TO_VERIFY, isUserTryingToVerify)
            args.putBoolean(IS_USER_TRYING_TO_VERIFY, isUserTryingToVerify)
            val fragment = ShadowBannedDialog()
            fragment.arguments = args

            return fragment
        }
    }

    override fun onButtonClicked() {
        binding.changePhotoButton.performClick()
    }
}