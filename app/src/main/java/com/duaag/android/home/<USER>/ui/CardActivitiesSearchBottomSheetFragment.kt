package com.duaag.android.home.profile_activities_card.ui

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.databinding.CardActivitiesSearchLayoutBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.adapter.ProfileActivitiesItemAdapter
import com.duaag.android.home.models.ProfileActivitiesModel
import com.duaag.android.home.models.getTitleOfProfileActivitiesCardSupport
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.setOnSingleClickListener
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import javax.inject.Inject

class CardActivitiesSearchBottomSheetFragment : BottomSheetDialogFragment() {
    private var _binding: CardActivitiesSearchLayoutBinding? = null
    private val binding get() = _binding


    private var listener: CardActivitiesSheetListener? = null

    private var fallbackItems : List<ProfileActivitiesModel>? = null


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
        listener = parentFragment as? CardActivitiesSheetListener
            ?: throw ClassCastException("$context must implement CardActivitiesSheetListener")

    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.setOnShowListener { dialog ->

            val bottomSheetDialog = dialog as BottomSheetDialog
            val parentLayout =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            parentLayout?.let { it ->
                val behaviour = BottomSheetBehavior.from(it)
                setupFullHeight(it)
                behaviour.state = BottomSheetBehavior.STATE_EXPANDED
                behaviour.skipCollapsed = true
                behaviour.isHideable = true
            }
        }
        dialog.dismissWithAnimation = true
        return dialog
    }

    private fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = CardActivitiesSearchLayoutBinding.inflate(inflater)
        homeViewModel.initFilteredProfileActivities()
        return binding?.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val searchView = binding?.searchView
        val recyclerView = binding?.recyclerView
        val adapter =
            ProfileActivitiesItemAdapter(
                onTagSelectedCallback = { model ->
                            // Check if the action is to deselect an item and the maximum count is not exceeded
                            if (model.isSelected || homeViewModel.profileActivitiesItems.value?.count { it.isSelected }!! < model.limit) {
                                homeViewModel.updateSelectedProfileActivityWithId(model.id)
                            } else CardActivitiesMaxLimitDialogFragment.newInstance().show(childFragmentManager,CardActivitiesMaxLimitDialogFragment.TAG)



                    //close keyboard if searching
                    hideKeyboard()

                })




        recyclerView?.adapter = adapter
        homeViewModel.filteredProfileActivitiesItems.observe(viewLifecycleOwner) {items->
            if(fallbackItems == null) fallbackItems = items
            adapter.submitList(items)
        }

        homeViewModel.profileActivitiesItems.observe(viewLifecycleOwner) {items->
            //set the title based on the type of the tags

            if (items?.isNotEmpty() == true) {
                val selectedItemsCount = items.count { it.isSelected }
                val selectedTagsText = requireContext().getString(
                    R.string.selected_counter_an,
                    "$selectedItemsCount",
                    "${items.first().limit}"
                )

                binding?.titleTextView?.setText(getTitleOfProfileActivitiesCardSupport(items.first().type))
                binding?.selectedTagsTextView?.text = selectedTagsText
                binding?.btnSave?.isEnabled = selectedItemsCount > 0
            }
        }




        //initialize recyclerview with its layout manager
        val flexLayoutManager =
            FlexboxLayoutManager(requireContext(), FlexDirection.ROW, FlexWrap.WRAP)
        flexLayoutManager.justifyContent = JustifyContent.FLEX_START
        recyclerView?.layoutManager = flexLayoutManager




        //filter tags based on query
        searchView?.addTextChangedListener { editable ->
            val query = editable.toString()
            homeViewModel.onProfileActivitiesQuery(query)
        }

        binding?.btnSave?.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }

    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        listener?.onCardActivitiesSheetDismissed(homeViewModel.profileActivitiesItems.value?.filter { it.isSelected }
            ?.map { it.id }?.toSet())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fallbackItems = null
        _binding = null
    }

    interface CardActivitiesSheetListener {
        fun onCardActivitiesSheetDismissed(items: Set<Int>?)
    }

    companion object {
        const val TAG = "CardActivitiesSearchBottomSheetFragment"

        fun newInstance(): CardActivitiesSearchBottomSheetFragment =
            CardActivitiesSearchBottomSheetFragment()
    }
}