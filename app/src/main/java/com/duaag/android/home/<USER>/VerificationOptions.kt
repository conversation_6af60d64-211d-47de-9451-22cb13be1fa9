package com.duaag.android.home.fragments

import android.animation.ObjectAnimator
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import com.duaag.android.R
import com.duaag.android.home.fragments.VerificationType.*
import com.duaag.android.base.models.AccountModel
import com.duaag.android.databinding.FragmentVerificationOptionsBinding
import com.duaag.android.signup.models.AuthMethod
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

enum class VerificationType {
    ALL_IMAGE_PROCESSING, ALL, IMAGE_VERIFICATION, PHONE_EMAIL_VERIFICATION, IMAGE_VERIFICATION_PROCESSING
}

class VerificationOptions : BottomSheetDialogFragment() {

    companion object {

        fun newInstance(listener: VerificationOptionsClickListener?, type: VerificationType, accountModel: AccountModel? = null): VerificationOptions {
            return VerificationOptions().apply {
                mListener = listener
                mType = type
                mAccountModel = accountModel
            }
        }
    }

    private var _binding: FragmentVerificationOptionsBinding? = null
    private val binding get() = _binding!!
    private var mListener: VerificationOptionsClickListener? = null
    private var mType: VerificationType = ALL
    private var mAccountModel: AccountModel? = null
    private var authMethod: AuthMethod? = null


    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentVerificationOptionsBinding.inflate(inflater)

        binding.button1.setOnClickListener {
            if (mType!=ALL_IMAGE_PROCESSING && mType!=IMAGE_VERIFICATION_PROCESSING)
            mListener?.onImageVerificationClicked(it, this)
        }
        binding.button2.setOnClickListener {
            mListener?.onPhoneEmailVerificationClicked(authMethod, this)
        }
        val badgeApproved = binding.img1
        val badgeProcessing = binding.img2

        mAccountModel?.let {mAccountModel ->
            if ((mAccountModel.phone == null && mAccountModel.email != null) || (mAccountModel.phone == null && mAccountModel.email == null)) {
               authMethod = AuthMethod.PHONE
            } else if (mAccountModel.email == null && mAccountModel.phone != null) {
                authMethod = AuthMethod.EMAIL
            }
        }

        binding.text2.text = getString(if (authMethod == AuthMethod.EMAIL) R.string.email_verification else R.string.phone_verification)
        when (mType) {
            ALL_IMAGE_PROCESSING -> {
                binding.text1.text=getString(R.string.processing)
                binding.button1.visibility=View.VISIBLE
                binding.button2.visibility=View.VISIBLE
                badgeProcessing.visibility=View.VISIBLE
                badgeApproved.setImageDrawable(ContextCompat.getDrawable(badgeApproved.context, R.drawable.ic_image_verification))
                badgeProcessing.setImageDrawable(ContextCompat.getDrawable(badgeProcessing.context, R.drawable.ic_image_verification_processinng))

                val fadeAnimator2 = ObjectAnimator.ofFloat(badgeApproved, "alpha", 1f, 0f).apply {

                    duration = 800

                    repeatCount = ObjectAnimator.INFINITE

                    repeatMode = ObjectAnimator.REVERSE

                }


                fadeAnimator2.start()
            }
            ALL -> {
                binding.text1.text=getString(R.string.image_verification)
                binding.button1.visibility=View.VISIBLE
                binding.button2.visibility=View.VISIBLE
                badgeApproved.setImageDrawable(ContextCompat.getDrawable(badgeApproved.context, R.drawable.ic_image_verification))
                badgeApproved.visibility = View.VISIBLE
                badgeProcessing.visibility = View.INVISIBLE
            }
            IMAGE_VERIFICATION -> {
                binding.text1.text=getString(R.string.image_verification)
                binding.button1.visibility=View.VISIBLE
                binding.button2.visibility=View.GONE
                badgeApproved.setImageDrawable(ContextCompat.getDrawable(badgeApproved.context, R.drawable.ic_image_verification))
                badgeApproved.visibility = View.VISIBLE
                badgeProcessing.visibility = View.INVISIBLE

            }
            PHONE_EMAIL_VERIFICATION -> {
                binding.button1.visibility=View.GONE
                binding.button2.visibility=View.VISIBLE
            }

            IMAGE_VERIFICATION_PROCESSING -> {
                binding.text1.text=getString(R.string.processing)
                binding.button1.visibility=View.VISIBLE
                binding.button2.visibility=View.VISIBLE
                badgeProcessing.visibility=View.VISIBLE
                binding.button2.visibility=View.GONE
                badgeApproved.setImageDrawable(ContextCompat.getDrawable(badgeApproved.context, R.drawable.ic_image_verification))
                badgeProcessing.setImageDrawable(ContextCompat.getDrawable(badgeProcessing.context, R.drawable.ic_image_verification_processinng))

                val fadeAnimator2 = ObjectAnimator.ofFloat(badgeApproved, "alpha", 1f, 0f).apply {

                    duration = 800

                    repeatCount = ObjectAnimator.INFINITE

                    repeatMode = ObjectAnimator.REVERSE

                }


                fadeAnimator2.start()
            }
        }

        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
                super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                    d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                        BottomSheetBehavior.STATE_EXPANDED
            }

        }
        return dialog
    }

    interface VerificationOptionsClickListener {
        fun onImageVerificationClicked(button: View, fragment: VerificationOptions)
        fun onPhoneEmailVerificationClicked(authMethod: AuthMethod?, fragment: VerificationOptions)
    }

    override fun onDestroy() {
        super.onDestroy()
        mListener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}