package com.duaag.android.home.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.home.models.SetHeightScreenState
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SetHeightViewModel @Inject constructor(
    val userRepository: UserRepository
): ViewModel() {

    private val _setHeightScreenState: MutableStateFlow<SetHeightScreenState> = MutableStateFlow(
        SetHeightScreenState.InitialState
    )
    val setHeightScreenState: StateFlow<SetHeightScreenState>
        get() = _setHeightScreenState

    fun addHeight(height: Int?) {
        viewModelScope.launch {
            val params = mapOf("profile" to mapOf("height" to height))
            try {
                _setHeightScreenState.update { SetHeightScreenState.Loading }
                val response = userRepository.updateUserWithResult(params)
                if(response.data == true) {
                    userRepository.user.value?.profile?.height = height

                    withContext(Dispatchers.IO) {
                        val user = userRepository.getLoggedInUserModel()
                        user?.profile?.height = height
                        user?.let { userRepository.updateUserInDB(user) }
                    }

                    _setHeightScreenState.update { SetHeightScreenState.Success() }
                } else {
                    _setHeightScreenState.update { SetHeightScreenState.Error() }
                }
            } catch (ex: Exception) {
                _setHeightScreenState.update { SetHeightScreenState.Error() }
            }
        }
    }

    fun setInitialHeight() {
        _setHeightScreenState.update { SetHeightScreenState.InitialState }
    }
}