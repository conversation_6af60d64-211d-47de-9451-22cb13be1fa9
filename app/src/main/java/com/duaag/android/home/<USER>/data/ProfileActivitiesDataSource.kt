package com.duaag.android.home.profile_activities_card.data

import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.google.gson.Gson
import com.google.gson.JsonParseException
import com.google.gson.reflect.TypeToken
import timber.log.Timber
import javax.inject.Inject

class ProfileActivitiesDataSource @Inject constructor(private val duaSharedPrefs: DuaSharedPrefs) {
    fun setProfileActivitiesTimeDismissedByTypeId(model: ProfileActivityDismissedModel) {
        try {
            val existedItems: MutableList<ProfileActivityDismissedModel> =
                duaSharedPrefs.getProfileActivitiesDismissed()?.let {
                    val tokenType = TypeToken.getParameterized(List::class.java, ProfileActivityDismissedModel::class.java).type
                    Gson().fromJson(it, tokenType)
                } ?: mutableListOf()

            existedItems.firstOrNull { it.typeId == model.typeId }?.let {
                existedItems.remove(it)
                existedItems.add(model)
            } ?: kotlin.run { existedItems.add(model) }
            duaSharedPrefs.setProfileActivitiesTimeDismissedByTypeId(Gson().toJson(existedItems))
        } catch (e: JsonParseException) {
            Timber.e(e)
        }

    }

    fun getProfileActivitiesDismissed(): List<ProfileActivityDismissedModel> = try {
        duaSharedPrefs.getProfileActivitiesDismissed()?.let {
            val tokenType = object : TypeToken<List<ProfileActivityDismissedModel>>() {}.type
            Gson().fromJson(it, tokenType)
        } ?: listOf()
    } catch (e: JsonParseException) {
        Timber.e(e)
        listOf()
    }

}