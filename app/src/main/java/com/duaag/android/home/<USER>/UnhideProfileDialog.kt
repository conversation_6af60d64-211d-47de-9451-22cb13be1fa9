package com.duaag.android.home.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.databinding.FragmentUnhideProfileBinding
import com.duaag.android.home.HomeActivity

//Show this Dialog when the user taps 'activate Boost' but the user is hidden + has Badge2
class UnhideProfileDialog : DialogFragment() {

    companion object {
        fun newInstance(): UnhideProfileDialog {
            return UnhideProfileDialog()
        }
    }

    private var _binding: FragmentUnhideProfileBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentUnhideProfileBinding.inflate(inflater)

        binding.unhideButton.setOnClickListener {
            (activity as HomeActivity).unHideProfile()
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    interface UnhideProfile {
        fun unHideProfile()
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val width = ViewGroup.LayoutParams.WRAP_CONTENT
            val height = ViewGroup.LayoutParams.WRAP_CONTENT
            dialog.window?.setLayout(width, height)
            dialog.window?.setBackgroundDrawableResource(R.drawable.bottom_sheet_rounded_24dp)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}