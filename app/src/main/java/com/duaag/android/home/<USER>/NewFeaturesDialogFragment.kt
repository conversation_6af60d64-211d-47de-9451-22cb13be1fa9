package com.duaag.android.home.fragments

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.databinding.NewFeaturesDialogBinding
import com.duaag.android.home.adapter.NewFeaturesAdapter


class NewFeaturesDialogFragment : DialogFragment() {

    private var _binding: NewFeaturesDialogBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //set style in order to open dialog on full screen
        setStyle(STYLE_NO_TITLE, R.style.MatchAnimationTheme)
    }

    override fun onCreateView(inflater: android.view.LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = NewFeaturesDialogBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.title.text = getString(R.string.whats_new_on_dua,getString(R.string.app_name))
        initializeRecyclerView()

        binding.continueButton.setOnClickListener {
            dialog?.dismiss()
        }
    }

    fun initializeRecyclerView() {
        binding.featureRv.layoutManager = LinearLayoutManager(context)
        val adapter = NewFeaturesAdapter()
        binding.featureRv.adapter = adapter
    }


    override fun onStart() {
        val fragment: Dialog? = dialog
        if (dialog != null) {
            val width = ViewGroup.LayoutParams.MATCH_PARENT
            val height = ViewGroup.LayoutParams.MATCH_PARENT
            fragment?.window?.setLayout(width, height)
            super.onStart()

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

