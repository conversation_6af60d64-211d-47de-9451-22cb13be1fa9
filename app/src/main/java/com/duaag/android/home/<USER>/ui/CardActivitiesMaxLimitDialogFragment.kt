package com.duaag.android.home.profile_activities_card.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.databinding.ActivitiesMaxReachedLayoutBinding
import com.duaag.android.utils.setOnSingleClickListener

class CardActivitiesMaxLimitDialogFragment : DialogFragment() {
    private var _binding: ActivitiesMaxReachedLayoutBinding? = null
    private val binding get() = _binding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = ActivitiesMaxReachedLayoutBinding.inflate(inflater)
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.okBtn?.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val TAG = "CardActivitiesMaxLimit"
        fun newInstance(): CardActivitiesMaxLimitDialogFragment =
            CardActivitiesMaxLimitDialogFragment()
    }

}