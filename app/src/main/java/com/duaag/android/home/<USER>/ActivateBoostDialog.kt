package com.duaag.android.home.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapBoostActivationSource
import com.duaag.android.databinding.FragmentActivateBoostBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import javax.inject.Inject

//Show this dialog to activate Boost
class ActivateBoostDialog : DialogFragment() {

    companion object {
        private const val ACTIVE_BOOST_ACTIVATION_SOURCE = "activeBoostActivationSource"

        fun newInstance(activeBoostActivationSource: ClevertapBoostActivationSource): ActivateBoostDialog {
            return ActivateBoostDialog().apply {
                arguments = Bundle().apply {
                    putString(ACTIVE_BOOST_ACTIVATION_SOURCE, activeBoostActivationSource.value)
                }
            }
        }
    }
    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: FragmentActivateBoostBinding? = null
    private val binding get() = _binding!!

    private val activeBoostActivationSource: ClevertapBoostActivationSource by lazy {
        ClevertapBoostActivationSource.fromValue(arguments?.getString(ACTIVE_BOOST_ACTIVATION_SOURCE) ?: ClevertapBoostActivationSource.FOR_YOU.value)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentActivateBoostBinding.inflate(inflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

        binding.activateBoost.setOnClickListener {
            homeViewModel.activateBoost(activeBoostActivationSource)
            dismissAllowingStateLoss()
        }

        binding.dismiss.setOnClickListener {
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}