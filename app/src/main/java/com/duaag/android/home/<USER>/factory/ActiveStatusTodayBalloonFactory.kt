package com.duaag.android.home.activity_status.factory

import android.content.Context
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LifecycleOwner
import com.duaag.android.R
import com.duaag.android.utils.convertDpToPixel
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.TextForm

class ActiveStatusTodayBalloonFactory : Balloon.Factory() {

  override fun create(context: Context, lifecycle: LifecycleOwner?): Balloon {
    val textForm = TextForm.Builder(context)
      .setText(context.getString(R.string.active_today))
      .setTextColorResource(R.color.gray_50)
      .setTextSize(16f)
      .setTextTypeface(ResourcesCompat.getFont(context,R.font.tt_norms_pro_normal))
      .build()
    return Balloon.Builder(context)
      .setArrowSize(10)
      .setArrowAlignAnchorPaddingRatio(convertDpToPixel(5f, context))
      .setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
      .setArrowOrientation(ArrowOrientation.BOTTOM)
      .setArrowPosition(0.5F)
      .setPadding(8)
      .setMarginRight(24)
      .setTextForm(textForm)
      .setArrowElevation(1)
      .setCornerRadius(8f)
      .setElevation(1)
      .setBackgroundColor(ContextCompat.getColor(context, R.color.green_500))
      .setLifecycleOwner(lifecycle)
      .build()
  }
}