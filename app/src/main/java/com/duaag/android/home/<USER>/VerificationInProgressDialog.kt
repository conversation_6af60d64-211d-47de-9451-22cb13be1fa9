package com.duaag.android.home.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.duaag.android.R
import com.duaag.android.databinding.VerificationInProgressDialogBinding

class VerificationInProgressDialog : DialogFragment() {

    private var _binding: VerificationInProgressDialogBinding? = null
    private val binding get() = _binding!!


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)

    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = VerificationInProgressDialogBinding.inflate(inflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

        Glide.with(binding.image)
            .load(R.drawable.verified_profile_badge)
            .transform(CenterCrop())
            .into(binding.image)

        binding.continueBtn.setOnClickListener {
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(): VerificationInProgressDialog {
            val args = Bundle()
            val fragment = VerificationInProgressDialog()
            fragment.arguments = args

            return fragment
        }

        fun showVerificationInProgressDialog(fragmentManager: FragmentManager) {
            if (fragmentManager.findFragmentByTag("VerificationInProgress") == null) {
                val dialog = newInstance()
                dialog.show(fragmentManager, "VerificationInProgress")
            }
        }
    }
}