package com.duaag.android.home.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.base.models.CommunityInfo
import com.duaag.android.home.models.AddEthnicitiesUiState
import com.duaag.android.home.models.EthnicityUiModel
import com.duaag.android.home.models.mapToEthnicityUiModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.user.UserRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class AddEthnicitiesViewModel @AssistedInject constructor(
    val userRepository: UserRepository,
    @Assisted var chosenItems: List<String> = emptyList()
) : ViewModel() {

    private val _addEthnicityUiState: MutableStateFlow<AddEthnicitiesUiState> =
        MutableStateFlow(AddEthnicitiesUiState())
    val addEthnicityUiState: StateFlow<AddEthnicitiesUiState>
        get() = _addEthnicityUiState.asStateFlow()

    private val initialItems: List<String> = chosenItems
    private var allCommunities: List<CommunityInfo>? = null

    init {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.localCommunities.collect { communities ->
                if (communities.isEmpty())
                    return@collect

                allCommunities = communities
                setCommunities(chosenItems, communities)
            }
        }
    }

    fun setCommunities(chosenItems: List<String>, communities: List<CommunityInfo>?) {
        if (communities == null)
            return

        viewModelScope.launch(Dispatchers.IO) {
            val chosenCommunities = communities.filter { community ->
                chosenItems.contains(community.id)
            }
            val ethnicities = communities.map { it.mapToEthnicityUiModel() }

            withContext(Dispatchers.Main) {
                _addEthnicityUiState.update { addEthnicityUiState ->
                    addEthnicityUiState.copy(
                        ethnicitiesList = ethnicities,
                        chosenEthnicitiesList = chosenCommunities.map { it.mapToEthnicityUiModel() }
                    )
                }
            }
        }
    }

    fun haveTheItemsChanged(): Boolean {
        return initialItems.sorted() != addEthnicityUiState.value.chosenEthnicitiesList.map { it.id }.sorted()
    }

    fun fetchCommunitiesIfNotCached() {
        viewModelScope.launch {
            val hasCachedCommunities = userRepository.hasCommunitiesCached()
            Timber.tag(SharedSignUpViewModel.TAG).d("hasCachedCommunities: $hasCachedCommunities")

            if (!hasCachedCommunities) {
                getCommunitiesList()
            }
        }
    }

    fun getCommunitiesList() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getCommunities()
        }
    }

    fun getCheckedCount(): Int {
        return _addEthnicityUiState.value.chosenEthnicitiesList.size + 1
    }

    fun onEthnicityClick(ethnicity: EthnicityUiModel) {
        _addEthnicityUiState.update { addEthnicityUiState ->
            val list = addEthnicityUiState.chosenEthnicitiesList.toMutableList()
            if (ethnicity.isChecked) {
                list.add(ethnicity)
            } else {
                list.removeIf { it.id == ethnicity.id }
            }

            addEthnicityUiState.copy(
                chosenEthnicitiesList = list
            )
        }
    }

    fun resetEthnicities() {
        setCommunities(emptyList(), allCommunities)
    }

}