package com.duaag.android.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.ItemFilterBinding
import com.duaag.android.databinding.SingleItemFilterBinding
import com.duaag.android.home.models.FilterTagUiModel
import com.duaag.android.home.models.FilterType

class FilterItemAdapter(
    private val clickListener: ItemFilterClickListener,
    private var filterType: FilterType,
    private val isProfileEdit: Boolean
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val CHECK_BOX_ITEM = 1
        const val RADIO_BUTTON_ITEM = 2
    }

    private var items: ArrayList<FilterTagUiModel> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            CHECK_BOX_ITEM -> {
                val binding = ItemFilterBinding.inflate(layoutInflater, parent, false)
                CheckBoxViewHolder(binding)
            }
            else -> {
                val binding = SingleItemFilterBinding.inflate(layoutInflater, parent, false)
                RadioViewHolder(binding)
            }
        }
    }

    fun setData(data: List<FilterTagUiModel>) {
        val diffCallback = CommunityDiffUtil(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    override fun getItemViewType(position: Int): Int {
        return if((filterType == FilterType.LOOKING_FOR || filterType == FilterType.RELIGION) && isProfileEdit) RADIO_BUTTON_ITEM else CHECK_BOX_ITEM
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            CHECK_BOX_ITEM -> (holder as CheckBoxViewHolder).bind(items[position])
            RADIO_BUTTON_ITEM -> (holder as RadioViewHolder).bind(items[position])
        }
    }

    inner class CheckBoxViewHolder(val binding: ItemFilterBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                if(bindingAdapterPosition < 0)
                    return@setOnClickListener

                val model = items[bindingAdapterPosition]
                clickListener.onClick(model)

            }
        }
        fun bind(model: FilterTagUiModel) {
            val showCheck = model.isChecked
            val communityName = model.tagName

            binding.checkBox.isChecked = showCheck
            binding.communityName.text = communityName
        }
    }

    inner class RadioViewHolder(val binding: SingleItemFilterBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                if(bindingAdapterPosition < 0)
                    return@setOnClickListener

                val model = items[bindingAdapterPosition]

                clickListener.onClick(model)
            }
        }
        fun bind(model: FilterTagUiModel) {
            val showCheck = model.isChecked
            val communityName = model.tagName

            binding.radioButton.isChecked = showCheck
            binding.communityName.text = communityName
        }
    }


    class CommunityDiffUtil(private val oldList: List<FilterTagUiModel>, private val newList: List<FilterTagUiModel>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size


        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }

    class ItemFilterClickListener(val clickListener: (model: FilterTagUiModel) -> Unit) {
        fun onClick(model: FilterTagUiModel) = clickListener(model)
    }
}