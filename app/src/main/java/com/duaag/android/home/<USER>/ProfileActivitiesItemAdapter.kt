package com.duaag.android.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.ProfileInterestsCardItemBinding
import com.duaag.android.home.models.ProfileActivitiesModel
import com.duaag.android.utils.setOnSingleClickListener

class ProfileActivitiesItemAdapter(
    private val onTagSelectedCallback: (
        ProfileActivitiesModel
    ) -> Unit,
) : androidx.recyclerview.widget.ListAdapter<ProfileActivitiesModel, RecyclerView.ViewHolder>(
    ProfileActivitiesModelDiffCallback()
) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ProfileInterestsCardItemBinding =
            ProfileInterestsCardItemBinding.inflate(layoutInflater, parent, false)
        return ProfileActivitiesItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position) as ProfileActivitiesModel
        (holder as ProfileActivitiesItemViewHolder).bind(
            item,
            onTagSelectedCallback,
        )
    }

    inner class ProfileActivitiesItemViewHolder(private val binding: ProfileInterestsCardItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(
            item: ProfileActivitiesModel,
            onTagSelectedCallback: (ProfileActivitiesModel) -> Unit,
        ) {
            binding.titleText.text = item.name

            binding.card.setCardBackgroundColor(
                ContextCompat.getColor(
                    binding.root.context,
                    if (item.isSelected) R.color.dua_red_color else R.color.transparent
                )
            )
            binding.titleText.setTextColor(
                if (item.isSelected) ContextCompat.getColor(
                    binding.root.context,
                    R.color.gray_50
                ) else ContextCompat.getColor(binding.root.context, R.color.title_primary)
            )
            binding.card.strokeColor = if (item.isSelected) ContextCompat.getColor(
                binding.root.context,
                R.color.transparent
            ) else ContextCompat.getColor(binding.root.context, R.color.border)
            binding.card.setOnSingleClickListener {
                // Check if the item's ID is -1(+nr of items selected but not rendered), and if so, return early from the function
                if (item.id == -1) return@setOnSingleClickListener

                onTagSelectedCallback(item)
            }
        }
    }
}


class ProfileActivitiesModelDiffCallback : DiffUtil.ItemCallback<ProfileActivitiesModel>() {
    override fun areItemsTheSame(
        oldItem: ProfileActivitiesModel,
        newItem: ProfileActivitiesModel
    ): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: ProfileActivitiesModel,
        newItem: ProfileActivitiesModel
    ): Boolean {
        return oldItem == newItem
    }
}