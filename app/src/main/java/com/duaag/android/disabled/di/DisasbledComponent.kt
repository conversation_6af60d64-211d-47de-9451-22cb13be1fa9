package com.duaag.android.disabled.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.disabled.DisabledActivity
import com.duaag.android.disabled.ui.DisabledProfileFragment
import com.duaag.android.disabled.ui.InappropriatePicturesFragment
import com.duaag.android.disabled.ui.WrongDataDisabledFragment
import com.duaag.android.disabled.ui.WrongProfileInformationFragment
import dagger.Subcomponent

// Definition of a Dagger subcomponent
@ActivityScope
@Subcomponent(modules = [DisabledViewModelModule::class])
interface DisabledComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): DisabledComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: DisabledActivity)
    fun inject(fragment: DisabledProfileFragment)
    fun inject(fragment: InappropriatePicturesFragment)
    fun inject(fragment: WrongProfileInformationFragment)
    fun inject(fragment: WrongDataDisabledFragment)

}