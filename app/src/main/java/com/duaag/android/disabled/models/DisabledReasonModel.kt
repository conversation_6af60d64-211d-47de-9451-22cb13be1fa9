package com.duaag.android.disabled.models

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import androidx.annotation.StringRes
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class DisabledReasonModel(val type: DisableUserType,
                               var isResolved: Boolean = false,
                               @StringRes val title: Int,
                               @StringRes val description: Int,
                               @DrawableRes val icon: Int): Parcelable
