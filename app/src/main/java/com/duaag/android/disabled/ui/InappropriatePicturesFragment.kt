package com.duaag.android.disabled.ui

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.base.fragment.ImagePickerFragment
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapRewardSourceValues
import com.duaag.android.clevertap.ClevertapRewardTypeValues
import com.duaag.android.clevertap.UploadImageStatusValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.InappropriatePicturesFragmentBinding
import com.duaag.android.disabled.DisabledActivity
import com.duaag.android.disabled.DisabledViewModel
import com.duaag.android.disabled.models.DisableUserType
import com.duaag.android.disabled.models.MarkReasonsAsResolvedRequestModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.image_verification.fragments.ImageDeniedFragment
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.DragCallback
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.NEW_IMAGES_REQUEST
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.REQUEST_CODE_PERMISSIONS
import com.duaag.android.manage_pictures.adapters.ProfileImagesAdapter
import com.duaag.android.manage_pictures.models.ProfileImageModel
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.fragment.guidelines.GuidelineItem
import com.duaag.android.signup.fragment.guidelines.GuidelinesAdapter
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.signup.models.VerifiedImageModel
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.utils.IMAGE_METADATA_TAG
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getPictureMetaData
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.openSettingsScreen
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.views.DragHandler
import com.duaag.android.views.ProgressDialog
import com.duaag.android.vision.ImageVisionInteractor
import com.duaag.android.vision.VisionImageProcessor
import com.duaag.android.vision.facedetector.FaceDetectorProcessor
import com.duaag.android.vision.textdetector.TextRecognitionProcessor
import com.google.android.material.tabs.TabLayoutMediator
import com.google.mlkit.vision.text.Text
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min

class InappropriatePicturesFragment : ImagePickerFragment(), ProfileImagesAdapter.UserPictureClickListener, DragHandler {

    companion object {
        fun newInstance() = InappropriatePicturesFragment()
    }

    private var _binding: InappropriatePicturesFragmentBinding? = null
    private val binding get() = _binding!!

    private val drawables by lazy {
        intArrayOf(
            R.drawable.first_page_first_cover,
            R.drawable.first_page_second_face,
            R.drawable.second_page_first_gun,
            R.drawable.second_page_second_nogun,
            R.drawable.third_page_first_nude,
            R.drawable.third_page_second_beach,
            R.drawable.fourth_page_first_celeb,
            R.drawable.fourth_page_second_real,
            R.drawable.fifth_page_first_baby,
            R.drawable.fifth_page_second_adult
        )
    }
    val items by lazy {
        listOf(
            GuidelineItem(getString(R.string.first_page_title), getString(R.string.first_page_guideline),
                drawables[0],
                drawables[1]),
            GuidelineItem(getString(R.string.second_page_title), getString(R.string.second_page_guideline),
                drawables[2],
                drawables[3]),
            GuidelineItem(getString(R.string.third_page_title), getString(R.string.third_guidelines),
                drawables[4],
                drawables[5]),
            GuidelineItem(getString(R.string.fourth_page_title), getString(
                R.string.fourth_guideline_an,getString(
                    R.string.dua_support)),
                drawables[6],
                drawables[7]),
            GuidelineItem(getString(R.string.fifth_page_title), getString(R.string.fifth_guideline),
                drawables[8],
                drawables[9])
        )
    }

    private val permissions = listOf(Manifest.permission.CAMERA)

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val disabledViewModel by viewModels<DisabledViewModel>({ activity as DisabledActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as DisabledActivity }) { viewModelFactory }
    private val profileImagesViewModel by viewModels<ProfileImagesViewModel>({ activity as DisabledActivity }) { viewModelFactory }
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel>({ activity as DisabledActivity }) { viewModelFactory }
    private var imageVisionInteractor: ImageVisionInteractor? = null
    private var faceDetectionCallback: FaceDetectorProcessor.FaceDetectionCallback? = null
    private var textDetectionCallback: TextRecognitionProcessor.TextDetectionCallback? = null

    private val itemTouchHelper by lazy { ItemTouchHelper(DragCallback(this)) }
    private var tabLayoutMediator: TabLayoutMediator? = null
    private var startItem = -1
    private var endItem = -1
    private lateinit var pickMedia: ActivityResultLauncher<PickVisualMediaRequest>
    var currentPictureMetadata: ImageMetaDataResultModel? = null
    private var uploadProgressDialog: ProgressDialog? = null

    private var guidelinesHeight = 0

    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as DisabledActivity).disabledComponent.inject(this)

        pickMedia = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            if (uri != null) {
                Timber.tag("IMAGE_PICKER").d("Selected URI: $uri")

                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), uri)
                }
                beginUCrop(uri)
            } else {
                Timber.tag("IMAGE_PICKER").d("No media selected")
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = InappropriatePicturesFragmentBinding.inflate(layoutInflater)

        sendScreenViewedAnalyticsEvent()

        imageVisionInteractor = createImageProcessors()

        val pager = binding.pagerGuidelines
        val adapter = GuidelinesAdapter(items, smallVersion = true)


        choosePicturesViewModel.imageCropped.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                handleCroppedImage(uri)
            }
        }

        choosePicturesViewModel.imageDetectionIsCompleted.observe(viewLifecycleOwner) {
            showUploadProgressDialog()
        }

        choosePicturesViewModel.imageChosen.observe(viewLifecycleOwner) { data ->
            val imageResultUri: Uri? = getPickImageResultUri(data)
            imageResultUri?.let {
                beginUCrop(imageResultUri)
                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), imageResultUri)
                }
            } ?: run {
                ToastUtil.toast(R.string.smthg_went_wrong)
            }
        }

        choosePicturesViewModel.imageVerified.observe(viewLifecycleOwner) { data ->
            sendUploadImageEvent(data)
        }

        choosePicturesViewModel.imageVerificationFailed.observe(viewLifecycleOwner) {
            val bundle = bundleOf(
                ImageDeniedFragment.ARG_REASON to it.invalidReason,
                ImageDeniedFragment.ARG_IMAGE_KEY to it.key
            )
            if(activity is SignUpActivity)
                findNavController().navigateSafer(R.id.action_uploadPhotosFragment_to_imageDeniedFragment, bundle)
            else
                findNavController().navigateSafer(R.id.action_choosePicturesBottomSheet2_to_imageDeniedFragment, bundle)
        }

        choosePicturesViewModel.uploadProgress.observe(viewLifecycleOwner) {
            uploadProgressDialog?.updateProgress(it)
            if (it == -1) {
                uploadProgressDialog?.dismiss()
            }
        }

        choosePicturesViewModel.showBadge2Dialog.observe(viewLifecycleOwner) {
            showBadge2LoseWarningDialog(
                it.verifiedImageModel,
                it.faceNotDetected,
                it.alreadyVerified
            )
        }

        choosePicturesViewModel.uploadFinished.observe(viewLifecycleOwner) {
            uploadProgressDialog?.dismiss()
        }

        choosePicturesViewModel.verifiedPictures.observe(viewLifecycleOwner) {
            handleVerificationSuccessful(
                it.map { it.key },
                choosePicturesViewModel.showBadge2Dialog.value != null
            )
        }

        profileImagesViewModel.photos.observe(viewLifecycleOwner) { list ->
            Timber.tag("PICTURESSS").d("photos:Size: ${list.size}")
            val mandatoryPictures = disabledViewModel.userModel?.disabledReasons?.filter { it.isMandatory }?.map { it.reason }
            list.forEach { it.isMandatory = mandatoryPictures?.contains(it.url) }
            (binding.recyclerProfile.adapter as ProfileImagesAdapter).betaSubmitList(list)
        }

        val user = profileImagesViewModel.user.value
        user?.let {
            val list = ArrayList<ProfileImageModel>()
            list.add(ProfileImageModel(it.profile.pictureUrl))
            it.profile.pictures.forEach {
                list.add(ProfileImageModel(it.url));
                Timber.tag("IMAGESTAG").d("onCreateView: ${it.url}")
            }
            profileImagesViewModel.setImages(list)
        }

        profileImagesViewModel.stateChanged.observe(viewLifecycleOwner) {
            checkStateChanged()
        }

        disabledViewModel.markedAsResolved.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }

        profileImagesViewModel.userUpdated.observe(viewLifecycleOwner) {
            syncPicturesInDisabledViewModel()
        }

        setupViewPager(adapter, pager)
        setupImagesRecyclerView()
        checkStateChanged()

        binding.expandBtn.setOnSingleClickListener{
            toggleGuidelines()
        }

        binding.doneBtn.setOnSingleClickListener{
            val reasons = disabledViewModel.userModel?.disabledReasons?.filter { it.category == DisableUserType.INAPPROPRIATE_PICTURES.type }

            val notMandatoryNotResolved = reasons?.filter{ !it.isMandatory && !it.isResolved }?.map { it.id } ?: emptyList()

            val profilePic = disabledViewModel.userModel?.profile?.pictureUrl
            val otherPictures = disabledViewModel.userModel?.profile?.pictures?.map { it.url }
            val userPictures = mutableListOf(profilePic, otherPictures)

            val thereAreMandatoryAndAreResolved = reasons?.filter { it.isMandatory }?.all { it.isResolved } ?: false

            val allMandatoryNotResolvedButDeleted = reasons
                ?.filter { it.isMandatory && !it.isResolved && !userPictures.contains(it.reason) }
                ?.map { it.id } ?: emptyList()

            val reasonIds = mutableListOf<Int>()
            reasonIds.addAll(allMandatoryNotResolvedButDeleted)
            reasonIds.addAll(notMandatoryNotResolved)

            if(thereAreMandatoryAndAreResolved || reasonIds.isEmpty()) {
                if (notMandatoryNotResolved.isEmpty()){
                    findNavController().popBackStack()
                    firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SAVE_CHANGES)
                }else {
                    val model = MarkReasonsAsResolvedRequestModel(notMandatoryNotResolved)
                    disabledViewModel.markReasonsAsResolved(model)
                    firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SAVE_CHANGES)
                }
            }else {
                showSaveChangesDialog(reasonIds)
            }
        }


        return binding.root
    }

    private fun sendScreenViewedAnalyticsEvent() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        sendClevertapEvent(ClevertapEventEnum.INAPPROPIATE_PICTURE_REVIEW, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
            ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.id
        ))
    }

    private fun handleCroppedImage(outputUri: Uri) {
        Timber.tag(IMAGE_METADATA_TAG).d("$currentPictureMetadata")
        choosePicturesViewModel.addNewImage(outputUri ,currentPictureMetadata)

        choosePicturesViewModel.addImageInFaceDetectionQueue(outputUri)

        choosePicturesViewModel.addImageInTextDetectionQueue(outputUri)
        imageVisionInteractor?.detectInImage(outputUri, requireActivity().contentResolver)

    }

    fun showBadge2LoseWarningDialog(
        verifiedImageModel: VerifiedImageModel,
        faceNotDetected: List<ImageResult>?,
        alreadyVerified: List<ImageResult>?,
    ) {
        uploadProgressDialog?.dismiss()
        val builder = AlertDialog.Builder(
            requireContext(),
            R.style.ThemeOverlay_MaterialComponents_Dialog_Alert
        )
        builder.apply {

            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle(resources.getString(R.string.warning_exclamation).replace("!", ""))
                .setMessage(resources.getString(R.string.warning_lose_badge_2_description))
                .setNegativeButton(resources.getString(R.string.continue_text)) { dialog, which ->
                    when {
                        verifiedImageModel.images.none { img -> !img.isValid } && faceNotDetected.isNullOrEmpty() -> {
                            //add new verified keys that came from api
                            val newImages =
                                verifiedImageModel.images.map { img -> img.key }.toMutableList()
                            alreadyVerified?.let { verified -> newImages.addAll(verified.map { it.key }) }

                            handleVerificationSuccessful(newImages, true)
                        }
                        else -> {
                            val allPictures = mutableListOf<ImageResult>()
                            verifiedImageModel.images.forEach { verified ->
                                verified.location =
                                    choosePicturesViewModel.selectedItems.value?.firstOrNull { it.s3Key == verified.key }?.url
                            }
                            allPictures.addAll(verifiedImageModel.images)
                            faceNotDetected?.let { allPictures.addAll(it) }
                        }
                    }
                    dialog.cancel()
                }
                .setPositiveButton(resources.getString(R.string.cancel)) { dialog, which ->
                    choosePicturesViewModel.removeVerifiedImages(verifiedImageModel.images.map { img -> img.key })
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.red_500
                    )
                )
                getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.purple_700
                    )
                )
            }
            setCanceledOnTouchOutside(false)
            setCancelable(false)

            show()
        }
    }

    private fun handleVerificationSuccessful(
        images: List<String>,
        willBadge2BeRemoved: Boolean = false,
    ) {
        if (images.isNotEmpty()) {
            profileImagesViewModel.setNewImages(images, willBadge2BeRemoved)
            profileImagesViewModel.checkStateChange()
        }
    }

    private fun setupViewPager(adapter: GuidelinesAdapter, pager: ViewPager2) {
        pager.adapter = adapter
        binding.tabLayout.let {
            pager.let { it1 ->
                tabLayoutMediator = TabLayoutMediator(it, it1) { _, _ ->}
                tabLayoutMediator?.attach()
            }
        }
    }

    private fun setupImagesRecyclerView(){
        binding.recyclerProfile.layoutManager = GridLayoutManager(context, 3, GridLayoutManager.VERTICAL, false)
        binding.recyclerProfile.adapter = ProfileImagesAdapter(clickListener = this,
            homeViewModel = homeViewModel,
            shouldShowPremiumScreen = profileImagesViewModel.user.value?.premiumType != null,
            showMaxItems = true)

        binding.recyclerProfile.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                Timber.tag("TOUCHEVENT").d("event.action: ACTION UP")
                if (endItem != -1 && startItem != -1 && !homeViewModel.isUserPremium())
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!(startItem < 3 && endItem < 3 || startItem >= 3 && endItem >= 3))
                            binding.recyclerProfile?.adapter?.notifyItemRangeChanged(startItem, endItem - startItem + 1)
                        startItem = -1
                        endItem = -1
                    }, 500)


            }
            false
        }
        with(receiver = itemTouchHelper, block = {
            attachToRecyclerView(binding.recyclerProfile)
        })
    }

    private fun toggleGuidelines(){
        val expanded = binding.picturesContainer.visibility == View.VISIBLE
        if(expanded){
            binding.picturesContainer.visibility = View.GONE
            binding.expandBtn.animate().rotation(0f).setDuration(200).start()
        }else {
            binding.picturesContainer.visibility = View.VISIBLE
            binding.expandBtn.animate().rotation(180f).setDuration(200).start()
            firebaseLogEvent(FirebaseAnalyticsEventsName.DISABLED_PROFILE_GUIDELINES)
        }

        //using constraint set method
//
//        if(expanded){
//            val constraintSet = ConstraintSet()
//            constraintSet.clone(binding.guidelinesContainer)
//            constraintSet.setVisibility(binding.picturesContainer.id, View.GONE)
//            val transition: Transition = ChangeBounds()
//            transition.interpolator = AnticipateOvershootInterpolator(1.0f)
//            transition.duration = 300
//            TransitionManager.beginDelayedTransition(binding.guidelinesContainer, transition)
//            constraintSet.applyTo(binding.guidelinesContainer)
//        }else {
//            val constraintSet = ConstraintSet()
//            constraintSet.clone(binding.guidelinesContainer)
//            constraintSet.setVisibility(binding.picturesContainer.id, View.VISIBLE)
//            val transition: Transition = ChangeBounds()
//            transition.interpolator = AnticipateOvershootInterpolator(1.0f)
//            transition.duration = 300
//            TransitionManager.beginDelayedTransition(binding.guidelinesContainer, transition)
//            constraintSet.applyTo(binding.guidelinesContainer)
//        }


        // the other method changing height of the container

//        val expanded = binding.picturesContainer.height >= 10
//        if(expanded){
//            val anim = ValueAnimator.ofInt(binding.guidelinesContainer.measuredHeight, 1)
//            anim.addUpdateListener { valueAnimator ->
//                val animatedValue = valueAnimator.animatedValue as Int
//                val layoutParams: ConstraintLayout.LayoutParams = binding.picturesContainer.layoutParams as ConstraintLayout.LayoutParams
//                layoutParams.height = animatedValue
//                binding.picturesContainer.layoutParams = layoutParams
//                binding.picturesContainer.requestLayout()
//            }
//            anim.duration = 300
//            anim.doOnEnd {
//                binding.picturesContainer.visibility = View.GONE
//            }
//            anim.start()
//        }else {
//            val anim = ValueAnimator.ofInt(1, guidelinesHeight)
//            anim.addUpdateListener { valueAnimator ->
//                val animatedValue = valueAnimator.animatedValue as Int
//                val layoutParams: ConstraintLayout.LayoutParams = binding.picturesContainer.layoutParams as ConstraintLayout.LayoutParams
//                layoutParams.height = animatedValue
//                binding.picturesContainer.layoutParams = layoutParams
//                binding.picturesContainer.requestLayout()
//            }
//            anim.duration = 300
//            anim.doOnEnd {
//                binding.picturesContainer.visibility = View.VISIBLE
//            }
//            anim.start()
//        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.pagerGuidelines.adapter = null
        _binding = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null

        imageVisionInteractor?.stopImageProcessors()
        imageVisionInteractor = null
        uploadProgressDialog?.dismiss()
        uploadProgressDialog = null
    }

    private fun sendUploadImageEvent(verified: BooleanArray) {
        if(activity is ManagePicturesActivity) {
            val eventPremiumType = getPremiumTypeEventProperty(choosePicturesViewModel.user.value)

            verified.forEach {
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.UPLOAD_IMAGE, mapOf(
                        FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                        FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                        FirebaseAnalyticsParameterName.UPLOAD_IMAGE_STATUS.value to if(it) UploadImageStatusValues.GUIDELINES_MET.value else UploadImageStatusValues.GUIDELINES_NOT_MET.value,
                        FirebaseAnalyticsParameterName.UPLOAD_IMAGE_SOURCE.value to (activity as ManagePicturesActivity).intent.getStringExtra(
                            ManagePicturesActivity.UPLOAD_IMAGE_SOURCE
                        ),
                    )
                )

                sendClevertapEvent(
                    ClevertapEventEnum.UPLOAD_IMAGE, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.UPLOAD_IMAGE_STATUS.propertyName to if(it) UploadImageStatusValues.GUIDELINES_MET.value else UploadImageStatusValues.GUIDELINES_NOT_MET.value,
                        ClevertapEventPropertyEnum.UPLOAD_IMAGE_SOURCE.propertyName to (activity as ManagePicturesActivity).intent.getStringExtra(
                            ManagePicturesActivity.UPLOAD_IMAGE_SOURCE
                        ),
                    )
                )
            }
        }
    }

    private fun checkStateChanged(){
        val reasons = disabledViewModel.userModel?.disabledReasons?.filter { it.category == DisableUserType.INAPPROPRIATE_PICTURES.type }

        val profilePic = profileImagesViewModel.user.value?.profile?.pictureUrl
        val otherPictures = profileImagesViewModel.user.value?.profile?.pictures?.map { it.url }?.toList() ?: emptyList()
        val userPictures = mutableListOf(profilePic)
        userPictures.addAll(otherPictures)

        val areThereAnyNotMandatoryNotResolved = reasons?.any { !it.isMandatory && !it.isResolved } ?: false

        val mandatoryImages = reasons?.filter { it.isMandatory }?: emptyList()

        val mandatoryNotResolvedImages = mandatoryImages.filter { !it.isResolved }.map { it.reason }

        val areAllMandatoryResolved = mandatoryImages.all { it.isResolved }

        val areAllMandatoryNotResolvedPicturesDeleted =
            if (mandatoryNotResolvedImages.isNullOrEmpty()) {
                false
            } else {
                mandatoryNotResolvedImages.all { !userPictures.contains(it) }
            }


        val shouldEnableDoneBtn = areAllMandatoryResolved
                || areAllMandatoryNotResolvedPicturesDeleted
                || ((areAllMandatoryResolved || areAllMandatoryNotResolvedPicturesDeleted) && areThereAnyNotMandatoryNotResolved)

        binding.doneBtn.isEnabled = shouldEnableDoneBtn
    }

    override fun onDeleteButtonClicked(position: Int) {
        if (NetworkChecker.isNetworkConnected(requireContext())) {
            if (position == 0 && profileImagesViewModel.getValidImagesNumber() == 1) {
                createDeleteLastPhotoDialog()
            } else {
                createDeletePictureDialog(position)
            }
        } else {
            ToastUtil.toast(getString(R.string.no_internet_string))
        }
    }

    private fun createDeleteLastPhotoDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)
        builder.apply {

            setTitle("Delete last photo")
                .setMessage("Do you want to delete your last photo")
                .setNegativeButton(resources.getString(R.string.cancel)) { dialog, _ ->
                    dialog.cancel()
                }
                .setPositiveButton(resources.getString(R.string.ok_dialog)) { dialog, _ ->
                    profileImagesViewModel.deletedLastPhoto = true
                    dialog.cancel()
                    showPhotoSourceDialog()
                }

        }
        return builder.create().run { show() }
    }

    private fun showSaveChangesDialog(reasonIds: List<Int>){
        val builder = AlertDialog.Builder(requireContext(),R.style.AlertDialogButtonTheme)
        builder.setTitle(getString(R.string.confirm_changes_title))
        builder.setMessage(R.string.confirm_changes_desc_inappropriate_pictures)
        builder.setPositiveButton(R.string.confirm_option) { dialogInterface, i ->
            val model = MarkReasonsAsResolvedRequestModel(reasonIds.distinct())
            disabledViewModel.markReasonsAsResolved(model)

            firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SAVE_CHANGES)

            val user = disabledViewModel.checkEnableUserObserver.value
            val eventPremiumType = getPremiumTypeEventProperty(user)

            sendClevertapEvent(ClevertapEventEnum.INAPPROPIATE_PICTURE_CHANGED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to user?.communityInfo?.id
            ))
        }
        builder.setNegativeButton(R.string.cancel, null)

        val dialog = builder.create()
        dialog.show()
    }

    private fun createDeletePictureDialog(position: Int) {
        val builder = AlertDialog.Builder(requireContext())
        builder.apply {
            setTitle(getString(R.string.delete_pic))
            setMessage(getString(R.string.are_sure_you))
            setNegativeButton(getString(R.string.cancel)) { dialog, _ -> dialog.dismiss() }
            setPositiveButton(getString(R.string.ok_dialog)) { _, _ ->

                //if clicked delete first picture (profile picture) and there are other pictures
                //besides that make the second picture profile pic and
                if (position == 0 && profileImagesViewModel.getValidImagesNumber() > 1) {
                    val images = profileImagesViewModel.getUserImages()
                    val secondImage = images[1].url
                    secondImage?.let {
                        profileImagesViewModel.setProfilePicture(it).observe(viewLifecycleOwner) {
                            when (it) {
                                is Result.Success -> {
                                    deleteImage(deleteAPIPosition = 1, removeUIPosition = 0)
                                }
                                is Result.Error -> {
                                    ToastUtil.toast("An Error Occurred")
                                }
                                else -> {}
                            }
                        }

                    }
                } else if (position == 0 && profileImagesViewModel.getValidImagesNumber() == 1) {
                    createDeleteLastPhotoDialog()
                } else {
                    deleteImage(position, position)
                }
            }
        }
        val dialog = builder.create()
        dialog.setOnShowListener {
            dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.description_primary))
        }
        dialog.show()
    }

    private fun deleteImage(deleteAPIPosition: Int, removeUIPosition: Int) {
        val model = profileImagesViewModel.getImageFromPosition(deleteAPIPosition)
        profileImagesViewModel.removePhoto(removeUIPosition)
        profileImagesViewModel.deleteImage(deleteAPIPosition, model)
    }

    override fun onAddButtonClicked(position: Int) {
        if (position >= ChoosePictureViewModel.MAX_IMAGES_FREEMIUM)
            if (profileImagesViewModel.photos.value!!.size > profileImagesViewModel.getUploadMaximumImagesCount()) {

                firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_PHOTOS)

                requireActivity().openPremiumPaywall(
                    viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.UPLOAD_MORE_PICTURES,
                    eventSourceClevertap = ClevertapEventSourceValues.PHOTOS,
                    placementId = PurchaselyPlacement.UPLOAD_PLUS_3_PHOTOS.id,
                    userModel = homeViewModel.userProfile.value
                )
                return
            }
        showPhotoSourceDialog()
    }

    override fun onDataDraged(from: Int, to: Int) {
        profileImagesViewModel.swapItemPositions(from, to)
        profileImagesViewModel.updateImages(profileImagesViewModel.getUserImages().map { it.url!! })
        profileImagesViewModel.checkStateChange()

        val start = min(from, to)
        val end = max(from, to)

        if (start < startItem || startItem == -1) startItem = start
        if (end > endItem || endItem == -1) endItem = end
    }

    fun showPhotoSourceDialog() {
        val builder = AlertDialog.Builder(requireContext())

        val galleryItem = getString(R.string.upload_photos)
        val cameraItem = getString(R.string.take_photo)

        val list =arrayOf(galleryItem, cameraItem)

        builder.setItems(list) { _, which ->
            when (which) {
                0 -> {
                    openImagePicker()
                }
                1 -> {
                    onCameraItemClick()
                }
            }
        }

        val dialog = builder.create()
        dialog.show()
    }

    override fun openImagePicker() {
        pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
    }

    fun onCameraItemClick() {
        if (choosePicturesViewModel.selectedItemSize() < choosePicturesViewModel.getUploadMaximumImagesCount()) {
            checkPermissionForImages()
        } else {
            openPremiumActivity()

            Toast.makeText(
                context,
                getString(
                    R.string.cant_more_than_,
                    choosePicturesViewModel.getUploadMaximumImagesCount().toString()
                ),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun checkPermissionForImages() {
        val resultCamera = ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.CAMERA
        )

        when {
            ( resultCamera == PackageManager.PERMISSION_GRANTED) -> {
                openImagePicker(true)
            }
            (shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) -> {
                showSettingsPermissionsDialog(getString(R.string.allow_gallery_camera))
            }
            else -> {
                requireActivity().requestPermissions(permissions.toTypedArray(), REQUEST_CODE_PERMISSIONS)
            }
        }
    }

    private fun showSettingsPermissionsDialog(description: String) {
        val builder = AlertDialog.Builder(requireContext())
            .setMessage(description)
            .setPositiveButton(R.string.settings) { _, _ ->
                try {
                    openSettingsScreen(requireContext())
                } catch (e: java.lang.Exception) {
                    Timber.tag("error").d(e.toString())
                }
            }
            .setNegativeButton(R.string.cancel) { _, _ -> }
            .setCancelable(false)

        val dialog = builder.create()
        dialog.setOnShowListener {
            dialog.getButton(AlertDialog.BUTTON_POSITIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_500))
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.red_500))
        }
        dialog.show()
    }

    private fun openPremiumActivity() {
        val featureRequiresPremiumPaywall = if (requireActivity() !is ManagePicturesActivity) {
            false
        } else {
            (requireActivity() as ManagePicturesActivity).featureRequiresPremium
        }

        if (DuaApplication.instance.getBillingAvailable() &&
            DuaApplication.instance.shouldShowPremium &&
            featureRequiresPremiumPaywall
        ) {
            requireActivity().openPremiumPaywall(
                viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.UPLOAD_MORE_PICTURES,
                eventSourceClevertap = ClevertapEventSourceValues.PHOTOS,
                placementId = PurchaselyPlacement.UPLOAD_PLUS_3_PHOTOS.id,
                userModel = choosePicturesViewModel.user.value
            )
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == NEW_IMAGES_REQUEST) {
            if (resultCode == Activity.RESULT_OK) {
                val imageUrls = data?.getStringArrayListExtra(ManagePicturesActivity.IMAGES_EXTRA)
                val willBadge2BeRemoved = data?.getBooleanExtra(ManagePicturesActivity.WILL_BADGE2_BE_REMOVED, false) ?: false

                if (!imageUrls.isNullOrEmpty()) {
                    profileImagesViewModel.setNewImages(imageUrls, willBadge2BeRemoved)
                    profileImagesViewModel.checkStateChange()
                }
            }
        }
    }

    private fun createImageProcessors(): ImageVisionInteractor? {
        faceDetectionCallback = object : FaceDetectorProcessor.FaceDetectionCallback {
            override fun onSuccess(faceDetected: Boolean) {
                Timber.tag("TAG").d("onSuccess: faceDetected $faceDetected")
                choosePicturesViewModel.setFaceDetected(faceDetected)
            }

            override fun onError(exception: Exception) {
                exception.printStackTrace()

                choosePicturesViewModel.removeLastItemFromFaceDetectionQueue()
                choosePicturesViewModel.checkIfImageDetectionIsCompleted()
            }
        }

        textDetectionCallback = object : TextRecognitionProcessor.TextDetectionCallback {
            override fun onSuccess(text: Text) {
                Timber.tag("TAG").d("onSuccess: textDetected ${text.text}")
                choosePicturesViewModel.setTextDetected(text)
            }

            override fun onError(exception: Exception) {
                exception.printStackTrace()

                choosePicturesViewModel.removeLastItemFromTextDetectionQueue()
                choosePicturesViewModel.checkIfImageDetectionIsCompleted()
            }
        }

        val imageProcessors = mutableListOf<VisionImageProcessor>()
        try {

            //Face Detection
            val faceDetector = FaceDetectorProcessor(requireContext(), null, faceDetectionCallback!!)
            imageProcessors.add(faceDetector)

            //Text Detection
            val textDetector = TextRecognitionProcessor(requireContext(), textDetectionCallback!!)
            imageProcessors.add(textDetector)
        } catch (e: Exception) {
            Timber.tag("TAG").e(e, "Can not create image processor: ${e.message}")
            return null
        }

        return ImageVisionInteractor(imageProcessors)
    }

    private fun showUploadProgressDialog() {
        uploadProgressDialog = ProgressDialog(requireContext(), resources.getString(R.string.uploading_images))
        uploadProgressDialog?.show()
        choosePicturesViewModel.uploadSelectedImages()


        firebaseLogEvent(FirebaseAnalyticsEventsName.UPLOADING_IMAGES_LOADER)
    }

    private fun syncPicturesInDisabledViewModel(){
        val disabledViewModelUser = disabledViewModel.userModel
        val profileImagesUserViewModel = profileImagesViewModel.user.value

        profileImagesUserViewModel?.profile?.pictureUrl?.let { disabledViewModelUser?.profile?.pictureUrl = it }
        profileImagesUserViewModel?.profile?.pictures?.let { disabledViewModelUser?.profile?.pictures = it }
    }
}