package com.duaag.android.disabled.ui

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.DisabledProfileFragmentBinding
import com.duaag.android.disabled.DisabledActivity
import com.duaag.android.disabled.DisabledReasonAdapter
import com.duaag.android.disabled.DisabledViewModel
import com.duaag.android.disabled.models.DisableUserType
import com.duaag.android.disabled.models.DisabledReasonModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.setOnSingleClickListener
import javax.inject.Inject

class DisabledProfileFragment : Fragment() {

    companion object {
        fun newInstance() = DisabledProfileFragment()
    }

    private var _binding: DisabledProfileFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val disabledViewModel by viewModels<DisabledViewModel>({ activity as DisabledActivity }) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as DisabledActivity).disabledComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DisabledProfileFragmentBinding.inflate(layoutInflater)

        val adapter = DisabledReasonAdapter(object : DisabledReasonAdapter.DisabledReasonClickListener{
            override fun onReasonClicked(item: DisabledReasonModel) {
                when (item.type) {
                    DisableUserType.INAPPROPRIATE_PICTURES -> {
                        findNavController().navigate(R.id.action_disabledProfileFragment_to_inappropriatePicturesFragment)
                        firebaseLogEvent(FirebaseAnalyticsEventsName.DP_INAPPROPRIATE_PICTURES_INITIATE)
                    }
                    DisableUserType.WRONG_PROFILE_INFORMATION -> {
                        findNavController().navigate(R.id.action_disabledProfileFragment_to_wrongProfileInformationFragment)
                        firebaseLogEvent(FirebaseAnalyticsEventsName.DP_WRONG_PROFILE_INFORMATION_INITIATE)
                    }
                    DisableUserType.WRONG_DATA -> {
                        findNavController().navigate(R.id.action_disabledProfileFragment_to_wrongDataDisabledFragment)
                        firebaseLogEvent(FirebaseAnalyticsEventsName.DP_WRONG_DATA_INITIATE)

                    }

                    DisableUserType.COMMUNITY_NOT_SUPPORTED -> {

                    }
                }
            }
        })

        binding.disabledReasons.layoutManager = LinearLayoutManager(requireContext())
        binding.disabledReasons.adapter = adapter

        disabledViewModel.reasons.observe(viewLifecycleOwner) {
            checkForResolvedReasons()
            adapter.setData(it)
        }

        disabledViewModel.sentRequestForReview.observe(viewLifecycleOwner) {
            requireActivity().finish()
        }
        binding.submitReviewButton.setOnSingleClickListener{
            disabledViewModel.submitForReview()
        }

        binding.cancelBtn.setOnSingleClickListener{
            firebaseLogEvent(FirebaseAnalyticsEventsName.DISABLED_PROFILE_CANCEL)

            val user = disabledViewModel.checkEnableUserObserver.value
            val eventPremiumType = getPremiumTypeEventProperty(user)

            sendClevertapEvent(ClevertapEventEnum.CANCEL_REVIEW_INITIATED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to user?.communityInfo?.id
            ))

            requireActivity().finish()
        }

        return binding.root
    }

    private fun checkForResolvedReasons(){
        binding.submitReviewButton.isEnabled = disabledViewModel.reasons.value?.all { it.isResolved } == true
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}