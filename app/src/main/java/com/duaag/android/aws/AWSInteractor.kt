package com.duaag.android.aws

import android.content.Context
import androidx.work.*
import com.amazonaws.auth.AWSCredentials
import com.amazonaws.internal.keyvaluestore.AWSKeyValueStore
import com.amazonaws.mobile.client.AWSMobileClient
import com.amazonaws.mobile.client.Callback
import com.amazonaws.mobile.client.UserState
import com.amazonaws.mobile.client.UserStateDetails
import com.amazonaws.mobile.client.results.*
import com.amazonaws.mobile.config.AWSConfiguration
import com.amazonaws.mobileconnectors.cognitoidentityprovider.util.CognitoJWTParser
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferNetworkLossHandler
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.duaag.android.BuildConfig
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws.models.*
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.ToastUtil
import kotlinx.coroutines.*
import org.json.JSONArray
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine


object AWSInteractor {

    const val AWS_TAG = "AWSInteractor"
    const val S3_TAG = "S3TAG"


    private val serviceJob = SupervisorJob()
    private val coroutineScope = CoroutineScope(Dispatchers.IO + serviceJob)

    private val mobileClient = AWSMobileClient.getInstance()

    fun initializeSilently(context: Context) {
        val config = getConfiguration(context)
        AWSMobileClient
            .getInstance()
            .initialize(context, config, object : Callback<UserStateDetails> {
                override fun onResult(userStateDetails: UserStateDetails) {
                    Timber.tag(AWS_TAG).d("User State ${userStateDetails.userState}")
                    when (userStateDetails.userState) {
                        UserState.SIGNED_IN -> {
                            Timber.tag(AWS_TAG).d("Logged IN")

                            setIfUserIsDisabled()

                            initRefreshTokenWorker()

                            checkIfIsCredentialUser()
                        }
                        else -> {
                            Timber.tag(AWS_TAG).d("Logged OUT")
                        }
                    }
                }

                override fun onError(e: Exception) {
                    e.printStackTrace()
                }
            })
    }

    fun initializeAWS(context: Context, listener: SplashActivity.SplashInterface) {
        mobileClient
            .initialize(context, object : Callback<UserStateDetails> {
                override fun onResult(userStateDetails: UserStateDetails) {
                    Timber.tag(AWS_TAG).d("User State ${userStateDetails.userState}")
                    when (userStateDetails.userState) {
                        UserState.SIGNED_IN -> {
                            Timber.tag(AWS_TAG).d("Logged IN")
                            setIfUserIsDisabled()
                            checkIfIsCredentialUser()
                        }
                        else -> {}
                    }
                    listener.onUserState(userStateDetails.userState)
                }

                override fun onError(e: Exception) {
                    Timber.tag(AWS_TAG).d(e.toString())
                    listener.onError(e)
                }
            })
    }


    private fun setIfUserIsDisabled() {
        coroutineScope.launch {
            try {
                val token = getToken()
                DuaApplication.instance.isLoggedInUserDisabled =
                    (CognitoJWTParser.getPayload(token)["cognito:groups"] as JSONArray).toString()
                        .contains("DisabledUsers")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun checkIfIsCredentialUser() {
        coroutineScope.launch {
            try {
                mobileClient.getUserAttributes(object: Callback<MutableMap<String, String>> {
                    override fun onResult(result: MutableMap<String, String>?) {
                        if (result == null) return

                        if (result.containsKey("custom:IsCredentialUser")) {
                            DuaApplication.instance.isCredentialUser =
                                result.getValue("custom:IsCredentialUser").toBoolean()

                            if (result.containsKey("phone_number")) {
                                DuaApplication.instance.userEmailOrPhone = "phone_number"
                                DuaApplication.instance.userEmailOrPhoneValue = result["phone_number"]
                            } else if (result.containsKey("email")) {
                                DuaApplication.instance.userEmailOrPhone = "email"
                                DuaApplication.instance.userEmailOrPhoneValue = result["email"]
                            } else {
                                DuaApplication.instance.userEmailOrPhone = null
                                DuaApplication.instance.userEmailOrPhoneValue = null
                            }

                        } else {
                            DuaApplication.instance.isCredentialUser = null
                        }
                    }

                    override fun onError(e: java.lang.Exception?) {
                        e?.printStackTrace()
                    }
                } )

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun getConfiguration(context: Context): AWSConfiguration {
        val config = AWSConfiguration(context)
        config.configuration = when (BuildConfig.FLAVOR_environment) {
            "qa" -> "qa"
            "dev" -> "dev"
            else -> "prod"
        }
        return config
    }

    private suspend fun getCSIAccessTokenKey(): String{
        val appClientId = mobileClient.configuration.optJsonObject("CognitoUserPool").getString("AppClientId")
        val key = "CognitoIdentityProvider.$appClientId.${mobileClient.username}.accessToken"
        return key
    }

    suspend fun refresh() {
        withContext(Dispatchers.IO) {
            try {
                val sharedPreferencesName = "CognitoIdentityProviderCache"
                val keyStore = AWSKeyValueStore(DuaApplication.instance.applicationContext,sharedPreferencesName, true)
                keyStore.remove(getCSIAccessTokenKey())
                //Recall the getToken method to get the new token
                getToken()
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    @Throws(IllegalStateException::class)
    suspend fun getToken(): String = suspendCoroutine { continuation ->
        val startTime = System.currentTimeMillis()
        val callback = object : Callback<Tokens> {
            override fun onResult(result: Tokens?) {
                result?.let {
                    Timber.tag("TOKEN_AUTH").e("Token received - ${System.currentTimeMillis() - startTime}ms")
                    continuation.resume(it.idToken.tokenString)
                } ?: continuation.resumeWithException(IllegalStateException("Token retrieval failed"))
            }

            override fun onError(e: Exception?) {
                Timber.tag("TOKEN_AUTH").e("Token retrieval failed - ${System.currentTimeMillis() - startTime}ms")
                continuation.resumeWithException(e ?: IllegalStateException("Token retrieval failed"))
            }
        }

        AWSMobileClient.getInstance().getTokens(callback)
    }

    fun getEmail(): String {
        return mobileClient.username
    }

    fun isSignedIn(): Boolean {
        return mobileClient.isSignedIn
    }

    fun getIdentityId(): String {
        return mobileClient.identityId
    }

    fun getCredentials(): AWSCredentials {
        return mobileClient.credentials
    }

    fun logIn(loginModel: LoginModel, callBack: LogInCallBack) {
        mobileClient
            .signIn(
                loginModel.emailAddress,
                loginModel.password,
                null,
                object : Callback<SignInResult> {
                    override fun onResult(signInResult: SignInResult) {
                        Timber.tag(AWS_TAG).d("Sign-in callback state: ${signInResult.signInState}")
                        setIfUserIsDisabled()
                        checkIfIsCredentialUser()
                        callBack.onResult(signInResult)
                    }

                    override fun onError(e: Exception) {
                        Timber.tag(AWS_TAG).d("Sign-in error")
                        callBack.onError(e)
                    }
                })
    }

    fun fetchUserInfo(listener: FetchUserInfoListener) {
        mobileClient.getUserAttributes(object : Callback<Map<String, String>> {
            override fun onResult(result: Map<String, String>?) {
                result?.let {
//                        val name = result["name"] ?: ""
                    val email = result["email"]
                    val cognitoID = result["sub"] ?: ""

                    listener.onResult(cognitoID, email)
                }
            }

            override fun onError(e: java.lang.Exception?) {
                e?.printStackTrace()
                listener.onError(e)
            }
        })
    }


    fun confirmLogIn(newPassword: String) {
        mobileClient
            .confirmSignIn(newPassword, object : Callback<SignInResult> {
                override fun onResult(signInResult: SignInResult) {
                    Timber.tag(AWS_TAG).d("Sign-in callback state: ${signInResult.signInState}")
                    when (signInResult.signInState) {
                        SignInState.DONE -> ToastUtil.toast("Sign-in done.")
                        SignInState.SMS_MFA -> ToastUtil.toast(
                            "Please confirm sign-in with SMS."
                        )
                        else -> ToastUtil.toast("Unsupported sign-in confirmation: " + signInResult.signInState)
                    }
                }

                override fun onError(e: Exception) {
                    Timber.tag(AWS_TAG).d("Sign-in error")
                }
            })
    }

    fun logOut() {
        mobileClient.signOut()
    }


    fun signUp(model: SignUpModel, callback: SignUpCallback) {
        val attributes = HashMap<String, String>()
        val appVersion = BuildConfig.VERSION_NAME.substringBefore("-")
        val os = "android"
        val language = ModifiedLingver.getInstance().getLanguage()
        val deviceId = model.deviceId
        val clientMetadata = hashMapOf(
            "appVersion" to appVersion,
            "os" to os,
            "language" to language,
            "reCaptchaToken" to model.reCaptchaToken,
            "deviceId" to deviceId
        )
        Timber.tag("signuplog")
            .d("appVersion  $appVersion os  $os language to $language  deviceId to $deviceId")
        when (model.authMethod) {
            AuthMethod.EMAIL -> attributes["email"] = model.phoneEmail
            AuthMethod.PHONE -> attributes["phone_number"] = model.phoneEmail
            else -> {
            }
        }

        mobileClient
            .signUp(
                model.username,
                model.password,
                attributes,
                null,
                clientMetadata,
                object : Callback<SignUpResult> {
                    override fun onResult(signUpResult: SignUpResult) {
                        Timber.tag(AWS_TAG)
                            .d("Sign-up callback state: $signUpResult.confirmationState")
                        callback.onResult(signUpResult)
                    }

                    override fun onError(e: Exception) {
                        Timber.tag(AWS_TAG).d(e.message)
                        callback.onError(e)
                    }
                })
    }

    fun confirmSignUp(model: VerifyAccountModel, callback: SignUpCallback) {
        mobileClient
            .confirmSignUp(model.username, model.code, object : Callback<SignUpResult> {
                override fun onResult(signUpResult: SignUpResult) {
                    Timber.tag(AWS_TAG)
                        .d("Sign-up callback state: ${signUpResult.confirmationState}")
                    callback.onResult(signUpResult)
                }

                override fun onError(e: Exception) {
                    Timber.tag(AWS_TAG).d(e.message)
                    callback.onError(e)
                }
            })
    }

    fun resendConfirmCode(
        reCaptchaToken: String,
        deviceId: String,
        userName: String,
        callback: ResendSignUpCallback
    ) {
        val appVersion = BuildConfig.VERSION_NAME.substringBefore("-")
        val os = "android"
        val language = ModifiedLingver.getInstance().getLanguage()
        val clientMetadata = hashMapOf(
            "appVersion" to appVersion,
            "os" to os,
            "language" to language,
            "reCaptchaToken" to reCaptchaToken,
            "deviceId" to deviceId
        )
        mobileClient
            .resendSignUp(userName, clientMetadata, object : Callback<SignUpResult> {
                override fun onResult(signUpResult: SignUpResult) {
                    Timber.tag(AWS_TAG).d(
                        "A verification code has been sent via ${signUpResult.userCodeDeliveryDetails.deliveryMedium} at ${signUpResult.userCodeDeliveryDetails.destination}"
                    )
                    callback.onResult(signUpResult)
                }

                override fun onError(e: Exception) {
                    e.printStackTrace()
                    callback.onError(e)
                }
            })
    }


    fun forgotPassword(
        deviceId: String,
        model: ForgotPasswordModel,
        callback: ForgotPasswordCallBack
    ) {
        val attributes = HashMap<String, String>()
        val appVersion = BuildConfig.VERSION_NAME.substringBefore("-")
        val os = "android"
        val language = ModifiedLingver.getInstance().getLanguage()
        val clientMetadata = hashMapOf(
            "appVersion" to appVersion,
            "os" to os,
            "language" to language,
            "reCaptchaToken" to model.reCaptchaToken,
            "deviceId" to deviceId
        )
        if ((model.authMethod == AuthMethod.PHONE)) {
            clientMetadata["isPhone"] = "true"
        }
        when (model.authMethod) {
            AuthMethod.EMAIL -> attributes["email"] = model.phoneEmail
            AuthMethod.PHONE -> attributes["phone_number"] = model.phoneEmail
            else -> {}
        }
        mobileClient.forgotPassword(
            model.phoneEmail,
            clientMetadata,
            object : Callback<ForgotPasswordResult> {
                override fun onResult(result: ForgotPasswordResult) {
                    callback.onResult(result)
                }

                override fun onError(e: Exception) {
                    callback.onError(e)
                }
            })
    }

    fun confirmForgotPassword(
        model: ConfirmForgotPasswordModel,
        callback: ForgotPasswordCallBack
    ) {
        mobileClient
            .confirmForgotPassword(
                model.newPassword,
                model.code,
                object : Callback<ForgotPasswordResult> {
                    override fun onResult(result: ForgotPasswordResult) {
                        callback.onResult(result)
                    }

                    override fun onError(e: Exception) {
                        callback.onError(e)
                    }
                })
    }

    fun resetPassword(model: ChangePasswordModel, callback: ChangePasswordCallBack) {
        mobileClient
            .changePassword(model.oldPassword, model.newPassword, object : Callback<Void> {
                override fun onResult(result: Void?) {
                    callback.onResult(result)
                }

                override fun onError(e: Exception) {
                    callback.onError(e)
                }

            })
    }


    fun getResourceUrl(randomUUID: String, fileExtension: String?): String {
        val extension = if (fileExtension.isNullOrEmpty()) ".jpg" else ".$fileExtension"
        val fileName = "$randomUUID${extension}"
        val url = "protected/${getIdentityId()}/$fileName"

        Timber.tag(S3_TAG).d(url)
        return url
    }

    fun uploadFile(file: File, fileName: String, transferListener: TransferListener) {
        TransferNetworkLossHandler.getInstance(DuaApplication.instance)

        val transferUtility = TransferUtility.builder()
            .context(DuaApplication.instance.applicationContext)
            .awsConfiguration(mobileClient.configuration)
            .s3Client(AmazonS3Client(getCredentials(), Region.getRegion(Regions.EU_WEST_1)))
            .build()

        val uploadObserver = transferUtility.upload(fileName, file)
        uploadObserver.setTransferListener(transferListener)
    }

    fun initRefreshTokenWorker() {
        val constraints = Constraints.Builder().setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        val myWork = PeriodicWorkRequestBuilder<RefreshTokenWorker>(6, TimeUnit.HOURS)
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                30,
                TimeUnit.MINUTES
            )
            .build()
        WorkManager.getInstance().enqueueUniquePeriodicWork(
            RefreshTokenWorker.TAG,
            ExistingPeriodicWorkPolicy.KEEP,
            myWork
        )
    }
}


interface SignUpCallback {
    fun onResult(signUpResult: SignUpResult)
    fun onError(e: Exception)
}

interface ResendSignUpCallback {
    fun onResult(resendSignUpResult: SignUpResult)
    fun onError(e: Exception)
}

interface LogInCallBack {
    fun onResult(signInResult: SignInResult)
    fun onError(e: Exception)
}

interface ForgotPasswordCallBack {
    fun onResult(forgotResult: ForgotPasswordResult)
    fun onError(e: Exception)
}

interface ChangePasswordCallBack {
    fun onResult(changeResult: Void?)
    fun onError(e: Exception)
}

interface FetchUserInfoListener {
    fun onResult(cognitoId: String, email: String?)
    fun onError(e: java.lang.Exception?)
}
