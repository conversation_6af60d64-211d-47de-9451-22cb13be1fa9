package com.duaag.android.image_verification.fragments

import android.Manifest
import android.app.Activity.RESULT_OK
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.MimeTypeMap
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws_liveness.domain.model.LivenessUiData
import com.duaag.android.aws_liveness.presentation.dialog.LivenessImageUploadSuccessDialog
import com.duaag.android.aws_liveness.presentation.dialog.LivenessVerificationFailedDialog
import com.duaag.android.aws_liveness.presentation.fragment.ImageDeniedFragmentLiveness
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendChangeProfilePhotoEvent
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentVerifyProfileWithBadge2PopUpBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getPictureMetaData
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.views.ProgressDialog
import com.duaag.android.vision.ImageVisionInteractor
import com.duaag.android.vision.VisionImageProcessor
import com.duaag.android.vision.facedetector.FaceDetectorProcessor
import com.duaag.android.vision.textdetector.TextRecognitionProcessor
import com.google.mlkit.vision.text.Text
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.util.UUID
import javax.inject.Inject

class VerifyProfileWithBadge2PopUp : Fragment() {


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel: LivenessViewModel by viewModels<LivenessViewModel>({
        when (val act = activity) {
            is HomeActivity -> act
            is SignUpActivity -> act
            else -> throw IllegalStateException("Unknown Activity")
        }
    }) {
        viewModelFactory
    }
    private val choosePictureViewModel: ChoosePictureViewModel by viewModels<ChoosePictureViewModel> { viewModelFactory }
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({  activity as SignUpActivity }) { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private var _binding: FragmentVerifyProfileWithBadge2PopUpBinding? = null
    private val binding get() = _binding!!

    lateinit var verificationSource: String
    var shouldCallSkipImagerVerificationApi: Boolean = false
    private var currentPictureMetadata: ImageMetaDataResultModel? = null
    private var imageVisionInteractor: ImageVisionInteractor? = null
    private var faceDetectionCallback: FaceDetectorProcessor.FaceDetectionCallback? = null
    private var textDetectionCallback: TextRecognitionProcessor.TextDetectionCallback? = null
    private var uploadProgressDialog: ProgressDialog? = null

    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            sendPermissionCameraToUseAllowed()

            onPermissionGranted()
        } else {
            handlePermissionDenied(requireActivity())
        }
    }

    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            if (uri != null) {
                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), uri)
                }
                beginUCrop(uri)
            } else {
                Timber.tag("PhotoPicker").d("No media selected")
            }
        }


    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (val activity = requireActivity()) {
            is HomeActivity -> activity.homeComponent.inject(this)
            is SignUpActivity -> activity.signUpComponent.inject(this)
            else -> throw IllegalStateException("Unknown Activity")
        }    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        verificationSource = arguments?.getString(EVENT_SOURCE) ?: ""
        shouldCallSkipImagerVerificationApi = arguments?.getBoolean(SHOULD_SKIP_IMAGE_VERIFICATION_API, false) ?: false

        arguments?.let {
            val pictureKey = it.getString("pictureKey")
            viewModel.setPictureKey(pictureKey)
            val userGender = it.getString("userGender")
            viewModel.setUserGender(userGender)
        }

    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVerifyProfileWithBadge2PopUpBinding.inflate(layoutInflater)
        val eventPremiumType = getPremiumTypeEventProperty(viewModel.user.value)
        imageVisionInteractor = createImageProcessors()

        binding.notNowBtn.setOnSingleClickListener {
             if(shouldCallSkipImagerVerificationApi) {
                 viewModel.skipImageVerification()
             }

            findNavController().popBackStack()
        }
        binding.continueBtn.setOnSingleClickListener {
            firebaseLogEvent(FirebaseAnalyticsEventsName.INITIATE_PUSH_VERIFICATION_TIER2)
            requestPermission(requireActivity())
        }

        binding.changePhoto.setOnSingleClickListener {
            val premiumType = getPremiumTypeEventProperty(DuaApplication.instance.userRepository.user.value)
            val signUpSignInMedium = if(activity is SignUpActivity) sharedSignUpViewModel.authMethod?.methodName else null

            sendChangeProfilePhotoEvent(
                verificationSource = ClevertapVerificationSourceValues.ONBOARDING.value,
                premiumType = premiumType,
                signUpSignInMedium = signUpSignInMedium ,
                eventSource = ClevertapEventSourceValues.IMAGE_VERIFICATION.value
            )

            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_PROFILE_PHOTO)

            pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.user.observe(viewLifecycleOwner) { userModel ->
            userModel?.let {
                if(userModel.profile.pictureUrl.isNotEmpty()) {
                    imageCircle(binding.userImage, userModel.profile.pictureUrl)
                }
            }
        }

        viewModel.uiData.asLiveData().observe(viewLifecycleOwner) { uiData->
            setVisibility(binding.notNowBtn, uiData.pictureKey.isNullOrEmpty())
            if (!uiData.pictureKey.isNullOrEmpty()) {
                imageCircle(binding.userImage, uiData.pictureKey)
            }
            if(uiData.skipVerificationStep == true) {
                val loveStoriesAction = R.id.action_verifyProfileWithBadge2PopUp_to_allSetSignUpFragment
                findNavController().navigate(loveStoriesAction)

                viewModel.onSkipVerificationStep()
            }
            handleLivenessVerificationUi(uiData)
            handleImageUpload(uiData)
        }
    }

    private fun handleImageUpload(uiData: LivenessUiData) {
        uiData.uploadedImageData?.let { data ->
            handleImageUploadProcess(data)
            trackUploadProgress(data)
            handleImageVerificationFailure(data)
            handleUploadCompletion(data)
        }
    }
    private fun handleUploadCompletion(data: LivenessUiData.UploadedImageData) {
        if (data.uploadFinished == true) {
            if (data.uploadFailed == true) {
                ToastUtil.toast("Upload Failed!")
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }

            if (!data.imagesVerified.isNullOrEmpty()) {
                val imageKey =  data.imagesVerified.firstOrNull()?.key
                imageKey?.let {
                    if(data.userPicturesUpdated != true) {
                        viewModel.updateImages(it)
                    }
                }
                if(data.userPicturesUpdated == true){
                    val dialog = LivenessImageUploadSuccessDialog.newInstance(data.imagesVerified.firstOrNull()?.key)
                    dialog.show(requireActivity().supportFragmentManager,"UploadSuccessDialog")
                    uploadProgressDialog?.dismiss()
                    if(activity is SignUpActivity) {
                        viewModel.setPictureKey(data.imagesVerified.firstOrNull()?.key)
                        choosePictureViewModel.setVerifiedPictureFromLiveness(data.imagesVerified)
                    }
                    viewModel.resetUploadProgress()
                } else if(data.userPicturesUpdated == false) {
                    uploadProgressDialog?.dismiss()
                    viewModel.resetUploadProgress()
                }

            }

        }
    }
    private fun handleImageVerificationFailure(data: LivenessUiData.UploadedImageData) {
        data.imageVerificationFailed?.let { verificationFailed ->
            val bundle = bundleOf(
                ImageDeniedFragmentLiveness.ARG_REASON to verificationFailed.invalidReason,
                ImageDeniedFragmentLiveness.ARG_IMAGE_KEY to verificationFailed.key,
            )
            findNavController().navigateSafer(R.id.action_verifyProfileWithBadge2PopUp_to_imageDeniedFragmentLiveness, bundle)
            viewModel.resetUploadImageData()
        }
    }
    private fun trackUploadProgress(data: LivenessUiData.UploadedImageData) {
        data.uploadProgress?.let { progress ->
            uploadProgressDialog?.updateProgress(progress)
            if (progress == -1) {
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }
        }
    }
    private fun handleImageUploadProcess(data: LivenessUiData.UploadedImageData) {
        if (data.faceDetectionCompleted == true && data.textDetectionCompleted == true) {
            startImageUploadProcess()
        }
    }
    private fun startImageUploadProcess() {
        uploadProgressDialog = ProgressDialog(requireContext(), resources.getString(R.string.uploading_images))
        uploadProgressDialog?.show()
        viewModel.uploadImage()
        viewModel.resetImageDetection()
    }
    private fun handleLivenessVerificationUi(uiState: LivenessUiData?){
        if(uiState?.verificationFailed == true) {
            LivenessVerificationFailedDialog.newInstance(uiState.failedImageUrl).show(childFragmentManager,"LivenessVerificationFailedDialog")
            viewModel.onLivenessVerificationFailedDialogShown()
        }
    }
    private fun createImageProcessors(): ImageVisionInteractor? {
        faceDetectionCallback = object : FaceDetectorProcessor.FaceDetectionCallback {
            override fun onSuccess(faceDetected: Boolean) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: faceDetected $faceDetected")
                viewModel.setFaceDetected(faceDetected)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: faceDetected exception $exception")
                exception.printStackTrace()

                viewModel.setFaceDetectionCompleted()
            }
        }

        textDetectionCallback = object : TextRecognitionProcessor.TextDetectionCallback {
            override fun onSuccess(text: Text) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: textDetected ${text.text}")
                viewModel.setTextDetected(text)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: textDetected exception $exception")
                exception.printStackTrace()

                viewModel.setTextDetectionCompleted()
            }
        }

        val imageProcessors = mutableListOf<VisionImageProcessor>()
        try {

            //Face Detection
            val faceDetector =
                FaceDetectorProcessor(requireContext(), null, faceDetectionCallback!!)
            imageProcessors.add(faceDetector)


            //Text Detection
            val textDetector = TextRecognitionProcessor(requireContext(), textDetectionCallback!!)
            imageProcessors.add(textDetector)
        } catch (e: Exception) {
            Timber.tag("IMAGE_DETECTION").e(e, "Can not create image processor: ${e.message}")
            return null
        }

        return ImageVisionInteractor(imageProcessors)
    }

    private fun beginUCrop(uri: Uri) {
        try {
            var randomFileName = UUID.randomUUID().toString()
            val extension: String?
            val cr = requireContext().contentResolver
            val type = cr.getType(uri)
            if (uri.toString().contains("file:")) extension = findExtension(uri)
            else extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(type)

            if (extension != null) randomFileName = "$randomFileName.$extension"
            Timber.tag("URITYPE").d("fileName: \$fileName")

            val destination = Uri.fromFile(File(requireContext().cacheDir, randomFileName))
            val options = UCrop.Options()
            options.setHideBottomControls(true)
            options.setCropGridRowCount(5)
            options.setCropGridColumnCount(4)
            options.setAllowedGestures(UCropActivity.ALL, UCropActivity.SCALE, UCropActivity.ROTATE)
            options.setToolbarTitle("")
            options.setDimmedLayerColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.background
                )
            )

            UCrop.of(uri, destination)
                .withAspectRatio(3f, 4f)
                .withOptions(options)

                .start(requireContext(), this,23)
        } catch (e: Exception) {
            e.printStackTrace()
        }


    }
    private fun findExtension(uri: Uri): String? {
        val stringUri = uri.toString()
        val lastIndex = stringUri.lastIndexOf('.')
        if (lastIndex == -1) {
            return null
        }
        return stringUri.substring(lastIndex + 1)
    }
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        //  super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == 23 && data != null) {
            val resultUri = UCrop.getOutput(data)
            resultUri?.let { outputUri ->
                if (!NetworkChecker.isNetworkConnected(requireContext())) {
                    ToastUtil.toast(getString(R.string.no_internet_string))
                    return
                }
                handleCroppedImage(outputUri)
            }
        }
    }

    private fun handleCroppedImage(outputUri: Uri) {
        viewModel.addNewImage(outputUri, currentPictureMetadata)
        imageVisionInteractor?.detectInImage(outputUri, requireActivity().contentResolver)
    }
    private fun requestPermission(
        activity: FragmentActivity,
        permission: String = Manifest.permission.CAMERA
    ) {
        val hasPermission = ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED

        if (hasPermission) {
            onPermissionGranted()
        } else {
            if (shouldShowRationale(activity)) {
                showSettingsRedirectDialog(activity)
                return
            }

            sendPermissionCameraToUsePopup()

            cameraPermissionLauncher.launch(permission)
        }
    }

    private fun handlePermissionDenied(
        activity: FragmentActivity,
        permission: String = Manifest.permission.CAMERA
    ) {
        if (activity.shouldShowRequestPermissionRationale(permission)) {
            showPermissionDeniedDialog(activity)
        } else {
            showSettingsRedirectDialog(activity)
        }
    }

    private fun shouldShowRationale(
        activity: FragmentActivity,
        permission: String = Manifest.permission.CAMERA
    ): Boolean {
        return activity.shouldShowRequestPermissionRationale(permission)
    }


    private fun showPermissionDeniedDialog(
        context: Context,
        permission: String = Manifest.permission.CAMERA
    ) {
        AlertDialog.Builder(context)
            .setTitle("Permission Denied")
            .setMessage("Without this permission the app is unable to function properly.")
            .setPositiveButton("Retry") { _, _ ->
                cameraPermissionLauncher.launch(permission)
            }
            .setNegativeButton("Cancel", null)
            .create()
            .show()
    }

    private fun showSettingsRedirectDialog(context: Context) {
        AlertDialog.Builder(context)
            .setTitle("Permission Denied Permanently")
            .setMessage("You have denied the permission permanently. Please enable it in settings.")
            .setPositiveButton("Go to Settings") { _, _ ->

                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri: Uri = Uri.fromParts("package", context.packageName, null)
                intent.data = uri
                context.startActivity(intent)
            }
            .setNegativeButton("Cancel", null)
            .create()
            .show()
    }

    private fun onPermissionGranted() {
        findNavController().navigateSafer(R.id.action_global_livenessProfileVerification)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        imageVisionInteractor?.stopImageProcessors()
        imageVisionInteractor = null
        uploadProgressDialog?.dismiss()
        uploadProgressDialog = null
        _binding = null
    }


    override fun onResume() {
        super.onResume()
        if (viewModel.user.value?.badge2.toString() != Badge2Status.NULL.status &&
            viewModel.user.value?.badge2.toString() != Badge2Status.NOT_APPROVED.status
        ) {
            findNavController().popBackStack()
        }
    }

    private fun sendPermissionCameraToUseAllowed() {
        if(activity is SignUpActivity) {
            sendClevertapEvent(ClevertapEventEnum.PERMISSION_CAMERA_TO_USE_ALLOWED,
                mapOf(
                    ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to ClevertapVerificationSourceValues.ONBOARDING.value,
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
                )
            )
        }
    }

    private fun sendPermissionCameraToUsePopup() {
        if(activity is SignUpActivity) {
            sendClevertapEvent(ClevertapEventEnum.PERMISSION_CAMERA_TO_USE_POPUP,
                mapOf(
                    ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to ClevertapVerificationSourceValues.ONBOARDING.value,
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedSignUpViewModel.authMethod?.methodName,
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
                )
            )
        }
    }

    companion object {
        const val EVENT_SOURCE = "event_source"
        const val SHOULD_SKIP_IMAGE_VERIFICATION_API = "should_skip_image_verification_api"
    }

}