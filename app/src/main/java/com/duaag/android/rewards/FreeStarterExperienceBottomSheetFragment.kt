package com.duaag.android.rewards

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapRewardSourceValues
import com.duaag.android.clevertap.ClevertapRewardTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentFreeStarterExperienceBottomsheetBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import javax.inject.Inject


class FreeStarterExperienceBottomSheetFragment : BottomSheetDialogFragment() {


    private  var _binding: FragmentFreeStarterExperienceBottomsheetBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as RewardsActivity }) { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as RewardsActivity).homeComponent.inject(this)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentFreeStarterExperienceBottomsheetBinding.inflate(inflater)


        initializeRecyclerView()

        binding.closeBtn.setOnClickListener {
            dismissAllowingStateLoss()
        }

        sendScreenViewedAnalyticsEvents()

        return binding.root
    }


    private fun sendScreenViewedAnalyticsEvents() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val profilePercentage = homeViewModel.userProfile.value?.profilePercentage


        firebaseLogEvent(
            FirebaseAnalyticsEventsName.REWARD_INCLUDED_VIEW, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.REWARD_TYPE.value to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
            FirebaseAnalyticsParameterName.REWARD_SOURCE.value to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
            ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

            ))
        sendAppsflyerEvent(
            AppsflyerEventsNameEnum.REWARD_INCLUDED_VIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

            ))
        sendClevertapEvent(
            ClevertapEventEnum.REWARD_INCLUDED_VIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

            ))
    }


    private fun initializeRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(context)
        val adapter = FreeStarterInfoAdapter(getRecyclerViewItems())
        binding.recyclerView.adapter = adapter
    }


    private fun getRecyclerViewItems(): List<FreeStarterItem> {

        //This is for future use when we do this dynamically
//        val counterConfigurationNames = homeViewModel.userProfile.value?.counterConfigurationNames
//        val counter = homeViewModel.userCounters?.firstOrNull { it.configurationName == counterConfigurationNames?.interactionCounterCN }
//        val hourInterval = if(counter != null) CalculateIntervalResetTimeInHourUseCase.invoke(counter) else 12

//        val likesDescription = getString(R.string.premium_light_likes_benefits_an,
//            (homeViewModel.getInteractionLimit()).toString(),
//            (hourInterval).toString()
//        )
//        val instachatDescription = getString(R.string.instachats_benefits_an, (homeViewModel.getInstachatLimit()).toString())
//        val boostDescription = getString(R.string.boost_benefits_an, (homeViewModel.getBoostLimit()).toString())
//        val photoDescription = getString(R.string.photo_benefits_an, 6)

        val isMale = homeViewModel.userProfileNU.value?.gender == GenderType.MAN.value

        val titleText = if(isMale) getString(R.string.free_starter_experience) else getString(R.string.free_premium_experience)
        val likesDescription = if(isMale) getString(R.string.premium_light_likes_benefits_an, 40.toString(),"12") else getString(R.string.unlimited)
        val boostDescription = if(isMale) getString(R.string.boost_benefits_an, "1") else getString(R.string._boost_per_month, "2")
        val instachatDescription = if(isMale) getString(R.string.instachats_benefits_an, "1") else getString(R.string.unlimited)
        val flightsDescription = getString(R.string.unlimited)
        val undoDescription = if(isMale) getString(R.string.premium_light_likes_benefits_an,
        1.toString(),
        "12"
        ) else getString(R.string.unlimited)

        val likedYouDescription = getString(R.string.liked_you_benefits_male)
        val photosDescription = getString(R.string.photo_benefits_an, 6)
        val callsDescription = getString(R.string.unlimited)
        val revealInstachatDescription = getString(R.string.all_revealed)
        binding.title.text = titleText
        val maleItems = listOf(
            FreeStarterItem(R.drawable.ic_cards_24_dp, getString(R.string.premium_light_overview_likes), likesDescription),
            FreeStarterItem(R.drawable.ic_instachat_24_dp, getString(R.string.instachats_string), instachatDescription),
            FreeStarterItem(R.drawable.ic_boost_24_dp, getString(R.string.boost), boostDescription),
            FreeStarterItem(R.drawable.ic_plane_24_dp, getString(R.string.flights), flightsDescription),
            FreeStarterItem(R.drawable.ic_undo_24_dp, getString(R.string.undo), undoDescription),
            // FreeStarterItem(R.drawable.ic_heart_24_dp, getString(R.string.liked_you), likedYouDescription),
            //  FreeStarterItem(R.drawable.ic_photo_24_dp, getString(R.string.premium_light_overview_photos), photosDescription),
            FreeStarterItem(R.drawable.ic_phone_24_dp, getString(R.string.calls_string), callsDescription),
            FreeStarterItem(R.drawable.ic_eye_24_dp, getString(R.string.reveal_instachats_premium), revealInstachatDescription),
        )
        val femaleItems = listOf(
            FreeStarterItem(R.drawable.ic_cards_24_dp, getString(R.string.premium_light_overview_likes), likesDescription),
            FreeStarterItem(R.drawable.ic_instachat_24_dp, getString(R.string.instachats_string), instachatDescription),
            FreeStarterItem(R.drawable.ic_boost_24_dp, getString(R.string.boost), boostDescription),
            FreeStarterItem(R.drawable.ic_plane_24_dp, getString(R.string.flights), flightsDescription),
            FreeStarterItem(R.drawable.ic_undo_24_dp, getString(R.string.undo), undoDescription),
            FreeStarterItem(R.drawable.ic_heart_24_dp, getString(R.string.liked_you), likedYouDescription),
            //  FreeStarterItem(R.drawable.ic_photo_24_dp, getString(R.string.premium_light_overview_photos), photosDescription),
            FreeStarterItem(R.drawable.ic_phone_24_dp, getString(R.string.calls_string), callsDescription),
           // FreeStarterItem(R.drawable.ic_eye_24_dp, getString(R.string.reveal_instachats_premium), revealInstachatDescription),
        )
        return if(isMale) maleItems else femaleItems
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
                super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                    d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                        BottomSheetBehavior.STATE_EXPANDED
            }
            d.dismissWithAnimation = true
        }
        return dialog
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
