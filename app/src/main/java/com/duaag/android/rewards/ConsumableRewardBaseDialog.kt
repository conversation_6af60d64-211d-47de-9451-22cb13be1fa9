package com.duaag.android.rewards

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.databinding.BoostRewardDialogLayoutBinding
import com.duaag.android.utils.setOnSingleClickListener

abstract class ConsumableRewardBaseDialog : DialogFragment() {
    var listener: ConsumableRewardListener? = null
    private var _binding: BoostRewardDialogLayoutBinding? = null
    private val binding get() = _binding

    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            listener = context as? ConsumableRewardListener
        } catch (e: ClassCastException) {
            throw ClassCastException("Activity must implement ConsumableRewardListener")
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BoostRewardDialogLayoutBinding.inflate(inflater)
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.claimNowBtn?.setOnSingleClickListener{
            listener?.onClaimNowClick(this)
            binding?.progressBar?.visibility = View.VISIBLE
            binding?.claimNowBtn?.visibility = View.GONE
        }
        binding?.title?.text = getTitle()
        binding?.description?.text = getDescription()
        binding?.image?.setImageResource(getImageResource())

    }

    abstract fun getTitle(): String
    abstract fun getImageResource(): Int
    abstract fun getDescription(): String

   interface ConsumableRewardListener {
       fun onClaimNowClick(dialog: ConsumableRewardBaseDialog)
   }
}