package com.duaag.android.rewards

import android.os.Bundle
import com.duaag.android.R

class InstachatRewardDialog : ConsumableRewardBaseDialog() {
    override fun getTitle(): String = requireContext().getString(R.string.you_won_instachat_title,arguments?.getInt(
        EXTRAS_REWARD_AMOUNT))
    override fun getImageResource(): Int  = R.drawable.reward_instachat_illustration
    override fun getDescription(): String = requireContext().getString(R.string.you_won_instachat_desc)

    companion object {
        private const val EXTRAS_REWARD_AMOUNT = "EXTRAS_REWARD_AMOUNT"

        fun newInstance(rewardAmount: Int) : ConsumableRewardBaseDialog {
            val fragment = InstachatRewardDialog()
            val args = Bundle()
            args.putInt(EXTRAS_REWARD_AMOUNT, rewardAmount)
            fragment.arguments = args
            return fragment
        }
    }

}