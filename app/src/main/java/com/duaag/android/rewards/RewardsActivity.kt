package com.duaag.android.rewards

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.ActivityRewardsBinding
import com.duaag.android.home.di.HomeComponent
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import timber.log.Timber
import javax.inject.Inject
import com.uxcam.UXCam

class RewardsActivity : AppCompatActivity(), GetRewardsFragment.GetRewardsCompleteProfileListener {

    private var _binding: ActivityRewardsBinding? = null
    private val binding get() = _binding!!

    lateinit var homeComponent: HomeComponent
    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel> { viewModelFactory }

    override fun onCreate(savedInstanceState: Bundle?) {
        homeComponent = (application as DuaApplication).appComponent.homeComponent().create()
        homeComponent.inject(this)

        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
        super.onCreate(savedInstanceState)

        _binding = ActivityRewardsBinding.inflate(layoutInflater)

        sendScreenViewedAnalyticsEvents()

        setContentView(binding.root)
    }

    private fun sendScreenViewedAnalyticsEvents() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val profilePercentage = homeViewModel.userProfile.value?.profilePercentage

        firebaseLogEvent(FirebaseAnalyticsEventsName.GET_REWARD_POPUP, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.REWARD_TYPE.value to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
            FirebaseAnalyticsParameterName.REWARD_SOURCE.value to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
            ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
        ))

       sendAppsflyerEvent(AppsflyerEventsNameEnum.GET_REWARD_POPUP, mapOf(
           ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName to DuaApplication.instance.getBillingAvailable(),
           ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
           ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
           ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
           ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
       ))
        sendClevertapEvent(
            ClevertapEventEnum.GET_REWARD_POPUP, mapOf(
                ClevertapEventPropertyEnum.BILLING_AVAILABLE.propertyName to DuaApplication.instance.getBillingAvailable(),
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
            ))
    }

    override fun onCompleteYouProfileClicked() {
        val intent = Intent(this, EditProfileActivity::class.java)
        intent.putExtra(EditProfileActivity.EDIT_PROFILE_SOURCE, ClevertapEditProfileSourceValues.REWARD_POPUP.value)
        startActivity(intent)

        finish()
    }

    override fun onDismissClicked() {
        finish()
    }
}