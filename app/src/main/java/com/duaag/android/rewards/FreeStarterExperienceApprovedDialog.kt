package com.duaag.android.rewards

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.duaag.android.R
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapRewardSourceValues
import com.duaag.android.clevertap.ClevertapRewardTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FreeStarterExperienceApprovedDialogBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import timber.log.Timber
import javax.inject.Inject

class FreeStarterExperienceApprovedDialog : DialogFragment() {

    private var _binding: FreeStarterExperienceApprovedDialogBinding? = null
    private val binding get() = _binding

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
        updateLocale(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)

        DuaAccount.shouldShowPremiumLiteApprovedDialog = false
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FreeStarterExperienceApprovedDialogBinding.inflate(inflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

         homeViewModel.userProfile.observe(viewLifecycleOwner){
             if(it == null) return@observe

             val isMale = it.gender == GenderType.MAN.value
             val descriptionResId = if(isMale) R.string.free_starter_experience_reward_desc else R.string.free_premium_experience_reward_desc

             binding?.description?.text = getString(descriptionResId)

         }
        binding?.image?.let {
            Glide.with(it)
                .load(R.drawable.celebration_image)
                .transform(CenterCrop())
                .into(it)
        }

        binding?.continueBtn?.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }

        sendScreenViewedAnalyticsEvents()

        return binding?.root
    }

    private fun sendScreenViewedAnalyticsEvents() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val profilePercentage = homeViewModel.userProfile.value?.profilePercentage


        firebaseLogEvent(
            FirebaseAnalyticsEventsName.REWARDED_POPUP, mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                FirebaseAnalyticsParameterName.REWARD_TYPE.value to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                FirebaseAnalyticsParameterName.REWARD_SOURCE.value to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

            ))
        sendAppsflyerEvent(
            AppsflyerEventsNameEnum.REWARDED_POPUP, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage

            ))
        sendClevertapEvent(
            ClevertapEventEnum.REWARDED_POPUP, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REWARD_TYPE.propertyName to ClevertapRewardTypeValues.PREMIUM_LIGHT.value,
                ClevertapEventPropertyEnum.REWARD_SOURCE.propertyName to ClevertapRewardSourceValues.COMPLETE_YOUR_PROFILE.value,
                ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to profilePercentage
            ))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val OPENED_FROM = "opened_from"
        fun newInstance(): FreeStarterExperienceApprovedDialog {
            val args = Bundle()
            val fragment = FreeStarterExperienceApprovedDialog()
            fragment.arguments = args

            return fragment
        }

        fun showFreeStarterExperienceApprovedDialog(fragmentManager: FragmentManager) {
            if (fragmentManager.findFragmentByTag("FreeStarterExperienceApprove") == null) {
                val dialog = newInstance()
                dialog.show(fragmentManager, "FreeStarterExperienceApprove")
            }
        }
    }
}