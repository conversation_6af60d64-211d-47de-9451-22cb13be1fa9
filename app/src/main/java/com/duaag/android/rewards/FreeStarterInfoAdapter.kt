package com.duaag.android.rewards

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.FreeStarterItemBinding

class FreeStarterInfoAdapter(val list: List<FreeStarterItem>) : RecyclerView.Adapter<FreeStarterInfoAdapter.FreeStarterViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FreeStarterViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        val binding: FreeStarterItemBinding = FreeStarterItemBinding.inflate(layoutInflater, parent, false)
        return FreeStarterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FreeStarterViewHolder, position: Int) {
        (holder).bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    class FreeStarterViewHolder(val binding: FreeStarterItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FreeStarterItem) {
            binding.title.text = item.title
            binding.description.text = item.details
        }
    }

}