package com.duaag.android.api.socket

import android.os.Build
import com.duaag.android.BuildConfig
import com.duaag.android.api.socket.model.ActionChatWebSocket
import com.duaag.android.api.socket.model.ErrorChatWebSocketModel
import com.duaag.android.api.socket.model.ErrorTypeActionEnum
import com.duaag.android.api.socket.model.InputActionEnum
import com.duaag.android.api.socket.model.LikedMessageStateChanged
import com.duaag.android.api.socket.model.NewConversationTokenModel
import com.duaag.android.api.socket.model.OutputActionsEnum
import com.duaag.android.api.socket.model.UpdateTypingStateChanged
import com.duaag.android.api.socket.model.UpdateTypingStateWebSocked
import com.duaag.android.api.socket.model.WebSocketMessageModel
import com.duaag.android.api.socket.model.mapToCreateMessageModel
import com.duaag.android.api.socket.model.mapToLikeUnlikeMessageWebSocket
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.chat.model.MessageModel
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import timber.log.Timber
import java.net.URI
import java.net.URISyntaxException
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLHandshakeException
import javax.net.ssl.SSLParameters
import javax.net.ssl.SSLSession
import javax.net.ssl.SSLSocket

object ChatWebSocket {
    private lateinit var mWebSocketClient: WebSocketClient
    private var coroutineScope = CoroutineScope(Dispatchers.IO+ SupervisorJob())
    private var chatWebSocketListeners: MutableList<ChatWebSocketReceivedInterface> = mutableListOf()
    private var isConnecting = false

    fun addListener(mChatWebSocketReceivedInterface: ChatWebSocketReceivedInterface) {
        chatWebSocketListeners.add(mChatWebSocketReceivedInterface)
    }

    fun removeListener(mChatWebSocketReceivedInterface: ChatWebSocketReceivedInterface){
        chatWebSocketListeners.remove(mChatWebSocketReceivedInterface)
    }

    fun connectWebSocket() {
        if (::mWebSocketClient.isInitialized && mWebSocketClient.isOpen && !AWSInteractor.isSignedIn()) {
            return
        }

        if (isConnecting) {
            Timber.tag("Websocket").i("Connection attempt already in progress")
            return
        }

        coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

        coroutineScope.launch {
            isConnecting = true
            try {
                val uri: URI
                try {
                    uri = URI("${BuildConfig.CHAT_WEB_SOCKET_URL}?Auth=${AWSInteractor.getToken()}")
                } catch (e: URISyntaxException) {
                    isConnecting = false
                    e.printStackTrace()
                    return@launch
                } catch (e: Exception) {
                    isConnecting = false
                    e.printStackTrace()
                    return@launch
                }

                mWebSocketClient = object : WebSocketClient(uri) {
                    override fun onOpen(serverHandshake: ServerHandshake) {
                        isConnecting = false
                        Timber.tag("Websocket").i("Opened ${serverHandshake.httpStatus}")
                    }

                    override fun onMessage(s: String) {
                        <EMAIL>(s)
                    }

                    override fun onClose(i: Int, s: String, b: Boolean) {
                        isConnecting = false
                        Timber.tag("Websocket").i("Closed $s")
                    }

                    override fun onError(e: Exception) {
                        isConnecting = false
                        Timber.tag("Websocket").i("Error ${e.message}")
                    }
                    //Check out the wiki https://github.com/TooTallNate/Java-WebSocket/wiki/No-such-method-error-setEndpointIdentificationAlgorithm
                    override fun onSetSSLParameters(sslParameters: SSLParameters?) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            super.onSetSSLParameters(sslParameters)
                        }
                    }
                }

                mWebSocketClient.connectBlocking()
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N){
                    verifyHostName()
                }
            } catch (e: InterruptedException) {
                isConnecting = false
                e.printStackTrace()
            } finally {
                isConnecting = false
            }
        }
    }


    private fun verifyHostName() {
        //Check out wiki https://github.com/TooTallNate/Java-WebSocket/wiki/Verify-hostname-after-handshake
        try {
            //Verify
            val hv: HostnameVerifier = HttpsURLConnection.getDefaultHostnameVerifier()
            val socket: SSLSocket = mWebSocketClient.socket as SSLSocket
            val s: SSLSession = socket.session

            if (!hv.verify(BuildConfig.CHAT_WEB_SOCKET_URL.replace("wss://",""), s)) {
                Timber.tag("Client").e("Expected echo.websocket.org, found ${s.peerPrincipal}")
                throw SSLHandshakeException("Expected websocket.org, found " + s.peerPrincipal);
            } else {
                Timber.tag("Client").i("Success")
            }
        } catch ( e: SSLHandshakeException) {
            mWebSocketClient.close();
        }
    }

    fun isConnected():Boolean = ::mWebSocketClient.isInitialized && mWebSocketClient.isOpen
    fun isClosing():Boolean = ::mWebSocketClient.isInitialized && mWebSocketClient.isClosing


    fun sendMessage(newMessage: MessageModel, conversationToken: String?) {
        val sendMessageModelJson = Gson().toJson(newMessage.mapToCreateMessageModel(conversationToken))
        mWebSocketClient.send(sendMessageModelJson)
    }

    fun likeUnlikeMessage(newMessage: MessageModel,isLike:Boolean, conversationToken: String?){
        val action: String = if (isLike) {
            OutputActionsEnum.LIKE_MESSAGE.value
        } else {
            OutputActionsEnum.UNLIKE_MESSAGE.value
        }

        val likeUnlikeMessageWebSocket = Gson().toJson(newMessage.mapToLikeUnlikeMessageWebSocket(action,conversationToken))
        mWebSocketClient.send(likeUnlikeMessageWebSocket)
    }

    fun sendIsTypingStatusChanged(updateTypingStateWebSocked: UpdateTypingStateWebSocked) {
        try {
            val event = Gson().toJson(updateTypingStateWebSocked)
            mWebSocketClient.send(event)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

  private  fun onMessage(s: String) {
      Timber.tag("Websocket").i("Message: $s")
        if (s.isBlank()) return
        val actionName = Gson().fromJson(s, ActionChatWebSocket::class.java)
         if (actionName.action.isNullOrEmpty()) return
        coroutineScope.launch(Dispatchers.Main) {
            when (InputActionEnum.fromString(actionName.action)) {
                InputActionEnum.NEW_CONVERSATION_TOKEN -> {
                    val newConversationTokenModel =
                        Gson().fromJson(s, NewConversationTokenModel::class.java)
                    chatWebSocketListeners.forEach {
                        it.newConversationToken(newConversationTokenModel)
                    }
                }
                InputActionEnum.MESSAGE_SENT -> {
                    val messageSentModel =
                        Gson().fromJson(s, WebSocketMessageModel::class.java)
                    chatWebSocketListeners.forEach {
                        it.messageSent(messageSentModel)
                    }
                }
                InputActionEnum.NEW_MESSAGE -> {
                    val messageSentModel =
                        Gson().fromJson(s, WebSocketMessageModel::class.java)
                    chatWebSocketListeners.forEach {
                        it.newWebSocketMessage(messageSentModel)
                    }
                }
                InputActionEnum.IS_LIKED_MESSAGE_STATE_CHANGED -> {
                    val likedMessageStateChanged =
                        Gson().fromJson(s, LikedMessageStateChanged::class.java)
                    chatWebSocketListeners.forEach {
                        it.likeMessageStateChange(likedMessageStateChanged)
                    }
                }

                InputActionEnum.IS_TYPING_CONVERSATION_STATE_CHANGED -> {
                    val updateTypingStateChanged =
                        Gson().fromJson(s, UpdateTypingStateChanged::class.java)
                    chatWebSocketListeners.forEach {
                        it.updateTypingStateChanged(updateTypingStateChanged)
                    }
                }
                InputActionEnum.ERROR -> {
                    val errorChatWebSocketModel =
                        Gson().fromJson(s, ErrorChatWebSocketModel::class.java)
                    when (ErrorTypeActionEnum.fromString(errorChatWebSocketModel.payload.data.type)) {
                        ErrorTypeActionEnum.USER_NOT_PART_OF_CONVERSATIONS -> {
                            chatWebSocketListeners.forEach {
                                it.userNotPartOfConversationsError(
                                    errorChatWebSocketModel)
                            }
                        }
                        ErrorTypeActionEnum.VALIDATE_ERROR -> {
                            chatWebSocketListeners.forEach {
                                it.validateError(errorChatWebSocketModel)
                            }
                        }
                        ErrorTypeActionEnum.DEFAULT -> {
                            chatWebSocketListeners.forEach {
                                it.defaultError(errorChatWebSocketModel)
                            }
                        }
                        else -> {
                        }
                    }
                }
                else -> {
                }
            }
        }

    }

    /**
     * Reconnect WebSocket when an FMC message comes before a WebSocket.
     * Creates a new WebSocketClient instance since they are not reusable.
     */
    fun reConnectWebSocket() {
        if (isConnecting) {
            Timber.tag("Websocket").i("Reconnection skipped - connection attempt already in progress")
            return
        }

        coroutineScope.launch {
            // Close existing connection if it exists
            if (::mWebSocketClient.isInitialized) {
                try {
                    if (!mWebSocketClient.isClosed) {
                        mWebSocketClient.close()
                    }
                } catch (e: Exception) {
                    Timber.tag("Websocket").w("Error closing existing connection: ${e.message}")
                }
            }
        }

        // Create new connection (this will handle the isConnecting flag)
        connectWebSocket()
    }

    fun close() {
        coroutineScope.launch {
            isConnecting = false
            if (::mWebSocketClient.isInitialized) {
                mWebSocketClient.close()
            }
            coroutineContext.cancel()
        }
    }

   sealed interface ChatWebSocketReceivedInterface {
        fun newConversationToken(newConversationTokenModel: NewConversationTokenModel)
        fun messageSent(webSocketMessageModel: WebSocketMessageModel)
        fun newWebSocketMessage(webSocketMessageModel: WebSocketMessageModel)
        fun likeMessageStateChange(likedMessageStateChanged: LikedMessageStateChanged)
       fun updateTypingStateChanged(updateTypingStateChanged: UpdateTypingStateChanged)
        //errors
        fun userNotPartOfConversationsError(errorChatWebSocketModel: ErrorChatWebSocketModel)
        fun validateError(errorChatWebSocketModel: ErrorChatWebSocketModel)
        fun defaultError(errorChatWebSocketModel: ErrorChatWebSocketModel)
    }

    interface HomeChatWebSocketReceivedInterface : ChatWebSocketReceivedInterface
    interface ConversationChatWebSocketReceivedInterface : ChatWebSocketReceivedInterface

}