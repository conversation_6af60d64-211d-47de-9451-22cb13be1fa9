package com.duaag.android.api


import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.aws.models.VerifyAccountModel
import com.duaag.android.aws.models.VerifyCustomSmsModel
import com.duaag.android.login.models.*
import com.duaag.android.settings.models.AddEmailModel
import com.duaag.android.settings.models.AddPhoneNumberModel
import com.duaag.android.settings.models.VerifyCodeModel
import com.duaag.android.signInWithSpotted.models.AddEmailModelSpotted
import com.duaag.android.signInWithSpotted.models.AddPhoneNumberModelSpotted
import com.duaag.android.signInWithSpotted.models.VerifyCodeModelSpotted
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST


interface DuaAuthService {

    companion object {
        var ENDPOINT = RemoteConfigUtils.getAuthAPI()
        const val resourcePath = "auth/"
    }

    @POST("fb")
    suspend fun loginWithFacebook(@Body model: AuthModel): Response<AuthProviderResponse>

    @POST("google/onetap")
    suspend fun loginWithGoogle(@Body model: AuthModel): Response<Void>

    @POST("spotted")
    suspend fun signUpWithSpotted(@Body model: SignUpWithSpottedBody): Response<SignUpWithSpottedResponse>

    @POST("phone")
    suspend fun confirmSignUpCustomSms(@Body model: VerifyAccountModel): Response<ResponseBody>

    @POST("phone")
    suspend fun confirmForgotCustomSms(@Body model: VerifyCustomSmsModel): Response<ResponseBody>

    @POST("account-attributes/phone")
    suspend fun addPhoneNumber(@Body model: AddPhoneNumberModel,
                               @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/email")
    suspend fun addEmail(@Body model: AddEmailModel,
                         @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/phone/verified")
    suspend fun verifyPhoneNumber(@Body model: VerifyCodeModel,
                                  @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/email/verified")
    suspend fun verifyEmail(@Body model: VerifyCodeModel,
                            @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/phone")
    suspend fun addPhoneNumberSpotted(@Body model: AddPhoneNumberModelSpotted,
                                      @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/email")
    suspend fun addEmailSpotted(@Body model: AddEmailModelSpotted,
                                @Header("Authorization") authHeader: String): Response<ResponseBody>


    @POST("account-attributes/phone/verified?mustSetPassword=true")
    suspend fun verifyPhoneNumberSpotted(@Body model: VerifyCodeModelSpotted,
                                  @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("account-attributes/email/verified?mustSetPassword=true")
    suspend fun verifyEmailSpotted(@Body model: VerifyCodeModelSpotted,
                            @Header("Authorization") authHeader: String): Response<ResponseBody>

    @POST("credentials")
    suspend fun sendEmailCredentialsBeforeLogin(@Body model: Credentials): Response<ResponseBody>

}
