package com.duaag.android.api


import com.duaag.android.aws_liveness.data.model.GetLivenessSessionIdResponse
import com.duaag.android.aws_liveness.data.model.LivenessCredentialsOutput
import com.duaag.android.aws_liveness.data.model.LivenessSubmitSessionIdRequest
import com.duaag.android.aws_liveness.data.model.SignUpConfigurationResponse
import com.duaag.android.base.models.*
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationModelUpdateResponse
import com.duaag.android.change_location.models.LocationSearchModelItem
import com.duaag.android.chat.blockspamlinks.data.db.models.SuspiciousLinksBody
import com.duaag.android.chat.model.*
import com.duaag.android.crosspath.data.remote.CrossPathUsersRaw
import com.duaag.android.crosspath.data.remote.dto.CrossPathsResponseDto
import com.duaag.android.crosspath.data.remote.dto.GroupedCrossPathsResponseDto
import com.duaag.android.crosspath.data.remote.dto.UpdateLocationDto
import com.duaag.android.disabled.models.MarkReasonsAsResolvedRequestModel
import com.duaag.android.disabled.models.MarkReasonsAsResolvedResponseModel
import com.duaag.android.home.models.*
import com.duaag.android.image_verification.models.ImageVerificationSubmitResponse
import com.duaag.android.image_verification.models.SubmitImageModel
import com.duaag.android.instagram.models.ConnectInstagram
import com.duaag.android.instagram.models.InstagramMediaResponse
import com.duaag.android.last_login.data.remote.dto.LastLoggedInResponse
import com.duaag.android.login.models.SignUpWithSpottedRaw
import com.duaag.android.manage_pictures.models.UpdateImagesResponse
import com.duaag.android.profile_new.editprofile.zodiac_sign.ZodiacSignRequestModel
import com.duaag.android.profile_new.models.UpdateUserResponse
import com.duaag.android.settings.fragments.account_settings.delete_account.model.LoveStoryRequest
import com.duaag.android.settings.fragments.block_contacts.data.dto.AddPhoneNumberBodyDto
import com.duaag.android.settings.fragments.block_contacts.data.dto.AddPhoneNumberResponseDto
import com.duaag.android.settings.fragments.block_contacts.data.dto.BlockedContactsDtoItem
import com.duaag.android.signup.models.CreateProfileModel
import com.duaag.android.signup.models.ImageVerificationModel
import com.duaag.android.signup.models.VerifiedImageModel
import com.duaag.android.sunny_hill.data.model.SunnyHillAttendeesResponse
import com.duaag.android.sunny_hill.domain.use_case.SunnyHillAttendeesCountModel
import com.duaag.android.utils.RemoteConfigUtils
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.*


interface UserService {

    companion object {
        var ENDPOINT = RemoteConfigUtils.getUserAPI()
        const val resourcePath = "users-api/"
    }

    @GET("recommendations")
    suspend fun getUserRecommended(@Query("mode") mode: String = "love",
                                   @Query("limit") limit: Int = 10): Response<List<RecommendedUserModel>>

    @GET("accounts/current")
    suspend fun getUserAccountInfo(): Response<AccountModel>

    @GET("accounts/last-logged-in")
    suspend fun getLastLoggedIn(
        @Query("deviceId") deviceId: String
    ): Response<LastLoggedInResponse>

    @POST("referrals/current")
    suspend fun getReferral(): Response<ReferralResponse>

    @GET("tags")
    suspend fun getTags(@Query("lang") lang: String
                        ,@Query("mode") mode: String = "love"): Response<TagsResponse>

    @POST("interactions?mode=love")
    suspend fun interactUser(@Body interactionsBody: InteractionBody): Response<InteractionsResponse>

    @POST("interactions/{cognitoUserId}/undo")
    suspend fun undoInteraction(@Path("cognitoUserId") cognitoId: String,
                                @Query("mode") mode: String = "love"): Response<ResponseBody>

    @POST("user-tags?mode=love")
    suspend fun updateUserTags(@Body updateTagsBody: UpdateTagsModel): Response<UpdateTagsResponse>

    @POST("user-tags/batch?mode=love")
    suspend fun updateUserTags(@Body updateTagsBody: UpdateTagsBatchModel): Response<UpdateTagsResponse>

    @POST("interactions?mode=love")
    suspend fun interactUserLikedYou(@Body interactionsBody: InteractionsBodyLikedYou): Response<InteractionsResponse>

    @POST("pictures/verification")
    suspend fun getImagesVerificationResults(@Body images: ImageVerificationModel): Response<VerifiedImageModel>

    @POST("image-verification")
    suspend fun submitImageVerification(@Query("mode") mode: String = "love",
                                        @Body body: SubmitImageModel): Response<ImageVerificationSubmitResponse>
    @POST("image-verification/skip")
    suspend fun skipImageVerification() : Response<Unit>

    @GET("image-verification/liveness/credentials")
    suspend fun getLivenessCredentials(): Response<LivenessCredentialsOutput>

    @GET("image-verification/liveness/")
    suspend fun getSessionId(): Response<GetLivenessSessionIdResponse>

    @POST("image-verification/liveness/")
    suspend fun submitSessionId(@Body request: LivenessSubmitSessionIdRequest): Response<Unit>

    @JvmSuppressWildcards
    @HTTP(method = "DELETE",path = "pictures/verification",hasBody = true)
    suspend fun deleteVerifiedPicture(@Body body: Map<String, List<String>>): Response<ResponseBody>

    @POST("users/current?mode=love")
    suspend fun createUserProfile(@Body recommendedUser: CreateProfileModel): Response<UserModel>

    @JvmSuppressWildcards
    @PUT("users/current?mode=love")
    suspend fun updateUserProfile(@Body param: Map<String, Any>): Response<UpdateUserResponse>

    @JvmSuppressWildcards
    @PUT("users/current?mode=love")
    suspend fun updateUserPictures(@Body param: Map<String, Any>): Response<UpdateImagesResponse>

    @JvmSuppressWildcards
    @PUT("users/current/filter?mode=love")
    suspend fun updateFiltersUser(@Body filter: UpdateFilterBody): Response<FilterResponse>

    @JvmSuppressWildcards
    @PUT("users/current/filter?mode=love")
    suspend fun updateFiltersUser(@Body param: Map<String, Any>): Response<FilterResponse>

    @DELETE("users/current/pictures/{position}?mode=love")
    suspend fun deletePicture(@Path("position") position: Int): Response<ResponseBody>

    @GET("users/current?mode=love")
    suspend fun getUserProfile(): Response<UserModel>

    @DELETE("users/current")
    suspend fun deleteUserProfile(): Response<ResponseBody>

    @POST("users/deactivate/current")
    suspend fun deactivateUserProfile(): Response<ResponseBody>

    @GET("users/{userId}")
    suspend fun getUserProfile(
        @Path("userId") userId: String,
        @Query("mode") mode: String = "love",
        @Query("includeInteraction") includeInteraction: Boolean): Response<RecommendedUserModel>

    @DELETE("matches/users/{userId}?mode=love")
    suspend fun unMatch(@Path("userId") userId: String): Response<Void>

    @POST("matches/{matchId}/seen")
    suspend fun seenMatch(@Path("matchId") matchId: Int): Response<Void>

    @POST("interactions/seen/{cognitoUserId}")
    suspend fun seenUserLike(
        @Path("cognitoUserId") cognitoUserId: String,
        @Query("mode") mode: String = "love"
    ): Response<ResponseBody>

    @Headers("Accept-Version: 3")
    @GET("interactions/likes")
    suspend fun getUserLikes(@Query("mode") mode: String = "love",
                             @Query("limit") limit: Int?,
                             @Query("next-cursor") nextCursor: String?,
                             @Query("previous-cursor") previousCursor: String?): Response<UserLikedYouResponse>


    @POST("interactions/unblur/{interactionId}")
    suspend fun unblurProfile(
        @Path("interactionId") interactionId: String,
        @Query("mode") mode: String = "love"
    ): Response<UserLikesModel>

    @POST("interactions/unblur")
    suspend fun unBlurLatestLike(
        @Query("mode") mode: String = "love"
    ): Response<UserLikesModel>

    @Headers("Accept-Version: 3")
    @GET("matches")
    suspend fun getMatches(@Query("filter") filter: String? = null,
                           @Query("mode") mode: String = "love",
                           @Query("limit") limit: Int?,
                           @Query("next-cursor") nextCursor: String?,
                           @Query("previous-cursor") previousCursor: String?): Response<UserMatchResponse>

    @JvmSuppressWildcards
    @Headers("Accept-Version: 2")
    @POST("users/current/location")
    suspend fun setLocation(@Query("mode") mode: String = "love",
                            @Query("type") type: String,
                            @Body coordinates: Map<String, Any>): Response<LocationModelUpdateResponse>

    @GET("interactions/likes-count?mode=love")
    suspend fun getUserLikeYouCount(): Response<UserLikedYouCountResponse>


    @GET("instagram/media/{cognitoUserId}")
    suspend fun getInstagramMedia(@Path("cognitoUserId") cognitoId: String,
                                  @Query("limit") limit: Int = 30): Response<InstagramMediaResponse>

    @POST("instagram/connect")
    suspend fun connectInstagram(@Body connect: ConnectInstagram): Response<ResponseBody>

    @POST("instagram/disconnect")
    suspend fun disconnectInstagram(): Response<ResponseBody>

    @POST("instagram/reconnect")
    suspend fun reconnectInstagram(@Body connect: ConnectInstagram): Response<ResponseBody>

    @POST("disabled-users/reasons/resolved")
    suspend fun markReasonsAsResolved(@Body connect: MarkReasonsAsResolvedRequestModel): Response<MarkReasonsAsResolvedResponseModel>

    @POST("user-review-requests")
    suspend fun userReviewRequest(): Response<ResponseBody>

    @GET("world-cities")
    suspend fun searchCities(@Query("search-term") searchTerm: String): List<LocationSearchModelItem>

    @GET("world-cities/top-cities")
    suspend fun topCities(@Query("mode") mode: String = "love"): Response<List<LocationModel>>

    @GET("communities")
    suspend fun getCommunities(): Response<List<CommunityInfo>>

    @GET("communities/{id}")
    suspend fun getCommunityById(@Path("id") id:String): Response<CommunityInfo>


    @GET("communities/predict/{name}")
    suspend fun predictCommunityByName(@Path("name") name:String): Response<CommunityPredictionResponse>

    @POST("users/boost")
    suspend fun boostProfile(@Query("mode") mode: String = "love"): Response<BoostResponseModel>

    @POST("profile-visits/{cognitoId}")
    suspend fun callProfileVisitedApi(
        @Path("cognitoId") cognitoUserId: String,
        @Body body: ProfileVisitBody
    ): Response<Unit>

    @POST("users/zodiac")
    suspend fun updateZodiacSign(@Body request: ZodiacSignRequestModel) : Response<Unit>

    @POST("users/lovestory")
    @Headers("Content-Type: application/json")
    suspend fun submitLoveStory(
        @Body loveStoryRequest: LoveStoryRequest
    ): Response<ResponseBody>

    @POST("users/current/suspicious-links")
    suspend fun suspiciousLinks(@Body suspiciousLinks: SuspiciousLinksBody) : Response<ResponseBody>

    @GET("block-phone-number")
    suspend fun getBlockedPhoneNumbers(): Response<List<BlockedContactsDtoItem>>

    @DELETE("block-phone-number")
    suspend fun deleteAllBlockedContacts(): Response<Void>

    @DELETE("block-phone-number/{id}")
    suspend fun deletePhoneNumberInBlockedContacts(
        @Path("id") id: Long
    ): Response<Void>

    @POST("block-phone-number")
    suspend fun addPhoneNumberInBlockedContacts(
        @Body body: AddPhoneNumberBodyDto
    ): Response<AddPhoneNumberResponseDto>

    @POST("user-recent-locations")
    suspend fun addNewUserLocationPin(
        @Body data: UpdateLocationDto
    ): Response<Unit>

    @GET("cross-path")
    suspend fun getCrossPaths(
        @Query("limit") limit: Int = 10,
        @Query("next-cursor") nextCursor: Long? = null
    ): Response<CrossPathsResponseDto>

    @GET("cross-path/clusters")
    suspend fun getGroupedCrossPaths(
        @Query("distance") distance: Double,
        @Query("latitude") latitude: Double,
        @Query("longitude") longitude: Double
    ): Response<GroupedCrossPathsResponseDto>

    @GET("cross-path/users")
    suspend fun getCrossPathUsers(
        @Query("userIds") userIds: String,
        @Query("clusterLatitude") clusterLatitude: Double?,
        @Query("clusterLongitude") clusterLongitude: Double?
    ): Response<CrossPathUsersRaw>

    @PUT("users/sunny-hill/attend")
    suspend fun attendSunnyHill(): Response<Unit>

    @GET("users/sunny-hill/count")
    suspend fun getSunnyHillAttendeesCunt(): Response<SunnyHillAttendeesCountModel>

    @GET("users/spotted")
    suspend fun getSpottedProfile(): Response<SignUpWithSpottedRaw>

    @GET("configurations/sign-up/")
    suspend fun getUserSignUpConfigurations(): Response<SignUpConfigurationResponse>

    @GET("users/sunny-hill/attendees")
    suspend fun getSunnyHillAttendees(
        @Query("limit") limit: Int = 20,
        @Query("next-cursor") nextCursor: String? = null
    ): Response<SunnyHillAttendeesResponse>

}