package com.duaag.android.api

/**
 * A generic class that holds a value with its loading status.
 *
 * Result is usually created by the Repository classes where they return
 * `LiveData<Result<T>>` to pass back the latest data to the UI with its fetch status.
 */
sealed class Resource<out T : Any> {

    class Success<out T : Any>(val data: T,val isFromDB: Boolean = false) : Resource<T>()
    class Error<out T : Any>( val data: T,val exception: Exception?=null,) : Resource<T>()
    object Loading : Resource<Nothing>()

    override fun toString(): String {
        return when (this) {
            is Success<*> -> "Success[data=$data]"
            is Error -> "Error[exception=$data]"
            Loading -> "Loading"
        }
    }
}

/**
 * `true` if [Result] is of type [Success] & holds non-null [Success.data].
 */
val Resource<*>.succeeded
    get() = this is Resource.Success
