package com.duaag.android.api.socket.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
class ErrorChatWebSocketModel(
    @SerializedName("action")
    val action: String,
    @SerializedName("eventId")
    val eventId: String?,
    @SerializedName("payload")
    val payload: PayloadError,
)

@Keep
class PayloadError(
    @SerializedName("conversationId")
    val conversationId: String?,
    @SerializedName("data")
    val data: DataCreateMessage,
)

@Keep
class DataError(
    @SerializedName("type")
    val type: String,
    @SerializedName("errors")
    val errors: List<String>?,
)
