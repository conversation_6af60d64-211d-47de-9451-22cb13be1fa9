package com.duaag.android.api.socket.model

import androidx.annotation.Keep

@Keep
enum class InputActionEnum(val value: String) {
    NEW_CONVERSATION_TOKEN("newConversationToken"),
    MESSAGE_SENT("messageSent"),
    NEW_MESSAGE("newMessage"),
    IS_LIKED_MESSAGE_STATE_CHANGED("isLikedMessageStateChanged"),
    IS_TYPING_CONVERSATION_STATE_CHANGED("isTypingConversationStateChanged"),
    ERROR("error");

    companion object {
        private val map = values().associateBy(InputActionEnum::value)
        fun fromString(type: String?) = map[type]
    }
}

@Keep
enum class ErrorTypeActionEnum(val value: String) {
    USER_NOT_PART_OF_CONVERSATIONS("user_not_part_of_conversations"),
    VALIDATE_ERROR("validate_error"),
    DEFAULT("default");

    companion object {
        private val map = ErrorTypeActionEnum.values().associateBy(ErrorTypeActionEnum::value)
        fun fromString(type: String) = map[type]
    }
}