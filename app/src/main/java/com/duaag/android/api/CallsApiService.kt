package com.duaag.android.api

import com.duaag.android.calls.models.EndCallBody
import com.duaag.android.calls.models.StartCallBody
import com.duaag.android.calls.models.StartCallResponse
import com.duaag.android.utils.RemoteConfigUtils
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface CallsApiService {
    companion object {
        var ENDPOINT = RemoteConfigUtils.getVideoCallsAPI()
        const val resourcePath = "video-calls-api/"
    }

    @POST("video-calls")
    suspend fun startCall(
        @Body startCallBody: StartCallBody,
        @Query("mode") mode: String = "love"
    ): Response<StartCallResponse>

    @POST("video-calls/ended")
    suspend fun endCall(@Body startCallBody: EndCallBody): Response<ResponseBody>

    @POST("video-calls/accept/{callerUserId}")
    suspend fun acceptCall(
        @Path("callerUserId") callerUserId: String,
        @Query("mode") mode: String = "love"
    ): Response<ResponseBody>
}