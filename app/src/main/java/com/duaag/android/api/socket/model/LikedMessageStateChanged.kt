package com.duaag.android.api.socket.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
class LikedMessageStateChanged(
    @SerializedName("action")
    val action: String,
    @SerializedName("eventId")
    val eventId: String,
    @SerializedName("payload")
    val payload: PayloadLikedMessage,
)

@Keep
class PayloadLikedMessage(
    @SerializedName("conversationId")
    val conversationId: String,
    @SerializedName("messageId")
    val messageId: String,
    @SerializedName("data")
    val data: DataLikedMessage,
)

@Keep
class DataLikedMessage(
    @SerializedName("isLiked")
    val isLiked: Boolean,
)