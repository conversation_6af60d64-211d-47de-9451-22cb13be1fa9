package com.duaag.android.login.fragments.forgotpass

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentForgotPassSetEmailBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.fragments.SignInFragment
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.CommonUtils.isEmailValid
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.showKeyboard
import javax.inject.Inject

class ForgotPassSetEmailFragment : Fragment() {

    companion object {
        fun newInstance(): ForgotPassSetEmailFragment = ForgotPassSetEmailFragment()
    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({activity as StartActivity }) { viewModelFactory }
    private var _binding: FragmentForgotPassSetEmailBinding? = null
    private val binding get() = _binding!!

    private var emailListener: TextWatcher? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentForgotPassSetEmailBinding.inflate(inflater, container, false)

        binding.btnContinue.isEnabled = false
        binding.btnContinue.isClickable = false

        sendClevertapEvent(ClevertapEventEnum.FORGOT_PASSWORD_SCREENVIEW, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.EMAIL.methodName
        ))

        binding.btnContinue.setOnClickListener{
            logSignUpEvent(AuthMethod.EMAIL, FirebaseAnalyticsEventsName.FP_TYPE_MEDIUM)

            validateInput(signInViewModel.phoneEmail.value.toString())
        }

        binding.emailInput.onKeyboardDone {
            binding.btnContinue.performClick()
        }

        setEmailListener()

        signInViewModel.signInCurrentPage.observe(viewLifecycleOwner) { page ->
            if (page == SignInFragment.SIGN_IN_EMAIL_PAGE) {
                binding.emailInput.requestFocus()
                binding.emailInput.showKeyboard()
            }
        }
        return binding.root
    }

    private fun validateInput(email: String) {
        if ((email.isNotEmpty() && isEmailValid(email))) {
            val action = ForgotPasswordFragmentDirections.actionForgotPasswordFragmentToForgotPassNewPassFragmentV2()
            findNavController().navigate(action)
        } else {
            if (!isEmailValid(email)) {
                handleEmailError(R.string.email_is_not_valid, R.drawable.error_corners_12dp)
            }
        }
    }

    private fun setEmailListener() {
        emailListener = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                clearEmailErrorMessage()
            }

            override fun afterTextChanged(s: Editable?) {
                val email = s.toString()
                signInViewModel.setEmailForgotInForgotPas(email)

                val isEmailValid = email.isNotEmpty()
                shouldContinueButton(isEmailValid)
            }
        }
        binding.emailInput.addTextChangedListener(emailListener)
    }

    private fun handleEmailError(errorMessage: Int, errorDrawable: Int) {
        binding.emailErrorText.visibility = View.VISIBLE
        binding.emailErrorText.setText(errorMessage)
        binding.emailInput.background = ContextCompat.getDrawable(requireContext(), errorDrawable)
        binding.emailInput.requestFocus()
    }

    private fun clearEmailErrorMessage() {
        binding.emailErrorText.visibility = View.GONE
        binding.emailInput.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }

    private fun shouldContinueButton(isEnabled: Boolean) {
        binding.btnContinue.isEnabled = isEnabled
        binding.btnContinue.isClickable = isEnabled
    }

    @SuppressLint("InflateParams")
    private fun blockedDialog() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.RECAPTCHA_BLOCKED_USER)
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setMessage(getString(R.string.blocked_suspicious_activity))
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }
    override fun onResume() {
        super.onResume()
        signInViewModel.setAuthMethod(AuthMethod.EMAIL)
    }
    override fun onDestroyView() {
        super.onDestroyView()
        binding.emailInput.removeTextChangedListener(emailListener)
        _binding = null
    }


}