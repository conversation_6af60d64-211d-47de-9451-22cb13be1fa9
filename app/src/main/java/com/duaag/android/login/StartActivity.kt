package com.duaag.android.login

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.applovin.sdk.AppLovinSdk
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.aws.FetchUserInfoListener
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.clevertap.onLoginCleverTap
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendLogInEventCleverTapUxCam
import com.duaag.android.databinding.ActivityStartBinding
import com.duaag.android.exceptions.ServerErrorException
import com.duaag.android.exceptions.UserDeletedExistException
import com.duaag.android.exceptions.UserNotExistException
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.logevents.firebaseanalytics.setUserId
import com.duaag.android.login.di.LogInComponent
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.ForgotPasswordAuthResult
import com.duaag.android.signup.models.SignInAuthResult
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.checkIfAppVersionIsSupported
import com.duaag.android.utils.openHomeActivity
import com.duaag.android.uxcam.sendUxCamEvent
import com.facebook.AccessToken
import com.facebook.login.LoginManager
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.Status
import com.google.android.gms.recaptcha.Recaptcha
import com.google.android.gms.recaptcha.RecaptchaAction
import com.google.android.gms.recaptcha.RecaptchaActionType
import com.google.android.gms.recaptcha.RecaptchaHandle
import com.uxcam.UXCam
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class StartActivity : AppCompatActivity() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel> { viewModelFactory }
    lateinit var loginComponent: LogInComponent

    private var recaptchaHandle: RecaptchaHandle? = null
    @Inject
    lateinit var duaAccount: DuaAccount

    private val remoteConfigUpdatedBroadcastReceiver by lazy { RemoteConfigUpdatedBroadcastReceiver() }


    private lateinit var binding: ActivityStartBinding

    companion object {
        const val FACEBOOK_TAG = "FACEBOOKTAG"
        const val SPOTTED_TOKEN= "spotted_token"

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        disconnectWithFacebook()
        initReCaptcha()

        loginComponent = (application as DuaApplication).appComponent.logInComponent().create()
        loginComponent.inject(this)

        Timber.tag("VIEWMODEL").d(signInViewModel.toString())

        binding = ActivityStartBinding.inflate(layoutInflater)

        ContextCompat.registerReceiver(
            this,
            remoteConfigUpdatedBroadcastReceiver,
            IntentFilter(RemoteConfigUtils.REMOTE_CONFIG_UPDATED_BROADCAST),
            ContextCompat.RECEIVER_EXPORTED
        )

        setUpLiveDataCallbacks()

        setContentView(binding.root)

        binding.backBtn.setOnClickListener {
            onBackPressed()
        }


        findNavController(R.id.nav_host_fragment_start).addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.start2Fragment -> {
                    binding.toolbar.visibility = View.GONE
                    signInViewModel.clearSignInData()
                }
                else -> {
                    binding.toolbar.visibility = View.VISIBLE
                }
            }
        }

        checkIfAppVersionIsSupported(this, null)
    }


    private fun setUpLiveDataCallbacks() {
        signInViewModel.signInResultLiveData.observe(this) { signInAuthResult ->
            when (signInAuthResult) {
                is SignInAuthResult.Success -> {
                    signInViewModel.getUserProfile().observe(this) {
                        when (it) {
                            is Result.Success -> {
                                val user = it.data

                                DuaApplication.instance.sendTokenToServerAndSave()

                                Timber.tag("signInCallback").e(it.data.language)

                                //Convert from al to sq
                                val lang = it.data.language
                                ModifiedLingver.getInstance()
                                        .setLocale(this@StartActivity, lang, "")
                                duaAccount.getNotification()


                                signInViewModel.thirdPartyUserData?.let {
                                    val authMethodName: String

                                    when(it.authMethod.methodName) {
                                        AuthMethod.GOOGLE.methodName -> {
                                            sendGmailSelectedEvent(ClevertapOnboardingTypeValues.SIGN_IN)

                                            authMethodName = FirebaseAnalyticsEventsName.GOOGLE.value
                                        }
                                        AuthMethod.FACEBOOK.methodName -> {
                                            authMethodName = FirebaseAnalyticsEventsName.FACEBOOK.value
                                        }
                                        else -> {
                                            authMethodName = it.authMethod.methodName
                                        }
                                    }

                                    firebaseLogEvent(authMethodName, mapOf(
                                        FirebaseAnalyticsParameterName.SIGN_IN.value to 1L
                                    ))
                                    sendClevertapEvent(ClevertapEventEnum.SIGN_IN_INITIATED, mapOf(
                                            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to it.authMethod.methodName
                                        ),
                                        false
                                    )
                                    sendUxCamEvent(
                                        ClevertapEventEnum.SIGN_IN_INITIATED, mapOf(
                                            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to it.authMethod.methodName
                                        )
                                    )
                                    Timber.tag("ESAT_TAGG").d("ClevertapEventEnum.SIGN_IN_INITIATED, ${it.authMethod.methodName}")

                                }
                                linkCleverTapAppsFlyer()
                                AppLovinSdk.getInstance(this).userIdentifier = user.cognitoUserId
                                UXCam.setUserIdentity(user.cognitoUserId)
                                signInViewModel.sendDataToAppsflyer()

                                onLoginCleverTap(signInViewModel.userEmail, signInViewModel.phoneNumber , user.cognitoUserId)

                                if (signInViewModel.duaSharedPrefs.isFirstLoginAfterInstall()) {
                                    sendAppsflyerEvent(AppsflyerEventsNameEnum.FIRST_LOGIN_AFTER_INSTALL)
                                    signInViewModel.duaSharedPrefs.firstLoginAfterInstall()
                                }

                                val premiumType = getPremiumTypeEventProperty(user)

                                sendAppsflyerEvent(AppsflyerEventsNameEnum.LOG_IN)
                                sendLogInEventCleverTapUxCam(
                                    signInViewModel.authMethod.value ?: signInViewModel.thirdPartyUserData?.authMethod,
                                    premiumType)

                                duaAccount.duaSharedPrefs.setAuthMethode(signInViewModel.authMethod.value?.methodName)
                                setUserId(user.cognitoUserId)
                                duaAccount.duaSharedPrefs.setUserGender(user.gender)

                                signInViewModel.loggingInProgress  = false

                                openHomeActivity(this)
                            }
                            is Result.Error -> {
                                signInViewModel.loggingInProgress  = false

                                when (it.exception) {
                                    is UserNotExistException -> {

                                        AWSInteractor.fetchUserInfo(object : FetchUserInfoListener {
                                            override fun onResult(cognitoId: String, email: String?) {
                                                val intent = Intent(this@StartActivity, SignUpActivity::class.java)
                                                intent.putExtra(
                                                    SignUpActivity.DIRECT_TO_CREATE_PROFILE,
                                                    true
                                                )
                                                signInViewModel.thirdPartyUserData?.let {
                                                    signInViewModel.duaSharedPrefs.setHasSeenShadowDialog(false)

                                                    onLoginCleverTap(signInViewModel.userEmail, signInViewModel.phoneNumber, cognitoId)

                                                    when(it.authMethod) {
                                                        AuthMethod.GOOGLE -> {
                                                            firebaseLogEvent(
                                                                FirebaseAnalyticsEventsName.GOOGLE, mapOf(
                                                                    FirebaseAnalyticsParameterName.SIGN_UP.value to 1L))

                                                            val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.GOOGLE.value

                                                            sendGmailSelectedEvent(ClevertapOnboardingTypeValues.SIGN_UP)

                                                            sendClevertapEvent(
                                                                ClevertapEventEnum.SIGN_UP_INITIATED, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium))

                                                            sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_ACCOUNT_CREATION_INITIATED)

                                                            intent.putExtra(
                                                                SignUpActivity.AUTH_METHOD_EXTRA,
                                                                it.authMethod
                                                            )
                                                            intent.putExtra(SignUpActivity.THIRD_PARTY_DATA, it)

                                                            sendClevertapEvent(
                                                                ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.GOOGLE.methodName,
                                                                    ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to it.email.isNullOrEmpty().not()))
                                                            sendUxCamEvent(
                                                                ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.GOOGLE.methodName,
                                                                    ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to it.email.isNullOrEmpty().not()))

                                                            logSignUpEvent(AuthMethod.GOOGLE, FirebaseAnalyticsEventsName.CREATE_ACCOUNT)
                                                        }
                                                        AuthMethod.FACEBOOK -> {
                                                            firebaseLogEvent(
                                                                FirebaseAnalyticsEventsName.FACEBOOK, mapOf(
                                                                    FirebaseAnalyticsParameterName.SIGN_UP.value to 1L))

                                                            val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.FACEBOOK.value

                                                            sendClevertapEvent(
                                                                ClevertapEventEnum.SIGN_UP_INITIATED, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium))

                                                            sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_ACCOUNT_CREATION_INITIATED)

                                                            intent.putExtra(
                                                                SignUpActivity.AUTH_METHOD_EXTRA,
                                                                it.authMethod
                                                            )
                                                            intent.putExtra(SignUpActivity.THIRD_PARTY_DATA, it)

                                                            sendClevertapEvent(
                                                                ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.FACEBOOK.methodName,
                                                                    ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to it.email.isNullOrEmpty().not()))
                                                            sendUxCamEvent(
                                                                ClevertapEventEnum.CREATE_ACCOUNT, mapOf(
                                                                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.FACEBOOK.methodName,
                                                                    ClevertapEventPropertyEnum.EMAIL_INFO_ACCESS.propertyName to it.email.isNullOrEmpty().not()))

                                                            logSignUpEvent(AuthMethod.FACEBOOK, FirebaseAnalyticsEventsName.CREATE_ACCOUNT)
                                                        }
                                                        else -> {}
                                                    }
                                                }
                                                email?.let {
                                                    intent.putExtra(SignUpActivity.USER_EMAIL, it)
                                                }
                                                signInViewModel.signUpWithSpottedEntity?.let {
                                                    intent.putExtra(SignUpActivity.SPOTTED_DATA, it)
                                                }
                                                duaAccount.duaSharedPrefs.setAuthMethode(signInViewModel.authMethod.value?.methodName)
                                                startActivity(intent)
                                            }

                                            override fun onError(e: Exception?) {
                                                lifecycleScope.launch(Dispatchers.Main) {
                                                    duaAccount.deleteAllData()
                                                    LoginManager.getInstance().logOut()
                                                    AWSInteractor.logOut()
                                                    signInViewModel.setShowHidenFaceBookProgressBar(false)
                                                    ToastUtil.toast(R.string.an_error_occurred)
                                                    logError(ErrorStatus.SIGN_IN_RESULT_LIVEDATA)
                                                }
                                            }
                                        })
                                    }
                                    is UserDeletedExistException -> {
                                        duaAccount.deleteAllData()
                                    }
                                    is ServerErrorException -> {
                                        val currentDestinationId = findNavController(R.id.nav_host_fragment_start).currentDestination?.id
                                        if (currentDestinationId == R.id.signInFragment) {
                                            AWSInteractor.logOut()
                                            signInViewModel.setSignInResultLiveData(SignInAuthResult.Error(ServerErrorException("ServerError")))
                                        } else {
                                            LoginManager.getInstance().logOut()
                                            AWSInteractor.logOut()
                                            signInViewModel.setShowHidenFaceBookProgressBar(false)
                                            ToastUtil.toast(R.string.an_error_occurred)
                                            logError(ErrorStatus.SIGN_IN_RESULT_LIVEDATA)
                                        }
                                    }
                                }
                            }
                            else -> {}
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private fun sendGmailSelectedEvent(onBoardingType: ClevertapOnboardingTypeValues) {
        sendClevertapEvent(ClevertapEventEnum.GMAIL_SELECTED,
            mapOf(
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to onBoardingType.value,
            )
        )
        sendUxCamEvent(ClevertapEventEnum.GMAIL_SELECTED,
            mapOf(
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to onBoardingType.value
            )
        )
    }

    private fun isFacebookLoggedIn(): Boolean {
        val accessToken = AccessToken.getCurrentAccessToken()
        return accessToken != null && !accessToken.isExpired
    }

    private fun disconnectWithFacebook() {
        if (isFacebookLoggedIn()) {
            LoginManager.getInstance().logOut()
        }
    }

    private fun initReCaptcha(){
        Recaptcha.getClient(this)
            .init(BuildConfig.RE_CAPTCHA_API_KEY)
            .addOnSuccessListener(this) {
                // Handle success
                recaptchaHandle = it

            }
            .addOnFailureListener(this){
                // Handle error if needed in the future
                if (it is ApiException) {
                    val apiException: ApiException = it as ApiException
                    val apiErrorStatus: Status = apiException.status
                    // Handle api errors ...
                } else {
                    // Handle other failures ...
                }
            }
    }

    fun executeForgotPassReCaptcha(){
        signInViewModel.setForgotPassResultLiveData(ForgotPasswordAuthResult.Loading(false))
        if (recaptchaHandle != null){
            Recaptcha.getClient(this).execute(recaptchaHandle!!, RecaptchaAction(RecaptchaActionType.OTHER))
                .addOnSuccessListener(this) {
                    val reCaptchaToken = it.tokenResult
                    if (reCaptchaToken.isNotEmpty()){
                        signInViewModel.forgotPassword(reCaptchaToken)
                    } else {
                        signInViewModel.forgotPassword("")
                    }
                }
                .addOnFailureListener(this) {
                    if (it is ApiException) {
                        val apiException: ApiException = it as ApiException
                        val apiErrorStatus: Status = apiException.status
                        // Handle api errors ...
                    } else {
                        // Handle other failures ...
                    }
                    signInViewModel.forgotPassword("")

                }
        } else {
            signInViewModel.forgotPassword("")
        }
    }

    private fun closeReCaptcha(){
        recaptchaHandle?.let {
            Recaptcha.getClient(this).close(it)
                .addOnSuccessListener(this) {
                    recaptchaHandle = null
                }
                .addOnFailureListener(this) {
                    if (it is ApiException) {
                        val apiException: ApiException = it as ApiException
                        val apiErrorStatus: Status = apiException.status
                        // Handle api errors ...
                    } else {
                        // Handle other failures ...
                    }
                    recaptchaHandle = null
                }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(remoteConfigUpdatedBroadcastReceiver)

        closeReCaptcha()
    }

    inner class RemoteConfigUpdatedBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Timber.tag("ESAT_LOGI").d("RemoteConfigUpdatedBroadcastReceiver onReceive")

            checkIfAppVersionIsSupported(this@StartActivity, null)
        }
    }
}