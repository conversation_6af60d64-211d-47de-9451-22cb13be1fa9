package com.duaag.android.login.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.LogInItemRvBinding
import com.duaag.android.login.models.LoginInfoDataClass
import com.uxcam.UXCam

class LoginInfoAdapter(val list: List<LoginInfoDataClass>) : RecyclerView.Adapter<LoginInfoAdapter.LoginInfoViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LoginInfoViewHolder {
        val binding: LogInItemRvBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.log_in_item_rv, parent, false)
        return LoginInfoViewHolder(binding)
    }

    override fun onBindViewHolder(holder: LoginInfoViewHolder, position: Int) {
        holder.binding.loginMethodClass = list[position]
    }

    override fun getItemCount(): Int = list.size

    class LoginInfoViewHolder(val binding: LogInItemRvBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            UXCam.occludeSensitiveView(binding.loginItemTxt)
        }
        fun bind(loginInfoDataClass: LoginInfoDataClass) {
            binding.loginMethodClass = loginInfoDataClass
            binding.executePendingBindings()
        }
    }

}