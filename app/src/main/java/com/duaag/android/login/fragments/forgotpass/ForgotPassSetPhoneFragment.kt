package com.duaag.android.login.fragments.forgotpass

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentForgotPassSetPhoneBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.fragments.SignInFragment
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.coutry.detectPrefixCountry
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.setWidth
import com.duaag.android.utils.showKeyboard
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ForgotPassSetPhoneFragment : Fragment() {

    companion object {
        fun newInstance(): ForgotPassSetPhoneFragment = ForgotPassSetPhoneFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({ activity as StartActivity }) { viewModelFactory }
    private var _binding: FragmentForgotPassSetPhoneBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        _binding = FragmentForgotPassSetPhoneBinding.inflate(inflater, container, false)

        sendClevertapEvent(ClevertapEventEnum.FORGOT_PASSWORD_SCREENVIEW, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
        ))

        binding.btnContinue.isEnabled = false
        binding.btnContinue.isClickable = false

        binding.let {
            it.countrycodepicker.detectPrefixCountry()
            signInViewModel.selectedCountryCodeAsInt?.let { selectedCountryCodeAsInt ->
                it.countrycodepicker.setCountryForPhoneCode(selectedCountryCodeAsInt)
            }

            it.countrycodepicker.registerPhoneNumberTextView(it.phoneNumberInput)

            it.phoneNumberInput.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    try {
                        signInViewModel.setPhoneNumberInForgotPass(it.countrycodepicker.selectedCountryCodeWithPlus + it.countrycodepicker.phoneNumber.nationalNumber)
                    } catch (e: Exception) {
                        signInViewModel.setPhoneNumberInForgotPass("")
                    }

                    if (binding.phoneNumberInput.text.isNotEmpty()) {
                        binding.btnContinue.isEnabled = true
                        binding.btnContinue.isClickable = true
                    } else {
                        binding.btnContinue.isEnabled = false
                        binding.btnContinue.isClickable = false
                    }
                }
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    clearPhoneError()
                }
            })

            it.countrycodepicker.setPhoneNumberInputValidityListener { _, isValid ->
                if (binding.countrycodepicker.isValid) {
                    binding.btnContinue.isEnabled = true
                    binding.btnContinue.isClickable = true
                }
            }

            it.countrycodepicker.setOnCountryChangeListener { _ ->
                signInViewModel.selectedCountryCodeAsInt =
                    it.countrycodepicker.selectedCountryCodeAsInt

                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        if (binding.countrycodepicker.isValid) {
                            binding.btnContinue.isEnabled = true
                            binding.btnContinue.isClickable = true
                        }
                    }
                }

            }
            it.phoneNumberInput.onKeyboardNext {
            }

        }

        if (binding.countrycodepicker.isValid) {
            binding.btnContinue.isEnabled = true
            binding.btnContinue.isClickable = true
        }


        binding.btnContinue.setOnClickListener {
            logSignUpEvent(AuthMethod.PHONE, FirebaseAnalyticsEventsName.FP_TYPE_MEDIUM)

            validateInput(signInViewModel.phoneEmail.value.toString())
        }

        binding.phoneNumberInput.onKeyboardDone {
            binding.btnContinue.performClick()
        }

        signInViewModel.signInCurrentPage.observe(viewLifecycleOwner) { page ->
            if (page == SignInFragment.SIGN_IN_PHONE_PAGE) {
                binding.phoneNumberInput.requestFocus()
                binding.phoneNumberInput.showKeyboard()
            }
        }

        binding.phoneNumberInput.setOnFocusChangeListener { _, hasFocus ->
            setContainerBackground(hasFocus)
        }

        return binding.root
    }

    private fun validateInput(phoneNumber: String) {
        if ((phoneNumber.isNotEmpty() && binding.countrycodepicker.isValid)) {
            val action = ForgotPasswordFragmentDirections.actionForgotPasswordFragmentToForgotPassNewPassFragmentV2()
            findNavController().navigate(action)
        } else {
            if (!binding.countrycodepicker.isValid) {
                handlePhoneError(R.string.phone_number_is_not_valid, R.drawable.error_corners_12dp)
            }
        }
    }

    private fun handlePhoneError(errorMessage: Int, errorDrawable: Int) {
        binding.phoneErrorText.visibility = View.VISIBLE
        binding.phoneErrorText.setText(errorMessage)
        binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.red_500)
        binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), errorDrawable)
        binding.phoneInputSeparator.setWidth(1f)
    }

    private fun clearPhoneError() {
        binding.phoneErrorText.visibility = View.GONE
        binding.phoneInputSeparator.setWidth(1f)

        binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.gray_200)
        setContainerBackground(binding.phoneNumberInput.hasFocus())
    }

    private fun setContainerBackground(hasFocus: Boolean) {
        if (hasFocus) {
            binding.phoneInputSeparator.setWidth(2f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.bg_input_focus)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_focused)
        } else {
            binding.phoneInputSeparator.setWidth(1f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.border)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_unfocused)
        }
    }

    @SuppressLint("InflateParams")
    private fun blockedDialog() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.RECAPTCHA_BLOCKED_USER)
        val builder = AlertDialog.Builder(
            requireContext(),
            R.style.ThemeOverlay_MaterialComponents_Dialog_Alert
        )

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setMessage(getString(R.string.blocked_suspicious_activity))
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.blue_500
                    )
                )
            }
            show()
        }
    }

    override fun onResume() {
        super.onResume()
        signInViewModel.setAuthMethod(AuthMethod.PHONE)
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}