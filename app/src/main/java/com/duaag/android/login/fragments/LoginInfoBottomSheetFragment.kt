package com.duaag.android.login.fragments

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.FragmentShowLoginInfoBottomsheetBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.login.adapter.LoginInfoAdapter
import com.duaag.android.login.models.LoginInfoDataClass
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import javax.inject.Inject


class LoginInfoBottomSheetFragment : BottomSheetDialogFragment() {

    @Inject
    lateinit var duaAccount: DuaAccount
    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }


    private  var _binding: FragmentShowLoginInfoBottomsheetBinding? = null
    private val binding get() = _binding!!

    private var logOutSpinningCircleDialog: SpinningCircleDialog? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentShowLoginInfoBottomsheetBinding.inflate(inflater)

        logOutSpinningCircleDialog = context?.let { SpinningCircleDialog(it) }

        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initializeRecyclerView()

        binding.logOutBtn.setOnClickListener {
            createLogOutDialog1()
        }
    }


    private fun initializeRecyclerView() {
        binding.loginInfoRv.layoutManager = LinearLayoutManager(context)
        val adapter = LoginInfoAdapter(getRercyclerViewItems())
        binding.loginInfoRv.adapter = adapter
    }


    private fun getRercyclerViewItems(): List<LoginInfoDataClass> {
        when {
            settingsViewModel.accountModel.value?.googleId != null -> {
                val items by lazy {
                    mutableListOf(LoginInfoDataClass(R.drawable.google_icon_outline, settingsViewModel.accountModel.value?.email))
                }
                return items
            }
            settingsViewModel.accountModel.value?.facebookId != null -> {
                val items by lazy {
                    mutableListOf(LoginInfoDataClass(R.drawable.ic_facebook_login, getString(R.string.facebook)))
                }
                return items
            }
            (settingsViewModel.accountModel.value?.facebookId == null && (settingsViewModel.accountModel.value?.phone != null && settingsViewModel.accountModel.value?.email != null)) -> {
                val items by lazy {
                    mutableListOf(
                            LoginInfoDataClass(R.drawable.ic_phone_login_info, settingsViewModel.accountModel.value?.phone),
                            LoginInfoDataClass(R.drawable.ic_email_login_info, settingsViewModel.accountModel.value?.email))
                }
                return items
            }
           ( settingsViewModel.accountModel.value?.phone != null && settingsViewModel.accountModel.value?.email == null) -> {
                val items by lazy {
                    mutableListOf(
                            LoginInfoDataClass(R.drawable.ic_phone_login_info, settingsViewModel.accountModel.value?.phone))
                }
                return items
            }
            (settingsViewModel.accountModel.value?.email != null &&  settingsViewModel.accountModel.value?.phone == null) -> {
                val items by lazy {
                    mutableListOf(
                            LoginInfoDataClass(R.drawable.ic_email_login_info, settingsViewModel.accountModel.value?.email))
                }
                return items
            }
            else -> {
                val items by lazy {
                    mutableListOf(
                            LoginInfoDataClass(R.drawable.ic_phone_login_info, settingsViewModel.accountModel.value?.phone),
                            LoginInfoDataClass(R.drawable.ic_email_login_info, settingsViewModel.accountModel.value?.email))
                }
                return items
            }
        }
    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
                super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                    d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                        BottomSheetBehavior.STATE_EXPANDED
            }
            d.dismissWithAnimation = true
        }
        return dialog
    }


    private fun createLogOutDialog1() {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)
        builder.apply {

            setTitle(resources.getString(R.string.sign_out))
                    .setMessage(resources.getString(R.string.sign_out_string))
                    .setNegativeButton(resources.getString(R.string.cancel)) { dialog, _ ->
                        dialog.cancel()
                    }
                    .setPositiveButton(resources.getString(R.string.ok_dialog)) { _, _ ->
                        logOutSpinningCircleDialog?.show()
                        logOutFromDevice()
                    }

        }
        return builder.create().run { show() }
    }


    private fun logOutFromDevice() {
        duaAccount.deleteUserDevice() { result ->
            if (result) {
//                ModifiedLingver.getInstance().setLocale(requireContext(), LanguageConstants.LANGUAGE_ENGLISH, "")

                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.LOG_OUT_REGULAR, mapOf(
                        FirebaseAnalyticsParameterName.LOG_OUT_REGULAR_COUNT.value to 1L))

                val eventPremiumType = getPremiumTypeEventProperty(settingsViewModel.userProfile.value)


                val logOutTypeValues = ClevertapLogOutTypeValues.REGULAR.values

                sendClevertapEvent(ClevertapEventEnum.LOG_OUT, mapOf(
                        ClevertapEventPropertyEnum.COMMUNITY.propertyName to settingsViewModel.userProfile.value?.communityInfo?.id,
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutTypeValues
                ))

                duaAccount.deleteAllData()
                logOutSpinningCircleDialog?.dismiss()
            } else {
                logOutSpinningCircleDialog?.dismiss()
                ToastUtil.toast(resources.getString(R.string.smthg_went_wrong))
                logError(ErrorStatus.LOG_OUT_SETTINGS)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        logOutSpinningCircleDialog = null
    }
}
