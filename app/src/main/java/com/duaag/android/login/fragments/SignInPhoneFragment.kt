package com.duaag.android.login.fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.amazonaws.AmazonClientException
import com.amazonaws.services.cognitoidentityprovider.model.NotAuthorizedException
import com.amazonaws.services.cognitoidentityprovider.model.UserNotFoundException
import com.duaag.android.R
import com.duaag.android.aws.models.LoginModel
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentSignInPhoneBinding
import com.duaag.android.exceptions.ServerErrorException
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSignInSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.models.Credentials
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.SignInAuthResult
import com.duaag.android.utils.coutry.detectPrefixCountry
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setWidth
import com.duaag.android.utils.showKeyboard
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject

class SignInPhoneFragment : Fragment() {

    companion object {
        fun newInstance(): SignInPhoneFragment = SignInPhoneFragment()
    }

    private var _binding: FragmentSignInPhoneBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({ activity as StartActivity }) { viewModelFactory }

    private var passwordListener: TextWatcher? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentSignInPhoneBinding.inflate(inflater)

        binding.signInButton.setOnSingleClickListener(2500L) {
            logSignUpEvent(AuthMethod.PHONE, FirebaseAnalyticsEventsName.SIGN_IN,FirebaseAnalyticsSignInSourceValues.DEFAULT)

//            signInViewModel.sendCredentialsBeforeLoginIn(binding.phoneNumberInput.text.toString(),binding.passwordInput.text.toString())
            validateInput(binding.phoneNumberInput.text.toString(), binding.passwordInput.text.toString())
            this.hideKeyboard()
        }

        binding.forgot.setOnSingleClickListener(1000L) {
            sendClevertapEvent(ClevertapEventEnum.FORGOT_PASSWORD_INITIATED,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
                )
            )
            sendUxCamEvent(ClevertapEventEnum.FORGOT_PASSWORD_INITIATED,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
                )
            )

            signInViewModel.setOnForgotPasswordClicked(AuthMethod.PHONE)
        }

        signInViewModel.userNotFound.observe(viewLifecycleOwner) { userNotFound ->
            if (userNotFound) {
                shouldEnableSignIn(false)
                val intent = Intent(requireActivity(), SignUpActivity::class.java)
                intent.putExtra(SignUpActivity.DIRECT_TO_CREATE_PROFILE, true)
                signInViewModel.thirdPartyUserData?.let {
                    intent.putExtra(SignUpActivity.AUTH_METHOD_EXTRA, it.authMethod)
                    intent.putExtra(SignUpActivity.THIRD_PARTY_DATA, it)
                }
                startActivity(intent)
            } else {
                shouldEnableSignIn(true)
            }
        }

        binding.phoneNumberInput.setText( if (signInViewModel.phoneNumber.length > 4) signInViewModel.phoneNumber.substring(
            4) else signInViewModel.phoneNumber )
        binding.passwordInput.setText(signInViewModel.userPassword)
        isInputEmpty(signInViewModel.phoneNumber, signInViewModel.userPassword)
        setCountryCodePickerListener()
        setPasswordListener()

        signInViewModel.signInResultLiveData.observe(viewLifecycleOwner) { signInAuthResult ->
            when (signInAuthResult) {
                is SignInAuthResult.Success -> {
//                    signInViewModel.fetchUserProfile()
                }

                is SignInAuthResult.Error -> {
                    binding.signInButton.isEnabled = true

                    when (signInAuthResult.e) {
                        is UserNotFoundException -> {
                            sendWrongAccountInformationEvent()
                            handlePasswordError(R.string.wrong_param, R.drawable.error_corners_12dp)
                        }
                        is NotAuthorizedException -> {
                            sendWrongAccountInformationEvent()
                            handlePasswordError(R.string.wrong_param, R.drawable.error_corners_12dp)
                        }
                        is AmazonClientException -> {
                            handlePasswordError(R.string.no_internet_connection, R.drawable.error_corners_12dp)
                        }
                        is ServerErrorException -> {
                            handlePasswordError(R.string.an_error_occurred, R.drawable.error_corners_12dp)
                        }
                        else -> {
                            handlePasswordError(R.string.an_error_occurred, R.drawable.error_corners_12dp)
                        }
                    }
                    logError(ErrorStatus.SIGN_IN_PHONE_RESULT)
                }
                else -> {}
            }
        }

        binding.phoneNumberInput.setOnFocusChangeListener { _, hasFocus ->
            setContainerBackground(hasFocus)
        }

        binding.phoneNumberInput.onKeyboardNext {
            binding.scrollView.post {
                binding.passwordInput.requestFocus()
            }
        }

        binding.passwordInput.onKeyboardDone {
            binding.signInButton.performClick()
        }

        signInViewModel.signInCurrentPage.observe(viewLifecycleOwner) { page ->
            if (page == SignInFragment.SIGN_IN_PHONE_PAGE) {
                binding.phoneNumberInput.requestFocus()
                binding.phoneNumberInput.showKeyboard()
            }
        }

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sendClevertapEvent(ClevertapEventEnum.SIGN_IN_SCREENVIEW, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
        ))
        sendUxCamEvent(ClevertapEventEnum.SIGN_IN_SCREENVIEW, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
        ))
    }

    private fun setCountryCodePickerListener() {
        binding.let { ccp ->
            ccp.ccPicker.detectPrefixCountry()
            signInViewModel.selectedCountryCodeAsInt?.let { selectedCountryCode ->
                ccp.ccPicker.setCountryForPhoneCode(selectedCountryCode)
            }
            ccp.ccPicker.registerPhoneNumberTextView(ccp.phoneNumberInput)
            ccp.phoneNumberInput.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    clearPhoneError()
                }

                override fun afterTextChanged(s: Editable?) {
                    try {
                        signInViewModel.setPhoneNumber(ccp.ccPicker.selectedCountryCodeWithPlus + ccp.ccPicker.phoneNumber.nationalNumber)
                    } catch (e: Exception) {
                        signInViewModel.setPhoneNumber("")
                    }
                        val isPhoneNumberValid = binding.phoneNumberInput.text.isNotEmpty()
                        val isPasswordValid = binding.passwordInput.text.isNotEmpty()

                        shouldEnableSignIn(isPhoneNumberValid && isPasswordValid)
                }
            })

            ccp.ccPicker.setOnCountryChangeListener {
                signInViewModel.selectedCountryCodeAsInt = ccp.ccPicker.selectedCountryCodeAsInt
            }
        }
    }

    private fun setPasswordListener() {
        passwordListener = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                clearPasswordError()
            }

            override fun afterTextChanged(s: Editable?) {
                val password = s.toString()
                signInViewModel.setPassword(password)

                val isPasswordValid = password.isNotEmpty()
                val isPhoneNumberValid = binding.phoneNumberInput.text.isNotEmpty()

                shouldEnableSignIn(isPhoneNumberValid && isPasswordValid)
            }
        }
        binding.passwordInput.addTextChangedListener(passwordListener)
    }

    private fun validateInput(phoneNumber: String, password: String) {
        if ((phoneNumber.isNotEmpty() && binding.ccPicker.isValid) && password.length >= 6) {
            val credentials = Credentials(phone = signInViewModel.phoneNumber, password = signInViewModel.userPassword)
            signInViewModel.sendCredentialsBeforeLoginIn(credentials)
            val loginModel = LoginModel(signInViewModel.phoneNumber, signInViewModel.userPassword)
            sendSignInInitiatedEvent()
            signInViewModel.loginUser(loginModel)
            binding.signInButton.isEnabled = false
        } else {
            if (!binding.ccPicker.isValid) {
                handlePhoneError(R.string.phone_number_is_not_valid, R.drawable.error_corners_12dp)
            }
            if (password.length < 6) {
                handlePasswordError(R.string.wrong_param, R.drawable.error_corners_12dp)
            }
        }
    }

    private fun sendSignInInitiatedEvent() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.START_SIGN_IN)
        sendClevertapEvent(ClevertapEventEnum.SIGN_IN_INITIATED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
            ),
            false)
        sendUxCamEvent(ClevertapEventEnum.SIGN_IN_INITIATED, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to AuthMethod.PHONE.methodName
        ))
    }

    private fun isInputEmpty(phoneNumber: String = "", password: String = "") {
        if (phoneNumber.isNotEmpty() && password.isNotEmpty()) {
            shouldEnableSignIn(true)
        } else {
            shouldEnableSignIn(false)
        }
    }

    private fun handlePasswordError(errorMessage: Int, errorDrawable: Int) {
        binding.passwordErrorText.visibility = View.VISIBLE
        binding.passwordErrorText.setText(errorMessage)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), errorDrawable)
    }

    private fun handlePhoneError(errorMessage: Int, errorDrawable: Int) {
        binding.phoneErrorText.visibility = View.VISIBLE
        binding.phoneErrorText.setText(errorMessage)
        binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.red_500)
        binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), errorDrawable)
        binding.phoneInputSeparator.setWidth(1f)
    }

    private fun clearPasswordError() {
        binding.passwordErrorText.visibility = View.GONE
        binding.phoneInputSeparator.setWidth(1f)

        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }

    private fun clearPhoneError() {
        binding.phoneErrorText.visibility = View.GONE
        binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.gray_200)
        setContainerBackground(binding.phoneNumberInput.hasFocus())
    }


    private fun setContainerBackground(hasFocus: Boolean) {
        if (hasFocus) {
            binding.phoneInputSeparator.setWidth(2f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.bg_input_focus)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_focused)
        } else {
            binding.phoneInputSeparator.setWidth(1f)

            binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.border)
            binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.border_unfocused)
        }
    }

    private fun shouldEnableSignIn(isEnabled: Boolean) {
        binding.signInButton.isEnabled = isEnabled
        binding.signInButton.isClickable = isEnabled
    }

    override fun onResume() {
        super.onResume()
        signInViewModel.setAuthMethod(AuthMethod.PHONE)
    }

    private fun sendWrongAccountInformationEvent() {
        sendClevertapEvent(ClevertapEventEnum.WRONG_ACCOUNT_INFORMATION)
        sendUxCamEvent(ClevertapEventEnum.WRONG_ACCOUNT_INFORMATION)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.passwordInput.removeTextChangedListener(passwordListener)
        _binding = null
    }
}