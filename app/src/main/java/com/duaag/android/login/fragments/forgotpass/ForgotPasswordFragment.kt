package com.duaag.android.login.fragments.forgotpass

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentForgotPassBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.fragments.SignInFragment
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.user_feed.adapters.UserFeedAdapter
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject

class ForgotPasswordFragment : Fragment() {

    companion object {
        fun newInstance(): ForgotPasswordFragment = ForgotPasswordFragment()
    }

    private var _binding: FragmentForgotPassBinding? = null
    private val binding get() = _binding!!

    private var customTabLayoutMediator: CustomTabLayoutMediator? = null


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel> ({ activity as StartActivity }) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentForgotPassBinding.inflate(inflater)

        initialize()

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        signInViewModel.onForgotPasswordClicked.observe(viewLifecycleOwner) { authMethod ->
            sendClevertapEvent(ClevertapEventEnum.FORGOT_PASSWORD_INITIATED,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signInViewModel.authMethod.value?.methodName
                )
            )
            sendUxCamEvent(ClevertapEventEnum.FORGOT_PASSWORD_INITIATED,
                mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signInViewModel.authMethod.value?.methodName
                )
            )
            logSignUpEvent(authMethod, FirebaseAnalyticsEventsName.FORGOT_PASSWORD)

            val action = ForgotPasswordFragmentDirections.actionForgotPasswordFragmentToForgotPassNewPassFragmentV2()
            findNavController().navigate(action)

            this.hideKeyboard()
        }

        return binding.root
    }


    fun initialize() {
        val viwPagerAdapter = UserFeedAdapter(childFragmentManager, viewLifecycleOwner.lifecycle)
        viwPagerAdapter.setFragments(listOf(ForgotPassSetPhoneFragment.newInstance(), ForgotPassSetEmailFragment.newInstance()))
        binding.viewpager.isUserInputEnabled = true
        binding.viewpager.offscreenPageLimit = 1
        binding.viewpager.adapter = viwPagerAdapter
        binding.viewpager.setCurrentItem(arguments?.getInt(SignInFragment.CURRENT_PAGE) ?: 0, false)

        binding.viewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                signInViewModel.setSignInCurrentPage(position)
            }
        })

        setupCustomTabLayout()
    }


    override fun onDestroyView() {
        super.onDestroyView()

        hideKeyboard()

        binding.viewpager.adapter = null

        customTabLayoutMediator?.detach()
        customTabLayoutMediator = null

        _binding = null
    }


    private fun setupCustomTabLayout() {
        binding.tabLayout.setSelectedTabIndicator(null) // Remove the default indicator

        customTabLayoutMediator = CustomTabLayoutMediator(
            binding.tabLayout,
            binding.viewpager
        ) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.phone)
                else -> getString(R.string.email_)
            }
        }

        customTabLayoutMediator?.attach()
    }
}