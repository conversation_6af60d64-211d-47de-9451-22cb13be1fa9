package com.duaag.android.data.prefs;

public interface Configs {
    void setIsChatScreenVisible(boolean isVisible);

    boolean isChatScreenVisible();

    void setShouldShowMessageOnBoard(boolean shouldShow);

    boolean shouldShowMessageOnBoard();

    void setShouldShowAllUsers(boolean showAllUsers);

    boolean shouldShowAllUsers();

    void setIfProfileExists(boolean profileExists);

    boolean doesProfileExist();

    void setIsThereANewMatch(boolean isThereANewMatch);

    boolean isThereANewMatch();

    void setIsThereANewMessage(boolean isThereANewMessage);

    boolean isThereANewMessage();
}
