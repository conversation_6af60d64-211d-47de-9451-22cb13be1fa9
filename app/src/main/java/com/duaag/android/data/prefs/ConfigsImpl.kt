package com.duaag.android.data.prefs

import android.content.Context
import com.duaag.android.R

class ConfigsImpl(context: Context?) : SharedPrefsUtils(context!!), Configs {
    override fun setIsChatScreenVisible(isVisible: <PERSON><PERSON>an) {
        setBooleanPreference(R.string.pref_dua_chat_screen_visible, isVisible)
    }

    override fun isChatScreenVisible(): <PERSON><PERSON>an {
        return getBooleanPreference(R.string.pref_dua_chat_screen_visible, false)
    }

    override fun setShouldShowMessageOnBoard(shouldShow: Boolean) {
        setBooleanPreference(R.string.pref_dua_message_on_board, shouldShow)
    }

    override fun shouldShowMessageOnBoard(): Bo<PERSON>an {
        return getBooleanPreference(R.string.pref_dua_message_on_board, false)
    }

    override fun setShouldShowAllUsers(showAllUsers: Boolean) {
        setBooleanPreference(R.string.pref_dua_show_all_users, showAllUsers)
    }

    override fun shouldShowAllUsers(): <PERSON><PERSON><PERSON> {
        return getBooleanPreference(R.string.pref_dua_show_all_users, false)
    }

    override fun setIfProfileExists(profileExists: Boolean) {
        setBooleanPreference(R.string.pref_dua_profile_exists, profileExists)
    }

    override fun doesProfileExist(): Boolean {
        return getBooleanPreference(R.string.pref_dua_profile_exists, true)
    }

    override fun setIsThereANewMatch(isThereANewMatch: Boolean) {
        setBooleanPreference(R.string.pref_dua_new_match, isThereANewMatch)
    }

    override fun isThereANewMatch(): Boolean {
        return getBooleanPreference(R.string.pref_dua_new_match, false)
    }

    override fun setIsThereANewMessage(isThereANewMessage: Boolean) {
        setBooleanPreference(R.string.pref_dua_new_message, isThereANewMessage)
    }

    override fun isThereANewMessage(): Boolean {
        return getBooleanPreference(R.string.pref_dua_new_message, false)
    }
}