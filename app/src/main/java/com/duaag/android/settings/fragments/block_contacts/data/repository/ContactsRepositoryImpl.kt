package com.duaag.android.settings.fragments.block_contacts.data.repository

import android.content.Context
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.UserService
import com.duaag.android.settings.fragments.block_contacts.data.datasource.ContactsDataSource
import com.duaag.android.settings.fragments.block_contacts.data.dto.AddContactErrorResponse
import com.duaag.android.settings.fragments.block_contacts.data.dto.AddPhoneNumberBodyDto
import com.duaag.android.settings.fragments.block_contacts.data.dto.Number
import com.duaag.android.settings.fragments.block_contacts.domain.ContactsRepository
import com.duaag.android.settings.fragments.block_contacts.domain.model.ContactModel
import com.duaag.android.utils.coutry.CountryCodeDetectionSource
import com.duaag.android.utils.coutry.CountryCodeDetector
import kotlinx.coroutines.CancellationException
import javax.inject.Inject
import javax.inject.Named

class ContactsRepositoryImpl @Inject constructor(
    val context: Context,
    private val contactsDataSource: ContactsDataSource,
    @Named("private") private val userService: UserService,
) : ContactsRepository {
    override suspend fun getContacts(): List<ContactModel> {
        return contactsDataSource.getContactsFromDevice().map {
                ContactModel(it.name, it.phoneNumber)
        }
    }

    override suspend fun getPhoneNumbersInBlockedList(): ResourceV2<List<ContactModel>> {
        return try {
            val response = userService.getBlockedPhoneNumbers()
            if(response.isSuccessful) {
                ResourceV2.Success(response.body()!!.map {
                    ContactModel(name = it.name, phoneNumber = it.phoneNumber, id = it.id, isBlocked = true)
                })
            } else {
                ResourceV2.Error(message = response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "", data = emptyList())
        }
    }

    override suspend fun addPhoneNumberInBlockedList(model: ContactModel): ResourceV2<List<ContactModel>> {
        return try {
            val simCountryCode = CountryCodeDetector(context)
                .detectCountryFromSource(CountryCodeDetectionSource.SIM)
                ?.uppercase()
            val response = userService.addPhoneNumberInBlockedContacts(
                AddPhoneNumberBodyDto(simCountryCode, listOf(Number(model.name, model.phoneNumber)))
            )
            if(response.isSuccessful) {
                val result = response.body()
                if (result == null) {
                    ResourceV2.Error(message = response.message(), errorType = AddContactErrorResponse.AN_ERROR_OCCURRED)
                } else {
                    if(result.isEmpty()) {
                        ResourceV2.Error(message = response.message(), errorType = AddContactErrorResponse.PHONE_NUMBER_ALREADY_ADDED)
                    } else {
                        ResourceV2.Success(response.body()!!.map {
                            ContactModel(name = it.name, phoneNumber = it.phoneNumber, id = it.id, isBlocked = true)
                        })
                    }
                }

            } else {
                ResourceV2.Error(response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "", data = emptyList())
        }
    }

    override suspend fun blockAllContactsList(list: List<ContactModel>): ResourceV2<List<ContactModel>> {
        return try {
            val simCountryCode = CountryCodeDetector(context)
                .detectCountryFromSource(CountryCodeDetectionSource.SIM)
                ?.uppercase()
            val response = userService.addPhoneNumberInBlockedContacts(
                AddPhoneNumberBodyDto(simCountryCode, list.map { Number(it.name, it.phoneNumber) })
            )
            if(response.isSuccessful) {
                ResourceV2.Success(response.body()!!.map {
                    ContactModel(name = it.name, phoneNumber = it.phoneNumber, id = it.id, isBlocked = true)
                })
            } else {
                ResourceV2.Error(response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "", data = emptyList())
        }
    }

    override suspend fun deletePhoneNumberInBlockedList(id: Long): ResourceV2<Unit> {
        return try {
            val response = userService.deletePhoneNumberInBlockedContacts(id)
            if(response.isSuccessful) {
                ResourceV2.Success(Unit)
            } else {
                ResourceV2.Error(response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "")
        }
    }

    override suspend fun deleteAllBlockedContacts(): ResourceV2<Unit> {
        return try {
            val response = userService.deleteAllBlockedContacts()
            if(response.isSuccessful) {
                ResourceV2.Success(Unit)
            } else {
                ResourceV2.Error(response.message())
            }
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "")
        }
    }
}