package com.duaag.android.settings.fragments.help_center

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import com.duaag.android.base.models.UserModel
import com.duaag.android.settings.fragments.help_center.model.HelpCenterItemType
import com.duaag.android.settings.fragments.help_center.model.helpCenterItemStringMap
import com.duaag.android.settings.fragments.help_center.model.helpCenterItemUrlMap
import com.duaag.android.settings.fragments.language.LanguageConstants
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

class HelpCenterViewModel @Inject constructor(
    private val userRepository: UserRepository,
) : ViewModel() {
    val user = userRepository.user
    private val _dataUi: MutableStateFlow<HelpCenterUiData> = MutableStateFlow(HelpCenterUiData())
    val dataUi: LiveData<HelpCenterUiData> get() = _dataUi.asLiveData()

    init {
        initUiData(user.value)
    }

    private fun initUiData(user: UserModel?) {
        _dataUi.update {
            it.copy(user = user)
        }
    }

    fun setHelpCenterItems() {
        val items: List<HelpCenterItemModel> = if (_dataUi.value.showVideoHelpOption) {
            HelpCenterItemType.values()
                .map { HelpCenterItemModel(titleRes = helpCenterItemStringMap[it], urlKey = helpCenterItemUrlMap[it]) }
        } else listOf(
            HelpCenterItemModel(
                helpCenterItemStringMap[HelpCenterItemType.FAQ],
                helpCenterItemUrlMap[HelpCenterItemType.FAQ]
            )
        )
        _dataUi.update {
            it.copy(items = items)
        }
    }
}

data class HelpCenterUiData(
    var items: List<HelpCenterItemModel>? = null,
    var user: UserModel? = null,
) {
    var showVideoHelpOption: Boolean = shouldShowVideoHelpOption(user)

    private fun shouldShowVideoHelpOption(user: UserModel?): Boolean =
        user?.language == LanguageConstants.LANGUAGE_ALBANIAN

}