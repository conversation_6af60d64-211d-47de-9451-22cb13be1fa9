package com.duaag.android.settings.fragments.block_contacts.domain.use_case

import com.duaag.android.api.ResourceV2
import com.duaag.android.settings.fragments.block_contacts.domain.ContactsRepository
import javax.inject.Inject

class DeleteAllBlockedContactUseCase @Inject constructor(private val contactsRepository: ContactsRepository) {
    suspend operator fun invoke(): ResourceV2<Unit> {
        return contactsRepository.deleteAllBlockedContacts()
    }
}