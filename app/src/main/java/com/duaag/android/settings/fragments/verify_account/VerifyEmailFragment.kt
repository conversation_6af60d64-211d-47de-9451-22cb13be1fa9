package com.duaag.android.settings.fragments.verify_account

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.clevertap.ClevertapVerificationTypeValues
import com.duaag.android.clevertap.sendEmailOrPhoneVerificationScreenViewEvent
import com.duaag.android.databinding.FragmentVerifyEmailBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.getHourFromErrorMessage
import com.duaag.android.utils.isEmailValid
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.google.android.material.floatingactionbutton.FloatingActionButton
import kotlinx.coroutines.delay
import timber.log.Timber
import javax.inject.Inject

class VerifyEmailFragment : Fragment() {

    companion object {
        const val IS_SIGN_IN_INTENT = "sign_in"
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel by viewModels<SettingsViewModel>({ activity as SettingsActivity }) { viewModelFactory }
    private var _binding: FragmentVerifyEmailBinding? = null
    private val binding get() = _binding!!

    private var isSignIn = true
    private var mIsChangeLogin : Boolean = false


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            isSignIn = it.getBoolean(IS_SIGN_IN_INTENT)
            VerifyEmailFragmentArgs.fromBundle(it).apply {
                mIsChangeLogin = isChangeLogin
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentVerifyEmailBinding.inflate(inflater).apply {
            viewModel = settingsViewModel
            lifecycleOwner = viewLifecycleOwner
            if (isSignIn) {
                txtEmail.text = getString(R.string.em_address)
                txtEmailDescription.text = getString(R.string.em_desc)
            }
        }

        binding.isChangeLogin = mIsChangeLogin

        sendEmailOrPhoneVerificationScreenViewEvent(settingsViewModel.userProfile.value, ClevertapVerificationTypeValues.EMAIL.value)

        checkEmailVisibility(binding.editTextEmail.toString())
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.let { it ->
            it.editTextEmail.addTextChangedListener {
                checkEmailVisibility(it.toString())
            }
            it.editTextEmail.onKeyboardNext {
                if (activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.isEnabled!!) {
                    activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.performClick()
                }
            }
            if (!mIsChangeLogin){
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.VERIFICATION_EMAILADRESS_SCREENVIEW, mapOf(
                        FirebaseAnalyticsParameterName.VERIFICATION_EMAILADRESS_SCREENVIEW_COUNT.value to 1L))
            }
        }

        settingsViewModel.addEmailAddress.observe(viewLifecycleOwner, Observer {
            when (it) {
                is Result.Success -> {
                    binding.progressBar.visibility = View.GONE
                    if (mIsChangeLogin){
                    val directions = VerifyEmailFragmentDirections.actionChangeLoginVerifyEmailFragmentToChangeLoginVerifyCodeFragment().apply {
                        isChangeLogin = true
                    }
                        view.findNavController().navigate(directions)
                    } else {
                        view.findNavController().navigate(R.id.action_verifyEmailFragment_to_verifyCodeFragment2)
                        firebaseLogEvent(
                            FirebaseAnalyticsEventsName.EMAIL_VERIFICATION_6DIGIT_SCREENVIEW, mapOf(
                                FirebaseAnalyticsParameterName.EMAIL_6DIGIT_SCREENVIEW_COUNT.value to 1L))
                    }
                }
                is Result.Error -> {
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.SHOWN)
                    binding.progressBar.visibility = View.GONE
                    when (it.exception) {
                        is LimitExceededException -> {
                            val message = getHourFromErrorMessage(it.exception.errorMessage)
                            binding.errorText.text = message
                        }
                        is CodeMismatchException -> {
                            binding.errorText.text = getString(R.string.wrong_verification_code)
                        }
                        else -> binding.errorText.text = it.exception.message
                    }
                }
                is Result.Loading -> {
                    clearErrorMessage()
                    binding.progressBar.visibility = View.VISIBLE
                }
                else -> {}
            }
        })
    }

    private fun checkEmailVisibility(email: String) {
        if (isEmailValid(email))
            (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.SHOWN)
        else
            (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun clearErrorMessage() {
        binding.errorText.text = ""
    }

    override fun onResume() {
        super.onResume()
        val title = if (mIsChangeLogin) R.string.change_email_address else
            when (settingsViewModel.authMethod) {
            AuthMethod.PHONE -> R.string.phone_verification
            else -> R.string.email_verification
        }
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(title))
        lifecycleScope.launchWhenResumed {
            delay(200)
            binding.editTextEmail.requestFocus()
            binding.editTextEmail.showKeyboard()
        }
    }
}
