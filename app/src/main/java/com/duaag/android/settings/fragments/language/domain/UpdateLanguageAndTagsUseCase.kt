package com.duaag.android.settings.fragments.language.domain

import javax.inject.Inject

class UpdateLanguageAndTagsUseCase @Inject constructor(
    private val languageRepository: LanguageRepository
){

    suspend operator fun invoke(appLanguage: String, currentUserLanguage: String): Boolean {
        return if (appLanguage != currentUserLanguage) {
            languageRepository.updateLanguage(appLanguage)
        } else {
            false
        }
    }
}