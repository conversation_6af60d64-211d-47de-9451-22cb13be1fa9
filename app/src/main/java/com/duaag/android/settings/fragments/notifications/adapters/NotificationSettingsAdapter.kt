package com.duaag.android.settings.fragments.notifications.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.RecyclerviewNotificationSettingsItemBinding
import com.duaag.android.settings.fragments.notifications.model.NotificationsSettingsType

/* #simpleItemClick usually used when have only one type of item to click*/
class NotificationSettingsAdapter(val simpleItemClick:(NotificationsSettingsType) -> Unit):
        RecyclerView.Adapter<NotificationSettingsAdapter.ViewHolder>() {

    var mItems: Array<NotificationsSettingsType> = emptyArray()
        set(value) {
            field = value
            notifyDataSetChanged()
        }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
        return ViewHolder(RecyclerviewNotificationSettingsItemBinding.inflate(view
                , parent, false))
    }

    override fun getItemCount(): Int = mItems.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(mItems[position])
    }

    inner class ViewHolder(var binding: RecyclerviewNotificationSettingsItemBinding)
        : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: NotificationsSettingsType) {
            binding.item = item
            binding.title.text = binding.root.context.getString(item.value)
            binding.root.setOnClickListener { simpleItemClick.invoke(item) }
        }
    }
}