package com.duaag.android.settings.fragments.notifications.pushnotification.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.RecyclerviewPushNotifcationDisabledItemBinding
import com.duaag.android.databinding.RecyclerviewPushNotifcationItemBinding
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationModel
import com.duaag.android.settings.fragments.notifications.pushnotification.models.pushNotificationStringMap
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility

class PushNotificationAdapter(
        private val simpleItemClick:(PushNotificationModel) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val ITEM_VIEW_TYPE = 0
        const val SUMMARY_ITEM_VIEW_TYPE = 1
    }
    private val mOnClickListener: View.OnClickListener = View.OnClickListener { v ->
        val item = v.tag as PushNotificationModel
        if(!item.isEnabled) return@OnClickListener
        // Notify the active callbacks interface (the activity, if the fragment is attached to
        // one) that an item has been selected
        item.isChecked = !item.isChecked!!
        simpleItemClick.invoke(item)
       return@OnClickListener
    }

    var mValues: List<PushNotificationModel> = emptyList()
        set(value) {
            field = value.filter { it.show }
            notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context)
        return when(viewType){
            SUMMARY_ITEM_VIEW_TYPE -> {
                val binding = RecyclerviewPushNotifcationDisabledItemBinding.inflate(view,parent,false)
                PushNotificationSummaryViewHolder(binding)
            }
            else -> {
                val binding = RecyclerviewPushNotifcationItemBinding.inflate(view, parent, false)
                PushNotificationViewHolder(binding)
            }
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = mValues[position]

        when (getItemViewType(position)) {
            SUMMARY_ITEM_VIEW_TYPE -> (holder as PushNotificationSummaryViewHolder).bind(item)
            else -> (holder as PushNotificationViewHolder).bind(item)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            mValues[position].hasSummary -> return SUMMARY_ITEM_VIEW_TYPE
            else -> ITEM_VIEW_TYPE
        }
    }
    override fun getItemCount(): Int = mValues.size

    inner class PushNotificationViewHolder(var binding: RecyclerviewPushNotifcationItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PushNotificationModel) {
            binding.item = item
            binding.switchWidget.text = pushNotificationStringMap[item.name]?.let {
                binding.root.context.getString(
                    it
                )
            }
            with(binding.switchWidget) {
                tag = item
                setOnSingleClickListener(mOnClickListener)
            }
        }
    }

    inner class PushNotificationSummaryViewHolder(var binding: RecyclerviewPushNotifcationDisabledItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PushNotificationModel) {
            with(binding){
                val textColor = if(item.isEnabled) ContextCompat.getColor(root.context, R.color.title_primary) else ContextCompat.getColor(root.context,R.color.disable_primary)
                val summaryColor = if(item.isEnabled) ContextCompat.getColor(root.context, R.color.description_secondary) else ContextCompat.getColor(root.context,R.color.disable_primary)
                switchWidget.apply {
                    isChecked = item.isChecked!! && item.isEnabled
                    isEnabled = item.isEnabled
                    tag = item
                    setOnSingleClickListener(mOnClickListener)
                }
                switchText.apply {
                    text = pushNotificationStringMap[item.name]?.let { context.getString(it) }
                    setTextColor(textColor)
                }
                switchSummary.apply {
                    text = item.getSummary(root.context)
                    setTextColor(summaryColor)
                }
                root.setOnSingleClickListener {
                    if(!item.isEnabled) return@setOnSingleClickListener
                    switchWidget.performClick()
                }
            }
        }
    }
}