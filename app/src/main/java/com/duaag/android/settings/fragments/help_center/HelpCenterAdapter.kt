package com.duaag.android.settings.fragments.help_center

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.IntegerRes
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.HelpCenterNotificationItemBinding
import com.duaag.android.utils.setOnSingleClickListener

data class HelpCenterItemModel(@IntegerRes var titleRes: Int?=null, val urlKey: String?=null)

class HelpCenterAdapter (
    private val helpCenterItemInteraction: HelpCenterItemInteraction,
) : RecyclerView.Adapter<HelpCenterAdapter.ItemViewHolder>() {
    private var items : List<HelpCenterItemModel> = ArrayList()

    fun setData(data: List<HelpCenterItemModel>) {
        items = data
        notifyDataSetChanged()
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = HelpCenterNotificationItemBinding.inflate(layoutInflater, parent, false)
        return ItemViewHolder(binding)    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = items.get(position)
        if (item != null) {
            holder.bind(item)
        }
    }

    override fun getItemCount(): Int = items.size


    inner class ItemViewHolder constructor(private val binding: HelpCenterNotificationItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: HelpCenterItemModel) {
          binding.title.text = item.titleRes?.let { binding.root.context.getString(it) }
            binding.arrow.setOnSingleClickListener{
                helpCenterItemInteraction.onItemInteraction(item)
            }
        }
    }

}

interface HelpCenterItemInteraction {
    fun onItemInteraction(helpCenterItem: HelpCenterItemModel)

}