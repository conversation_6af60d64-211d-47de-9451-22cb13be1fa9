package com.duaag.android.settings.fragments.block_contacts.domain.use_case

import com.duaag.android.api.ResourceV2
import com.duaag.android.settings.fragments.block_contacts.domain.ContactsRepository
import com.duaag.android.settings.fragments.block_contacts.domain.model.ContactModel
import javax.inject.Inject

class BlockAllContactsUseCase @Inject constructor(private val contactsRepository: ContactsRepository) {
    suspend operator fun invoke(contact: List<ContactModel>): ResourceV2<List<ContactModel>> {
        return contactsRepository.blockAllContactsList(contact)
    }
}