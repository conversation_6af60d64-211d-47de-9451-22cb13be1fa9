package com.duaag.android.settings.fragments.verify_account

import android.animation.ObjectAnimator
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavDeepLinkBuilder
import androidx.navigation.fragment.findNavController
import com.applovin.impl.sdk.AppLovinBroadcastManager.sendBroadcast
import com.duaag.android.R
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.FragmentAccountVerificationBinding
import com.duaag.android.home.models.DeepLinkScreenNames
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.launcher.SplashActivity.Companion.DEEP_LINK_SCREENS
import com.duaag.android.launcher.SplashActivity.Companion.SCREENS_INTENT_FILTER
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import sendVerifyYourProfileInitiatedAnalyticsEvent
import javax.inject.Inject


class AccountVerificationFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}
    private var _binding: FragmentAccountVerificationBinding? = null
    private val binding get() = _binding!!



    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.profile_progress_title_2))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {

        // Inflate the layout for this fragment
        _binding = FragmentAccountVerificationBinding.inflate(inflater)
        binding.lifecycleOwner = this
        binding.viewModel = settingViewModel


        binding.verifyAccountImage.text = if (settingViewModel.badge2.value == Badge2Status.APPROVED) getString(R.string.image_verification) else getString(R.string.image_verification)

        settingViewModel.badge2.observe(viewLifecycleOwner, Observer {
            when (it) {
                Badge2Status.APPROVED -> {
                    binding.badgeApproved.setImageResource(R.drawable.ic_image_verification)
                    binding.badgeApproved.visibility = View.VISIBLE
                    binding.badgeProcessing.visibility = View.INVISIBLE
                    binding.badgeApproved.alpha = 1f
                    binding.imageVerificationArrow.visibility = View.INVISIBLE
                }
                Badge2Status.NOT_APPROVED -> {
                    binding.badgeApproved.setImageResource(R.drawable.ic_image_verification_failed)
                    binding.badgeApproved.visibility = View.VISIBLE
                    binding.badgeProcessing.visibility = View.INVISIBLE
                    binding.badgeApproved.alpha = 1f
                    binding.imageVerificationArrow.visibility = View.INVISIBLE
                }
                Badge2Status.PROCESSING -> {
                    binding.badgeApproved.setImageDrawable(binding.badgeApproved.context?.let { ContextCompat.getDrawable(it, R.drawable.ic_image_verification) })
                    binding.badgeProcessing.setImageDrawable(binding.badgeProcessing.context?.let { ContextCompat.getDrawable(it, R.drawable.ic_image_verification_processinng) })
                    binding.badgeApproved.visibility = View.VISIBLE
                    binding.badgeProcessing.visibility = View.VISIBLE
                    binding.badgeProcessing.alpha = 1f
                    binding.imageVerificationArrow.visibility = View.INVISIBLE
                    val fadeAnimator2 = ObjectAnimator.ofFloat(binding.badgeApproved, "alpha", 1f, 0f).apply {
                        duration = 800
                        repeatCount = ObjectAnimator.INFINITE
                        repeatMode = ObjectAnimator.REVERSE
                    }

                    fadeAnimator2.start()
                }
                Badge2Status.NULL -> {
                    binding.imageVerificationArrow.setImageResource(R.drawable.ic_chevron_right)
                    binding.imageVerificationArrow.visibility = View.VISIBLE
                    binding.badgeProcessing.visibility = View.INVISIBLE
                    binding.badgeApproved.visibility = View.INVISIBLE
                }
            }
        })



        binding.phoneEmailDrawable.visibility = if (settingViewModel.hasBadge1) View.VISIBLE else View.INVISIBLE
        binding.phoneEmailArrow.visibility = if (settingViewModel.hasBadge1) View.INVISIBLE else View.VISIBLE




        binding.verifyAccount.setOnClickListener {
            if (settingViewModel.badge2.value != Badge2Status.APPROVED && settingViewModel.badge2.value != Badge2Status.PROCESSING) {

                val eventPremiumType =
                    getPremiumTypeEventProperty(settingViewModel.userProfile.value)

                sendVerifyYourProfileInitiatedAnalyticsEvent(
                    ClevertapVerificationSourceValues.SETTINGS.value,
                    eventPremiumType
                )

                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.INITIATE_IMAGE_VERIFICATION, mapOf(
                        FirebaseAnalyticsParameterName.IMAGE_VERIFICATION_INITIATE_SETTINGS.value to 1L))

                val intent = Intent(SCREENS_INTENT_FILTER)
                intent.apply {
                    `package` = requireContext().packageName
                }
                intent.putExtra(DEEP_LINK_SCREENS, DeepLinkScreenNames.VERIFY_IMAGE.value)
                intent.putExtra(EVENT_SOURCE, ClevertapVerificationSourceValues.SETTINGS.value)
                requireActivity().sendBroadcast(intent)

                requireActivity().finish()
            }
        }

        binding.phoneEmailVerify.setOnClickListener {
            val account = settingViewModel.accountModel.value
            if (settingViewModel.userProfile.value?.hasBadge1 == false) {
                account?.let {
                    if (it.phone == null && it.email == null) {
                        settingViewModel.authMethod = AuthMethod.PHONE
                        val action = AccountVerificationFragmentDirections.actionAccountVerificationFragmentToVerifyPhoneFragment()
                        sendEmailOrPhoneVerificationButtonClickEvent(settingViewModel.userProfile.value, ClevertapVerificationTypeValues.PHONE.value)
                        findNavController().navigate(action)
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.PHONE_VERIFICATION_BUTTONCLICK, mapOf(
                                FirebaseAnalyticsParameterName.PHONE_VERIFICATION_BUTTONCLICK_COUNT.value to 1L))
                    } else if (it.phone == null && it.email != null) {
                        settingViewModel.authMethod = AuthMethod.PHONE
                        val action = AccountVerificationFragmentDirections.actionAccountVerificationFragmentToVerifyPhoneFragment()
                        sendEmailOrPhoneVerificationButtonClickEvent(settingViewModel.userProfile.value, ClevertapVerificationTypeValues.PHONE.value)
                        findNavController().navigate(action)
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.PHONE_VERIFICATION_BUTTONCLICK, mapOf(
                                FirebaseAnalyticsParameterName.PHONE_VERIFICATION_BUTTONCLICK_COUNT.value to 1L))
                    } else if (it.email == null && it.phone != null) {
                        settingViewModel.authMethod = AuthMethod.EMAIL
                        val action = AccountVerificationFragmentDirections.actionAccountVerificationFragmentToVerifyEmailFragment()
                        sendEmailOrPhoneVerificationButtonClickEvent(settingViewModel.userProfile.value, ClevertapVerificationTypeValues.EMAIL.value)
                        findNavController().navigate(action)
                        firebaseLogEvent(
                                FirebaseAnalyticsEventsName.EMAIL_VERIFICATION_BUTTONCLICK, mapOf(
                                FirebaseAnalyticsParameterName.EMAIL_VERIFICATION_BUTTONCLICK_COUNT.value to 1L))
                    }
                }
            }
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, pressedCallback)

        when (settingViewModel.authMethod) {
            AuthMethod.PHONE ->
                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.VERIFIED_PHONE_SCREENVIEW, mapOf(
                        FirebaseAnalyticsParameterName.VERIFIED_PHONE_SCREENVIEW_COUNT.value to 1L))
            else ->
                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.VERIFIED_EMAIL_SCREENVIEW, mapOf(
                        FirebaseAnalyticsParameterName.VERIFIED_EMAIL_SCREENVIEW_COUNT.value to 1L))

        }

        settingViewModel.accountModel.observe(viewLifecycleOwner) { accountModel ->
            if (accountModel != null) {
                val verificationText = when {
                    accountModel.phone == null && accountModel.email == null ->
                        if (settingViewModel.hasBadge1) getString(R.string.verified) else getString(R.string.phone_verification)
                    accountModel.phone == null ->
                        if (settingViewModel.hasBadge1) getString(R.string.verified) else getString(R.string.phone_verification)
                    accountModel.email == null ->
                        if (settingViewModel.hasBadge1) getString(R.string.verified) else getString(R.string.email_verification)
                    else ->
                        if (settingViewModel.hasBadge1) getString(R.string.verified) else getString(R.string.phone_verification)
                }
                binding.verifyAccountPhoneEmail.text = verificationText
            }
        }

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}