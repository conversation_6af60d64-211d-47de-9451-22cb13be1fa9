package com.duaag.android.settings.fragments.premium

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentRedeemCodeBottomSheetBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.data.dto.RedeemCodeErrorType
import com.duaag.android.premium_subscription.openPremiumPaywallAsync
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.models.PromoCodeErrorTypeEventValues
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.DuaButton
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.coroutines.launch
import javax.inject.Inject

class RedeemCodeBottomSheet : BottomSheetDialogFragment() {

    private var _binding: FragmentRedeemCodeBottomSheetBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<RedeemCodeViewModel> { viewModelFactory }
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRedeemCodeBottomSheetBinding.inflate(inflater, container, false)
        dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        binding.actionBtn.setOnSingleClickListener {
            val code = binding.codeInput.text.toString().trim()

            if(code.isNotEmpty()) {
                viewModel.redeemCode(code = code)
            } else {
                dismissAllowingStateLoss()
            }
        }

        binding.codeInput.addTextChangedListener { text ->
            viewModel.setInitialState()

            if (text == null) {
                binding.actionBtn.setButtonType(DuaButton.ButtonType.SECONDARY)
                binding.actionBtn.text = getString(R.string.cancel)
            }
            else {
                if(text.isNotEmpty()) {
                    binding.actionBtn.setButtonType(DuaButton.ButtonType.PRIMARY)
                    binding.actionBtn.text = getString(R.string.redeem_btn)
                } else {
                    binding.actionBtn.setButtonType(DuaButton.ButtonType.SECONDARY)
                    binding.actionBtn.text = getString(R.string.cancel)
                }
            }

        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.redeemCodeException.collect {
                    when(it) {
                        is RedeemCodeScreenState.Success -> {
                            sendPromoCodeRedeemedSuccessfullyEvent()
                            viewModel.setInitialState()

                            requireActivity().openPremiumPaywallAsync(
                                eventSourceClevertap = ClevertapEventSourceValues.PROMO_CODE,
                                placementId = it.paywallId,
                                promoCode = it.promoCode
                            )

                            dismissAllowingStateLoss()
                        }
                        is RedeemCodeScreenState.Error -> {
                            toggleLoadingVisibility(false)

                            when(it.errorType) {
                                RedeemCodeErrorType.PROMO_CODE_NOT_FOUND_ERROR -> {
                                    sendRedeemPromoCodeEvent(PromoCodeErrorTypeEventValues.NOT_VALID)
                                    handleCodeError(R.string.code_not_valid_info, R.drawable.error_corners_12dp)
                                }
                                RedeemCodeErrorType.PROMO_CODE_REDEEM_LIMIT_REACHED_ERROR -> {
                                    sendRedeemPromoCodeEvent(PromoCodeErrorTypeEventValues.LIMIT_REACHED)
                                    handleCodeError(R.string.redeem_code_limit_error, R.drawable.error_corners_12dp)
                                }
                                RedeemCodeErrorType.PROMO_CODE_EXPIRED_ERROR -> {
                                    sendRedeemPromoCodeEvent(PromoCodeErrorTypeEventValues.EXPIRED)
                                    handleCodeError(R.string.code_expired_info, R.drawable.error_corners_12dp)
                                }
                                RedeemCodeErrorType.NO_NETWORK_CONNECTION -> {
                                    handleCodeError(R.string.no_internet_connection, R.drawable.error_corners_12dp)
                                }

                                RedeemCodeErrorType.AN_ERROR_OCCURRED -> {
                                    handleCodeError(R.string.an_error_occurred, R.drawable.error_corners_12dp)
                                }
                            }
                            logError(ErrorStatus.REDEEM_CODE_EXCEPTION)
                        }
                        RedeemCodeScreenState.Loading -> {
                            toggleLoadingVisibility(true)
                            clearCodeErrorMessage()
                        }

                        RedeemCodeScreenState.InitialState -> {
                            toggleLoadingVisibility(false)
                            clearCodeErrorMessage()
                        }
                    }
                }
            }
        }

        return binding.root
    }

    fun toggleLoadingVisibility(showLoading: Boolean = false) {
        if(showLoading) {
            binding.actionBtn.visibility = View.INVISIBLE
            binding.progressBar.visibility = View.VISIBLE
        } else {
            binding.actionBtn.visibility = View.VISIBLE
            binding.progressBar.visibility = View.GONE
        }
    }

    private fun sendRedeemPromoCodeEvent(type: PromoCodeErrorTypeEventValues) {
        val premiumTypeValue = getPremiumTypeEventProperty(DuaAccount.user)

        premiumTypeValue?.let {
            sendClevertapEvent(
                ClevertapEventEnum.PROMO_CODE_ERROR,
                mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                    ClevertapEventPropertyEnum.PROMO_CODE_ERROR_TYPE.propertyName to type.value,
                )
            )
            firebaseLogEvent(
                FirebaseAnalyticsEventsName.PROMO_CODE_ERROR,
                mapOf(
                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumTypeValue,
                    FirebaseAnalyticsParameterName.PROMO_CODE_ERROR_TYPE.value to type.value,
                )
            )
        }
    }

    private fun sendPromoCodeRedeemedSuccessfullyEvent() {
        val premiumTypeValue = getPremiumTypeEventProperty(DuaAccount.user)

        premiumTypeValue?.let {
            sendClevertapEvent(
                ClevertapEventEnum.PROMO_CODE_REDEEMED,
                mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                )
            )
            firebaseLogEvent(
                FirebaseAnalyticsEventsName.PROMO_CODE_REDEEMED,
                mapOf(
                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumTypeValue,
                )
            )
        }
    }

    private fun handleCodeError(errorMessage: Int, errorDrawable: Int) {
        binding.emailErrorText.visibility = View.VISIBLE
        binding.emailErrorText.setText(errorMessage)
        binding.codeInput.background = ContextCompat.getDrawable(requireContext(), errorDrawable)
        binding.codeInput.requestFocus()
    }

    private fun clearCodeErrorMessage() {
        binding.emailErrorText.visibility = View.GONE
        binding.codeInput.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.codeInput.requestFocus()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
            super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                    BottomSheetBehavior.STATE_EXPANDED
            }
            d.dismissWithAnimation = true
        }
        return dialog
    }


    override fun onDestroyView() {
        dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        super.onDestroyView()
        _binding = null
    }
}