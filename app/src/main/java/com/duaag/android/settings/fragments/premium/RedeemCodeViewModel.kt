package com.duaag.android.settings.fragments.premium

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.premium_subscription.data.dto.RedeemCodeErrorType
import com.duaag.android.premium_subscription.domain.RedeemCodeException
import com.duaag.android.premium_subscription.domain.use_case.RedeemCodeUseCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import javax.inject.Inject

class RedeemCodeViewModel @Inject constructor(
    private val redeemCodeUseCase: RedeemCodeUseCase
) : ViewModel() {

    private val _redeemCodeScreenState: MutableStateFlow<RedeemCodeScreenState> = MutableStateFlow(
        RedeemCodeScreenState.InitialState
    )
    val redeemCodeException: StateFlow<RedeemCodeScreenState>
        get() = _redeemCodeScreenState

    fun redeemCode(code: String) {
        redeemCodeUseCase(code)
            .onEach { result ->
                when (result) {
                    is Resource.Success -> {
                        _redeemCodeScreenState.value = RedeemCodeScreenState.Success(result.data.paywallId, code)
                    }
                    is Resource.Error -> {
                    }
                    is Resource.Loading -> {
                        _redeemCodeScreenState.value = RedeemCodeScreenState.Loading
                    }
                }
            }
            .catch { exception ->
                withContext(Dispatchers.Main){
                    when (exception) {
                        is RedeemCodeException -> {
                            _redeemCodeScreenState.value = RedeemCodeScreenState.Error(exception.type)
                        }
                        is NoConnectivityException -> {
                            _redeemCodeScreenState.value = RedeemCodeScreenState.Error(RedeemCodeErrorType.NO_NETWORK_CONNECTION)
                        }
                        else -> {
                            _redeemCodeScreenState.value = RedeemCodeScreenState.Error(RedeemCodeErrorType.AN_ERROR_OCCURRED)
                        }
                    }
                    exception.printStackTrace()
                }
            }
            .launchIn(viewModelScope)
    }

    fun setInitialState() {
        _redeemCodeScreenState.value = RedeemCodeScreenState.InitialState
    }
}
