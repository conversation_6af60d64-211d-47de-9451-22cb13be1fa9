package com.duaag.android.settings.fragments.notifications.pushnotification

import android.content.Context
import androidx.lifecycle.ViewModel
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationBody
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class PushNotificationViewModel @Inject constructor(
  private val notificationRepository: NotificationRepository,
  val context: Context,
  val userRepository: UserRepository
) : ViewModel() {

  val user = userRepository.user
  fun setNotification(pushNotificationBody: PushNotificationBody) = notificationRepository.setSettingsNotifications(pushNotificationBody)
  fun getNotifications() = notificationRepository.getSettingsNotifications()

}
