package com.duaag.android.ads.likedyou.persantation

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.duaag.android.R
import com.duaag.android.ads.likedyou.persantation.routing.WatchLikedYouAdArgs
import com.duaag.android.databinding.LikedYouAddBottomSheetFragmentBinding
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class WatchLikedYouAdBottomSheetFragment: BottomSheetDialogFragment() {

    var _binding: LikedYouAddBottomSheetFragmentBinding? = null
    val binding get() = _binding!!
    val rewardedAds: Int? by lazy { arguments?.getInt(WatchLikedYouAdArgs.REWARDED_ADS) }
    val currentWatchedAd: Int? by lazy { arguments?.getInt(WatchLikedYouAdArgs.CURRENT_WATCHED_AD) }
    val isAdLoaded: Boolean? by lazy { arguments?.getBoolean(WatchLikedYouAdArgs.IS_AD_LOADED) }
    var listener: WatchLikedYouAdBottomSheetListener? = null


    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            listener = parentFragment as WatchLikedYouAdBottomSheetListener
        } catch (e: ClassCastException) {
            throw ClassCastException("$context must implement WatchLikedYouAdBottomSheetListener")
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            BottomSheetBehavior.from<FrameLayout?>(bottomSheet!!).state =
                BottomSheetBehavior.STATE_EXPANDED
            d.dismissWithAnimation = true
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = LikedYouAddBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.apply {
            title.text = getString(R.string.watch_ads_reveal_profile,rewardedAds.toString())
            progressCount.text = "$currentWatchedAd ${getString(R.string.of_step)} $rewardedAds"

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                progressBar.setProgress(currentWatchedAd ?: 0, true)
                progressBar.max = rewardedAds ?: 5
            } else {
                progressBar.progress = currentWatchedAd ?: 0
                progressBar.max = rewardedAds ?: 5
            }


            noAdsText.visibility = if (isAdLoaded == true) View.GONE else View.VISIBLE
            continueButton.isEnabled = isAdLoaded == true
            continueButton.setOnClickListener {
                if (isAdLoaded != true) return@setOnClickListener
                listener?.onLikedYouAdContinueButtonClick()
                dismissAllowingStateLoss()
            }
        }

        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }
    interface WatchLikedYouAdBottomSheetListener {
        fun onLikedYouAdContinueButtonClick()
    }

    companion object {
        const val TAG = "WatchLikedYouAdBottomSheetFragment"
        fun newInstance(args: WatchLikedYouAdArgs) =
            WatchLikedYouAdBottomSheetFragment().apply {
                arguments = Bundle().apply {
                    putInt(WatchLikedYouAdArgs.REWARDED_ADS, args.rewardedAds)
                    putInt(WatchLikedYouAdArgs.CURRENT_WATCHED_AD, args.currentWatchedAd)
                    putBoolean(WatchLikedYouAdArgs.IS_AD_LOADED, args.isAdLoaded)
                }
            }
    }
}