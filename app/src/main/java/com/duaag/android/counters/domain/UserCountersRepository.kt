package com.duaag.android.counters.domain

import com.duaag.android.api.ResourceV2
import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.domain.model.CounterModel
import kotlinx.coroutines.flow.Flow

interface UserCountersRepository {
    suspend fun getUserCounters(configurationsName: List<String>):Flow<List<CounterEntity>>
    suspend fun getUserCounter(name: String): Flow<CounterEntity?>
    suspend fun updateUserCounter(counter: CounterEntity)
    suspend fun syncUserCounters():Flow<List<CounterEntity>>
    suspend fun getLocalCounters(configurationsName: List<String>):Flow<List<CounterEntity>?>
    suspend fun getCounter(counter: String): ResourceV2<CounterModel>
    suspend fun incrementCounter(counter: String): ResourceV2<Unit>
}