package com.duaag.android.counters.domain

import com.duaag.android.api.ResourceV2
import com.duaag.android.counters.domain.model.CounterModel
import javax.inject.Inject

class GetCounterAPIUseCase @Inject constructor(private val userCountersRepository: UserCountersRepository) {
    suspend operator fun invoke(counter: String): ResourceV2<CounterModel> {
        return userCountersRepository.getCounter(counter)
    }
}