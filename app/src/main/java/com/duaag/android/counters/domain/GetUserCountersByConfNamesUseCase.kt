package com.duaag.android.counters.domain

import com.duaag.android.counters.data.models.CounterEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetUserCountersByConfNamesUseCase @Inject constructor(
    private val userCountersRepository: UserCountersRepository
) {
    suspend operator fun invoke(configurationsName: List<String>): Flow<List<CounterEntity>> =
        userCountersRepository.getUserCounters(configurationsName)
}