package com.duaag.android.counters.di

import com.duaag.android.counters.data.UserCountersRepositoryImpl
import com.duaag.android.counters.domain.UserCountersRepository
import dagger.Module
import dagger.Provides
import javax.inject.Singleton

@Module
class UserCountersModule {

    @Provides
    @Singleton
    fun provideUserCountersRepository(userCountersRepositoryImpl: UserCountersRepositoryImpl): UserCountersRepository {
        return userCountersRepositoryImpl
    }
}