package com.duaag.android.counters.domain

import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.data.models.CounterTimeUnit

object CalculateIntervalResetTimeInHourUseCase {
    fun invoke(counterEntity: CounterEntity):Long {
        val value = counterEntity.configuration.expireConfiguration.value.toLong()
        val hour:Long =  when(counterEntity.configuration.expireConfiguration.unit) {
            CounterTimeUnit.Second.value -> value * 3600
            CounterTimeUnit.Minute.value -> value * 60
            CounterTimeUnit.Hour.value -> value
            CounterTimeUnit.Day.value -> value / 24
            CounterTimeUnit.Week.value -> value / (24 * 7)
            CounterTimeUnit.Month.value -> value / (24 * 30)
            CounterTimeUnit.Year.value -> value / (24 * 365)
            else -> 0
        }
        return hour
    }
}