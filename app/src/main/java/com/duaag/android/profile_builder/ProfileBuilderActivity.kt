package com.duaag.android.profile_builder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.ActivityProfileBuilderBinding
import com.duaag.android.profile_builder.adapter.ProfileQuestionAdapter
import com.duaag.android.profile_builder.di.ProfileBuilderComponent
import com.duaag.android.profile_builder.models.ProfileBuilderEvent
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import com.duaag.android.profile_builder.viewmodel.ProfileBuilderState
import com.duaag.android.profile_builder.viewmodel.ProfileBuilderViewModel
import com.duaag.android.profile_builder.viewmodel.ProfileBuilderViewModel.Companion.BE_YOURSELF_SCREEN_INDEX
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.getParcelableExtraCompat
import com.duaag.android.utils.openHomeActivity
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.uxcam.sendUxCamEvent
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class ProfileBuilderActivity : AppCompatActivity() {

    companion object {
        const val TAG = "ProfileBuilderActivity"
        private const val EXTRA_USER_MODEL = "extra_user_model"
        private const val EXTRA_IS_SIGNING_UP = "EXTRA_IS_SIGNING_UP"
        private const val EXTRA_IS_SPOTTED_USER = "EXTRA_IS_SPOTTED_USER"



        fun createIntent(context: Context, userModel: UserModel, isSigningUp: Boolean, isSpottedUser: Boolean): Intent {
            val bundle = Bundle().apply {
                putParcelable(EXTRA_USER_MODEL, userModel)
                putBoolean(EXTRA_IS_SIGNING_UP, isSigningUp)
                putBoolean(EXTRA_IS_SPOTTED_USER, isSpottedUser)
            }
            return Intent(context, ProfileBuilderActivity::class.java).apply {
                putExtras(bundle)
            }
        }
    }

    private lateinit var binding: ActivityProfileBuilderBinding
    private lateinit var questionAdapter: ProfileQuestionAdapter
    private lateinit var userModel: UserModel
    private var isSigningUp: Boolean = false
    private var isSpottedUser: Boolean = false
    private var lastTrackedPosition: Int = -1

    val profileBuilderComponent: ProfileBuilderComponent by lazy {
        (application as DuaApplication).appComponent.profileBuilderComponent().create()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private val viewModel: ProfileBuilderViewModel by viewModels { viewModelFactory }

    override fun onCreate(savedInstanceState: Bundle?) {
        profileBuilderComponent.inject(this)
        super.onCreate(savedInstanceState)

        userModel = intent.getParcelableExtraCompat(EXTRA_USER_MODEL, UserModel::class.java) ?: run {
            Timber.tag(TAG).d("UserModel not provided to ProfileBuilderActivity, finishing.")
            Toast.makeText(this, "User data missing.", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        isSigningUp = intent.getBooleanExtra(EXTRA_IS_SIGNING_UP, false)
        isSpottedUser = intent.getBooleanExtra(EXTRA_IS_SPOTTED_USER, false)

        Timber.tag(TAG).d("ProfileBuilder initialized with: isSigningUp=$isSigningUp, isSpottedUser=$isSpottedUser")

        binding = ActivityProfileBuilderBinding.inflate(layoutInflater)
        setContentView(binding.root)

        Timber.tag(TAG).d("Profile builder starting with user: ${userModel.id}")

        setupToolbar()
        setupViewPager()
        setupNavigationButtons()
        observeViewModel()

        if (savedInstanceState == null && !viewModel.state.value.isLoading) {
            viewModel.initializeWithUserModel(userModel)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("current_position", binding.viewPager.currentItem)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        savedInstanceState.getInt("current_position", 0).let { position ->
            if (position > 0) {
                binding.viewPager.post {
                    binding.viewPager.setCurrentItem(position, false)
                }
            }
        }
        // Reset tracking to allow event to be sent for restored position
        lastTrackedPosition = -1
    }

    private fun setupToolbar() {
        binding.backBtn.setOnSingleClickListener {
            viewModel.onBackClicked()
        }
    }

    private fun setupViewPager() {
        questionAdapter = ProfileQuestionAdapter(
            onTagSelected = { item ->
                viewModel.onTagSelected(item)
            },
            onStartQuestionsClicked = {
                viewModel.onStartQuestionsClicked()
            },
            onProfileReadyClicked = {
                openHomeActivity(
                    context = this,
                    isSigningUp = isSigningUp,
                    isSpottedUser = isSpottedUser
                )
            }
        )

        binding.viewPager.apply {
            adapter = questionAdapter
            isUserInputEnabled = false
            offscreenPageLimit = 3
            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    sendProfileBuilderScreenViewEvent(position)
                }
            })
        }

        questionAdapter.submitList(listOf(ProfileQuestionAdapter.ProfileBuilderItem.BeYourselfLandingItem(userModel.gender.toString())))
    }

    private fun setupNavigationButtons() {
        binding.btnContinue.setOnSingleClickListener {
            viewModel.onNextClicked()
        }

        binding.retryButton.setOnSingleClickListener {
            viewModel.retry()
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.state.collect { state ->
                        updateUI(state)
                    }
                }
                launch {
                    viewModel.events.collect { event ->
                        handleEvent(event)
                    }
                }
            }
        }
    }

    /**
     * Updates the UI based on the current state from the ViewModel.
     */
    private fun updateUI(state: ProfileBuilderState) {
        val hasError = state.error != null
        binding.errorContainer.isVisible = hasError && !state.isLoading
        if (hasError) {
            binding.errorMessage.text = state.error
            binding.viewPager.isVisible = false
            binding.navigationButtons.isVisible = false
            binding.appBarLayout.isVisible = false
            binding.progressIndicator.isVisible = false
            Timber.tag(TAG).e("Error state: ${state.error}")
            return
        }

        val isWelcomeScreen = state.isLandingScreen()

        binding.appBarLayout.isVisible = !isWelcomeScreen && !state.isAllReadyToSubmitScreen()
        binding.progressIndicator.isVisible = !isWelcomeScreen && !state.isAllReadyToSubmitScreen()

        if (!isWelcomeScreen && !state.isAllReadyToSubmitScreen()) {
            binding.progressIndicator.progress = state.progressValue
        }

        binding.viewPager.isVisible = true
        binding.navigationButtons.isVisible = !isWelcomeScreen

        val currentQuestionType = state.currentQuestionType
        val hasSelection = currentQuestionType?.let { state.selections[it]?.isNotEmpty() } == true
        val isMultiSelect = currentQuestionType == ProfileQuestionType.LANGUAGES

        binding.btnContinue.isVisible = isMultiSelect && hasSelection


        val items = listOf(ProfileQuestionAdapter.ProfileBuilderItem.BeYourselfLandingItem(userModel.gender)) +
                ProfileQuestionType.orderedQuestionTypes.map { type ->
                    val options = if (state.isLoading) {
                        null
                    } else {
                        val selections = state.selections[type] ?: emptySet()
                        val baseOptions = state.allQuestionOptions[type] ?: state.currentQuestionOptions

                        baseOptions?.map { option ->
                            if (option is ProfileTagOptionModel.Item) {
                                val shouldBeSelected = option.tagId in selections
                                if (option.isSelected != shouldBeSelected) {
                                    option.copy(isSelected = shouldBeSelected)
                                } else {
                                    option
                                }
                            } else {
                                option
                            }
                        }
                    }

                    ProfileQuestionAdapter.ProfileBuilderItem.Question(type, options, userModel.gender )
                } + listOf<ProfileQuestionAdapter.ProfileBuilderItem>(
                    ProfileQuestionAdapter.ProfileBuilderItem.AllReadyItem(
                        userGender = userModel.gender,
                        userName = userModel.firstName,
                        isSubmitting = state.isSubmitting,
                        isComplete = state.isComplete
                    )
                )
        questionAdapter.submitList(items)

        // Ensure ViewPager is on the correct page
        if (binding.viewPager.currentItem != state.currentStepIndex && !isFinishing) {
            binding.viewPager.setCurrentItem(state.currentStepIndex, true)
        }

    }

    /**
     * Handles one-time events from the ViewModel.
     */
    private fun handleEvent(event: ProfileBuilderEvent) {
        when (event) {
            is ProfileBuilderEvent.ShowError -> {
                Toast.makeText(this, event.message, Toast.LENGTH_SHORT).show()
            }
            is ProfileBuilderEvent.NavigateToPage -> {
                binding.viewPager.setCurrentItem(event.position, event.smoothScroll)
            }
            is ProfileBuilderEvent.Complete -> {
                Timber.tag(TAG).d("Profile building complete event received.")
            }
            is ProfileBuilderEvent.Success -> {
                Timber.tag(TAG).d("Profile submission success event received.")
            }
            is ProfileBuilderEvent.TagsLoaded -> {
                viewModel.initializeWithUserModel(userModel)
            }

            is ProfileBuilderEvent.ShowFunWarning -> {
               showForFunWarningDialog()
            }
        }
    }

    /**
     * Sends CleverTap screen view event for profile builder screens.
     * Only sends the event once per position to avoid duplicate tracking.
     */
    private fun sendProfileBuilderScreenViewEvent(position: Int) {
        // Avoid sending duplicate events for the same position
        if (lastTrackedPosition == position) {
            return
        }

        lastTrackedPosition = position

        val state = viewModel.state.value

        // Save persistence step when user reaches "Be Yourself" screen
        if (position == BE_YOURSELF_SCREEN_INDEX) {
            viewModel.saveBeYourselfStep()
        }

        // Determine the specific event to send based on position
        val eventToSend = when {
            position == BE_YOURSELF_SCREEN_INDEX -> ClevertapEventEnum.BUILD_PROFILE_SCREENVIEW
            position == ProfileQuestionType.orderedQuestionTypes.size + 1 -> ClevertapEventEnum.PROFILE_SET_SCREENVIEW
            position > 0 && position <= ProfileQuestionType.orderedQuestionTypes.size -> {
                val questionIndex = position - 1
                if (questionIndex < ProfileQuestionType.orderedQuestionTypes.size) {
                    when (ProfileQuestionType.orderedQuestionTypes[questionIndex]) {
                        ProfileQuestionType.CHILDREN_HAVE -> ClevertapEventEnum.DO_YOU_HAVE_CHILDREN_SCREENVIEW
                        ProfileQuestionType.CHILDREN_WANT -> ClevertapEventEnum.DO_YOU_WANT_CHILDREN_SCREENVIEW
                        ProfileQuestionType.SMOKING -> ClevertapEventEnum.DO_YOU_SMOKE_SCREENVIEW
                        ProfileQuestionType.LANGUAGES -> ClevertapEventEnum.LANGUAGES_SCREENVIEW
                        ProfileQuestionType.RELIGION -> ClevertapEventEnum.WHATS_YOUR_RELIGION_SCREENVIEW
                        ProfileQuestionType.LOOKING_FOR -> ClevertapEventEnum.LOOKING_FOR_SCREENVIEW
                        ProfileQuestionType.BE_YOURSELF -> ClevertapEventEnum.BUILD_PROFILE_SCREENVIEW // fallback
                        ProfileQuestionType.ALL_READY -> ClevertapEventEnum.PROFILE_SET_SCREENVIEW // fallback
                    }
                } else {
                    ClevertapEventEnum.PROFILE_BUILDER_SCREENVIEW // fallback
                }
            }
            else -> ClevertapEventEnum.PROFILE_BUILDER_SCREENVIEW // fallback
        }

        // Get auth method from shared preferences and convert to CleverTap value
        val authMethodString = duaSharedPrefs.getAuthMethode()
        val signUpOrSignInMediumValue = when (authMethodString) {
            AuthMethod.EMAIL.methodName -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
            AuthMethod.PHONE.methodName -> ClevertapSignUpOrSignInMediumValues.PHONE.value
            AuthMethod.GOOGLE.methodName -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
            AuthMethod.FACEBOOK.methodName -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
            else -> ClevertapSignUpOrSignInMediumValues.NULL.value
        }

        val properties = mutableMapOf<String, Any?>(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue
        )

        // Add persist_user_progress_step property based on position
        when {
            position == BE_YOURSELF_SCREEN_INDEX -> {
                properties[ClevertapEventPropertyEnum.PERSIST_USER_PROGRESS_STEP.propertyName] = "be_yourself"
            }
            position == ProfileQuestionType.orderedQuestionTypes.size + 1 -> {
                properties[ClevertapEventPropertyEnum.PERSIST_USER_PROGRESS_STEP.propertyName] = "all_set"
            }
        }

        // Add profile information properties for PROFILE_SET_SCREENVIEW event
        if (eventToSend == ClevertapEventEnum.PROFILE_SET_SCREENVIEW) {
            addProfileInfoProperties(properties, state)
        }

        sendClevertapEvent(eventToSend, properties)
        sendUxCamEvent(eventToSend, properties)

        Timber.tag(TAG).d("Profile builder screen view event sent: position=$position, event=${eventToSend.eventName}")
    }

    private fun showForFunWarningDialog() {
        val forFunWarningDialog = ForFunWarningDialogFragment.newInstance(userModel.gender)
        forFunWarningDialog.show(supportFragmentManager, ForFunWarningDialogFragment.TAG)

        sendLookingForFunEvent()
    }

    private fun sendLookingForFunEvent() {
        val authMethodString = duaSharedPrefs.getAuthMethode()
        val signUpOrSignInMediumValue = when (authMethodString) {
            AuthMethod.EMAIL.methodName -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
            AuthMethod.PHONE.methodName -> ClevertapSignUpOrSignInMediumValues.PHONE.value
            AuthMethod.GOOGLE.methodName -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
            AuthMethod.FACEBOOK.methodName -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
            else -> ClevertapSignUpOrSignInMediumValues.NULL.value
        }

        val properties = mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue
        )
        sendClevertapEvent(ClevertapEventEnum.LOOKING_FOR_FUN_POPUP, properties)
        sendUxCamEvent(ClevertapEventEnum.LOOKING_FOR_FUN_POPUP, properties)
    }

    /**
     * Adds profile information properties to the CleverTap event properties for PROFILE_SET_SCREENVIEW.
     * Each property contains the selected tag ID(s) for the corresponding question type.
     */
    private fun addProfileInfoProperties(properties: MutableMap<String, Any?>, state: ProfileBuilderState) {
        // Helper function to get the first selected tag ID for single-select questions
        fun getFirstSelectedTagId(questionType: ProfileQuestionType): Int? {
            return state.selections[questionType]?.firstOrNull()
        }

        // Helper function to get selected tag IDs as comma-separated string for multi-select questions
        fun getSelectedTagIdsAsString(questionType: ProfileQuestionType): String {
            val selectedIds = state.selections[questionType]?.toList() ?: emptyList()
            return selectedIds.joinToString(",")
        }

        // Add properties for each question type
        properties[ClevertapEventPropertyEnum.DO_YOU_HAVE_CHILDREN_INFO.propertyName] =
            getFirstSelectedTagId(ProfileQuestionType.CHILDREN_HAVE)

        properties[ClevertapEventPropertyEnum.DO_YOU_WANT_CHILDREN_INFO.propertyName] =
            getFirstSelectedTagId(ProfileQuestionType.CHILDREN_WANT)

        properties[ClevertapEventPropertyEnum.DO_YOU_SMOKE_INFO.propertyName] =
            getFirstSelectedTagId(ProfileQuestionType.SMOKING)

        properties[ClevertapEventPropertyEnum.WHATS_YOUR_RELIGION_INFO.propertyName] =
            getFirstSelectedTagId(ProfileQuestionType.RELIGION)

        properties[ClevertapEventPropertyEnum.LOOKING_FOR_INFO.propertyName] =
            getFirstSelectedTagId(ProfileQuestionType.LOOKING_FOR)

        // Languages is multi-select, so we send a comma-separated string of IDs
        properties[ClevertapEventPropertyEnum.LANGUAGES_INFO.propertyName] =
            getSelectedTagIdsAsString(ProfileQuestionType.LANGUAGES)

        Timber.tag(TAG).d("Added profile info properties to PROFILE_SET_SCREENVIEW event: $properties")
    }

    /**
     * Handle memory pressure by notifying ViewModel to release unnecessary resources.
     */
    override fun onLowMemory() {
        super.onLowMemory()
        viewModel.onLowMemory()
    }

    override fun onBackPressed() {
        // TODO: to check in the future if this should be modified into advancing to different scenarios
         if (viewModel.state.value.currentStepIndex > BE_YOURSELF_SCREEN_INDEX && viewModel.state.value.currentStepIndex < ProfileQuestionType.orderedQuestionTypes.size + 1) {
             viewModel.onBackClicked()
         } else {
             super.onBackPressed()
         }
     }
}