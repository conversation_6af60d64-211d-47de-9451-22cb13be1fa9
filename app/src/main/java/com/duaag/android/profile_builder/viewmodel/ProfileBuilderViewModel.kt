package com.duaag.android.profile_builder.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.base.models.TagTypeModel
import com.duaag.android.base.models.TagsResponse
import com.duaag.android.base.models.UpdateTagsBatchModel
import com.duaag.android.base.models.UpdateTagsModel
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.domain.GetOptionsForQuestionUseCase
import com.duaag.android.profile_builder.domain.ManageCacheUseCase
import com.duaag.android.profile_builder.domain.PreloadAdjacentQuestionsUseCase
import com.duaag.android.profile_builder.domain.UpdateSelectionUseCase
import com.duaag.android.profile_builder.models.FOR_FUN_TAG_ITEM_ID
import com.duaag.android.profile_builder.models.ProfileBuilderEvent
import com.duaag.android.profile_builder.models.ProfileQuestionResources
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import com.duaag.android.profile_builder.models.questionTypeToSignUpPersistStepsEnum
import com.duaag.android.profile_builder.models.questionTypeToTagTypeId
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.signup_persist.domain.SignUpPersistManager
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistModel
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.signup_persist.domain.use_cases.ClearAllSignUpPersistStepsUseCase
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * The ViewModel for the Profile Builder feature.
 * Handles the business logic for the profile builder flow, including loading tags, updating selections, and submitting the profile.
 */
@ActivityScope
class ProfileBuilderViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val duaSharedPrefs: DuaSharedPrefs,
    private val getOptionsUseCase: GetOptionsForQuestionUseCase,
    private val updateSelectionUseCase: UpdateSelectionUseCase,
    private val preloadAdjacentQuestionsUseCase: PreloadAdjacentQuestionsUseCase,
    private val manageCacheUseCase: ManageCacheUseCase,
    private val signUpPersistManager: SignUpPersistManager,
    private val profileOptionsRepository: ProfileOptionsRepository,
    private val clearAllPersistStepsIncludingProfileBuilderUseCase: ClearAllPersistStepsIncludingProfileBuilderUseCase
) : ViewModel() {

    companion object {
        /**
         * The index of the "Be Yourself" screen.
         */
        const val BE_YOURSELF_SCREEN_INDEX = 0
        private const val VIEWMODEL_TAG = "ProfileBuilderVM"
    }

    /**
     * The current state of the Profile Builder.
     */
    private val _state = MutableStateFlow(ProfileBuilderState())
    val state: StateFlow<ProfileBuilderState> = _state.asStateFlow()

    /**
     * The events emitted by the ViewModel.
     */
    private val _events = MutableSharedFlow<ProfileBuilderEvent>(
        replay = 0,
        extraBufferCapacity = 64
    )
    val events: SharedFlow<ProfileBuilderEvent> = _events.asSharedFlow()

    /**
     * The count of options fetches for each question type.
     */
    private var optionsFetchCount = mutableMapOf<ProfileQuestionType, Int>()
    private var allAvailableOptions: Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>> = emptyMap()

    /**
     * The mapping of tag type IDs to question types.
     */
    private val tagTypeIdToQuestionType = questionTypeToTagTypeId.entries.associateBy({ it.value }) { it.key }
    private val signUpPersistStepToQuestionType = questionTypeToSignUpPersistStepsEnum.entries.associateBy({ it.value }) { it.key }

    /**
     * The set of multi-select questions.
     */
    private val multiSelectQuestions = setOf(
        ProfileQuestionType.LANGUAGES,
    )

    init {
        duaSharedPrefs.setIsProfileBuildingInProgress(true)
        loadTags()
    }

    /**
     * Loads persisted options from SignUpPersistManager and applies selections to the provided options.
     * This ensures that any previously selected values are restored when the question options are displayed.
     *
     * @param options The question options to apply selections to
     * @return Map of question types to their updated options with selections applied
     */
    private suspend fun loadPersistedOptions(options: Map<ProfileQuestionType, List<ProfileTagOptionModel>>): Map<ProfileQuestionType, List<ProfileTagOptionModel>> {
        val persistedOptions = signUpPersistManager.getProfileBuilderSteps()
        if (persistedOptions.isEmpty()) {
            return options
        }

        val updatedSelections = _state.value.selections.toMutableMap()

        persistedOptions.forEach { persistedModel ->
            val questionType = signUpPersistStepToQuestionType[persistedModel.type]

            if (questionType != null) {
                val questionOptions = options[questionType]
                    ?.filterIsInstance<ProfileTagOptionModel.Item>()
                    ?: emptyList()

                if (questionOptions.isNotEmpty() && persistedModel.value != null) {
                    if (isMultiSelectQuestion(questionType)) {
                        val selectedNames = persistedModel.value.split(",")
                        val selectedTagIds = questionOptions
                            .filter { it.name in selectedNames }
                            .map { it.tagId }
                            .toSet()

                        if (selectedTagIds.isNotEmpty()) {
                            updatedSelections[questionType] = selectedTagIds
                        }
                    } else {
                        val selectedOption = questionOptions.find { it.name == persistedModel.value }

                        if (selectedOption != null) {
                            updatedSelections[questionType] = setOf(selectedOption.tagId)
                        }
                    }
                }
            }
        }

        val updatedAllQuestionOptions = options.toMutableMap()

        updatedSelections.forEach { (questionType, selections) ->
            val options = updatedAllQuestionOptions[questionType]
            if (options != null) {
                updatedAllQuestionOptions[questionType] = options.map { option ->
                    if (option is ProfileTagOptionModel.Item) {
                        option.copy(isSelected = option.tagId in selections)
                    } else {
                        option
                    }
                }
            }
        }

        _state.update { it.copy(selections = updatedSelections) }

        val currentType = _state.value.currentQuestionType
        if (currentType != null) {
            val updatedCurrentOptions = updatedAllQuestionOptions[currentType]

            if (updatedCurrentOptions != null) {
                _state.update { it.copy(currentQuestionOptions = updatedCurrentOptions) }
            } else if (updatedSelections.containsKey(currentType)) {
                updateOptionsWithSelections(currentType, updatedSelections[currentType] ?: emptySet())
            }
        }

        return updatedAllQuestionOptions
    }

    /**
     * Updates the current question options with the given selections.
     *
     * @param questionType The question type to update
     * @param selections The set of selected tag IDs
     */
    private fun updateOptionsWithSelections(questionType: ProfileQuestionType, selections: Set<Int>) {
        val currentOptions = _state.value.currentQuestionOptions
        if (currentOptions != null) {
            val updatedOptions = currentOptions.map { option ->
                if (option is ProfileTagOptionModel.Item) {
                    option.copy(isSelected = option.tagId in selections)
                } else {
                    option
                }
            }

            updateState { it.copy(currentQuestionOptions = updatedOptions) }
        }
    }

    /**
     * Called when the ViewModel is being cleared.
     * Releases resources held by the cache manager.
     */
    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            manageCacheUseCase.release()
        }
    }

    /**
     * Handles low memory conditions by trimming the cache.
     * Called when the app receives onLowMemory() event.
     */
    fun onLowMemory() {
        viewModelScope.launch {
            manageCacheUseCase.handleLowMemory(_state.value.currentQuestionType)
        }
    }

    /**
     * Loads all tag options from the backend.
     * Initiates the API call to fetch tags in the user's current language.
     */
    private fun loadTags() {
        viewModelScope.launch {
            clearError()
            updateLoadingState(true)

            try {
                val language = ModifiedLingver.getInstance().getLanguage()
                val result = userRepository.getAndSaveAllTags(language)
                processTagLoadResult(result)
            } catch (e: Exception) {
                handleTagLoadException(e)
            }
        }
    }

    /**
     * Updates only the loading state.
     *
     * @param isLoading Whether the ViewModel is currently loading
     */
    private fun updateLoadingState(isLoading: Boolean) {
        updateState { it.copy(isLoading = isLoading) }
    }

    /**
     * Clears any existing error.
     */
    private fun clearError() {
        updateState { it.copy(error = null) }
    }

    /**
     * Sets an error message.
     *
     * @param errorMessage The error message to set
     */
    private fun setError(errorMessage: String?) {
        updateState { it.copy(error = errorMessage) }
    }

    /**
     * Processes the result of the tag loading API call.
     *
     * @param result The result of the API call
     */
    private fun processTagLoadResult(result: ResourceV2<*>) {
        when (result) {
            is ResourceV2.Success -> handleSuccessfulTagLoad((result.data as TagsResponse).tags)
            is ResourceV2.Error -> handleTagLoadError(result.message)
        }
    }

    /**
     * Processes successful tag response from backend.
     * Transforms backend tag data into UI models, loads all question options,
     * applies persisted selections from SignUpPersistManager, and updates the state.
     *
     * @param tags The list of tag types retrieved from the backend
     */
    private fun handleSuccessfulTagLoad(tags: List<TagTypeModel>) {
        val processedOptions = processTagsFromBackend(tags)
        profileOptionsRepository.setAllAvailableOptions(processedOptions)
        viewModelScope.launch {
            val allOptions = mutableMapOf<ProfileQuestionType, List<ProfileTagOptionModel>>()
            ProfileQuestionType.orderedQuestionTypes.forEach { questionType ->
                val options = getOptionsForQuestion(questionType, forceRefresh = true)
                if (options != null) {
                    allOptions[questionType] = options
                }
            }

            val updatedOptions = loadPersistedOptions(allOptions)
            updateStateWithOptions(updatedOptions)
            emitEvent(ProfileBuilderEvent.TagsLoaded)
        }
    }

    /**
     * Handles error response when loading tags.
     *
     * @param message The error message
     */
    private fun handleTagLoadError(message: String) {
        updateState { it.copy(isLoading = false, error = "Failed to load tags: $message") }
    }

    /**
     * Handles exceptions that occur during tag loading.
     *
     * @param e The exception that occurred
     */
    private fun handleTagLoadException(e: Exception) {
        val errorMsg = e.message ?: "An unexpected error occurred"
        updateState { it.copy(isLoading = false, error = errorMsg) }
    }

    /**
     * Transforms backend tag data into the format needed by the UI.
     *
     * @param backendTags The list of tag types from the backend
     * @return Map of question types to their corresponding tag options
     */
    private fun processTagsFromBackend(backendTags: List<TagTypeModel>): Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>> {
        return questionTypeToTagTypeId.mapNotNull { (questionType, tagTypeId) ->
            val tagType = backendTags.find { it.id == tagTypeId }

            if (tagType == null) {
                logTagTypeNotFound(tagTypeId, questionType)
                return@mapNotNull null
            }

            val options = createTagOptionItems(tagType).toMutableList()

            // Add "Fun" option to LOOKING_FOR question type
            if (questionType == ProfileQuestionType.LOOKING_FOR) {
                options.add(
                    ProfileTagOptionModel.Item(
                        tagId = FOR_FUN_TAG_ITEM_ID,
                        tagTypeId = tagTypeId,
                        name = "Fun",
                        limit = tagType.limit
                    )
                )
            }

            questionType to options
        }.toMap()
    }

    /**
     * Logs when a tag type is not found in the backend response.
     *
     * @param tagTypeId The ID of the missing tag type
     * @param questionType The question type associated with the missing tag type
     */
    private fun logTagTypeNotFound(tagTypeId: Int, questionType: ProfileQuestionType) {
        Timber.w("Tag type $tagTypeId (for $questionType) not found in backend response")
    }

    /**
     * Creates UI models for tag options from backend tag model.
     *
     * @param tagType The backend tag model
     * @return List of tag option items
     */
    private fun createTagOptionItems(tagType: TagTypeModel): List<ProfileTagOptionModel.Item> {
        return tagType.items.filter { it.deletedAt == null }.map { tagModel ->
            tagModel.let {
                ProfileTagOptionModel.Item(
                    tagId = it.id,
                    tagTypeId = it.tagTypeId,
                    name = it.title,
                    limit = tagType.limit
                )
            }
        }
    }

    /**
     * Retrieves options for a specific question type.
     * First checks cache, then falls back to all available options if needed.
     *
     * @param questionType The question type to retrieve options for
     * @param forceRefresh Whether to force a refresh of the options
     * @return List of options for the question type, or null if not found
     */
    private suspend fun getOptionsForQuestion(questionType: ProfileQuestionType, forceRefresh: Boolean = false): List<ProfileTagOptionModel>? {
        trackOptionFetchFrequency(questionType)
        return try {
            val options = getOptionsUseCase(questionType, forceRefresh).first()
            options
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Gets the current selections for a question type.
     *
     * @param questionType The question type to retrieve selections for
     * @return Set of selected tag IDs for the question type
     */
    private fun getSelectionsForQuestion(questionType: ProfileQuestionType): Set<Int> {
        return _state.value.selections[questionType] ?: emptySet()
    }

    /**
     * Helper function to apply the current selection state to a list of options.
     * This ensures that regardless of whether options came from cache or remote,
     * they reflect the user's choices stored in the ViewModel's state.
     *
     * @param options The list of options to apply selections to
     * @param questionType The question type associated with the options
     * @return List of options with selections applied
     */
    private fun applySelectionsToOptions(
        options: List<ProfileTagOptionModel>?,
        questionType: ProfileQuestionType?
    ): List<ProfileTagOptionModel>? {
        if (options == null || questionType == null) {
            return options
        }

        val currentSelections = getSelectionsForQuestion(questionType)
        if (currentSelections.isEmpty()) {
            return options
        }

        return options.map { option ->
            if (option is ProfileTagOptionModel.Item) {
                option.copy(isSelected = option.tagId in currentSelections)
            } else {
                option
            }
        }
    }

    /**
     * Tracks how frequently options are being fetched for a question type.
     * Logs a warning if the frequency seems excessive to help identify performance issues.
     *
     * @param questionType The question type to track
     */
    private fun trackOptionFetchFrequency(questionType: ProfileQuestionType) {
        val currentCount = (optionsFetchCount[questionType] ?: 0) + 1
        optionsFetchCount[questionType] = currentCount

        if (currentCount % 5 == 0) {
            Timber.w("getOptionsForQuestion called $currentCount times for $questionType")
        }
    }

    /**
     * Handles the selection of a tag option.
     * For single-select questions, replaces the current selection and automatically advances.
     * For multi-select questions, toggles the selection state of the option.
     *
     * @param item The selected tag option
     */
    fun onTagSelected(item: ProfileTagOptionModel.Item) {
        val currentType = _state.value.currentQuestionType ?: return

        if(currentType == ProfileQuestionType.LOOKING_FOR && item.tagId == FOR_FUN_TAG_ITEM_ID) {
            emitEvent(ProfileBuilderEvent.ShowFunWarning)
            return
        }
        val isMultiSelect = isMultiSelectQuestion(currentType)

        val updatedSelections = updateSelections(currentType, item, isMultiSelect)

        viewModelScope.launch {
            updateSelectionUseCase.updateMultipleSelections(currentType, updatedSelections)
        }

        updateStateForSelection(currentType, updatedSelections)

        // Send CleverTap event for language selection
        if (currentType == ProfileQuestionType.LANGUAGES) {
            sendLanguageInitiatedEvent()
        }

        if (!isMultiSelect) {
            onNextClicked()
        }

        viewModelScope.launch {
            preloadAdjacentQuestionsUseCase(
                currentType,
                { type -> _state.value.selections[type] ?: emptySet() }
            )
        }
    }

    /**
     * Updates the selection set based on user action.
     *
     * @param questionType The question type to update
     * @param selectedItem The selected tag option
     * @param isMultiSelect Whether the question is multi-select
     * @return Set of updated selected tag IDs
     */
    private fun updateSelections(
        questionType: ProfileQuestionType,
        selectedItem: ProfileTagOptionModel.Item,
        isMultiSelect: Boolean
    ): Set<Int> {
        val currentSelections = _state.value.selections[questionType]?.toMutableSet() ?: mutableSetOf()

        if (isMultiSelect) {
            if (selectedItem.tagId in currentSelections) {
                currentSelections.remove(selectedItem.tagId)
            } else {
                // Check if adding this item would exceed the limit
                if (selectedItem.limit > 0 && currentSelections.size >= selectedItem.limit) {
                    return currentSelections
                } else {
                    currentSelections.add(selectedItem.tagId)
                }
            }

            saveMultiSelectionInRealTime(questionType, currentSelections)
        } else {
            currentSelections.clear()
            currentSelections.add(selectedItem.tagId)

            saveSingleSelectionInRealTime(questionType, selectedItem)
        }

        return currentSelections
    }

    /**
     * Updates the view state after a selection is made.
     *
     * @param questionType The question type to update
     * @param newSelections The new set of selected tag IDs
     */
    private fun updateStateForSelection(
        questionType: ProfileQuestionType,
        newSelections: Set<Int>
    ) {
        viewModelScope.launch {
            val baseOptions = getOptionsForQuestion(questionType)
            val optionsWithNewSelections = baseOptions?.map { option ->
                if (option is ProfileTagOptionModel.Item) {
                    option.copy(isSelected = option.tagId in newSelections)
                } else {
                    option
                }
            }

            updateState { currentState ->
                val updatedMap = currentState.selections.toMutableMap()
                updatedMap[questionType] = newSelections
                currentState.copy(
                    selections = updatedMap,
                    currentQuestionOptions = optionsWithNewSelections
                )
            }

            preloadAdjacentQuestions(questionType)
        }
    }

    /**
     * Determines if a question allows multiple selections.
     *
     * @param questionType The question type to check
     * @return Whether the question is multi-select
     */
    private fun isMultiSelectQuestion(questionType: ProfileQuestionType): Boolean {
        return questionType in multiSelectQuestions
    }

    /**
     * Navigates to a specific position in the profile builder flow.
     * Updates state and emits navigation event.
     *
     * @param position The position to navigate to
     * @param forceEvent Whether to force the navigation event
     * @param forceRefreshOptions Whether to force a refresh of the options
     * @param smoothScroll Whether to use smooth scrolling animation
     */
    private fun navigateToPosition(
        position: Int,
        forceEvent: Boolean = true,
        forceRefreshOptions: Boolean = true,
        smoothScroll: Boolean = true
    ) {
        if (!isValidPosition(position)) {
            Timber.w("Invalid navigation position: $position")
            return
        }

        val questionType = getQuestionTypeForPosition(position)

        viewModelScope.launch {
            manageCacheUseCase.updateCurrentPosition(position)
        }

        viewModelScope.launch {
            val options = if (forceRefreshOptions) {
                loadOptionsForPosition(questionType, true)
            } else {
                _state.value.allQuestionOptions[questionType]
            }

            val optionsWithSelections = if (!forceRefreshOptions && options != null) {
                options
            } else {
                applySelectionsToOptions(options, questionType)
            }

            updateStateForNavigation(position, questionType, optionsWithSelections)

            if (forceEvent) {
                emitNavigationEvent(position, smoothScroll)
            }
        }

        questionType?.let {
            preloadAdjacentQuestions(it)
            val currentOptions = _state.value.allQuestionOptions[it]
            if (currentOptions != null) {
                updateState { it.copy(currentQuestionOptions = currentOptions) }
            }
        }
    }

    /**
     * Checks if a position is valid for navigation.
     *
     * @param position The position to check
     * @return Whether the position is valid
     */
    private fun isValidPosition(position: Int): Boolean {
        return position >= BE_YOURSELF_SCREEN_INDEX &&
                position <= ProfileQuestionType.orderedQuestionTypes.size + 1
    }

    /**
     * Gets the question type corresponding to a position.
     *
     * @param position The position to get the question type for
     * @return The question type, or null if not found
     */
    private fun getQuestionTypeForPosition(position: Int): ProfileQuestionType? {
        return if (position > BE_YOURSELF_SCREEN_INDEX) {
            val index = position - 1
            ProfileQuestionType.orderedQuestionTypes.getOrNull(index)
        } else {
            null
        }
    }

    /**
     * Loads options for a position, trying cache first.
     * Falls back to remote fetch if not cached or if explicitly forced.
     *
     * @param questionType The question type to load options for
     * @param forceRefresh Whether to force a refresh of the options
     * @return List of options for the question type, or null if not found
     */
    private suspend fun loadOptionsForPosition(
        questionType: ProfileQuestionType?,
        forceRefresh: Boolean
    ): List<ProfileTagOptionModel>? {
        if (questionType == null) return null

        return getOptionsForQuestion(questionType, forceRefresh)
            ?: getOptionsForQuestion(questionType, forceRefresh = true)
    }

    /**
     * Updates state for navigation to new position.
     *
     * @param position The new position
     * @param questionType The question type to update
     * @param options The options to update
     */
    private fun updateStateForNavigation(
        position: Int,
        questionType: ProfileQuestionType?,
        options: List<ProfileTagOptionModel>?
    ) {
        val currentState = _state.value

        if (currentState.currentStepIndex != position ||
            currentState.currentQuestionType != questionType ||
            currentState.currentQuestionOptions != options) {

            updateState {
                it.copy(
                    currentStepIndex = position,
                    currentQuestionType = questionType,
                    currentQuestionOptions = options,
                )
            }
            updateProgress()
        }
    }

    /**
     * Emits a navigation event to change pages.
     *
     * @param position The position to navigate to
     * @param smoothScroll Whether to use smooth scrolling animation
     */
    private fun emitNavigationEvent(position: Int, smoothScroll: Boolean = false) {
        Timber.tag("ProfileBuilderNav").d("Emitting navigation event: position=$position, smoothScroll=$smoothScroll")
        emitEvent(ProfileBuilderEvent.NavigateToPage(position, smoothScroll))
    }

    /**
     * Updates the progress indicator based on current position.
     */
    private fun updateProgress() {
        val currentIndex = _state.value.currentStepIndex
        val totalQuestions = ProfileQuestionType.orderedQuestionTypes.size + 1

        val progress = calculateProgress(currentIndex, totalQuestions)

        if (_state.value.progressValue != progress) {
            updateState { it.copy(progressValue = progress) }
        }
    }

    /**
     * Calculates the progress percentage.
     *
     * @param currentIndex The current index
     * @param totalQuestions The total number of questions
     * @return The progress percentage
     */
    private fun calculateProgress(currentIndex: Int, totalQuestions: Int): Int {
        return if (currentIndex > BE_YOURSELF_SCREEN_INDEX && totalQuestions > 0) {
            (currentIndex * 100) / totalQuestions
        } else {
            0
        }
    }

    /**
     * Checks if the current question has at least one selection.
     *
     * @return Whether the current question has a selection
     */
    private fun hasSelectionForCurrentQuestion(): Boolean {
        val currentType = _state.value.currentQuestionType ?: return false
        return _state.value.selections[currentType]?.isNotEmpty() == true
    }

    /**
     * Checks if all required questions have been answered.
     *
     * @return Whether all required questions have been answered
     */
    private fun allRequiredQuestionsAnswered(): Boolean {
        return ProfileQuestionType.orderedQuestionTypes.all { type ->
            _state.value.selections[type]?.isNotEmpty() == true
        }
    }

    /**
     * Submits profile answers to the backend.
     */
    private fun submitAnswers() {
        viewModelScope.launch {
            try {
                val selectionsToSubmit = formatSelectionsForSubmission()
                val result = userRepository.updateUserProfileTags(userModel = userRepository.user.value , userTags = selectionsToSubmit)
                handleSubmissionResult(result)
            } catch (e: Exception) {
                handleSubmissionException(e)
            }
        }
    }

    /**
     * Formats selections into the format required by the backend.
     *
     * @return List of UpdateTagsModel for submission to backend
     */
    private fun formatSelectionsForSubmission(): UpdateTagsBatchModel {
        val tagsToUpdate = _state.value.selections
            .mapNotNull { (questionType, selectedIdsSet) ->
                val tagTypeId = questionTypeToTagTypeId[questionType]
                if (tagTypeId != null && selectedIdsSet.isNotEmpty()) {
                    UpdateTagsModel(
                        typeId = tagTypeId,
                        itemIds = selectedIdsSet.toList()
                    )
                } else {
                    null
                }
            }

        return UpdateTagsBatchModel(tags = tagsToUpdate)
    }

    /**
     * Handles the result of profile submission.
     *
     * @param result The result of the submission
     */
    private fun handleSubmissionResult(result: ResourceV2<*>) {
        when (result) {
            is ResourceV2.Success -> {
                updateState {
                    it.copy(
                        isSubmitting = false,
                        isComplete = true,
                        error = null
                    )
                }
                duaSharedPrefs.setIsProfileBuildingInProgress(false)

                // Clear all persistence steps since entire onboarding flow is completed
                clearAllPersistenceSteps()

                emitEvent(ProfileBuilderEvent.Success)
                emitEvent(ProfileBuilderEvent.Complete)
            }
            is ResourceV2.Error<*> -> {
                handleSubmissionError(result.message)
            }
        }
    }

    /**
     * Handles error during profile submission.
     *
     * @param message The error message
     */
    private fun handleSubmissionError(message: String?) {
        val errorMsg = message ?: "Failed to save profile."
        updateState { it.copy(isSubmitting = false, error = errorMsg) }
        emitEvent(ProfileBuilderEvent.ShowError(errorMsg))
    }

    /**
     * Handles exception during profile submission.
     *
     * @param e The exception that occurred
     */
    private fun handleSubmissionException(e: Exception) {
        val errorMsg = e.message ?: "An unexpected error occurred."
        updateState { it.copy(isSubmitting = false, error = errorMsg) }
        emitEvent(ProfileBuilderEvent.ShowError(errorMsg))
    }

    /**
     * Initializes the ViewModel state with data from an existing UserModel.
     * Used when profile builder is showing a partially completed profile.
     *
     * @param userModel The UserModel to initialize with
     */
    fun initializeWithUserModel(userModel: UserModel) {
        if (_state.value.isLoading) {
            return
        }

        viewModelScope.launch {
            if (_state.value.allQuestionOptions.isEmpty()) {
                return@launch
            }

            val persistedOptions = signUpPersistManager.getProfileBuilderSteps()
            if (persistedOptions.isEmpty() || persistedOptions.all { it.value.isNullOrEmpty() }) {
                initializeWithUserModelFallback(userModel)
                return@launch
            }

            // Check if user has completed profile builder
            val isAllSet = persistedOptions
                .find { it.type == SignUpPersistStepsEnum.PROFILE_BUILDER_ALL_SET }
                ?.value?.equals("true") == true

            if (isAllSet) {
                // Navigate to final "You're all Set" screen
                val finalPosition = ProfileQuestionType.orderedQuestionTypes.size + 1
                navigateToPosition(finalPosition, forceEvent = true, forceRefreshOptions = false, smoothScroll = false)
                updateState { it.copy(isComplete = true) }
                return@launch
            }

            applyPersistedOptionsToState()
        }
    }

    /**
     * Applies persisted options from SignUpPersistManager to the current state.
     * Updates the UI state with selections that match the option IDs.
     */
    private suspend fun applyPersistedOptionsToState() {
        val updatedOptions = loadPersistedOptions(_state.value.allQuestionOptions)
        updateStateWithOptions(updatedOptions)

        // Determine the correct position based on persisted steps
        val persistedOptions = signUpPersistManager.getProfileBuilderSteps()
        val hasSeenBeYourself = persistedOptions
            .find { it.type == SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF }
            ?.value?.equals("true") == true

        if (hasSeenBeYourself) {
            // User has seen "Be Yourself" screen, find the first incomplete question
            findAndNavigateToFirstIncompleteStep()
        }
        // If user hasn't seen "Be Yourself" screen, stay at position 0 (default)
    }

    /**
     * Updates the state with the given options and handles current question type.
     *
     * @param updatedOptions The updated options
     */
    private fun updateStateWithOptions(updatedOptions: Map<ProfileQuestionType, List<ProfileTagOptionModel>>) {
        val currentPosition = _state.value.currentStepIndex
        val currentQuestionType = getQuestionTypeForPosition(currentPosition)
        val optionsWithSelections = currentQuestionType?.let {
            updatedOptions[it]
        }

        updateState { currentState ->
            currentState.copy(
                allQuestionOptions = updatedOptions,
                currentStepIndex = currentPosition,
                currentQuestionType = currentQuestionType,
                currentQuestionOptions = optionsWithSelections,
                isLoading = false
            )
        }

        updateProgress()
        currentQuestionType?.let { preloadAdjacentQuestions(it) }

        // Find and navigate to the first non-skippable step
        findAndNavigateToFirstNonSkippableStep()
    }

    /**
     * Finds the first incomplete step and navigates to it.
     * This is called after loading persisted options to ensure user starts
     * at the correct step in the profile building flow.
     */
    private fun findAndNavigateToFirstIncompleteStep() {
        viewModelScope.launch {
            val currentSelections = _state.value.selections

            // Find the first question that doesn't have a selection
            var targetPosition = BE_YOURSELF_SCREEN_INDEX + 1

            for (i in ProfileQuestionType.orderedQuestionTypes.indices) {
                val questionType = ProfileQuestionType.orderedQuestionTypes[i]
                val hasSelection = currentSelections[questionType]?.isNotEmpty() == true

                if (!hasSelection) {
                    targetPosition = i + 1 // +1 because position 0 is "Be Yourself" screen
                    break
                }
            }

            // If all questions are answered, go to final screen
            if (targetPosition == BE_YOURSELF_SCREEN_INDEX + 1 &&
                ProfileQuestionType.orderedQuestionTypes.all { currentSelections[it]?.isNotEmpty() == true }) {
                targetPosition = ProfileQuestionType.orderedQuestionTypes.size + 1
            }

            if (_state.value.currentStepIndex != targetPosition) {
                navigateToPosition(targetPosition, forceEvent = true, forceRefreshOptions = false, smoothScroll = false)
            }
        }
    }

    /**
     * Finds the first non-skippable step and navigates to it.
     * This is called after loading persisted options to ensure user starts
     * at the correct step in the profile building flow.
     */
    private fun findAndNavigateToFirstNonSkippableStep() {
        viewModelScope.launch {
            var firstNonSkippedPosition = BE_YOURSELF_SCREEN_INDEX + 1

            for (i in ProfileQuestionType.orderedQuestionTypes.indices) {
                val questionType = ProfileQuestionType.orderedQuestionTypes[i]
                val stepEnum = questionTypeToSignUpPersistStepsEnum[questionType] ?: continue

                if (!signUpPersistManager.shouldSkipStep(stepEnum)) {
                    firstNonSkippedPosition = i
                    break
                }
            }

            if (_state.value.currentStepIndex != firstNonSkippedPosition) {
                navigateToPosition(firstNonSkippedPosition, forceEvent = true, forceRefreshOptions = false, smoothScroll = false)
            }
        }
    }

    /**
     * Initializes with user model data if persisted options are not available.
     * This is used when signUpPersistManager doesn't have data for the profile builder steps.
     *
     * @param userModel The UserModel to initialize with
     */
    private fun initializeWithUserModelFallback(userModel: UserModel) {
        val currentPosition = _state.value.currentStepIndex
        val currentQuestionType = getQuestionTypeForPosition(currentPosition)
        val selections = extractSelectionsFromUserModel(userModel)

        viewModelScope.launch {
            val updatedAllOptions = applySelectionsToAllOptions(selections)
            val optionsWithSelections = applySelectionsToOptions(
                currentQuestionType?.let { getOptionsForQuestion(it) },
                currentQuestionType
            )

            updateState { currentState ->
                currentState.copy(
                    selections = selections,
                    allQuestionOptions = updatedAllOptions,
                    currentStepIndex = currentPosition,
                    currentQuestionType = currentQuestionType,
                    currentQuestionOptions = optionsWithSelections,
                    isLoading = false
                )
            }

            updateProgress()
            currentQuestionType?.let { preloadAdjacentQuestions(it) }
        }
    }

    /**
     * Applies selections to all options in the state.
     *
     * @param selections The map of question types to selected tag IDs
     * @return Map of question types to their updated options with selections applied
     */
    private fun applySelectionsToAllOptions(selections: Map<ProfileQuestionType, Set<Int>>): Map<ProfileQuestionType, List<ProfileTagOptionModel>> {
        return _state.value.allQuestionOptions.mapValues { (type, options) ->
            val typeSelections = selections[type] ?: emptySet()
            options.map { option ->
                if (option is ProfileTagOptionModel.Item) {
                    option.copy(isSelected = option.tagId in typeSelections)
                } else {
                    option
                }
            }
        }
    }

    /**
     * Handles the click on the "Start Questions" button.
     * Advances from introduction screen to the first question.
     */
    fun onStartQuestionsClicked() {
        // Save that user has navigated from the "Be Yourself" screen
        saveBeYourselfStep()

        val nextIndex = BE_YOURSELF_SCREEN_INDEX + 1

        if (isValidQuestionIndex(nextIndex)) {
            navigateToPosition(nextIndex, forceRefreshOptions = true, smoothScroll = true)
        }
    }

    /**
     * Checks if an index is a valid question index.
     *
     * @param index The index to check
     * @return Whether the index is valid
     */
    private fun isValidQuestionIndex(index: Int): Boolean {
        return index <= ProfileQuestionType.orderedQuestionTypes.size + 1
    }

    /**
     * Handles the next button click.
     * Validates current question has a selection before advancing.
     */
    fun onNextClicked() {
        if (shouldPreventNavigation()) {
            emitEvent(ProfileBuilderEvent.ShowError("Please make a selection to continue."))
            return
        }

        // Send CleverTap event for languages added successfully
        val currentType = _state.value.currentQuestionType
        if (currentType == ProfileQuestionType.LANGUAGES) {
            sendLanguagesAddedSuccessfullyEvent()
        }

        val nextIndex = _state.value.currentStepIndex + 1
        if (nextIndex == ProfileQuestionType.orderedQuestionTypes.size + 1){
            beginProfileSubmission()
        }
        if (isValidQuestionIndex(nextIndex)) {
            navigateToPosition(nextIndex, forceRefreshOptions = true, smoothScroll = true)
        }
    }

    /**
     * Determines if navigation should be prevented due to missing selection.
     *
     * @return Whether navigation should be prevented
     */
    private fun shouldPreventNavigation(): Boolean {
        return _state.value.currentQuestionType != null && !hasSelectionForCurrentQuestion()
    }

    /**
     * Handles the back button click.
     * Navigates to the previous question or intro screen.
     */
    fun onBackClicked() {
        val prevIndex = _state.value.currentStepIndex - 1
        if (prevIndex >= BE_YOURSELF_SCREEN_INDEX) {
            navigateToPosition(prevIndex, forceRefreshOptions = false, smoothScroll = true)
        }
    }


    /**
     * Begins the process of submitting the profile.
     */
    private fun beginProfileSubmission() {
        viewModelScope.launch {
            updateState { it.copy(isSubmitting = true) }
            submitAnswers()
        }
    }

    /**
     * Checks if the user is on the last question.
     *
     * @return Whether the user is on the last question
     */
    private fun isOnLastQuestion(): Boolean {
        return _state.value.currentStepIndex == ProfileQuestionType.orderedQuestionTypes.size + 1
    }

    /**
     * Generates an error message for missing answers.
     *
     * @return The error message
     */
    private fun generateMissingAnswersErrorMessage(): String {
        val firstUnanswered = ProfileQuestionType.orderedQuestionTypes.firstOrNull {
            _state.value.selections[it].isNullOrEmpty()
        }

        return firstUnanswered?.let {
            "Please answer: ${ProfileQuestionResources.getQuestionTitle(it)}"
        } ?: "Please answer all required questions."
    }

    /**
     * Retries loading tags after a previous failure.
     */
    fun retryLoadingTags() {
        clearError()
        loadTags()
    }

    /**
     * Preloads options for questions adjacent to the current one.
     *
     * @param currentType The current question type
     */
    private fun preloadAdjacentQuestions(currentType: ProfileQuestionType) {
        if (_state.value.isLoading) return

        viewModelScope.launch {
            preloadAdjacentQuestionsUseCase(
                currentType,
                { type -> _state.value.selections[type] ?: emptySet() }
            )
        }
    }

    /**
     * Extracts tag selections from user model.
     *
     * @param userModel The UserModel to extract selections from
     * @return Map of question types to selected tag IDs
     */
    private fun extractSelectionsFromUserModel(userModel: UserModel): Map<ProfileQuestionType, Set<Int>> {
        val initialSelections = mutableMapOf<ProfileQuestionType, MutableSet<Int>>()

        userModel.tags?.forEach { userTag ->
            userTag.tagTypeId.let { typeId ->
                tagTypeIdToQuestionType[typeId]?.let { questionType ->
                    val set = initialSelections.getOrPut(questionType) { mutableSetOf() }
                    set.add(userTag.tagItemId)
                }
            }
        }

        return initialSelections.mapValues { it.value.toSet() }
    }

    /**
     * Updates the ViewModel state using a transform function.
     *
     * @param update The transform function to apply
     */
    private fun updateState(update: (ProfileBuilderState) -> ProfileBuilderState) {
        _state.update(update)
    }

    /**
     * Emits a one-time event.
     *
     * @param event The event to emit
     */
    private fun emitEvent(event: ProfileBuilderEvent) {
        viewModelScope.launch {
            _events.emit(event)
        }
    }

    /**
     * Saves multi-selection changes in real-time.
     *
     * @param questionType The question type to save
     * @param selections The set of selected tag IDs
     */
    private fun saveMultiSelectionInRealTime(questionType: ProfileQuestionType, selections: Set<Int>) {
        if (questionType == ProfileQuestionType.BE_YOURSELF) return

        viewModelScope.launch {
            try {
                val selectedItems = _state.value.currentQuestionOptions
                    ?.filterIsInstance<ProfileTagOptionModel.Item>()
                    ?.filter { it.tagId in selections }
                    ?.toSet() ?: emptySet()

                val serializedValue = if (selectedItems.isEmpty()) {
                    null
                } else {
                    selectedItems.joinToString(",") { it.name }
                }

                val signUpStep = questionTypeToSignUpPersistStepsEnum[questionType] ?: return@launch
                val model = SignUpPersistModel(signUpStep, serializedValue)
                signUpPersistManager.saveStep(model)

                Timber.tag(VIEWMODEL_TAG).d("Saved multi-selection for $questionType: $serializedValue")
            } catch (e: Exception) {
                Timber.tag(VIEWMODEL_TAG).e(e, "Error saving multi-selection in real-time")
            }
        }
    }

    /**
     * Saves single-selection changes in real-time.
     *
     * @param questionType The question type to save
     * @param selectedItem The selected tag option
     */
    private fun saveSingleSelectionInRealTime(questionType: ProfileQuestionType, selectedItem: ProfileTagOptionModel.Item) {
        if (questionType == ProfileQuestionType.BE_YOURSELF) return

        viewModelScope.launch {
            try {
                val value = selectedItem.name

                val signUpStep = questionTypeToSignUpPersistStepsEnum[questionType] ?: return@launch
                val model = SignUpPersistModel(signUpStep, value)
                signUpPersistManager.saveStep(model)

                Timber.tag(VIEWMODEL_TAG).d("Saved single-selection for $questionType: $value")
            } catch (e: Exception) {
                Timber.tag(VIEWMODEL_TAG).e(e, "Error saving single-selection in real-time")
            }
        }
    }

    /**
     * Retries the current operation.
     * If the user is on the final review screen, it retries submitting answers to the backend.
     * Otherwise, it retries loading tags from the backend.
     */
    fun retry() {
        if (_state.value.currentStepIndex == ProfileQuestionType.orderedQuestionTypes.size + 1){
            retrySubmittingAnswers()
        } else
            retryLoadingTags()
    }

    /**
     * Retries submitting profile answers to the backend after a previous failure.
     * Clears any existing error message and initiates the submission process again.
     */
    private fun retrySubmittingAnswers() {
        clearError()
        beginProfileSubmission()
    }

    /**
     * Saves a profile builder persistence step.
     *
     * @param step The step to save
     * @param value The value to save for the step
     */
    private fun saveProfileBuilderStep(step: SignUpPersistStepsEnum, value: String) {
        viewModelScope.launch {
            val model = SignUpPersistModel(step, value)
            signUpPersistManager.saveStep(model)
        }
    }

    /**
     * Saves that the user has reached the "Be Yourself" screen.
     * This is called when the user first views the screen.
     */
    fun saveBeYourselfStep() {
        saveProfileBuilderStep(SignUpPersistStepsEnum.PROFILE_BUILDER_BE_YOURSELF, "true")
    }

    /**
     * Clears all persistence steps (signup and profile builder).
     * This is called when the profile builder is completed to ensure
     * no persistence data remains for future app launches since the entire
     * onboarding flow is finished.
     */
    private fun clearAllPersistenceSteps() {
        viewModelScope.launch {
            // Use the new use case to clear ALL persistence steps (signup + profile builder)
            // since the entire onboarding flow is completed
            clearAllPersistStepsIncludingProfileBuilderUseCase()

            Timber.tag("ProfileBuilderPersist").d("Cleared all persistence steps using ClearAllPersistStepsIncludingProfileBuilderUseCase")
        }
    }

    /**
     * Sends CleverTap event when user initiates language selection.
     */
    private fun sendLanguageInitiatedEvent() {
        val authMethodString = duaSharedPrefs.getAuthMethode()
        val signUpOrSignInMediumValue = when (authMethodString) {
            AuthMethod.EMAIL.methodName -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
            AuthMethod.PHONE.methodName -> ClevertapSignUpOrSignInMediumValues.PHONE.value
            AuthMethod.GOOGLE.methodName -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
            AuthMethod.FACEBOOK.methodName -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
            else -> ClevertapSignUpOrSignInMediumValues.NULL.value
        }

        val properties = mutableMapOf<String, Any?>(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue
        )

        sendClevertapEvent(ClevertapEventEnum.ADD_LANGUAGES_INITIATED, properties)
        Timber.tag(VIEWMODEL_TAG).d("CleverTap event sent: ${ClevertapEventEnum.ADD_LANGUAGES_INITIATED.eventName}")
    }

    /**
     * Sends CleverTap event when user successfully adds languages and continues.
     */
    private fun sendLanguagesAddedSuccessfullyEvent() {
        val authMethodString = duaSharedPrefs.getAuthMethode()
        val signUpOrSignInMediumValue = when (authMethodString) {
            AuthMethod.EMAIL.methodName -> ClevertapSignUpOrSignInMediumValues.EMAIL.value
            AuthMethod.PHONE.methodName -> ClevertapSignUpOrSignInMediumValues.PHONE.value
            AuthMethod.GOOGLE.methodName -> ClevertapSignUpOrSignInMediumValues.GOOGLE.value
            AuthMethod.FACEBOOK.methodName -> ClevertapSignUpOrSignInMediumValues.FACEBOOK.value
            else -> ClevertapSignUpOrSignInMediumValues.NULL.value
        }

        val properties = mutableMapOf<String, Any?>(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue
        )

        sendClevertapEvent(ClevertapEventEnum.LANGUAGES_ADDED_SUCCESSFULLY, properties)
        Timber.tag(VIEWMODEL_TAG).d("CleverTap event sent: ${ClevertapEventEnum.LANGUAGES_ADDED_SUCCESSFULLY.eventName}")
    }
}

