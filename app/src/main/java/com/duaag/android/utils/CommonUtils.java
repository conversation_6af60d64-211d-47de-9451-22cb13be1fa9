package com.duaag.android.utils;

import android.annotation.TargetApi;
import android.app.DatePickerDialog;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.text.Editable;
import android.text.format.DateFormat;
import android.util.Patterns;
import android.view.Window;
import android.widget.DatePicker;
import android.widget.EditText;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.duaag.android.BuildConfig;
import com.duaag.android.R;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-05-02.
 */
public class CommonUtils {
    private static final String TAG = "CommonUtils";

    public static final String BASE_URL = "https://linkplus-it.dua.com/dua";

    public static String getTextFromEditText(@Nullable EditText editText) {
        if (editText == null) {
            return "";
        }
        Editable editable = editText.getText();
        if (editable == null) {
            return "";
        }
        return editable.toString();
    }

    public static boolean isEmailValid(String email) {
        String emailPattern = Patterns.EMAIL_ADDRESS.pattern();
        return email.matches(emailPattern);
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return DateFormat.format("dd   |   MM   |   yyyy", date).toString();
    }

    public static String formatToServerDate(Date date) {
        if (date == null) {
            return "";
        }
        return DateFormat.format("yyyy-MM-dd", date).toString();
    }

    public static String formatToIsoDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat output = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        output.setTimeZone(TimeZone.getTimeZone("GMT"));
        return output.format(date);
    }

    public static Date formatToDate(String serverDate) {
        if (serverDate == null || serverDate.isEmpty()) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        try {
            return simpleDateFormat.parse(serverDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String formatIsoDate(String isoDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        SimpleDateFormat output = new SimpleDateFormat("dd-MM-yyyy - HH:mm", Locale.getDefault());
        try {
            Date date = sdf.parse(isoDate);
            return output.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String formatIsoDateToDdMmYyyy(String isoDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        SimpleDateFormat output = new SimpleDateFormat("dd-MM-yyyy", Locale.getDefault());
        try {
            Date date = sdf.parse(isoDate);
            return output.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String formatIsoDateToHhMm(String isoDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        SimpleDateFormat output = new SimpleDateFormat("HH:mm", Locale.getDefault());
        try {
            Date date = sdf.parse(isoDate);
            return output.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    @Nullable
    public static Date formatIsoDateToDate(String isoDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            return sdf.parse(isoDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date formatToDateYyyyMmDd(String serverDate) {
        if (serverDate == null || serverDate.isEmpty()) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        try {
            return simpleDateFormat.parse(serverDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date formatYyMmDdToDate(String serverDate) {
        if (serverDate == null || serverDate.isEmpty()) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        try {
            return simpleDateFormat.parse(serverDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String formatServerDateForChat(String serverDate) {
        if (serverDate == null || serverDate.isEmpty()) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        SimpleDateFormat output = new SimpleDateFormat("dd-MM-yyyy - HH:mm", Locale.getDefault());
        try {
            Date date = simpleDateFormat.parse(serverDate);
            if (date == null) {
                return "";
            }
            return output.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void cancelTask(Call<?> task) {
        if (task != null && task.isExecuted()) {
            task.cancel();
        }
    }

    public static boolean isPasswordValid(String password) {
        return password.length() > 5;
    }

    public static String getAge(String serverDate) {
        Date date = formatToDate(serverDate);
        if (date == null) {
            return "";
        }
        Calendar dob = Calendar.getInstance();
        Calendar today = Calendar.getInstance();

        dob.setTime(date);

        int age = today.get(Calendar.YEAR) - dob.get(Calendar.YEAR);

        if (today.get(Calendar.DAY_OF_YEAR) < dob.get(Calendar.DAY_OF_YEAR)) {
            age--;
        }

        int ageInt = age;

        return Integer.toString(ageInt);
    }

    public static MultipartBody.Part prepareFilePart(Uri fileUri, Context context) {
        File file;
        try {
            String path = FileUtils.getPath(context, fileUri);
            file = new File(path);
        } catch (Exception e) {
            e.printStackTrace();
            file = new File(fileUri.getPath());
        }

        // create RequestBody instance from file
        RequestBody requestFile =
                RequestBody.create(file, MediaType.parse("image/*"));

        // MultipartBody.Part is used to send also the actual file name
        return MultipartBody.Part.createFormData("pic", file.getName(), requestFile);
    }

    public static void showDatePicker(Context context, Calendar calendar, @Nullable Date birthDate, DatePickerDialog.OnDateSetListener listener) {
        calendar.add(Calendar.YEAR, -17);
        DatePickerDialog datePickerDialog;
        if (birthDate != null) {
            Calendar birthDateCalendar = Calendar.getInstance();
            birthDateCalendar.setTime(birthDate);

            int yy = birthDateCalendar.get(Calendar.YEAR);
            int mm = birthDateCalendar.get(Calendar.MONTH);
            int dd = birthDateCalendar.get(Calendar.DAY_OF_MONTH);
            datePickerDialog = new DatePickerDialog(context, R.style.PickerDialogCustom, listener, yy, mm, dd);
        } else {
            int yy = calendar.get(Calendar.YEAR);
            int mm = calendar.get(Calendar.MONTH);
            int dd = calendar.get(Calendar.DAY_OF_MONTH);
            datePickerDialog = new DatePickerDialog(context, R.style.PickerDialogCustom, listener, yy, mm, dd);
        }
        DatePicker datePicker = datePickerDialog.getDatePicker();
        datePicker.setMaxDate(calendar.getTimeInMillis());
        datePickerDialog.getDatePicker().setLayoutMode(1);
        datePickerDialog.show();
    }

    public static void setTranslucentStatusBar(Window window) {
        if (window == null) return;
        setTranslucentStatusBarLollipop(window);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private static void setTranslucentStatusBarLollipop(Window window) {
        window.setStatusBarColor(ContextCompat.getColor(window.getContext(), android.R.color.transparent));
    }

    public static String getAppVersion() {
        return BuildConfig.BUILD_TYPE + " v" + BuildConfig.VERSION_NAME + " (" + BuildConfig.VERSION_CODE + ")";
    }
}
