package com.duaag.android.utils

import android.app.AlertDialog
import android.content.Context
import androidx.core.content.ContextCompat
import com.duaag.android.R
import com.google.android.material.dialog.MaterialAlertDialogBuilder

fun showRemoveInstachatDialog(
    context: Context,
    onPositiveButtonClick: () -> Unit
) {
    MaterialAlertDialogBuilder(context)
        .setTitle(context.getString(R.string.insta_remove_title_alert))
        .setMessage(context.getString(R.string.insta_remove_desc_alert))
        .setNegativeButton(context.getString(R.string.cancel)) { _, _ -> }
        .setPositiveButton(context.getString(R.string.remove_title)) { _, _ ->
            onPositiveButtonClick()
        }
        .show()
        .getButton(AlertDialog.BUTTON_NEGATIVE)
        .setTextColor(ContextCompat.getColor(context, R.color.description_primary))
}

fun showRemoveRmodDialog(
    context: Context,
    onPositiveButtonClick: () -> Unit
) {
    MaterialAlertDialogBuilder(context)
        .setTitle(context.getString(R.string.insta_remove_title_alert))
        .setMessage(context.getString(R.string.insta_remove_desc_alert))
        .setNegativeButton(context.getString(R.string.cancel)) { _, _ -> }
        .setPositiveButton(context.getString(R.string.remove_title)) { _, _ ->
            onPositiveButtonClick()
        }
        .show()
        .getButton(AlertDialog.BUTTON_NEGATIVE)
        .setTextColor(ContextCompat.getColor(context, R.color.description_primary))
}