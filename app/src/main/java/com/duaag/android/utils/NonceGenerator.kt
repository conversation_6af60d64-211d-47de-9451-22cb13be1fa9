package com.duaag.android.utils

import java.security.SecureRandom
import android.util.Base64

object NonceGenerator {
    private val secureRandom = SecureRandom()

    fun generateNonce(bytesSize: Int = 16): String {
        val nonceBytes = ByteArray(bytesSize)
        secureRandom.nextBytes(nonceBytes)

        return Base64.encodeToString(nonceBytes, Base64.URL_SAFE or Base64.NO_PADDING or Base64.NO_WRAP)
    }
}
