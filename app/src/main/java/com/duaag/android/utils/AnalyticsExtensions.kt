
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.uxcam.sendUxCamEvent

fun sendVerifyYourProfilePopupAnalyticsEvent(
    premiumType: String?,
    verificationSource: String,
    isItForcedVerification: Boolean = false,
) {
    firebaseLogEvent(
        FirebaseAnalyticsEventsName.VERIFY_YOUR_PROFILE_POPUP, mapOf(
            ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to verificationSource,
            ClevertapEventPropertyEnum.IS_IT_FORCED_VERIFICATION.propertyName to isItForcedVerification,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
        )
    )

    sendUxCamEvent(
        ClevertapEventEnum.VERIFY_YOUR_PROFILE_POPUP,
        mapOf(
            ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to verificationSource,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.IS_IT_FORCED_VERIFICATION.propertyName to isItForcedVerification
        )
    )

    sendClevertapEvent(
        ClevertapEventEnum.VERIFY_YOUR_PROFILE_POPUP,
        mapOf(
            ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to verificationSource,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.IS_IT_FORCED_VERIFICATION.propertyName to isItForcedVerification
        )
    )
}

fun sendVerifyYourProfileInitiatedAnalyticsEvent(
    verificationSource: String,
    premiumType: String?,
    signUpSignInMedium: String? = null
) {
    firebaseLogEvent(FirebaseAnalyticsEventsName.VERIFY_YOUR_PROFILE_IMAGE_INITIATED, mapOf(
            ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to verificationSource,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
         )
    )

    val properties = hashMapOf(
        ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName to verificationSource,
        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
    )

    if(signUpSignInMedium != null)
        properties[ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName] = signUpSignInMedium

    sendClevertapEvent(
        ClevertapEventEnum.VERIFY_YOUR_PROFILE_IMAGE_INITIATED,
        properties
    )
    sendUxCamEvent(
        ClevertapEventEnum.VERIFY_YOUR_PROFILE_IMAGE_INITIATED,
        properties
    )
}

