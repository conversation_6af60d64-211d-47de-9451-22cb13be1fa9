package com.duaag.android.utils

import timber.log.Timber
import java.util.*

object AgeUtils {
    fun getAge(cl: String?): String {
        cl?.let {
            val calendar = getDateFromStringCalendar(cl)
            val dob = calendar
            val today = Calendar.getInstance()
            val birthdayYear = dob.get(Calendar.YEAR)
            val currentYear = today.get(Calendar.YEAR)

            var age = currentYear - birthdayYear

            Timber.tag("tod22").e("today day: ${today.get(Calendar.DAY_OF_YEAR)},  birthday day: ${dob.get(Calendar.DAY_OF_YEAR)}")

            //check if the year is leap year
            if ((birthdayYear % 400 == 0) || ((birthdayYear % 4 == 0) && (birthdayYear % 100 != 0))) {

                if ((currentYear % 400 == 0) || ((currentYear % 4 == 0) && (currentYear % 100 != 0))) {
                    if (today.get(Calendar.DAY_OF_YEAR) < dob.get(Calendar.DAY_OF_YEAR)) {
                        age--
                    }
                } else {
                    if (today.get(Calendar.DAY_OF_YEAR) < (dob.get(Calendar.DAY_OF_YEAR) - 1)) {
                        age--
                    }
                }
            } else {

                if ((currentYear % 400 == 0) || ((currentYear % 4 == 0) && (currentYear % 100 != 0))) {
                    if ((today.get(Calendar.DAY_OF_YEAR) - 1) < dob.get(Calendar.DAY_OF_YEAR)) {
                        age--
                    }
                } else {
                    if (today.get(Calendar.DAY_OF_YEAR) < (dob.get(Calendar.DAY_OF_YEAR) - 1)) {
                        age--
                    }
                }
            }

            val ageInt = age

            return ageInt.toString()
        }
        return ""
    }

    fun getDateFromStringCalendar(dateNow: String): Calendar {

        val dm = dateNow.substringBeforeLast("-")
        val day = dm.substringBeforeLast("-")
        val month = dm.substringAfterLast("-")
        val year = dateNow.substringAfterLast("-")
        val cal = Calendar.getInstance()
        cal.set(Calendar.YEAR, year.toInt())
        cal.set(Calendar.MONTH, month.toInt() - 1)
        cal.set(Calendar.DAY_OF_MONTH, day.toInt())

        return cal
    }

    //is age within 18 - 64
    fun isAgeAllowed(calendar: Calendar): Boolean {
        val age = getAge(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH),calendar.get(Calendar.DAY_OF_MONTH))
        return (age.toInt() in 18..100)
    }

    private fun getAge(year: Int, month: Int, day: Int): String {
        val dob = Calendar.getInstance()
        val today = Calendar.getInstance()
        dob[year, month] = day
        var age = today[Calendar.YEAR] - dob[Calendar.YEAR]
        if (today[Calendar.DAY_OF_YEAR] < dob[Calendar.DAY_OF_YEAR]) {
            age--
        }
        val ageInt = age
        return ageInt.toString()
    }
}