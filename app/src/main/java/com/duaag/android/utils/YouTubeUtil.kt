package com.duaag.android.utils

import java.util.regex.Pattern

object YouTubeUtil {

    @Throws
    fun getThumbnailUrl(youtubeUrl: String): String? {
        return try {
            val videoId = extractVideoId(youtubeUrl)
            videoId?.let { "https://img.youtube.com/vi/$it/maxresdefault.jpg" }
        } catch (e: IllegalArgumentException) {
            throw  e
        }
    }

    @Throws
    fun extractVideoId(youtubeUrl: String?): String? {
        // YouTube video URL patterns
        val patternLong = """(?:https?://)?(?:www\.)?youtube\.com/(?:watch\?v=|embed/|v/|e/|youtu\.be/)([^#&?]*)"""
        val patternShort = """(?:https?://)?(?:www\.)?youtu\.be/([^#&?]*)"""

        // Check if the URL is not null and matches the YouTube patterns
        if (youtubeUrl != null) {
            val matcherLong = Pattern.compile(patternLong).matcher(youtubeUrl)
            val matcherShort = Pattern.compile(patternShort).matcher(youtubeUrl)

            return when {
                matcherLong.find() -> {
                    val videoId = matcherLong.group(1)

                    if (videoId.matches(Regex("[a-zA-Z0-9_-]{11}"))) {
                        videoId
                    } else {
                        throw IllegalArgumentException("Invalid video ID format")
                    }
                }
                matcherShort.find() -> matcherShort.group(1)
                else -> {
                    throw IllegalArgumentException("Invalid YouTube URL")
                }
            }
        } else {
            return null
        }
    }}

