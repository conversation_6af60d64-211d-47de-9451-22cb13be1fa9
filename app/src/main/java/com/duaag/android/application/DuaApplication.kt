package com.duaag.android.application

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_NO
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_YES
import androidx.appcompat.app.AppCompatDelegate.setDefaultNightMode
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinSdk
import com.applovin.sdk.AppLovinSdkSettings
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerInAppPurchaseValidatorListener
import com.appsflyer.AppsFlyerLib
import com.appsflyer.api.PurchaseClient
import com.appsflyer.api.Store
import com.appsflyer.attribution.AppsFlyerRequestListener
import com.appsflyer.deeplink.DeepLink
import com.appsflyer.deeplink.DeepLinkResult
import com.appsflyer.internal.models.InAppPurchaseValidationResult
import com.appsflyer.internal.models.SubscriptionValidationResult
import com.clevertap.android.sdk.ActivityLifecycleCallback
import com.clevertap.android.sdk.CleverTapAPI
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.domain.AppsFlyerBackendManager
import com.duaag.android.appsflyer.models.DeepLinksTypeEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.base.models.UserModel
import com.duaag.android.chat.ChatRepository
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.clevertap.sendAppClosedEvent
import com.duaag.android.clevertap.sendAppOpenEvent
import com.duaag.android.clevertap.setCountersLeftUserProperties
import com.duaag.android.counters.domain.GetLocalCountersUseCase
import com.duaag.android.di.AppComponent
import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.DaggerAppComponent
import com.duaag.android.di.IoDispatcher
import com.duaag.android.events_tracker.models.FaceBookDeepLinkBodyModel
import com.duaag.android.fingerprint_pro.domain.usecase.VisitorIdUseCase
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity.Companion.HAS_PREMIUM_OFFER_INTENT
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PREMIUM_OFFER_ID
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.premium_subscription.models.PremiumOfferId
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.settings.fragments.language.LanguageConstants.LANGUAGE_ENGLISH
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.settings.fragments.language.locale.PreferenceLocaleStore
import com.duaag.android.settings.models.AppThemeEnum
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.signup_persist.domain.use_cases.GetLastSignUpPersistStepUseCase
import com.duaag.android.signup.signup_persist.domain.use_cases.GetProfileBuilderLastStepUseCase
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.getNotificationsOnClevertapValue
import com.duaag.android.utils.getStringResourceByName
import com.facebook.ads.AdSettings
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessaging
import com.uxcam.UXCam
import com.uxcam.datamodel.UXConfig
import com.uxcam.screenshot.model.UXCamOccludeAllTextFields
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject


class DuaApplication : Application(), LifecycleObserver {

    companion object {
        lateinit var instance: DuaApplication
            private set
        const val TAG = "REMOTE_CONFIG"
        const val ON_APP_FOREGROUNDED = "ON_APP_FOREGROUNDED"
        const val IS_ON_APP_FOREGROUNDED = "IS_ON_APP_FOREGROUNDED"
        const val BILLING_AVAILABLE = "BILLING_AVAILABLE"

        var screenWidth = 0
        var screenHeight = 0
        var screenHeightDp = 0f
    }

    // Instance of the AppComponent that will be used by all the Activities in the project
    val appComponent: AppComponent by lazy {
        initializeComponent()
    }

    @Inject
    lateinit var notificationRepository: NotificationRepository

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var chatRepository: ChatRepository

    @Inject
    lateinit var userRepository: UserRepository

    @IoDispatcher
    @Inject
    lateinit var ioDispatcher: CoroutineDispatcher

    @ApplicationScope
    @Inject
    lateinit var externalScope: CoroutineScope

    @Inject
    lateinit var appsFlyerBackendManager: AppsFlyerBackendManager

    @Inject
    lateinit var getLocalCountersUseCase: GetLocalCountersUseCase

    @Inject
    lateinit var visitorIdUseCase: VisitorIdUseCase

    @Inject
    lateinit var getLastSignUpPersistStepUseCase: GetLastSignUpPersistStepUseCase

    @Inject
    lateinit var getProfileBuilderLastStepUseCase: GetProfileBuilderLastStepUseCase

    private var broadcaster: LocalBroadcastManager? = null

    private var remoteBillingEnabled: Boolean = false
    private var playBillingAvailable: Boolean = true

    var xUserAgent = ""

    var isFromBackground: Boolean = false

    val shouldShowPremium: Boolean by lazy { RemoteConfigUtils.isBuyPremiumDisabled().not() }

    var shouldShowPhoneAndVideo: Boolean = false

    var isLoggedInUserDisabled = false

    var isCredentialUser: Boolean? = null

    var userEmailOrPhone :String? = null
    var userEmailOrPhoneValue :String? = null


    private var isApplovinInitialized = false
    private var isApplovinStartedToInitialize = false

    private val appsflayerListener = object : AppsFlyerRequestListener {
        override fun onSuccess() {
            Timber.tag(TAG).d("Launch sent successfully, got 200 response code from server")
        }

        override fun onError(p0: Int, p1: String) {
            Timber.tag(TAG).d(" Launch failed to be sent: Error code: $p0 Error description: $p1")
        }
    }

    private val uxCamConfig: UXConfig by lazy {
        val textFields = UXCamOccludeAllTextFields.Builder()
            .build()
        UXConfig.Builder(BuildConfig.UXCAM_API_KEY)
            .occlusions(listOf(textFields))
            .build()
    }
    override fun onCreate() {
        RemoteConfigUtils.init(applicationContext)

        appComponent.inject(this)

        setAppTheme()

        ActivityLifecycleCallback.register(this)

        super.onCreate()

        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        broadcaster = LocalBroadcastManager.getInstance(this)

        instance = this

        xUserAgent = getXUserAgentInfo()

        //store to cache localization
        val store = PreferenceLocaleStore(this, Locale(LANGUAGE_ENGLISH))
        // you can use this instance for DI or get it via ModifiedLingver.getInstance() later on
        ModifiedLingver.init(this, store)

        externalScope.launch {
         val visitorIdResult = visitorIdUseCase.getVisitorId()
            withContext(Dispatchers.Main){
                Timber.tag(TAG).d("VisitorIdResult:$visitorIdResult")
            }
        }
        AWSInteractor.initializeSilently(applicationContext)

        //region appsflyer
        val appsflyer = AppsFlyerLib.getInstance()
        appsflyer.setOneLinkCustomDomain("dua.com")
        appsflyer.setDebugLog(!DuaAccount.releaseModel)
        appsflyer.setMinTimeBetweenSessions(0)
        subscribeForOpenLink(appsflyer)
        appsflyer.init(getString(R.string.appsflyer_key), appsFlyerConversionListener, this)
        linkCleverTapAppsFlyer()
        appsFlyerBackendManager.sendData(true)

        appsflyer.start(this, getString(R.string.appsflyer_key), appsflayerListener)
        appsflyer.registerValidatorListener(this, object : AppsFlyerInAppPurchaseValidatorListener {
            override fun onValidateInApp() {
                Timber.tag(TAG).d("Purchase validated successfully")
            }

            override fun onValidateInAppFailure(error: String) {
                Timber.tag(TAG).d("onValidateInAppFailure called: $error")
            }
        })

        val builder = PurchaseClient.Builder(applicationContext, Store.GOOGLE)
        builder.logSubscriptions(true)
        builder.autoLogInApps(true)
        //make this false when making release builds
        val roi360Sandbox = BuildConfig.BUILD_TYPE != "release" && BuildConfig.FLAVOR_environment != "prod"
        builder.setSandbox(roi360Sandbox)
        builder.setSubscriptionValidationResultListener(object :
            PurchaseClient.SubscriptionPurchaseValidationResultListener {
            override fun onResponse(result: Map<String, SubscriptionValidationResult>?) {
                result?.forEach { (k: String, v: SubscriptionValidationResult?) ->
                    if (v?.success == true) {
                        Timber.tag(TAG).d("[PurchaseConnector]: Subscription with ID $k was validated successfully")
                        val subscriptionPurchase = v?.subscriptionPurchase
                        Timber.tag(TAG).d(subscriptionPurchase?.toString())
                    } else {
                        Timber.tag(TAG).d("[PurchaseConnector]: Subscription with ID $k wasn't validated successfully")
                        val failureData = v?.failureData
                        Timber.tag(TAG).d(failureData?.toString())
                    }
                }
            }

            override fun onFailure(result: String, error: Throwable?) {
                Timber.tag(TAG).d("[PurchaseConnector]: Validation fail: $result")
                error?.printStackTrace()
            }
        })
        builder.setInAppValidationResultListener(object :
            PurchaseClient.InAppPurchaseValidationResultListener {
            override fun onResponse(result: Map<String, InAppPurchaseValidationResult>?) {
                result?.forEach { (k: String, v: InAppPurchaseValidationResult?) ->
                    if (v?.success == true) {
                        Timber.tag(TAG).d("[PurchaseConnector]:  Product with Purchase Token$k was validated successfully")
                        val productPurchase = v?.productPurchase
                        Timber.tag(TAG).d(productPurchase?.toString())
                    } else {
                        Timber.tag(TAG).d("[PurchaseConnector]:  Product with Purchase Token $k wasn't validated successfully")
                        val failureData = v?.failureData
                        Timber.tag(TAG).d(failureData?.toString())
                    }
                }
            }

            override fun onFailure(result: String, error: Throwable?) {
                Timber.tag(TAG).d("[PurchaseConnector]: Validation fail: $result")
                error?.printStackTrace()
            }
        })
        val purchaseClient: PurchaseClient = builder.build()
        purchaseClient.startObservingTransactions()

        //endregion

        //UXCam
        UXCam.startWithConfiguration(uxCamConfig)

        if (!duaSharedPrefs.isRegisteredRemoteConfigTopic()) {
            subscribeToFirebaseTopic()
        }

        remoteBillingEnabled = RemoteConfigUtils.getBillingEnabled()
        shouldShowPhoneAndVideo = RemoteConfigUtils.areVideoCallsEnabled()

        isAppOpenForTheFirstTime()

        if (BuildConfig.DEBUG) {
            Timber.plant(object : Timber.DebugTree() {
                override fun createStackElementTag(element: StackTraceElement): String? {
                    return String.format("Class: %s, Line: %s, Method: %s",
                        super.createStackElementTag(element),
                        element.lineNumber,
                        element.methodName)
                }
            })
        }

        PurchaselyManager.setupPurchasely(duaSharedPrefs.getUserCognitoId())

        sendQualityUserAppOpenEvent()
        setUninstallTracking()
    }

    private fun initializeComponent(): AppComponent {
        // Creates an instance of AppComponent using its Factory constructor
        // We pass the applicationContext that will be used as Context in the graph
        return DaggerAppComponent.factory().create(applicationContext)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    fun onAppBackgrounded() {

        //App in background
        isFromBackground = true
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.APP_CLOSED, mapOf(
                FirebaseAnalyticsParameterName.APP_CLOSE_COUNT.value to 1L))

        //Send AppClosed event in Clevertap
        sendAppStateClevertapEvent(ClevertapEventEnum.APP_CLOSED)

        externalScope.launch(Dispatchers.IO) {
            val user = userRepository.getLoggedInUserModel()
            user?.let { sendUserCountersToClevertap(it) }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    fun onAppForegrounded() {

        if (isFromBackground) {
            RemoteConfigUtils.init(applicationContext)
        }

        val broadcastIntent = Intent(ON_APP_FOREGROUNDED)
        broadcastIntent.putExtra(IS_ON_APP_FOREGROUNDED, true)
        broadcaster?.sendBroadcast(broadcastIntent)

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.APP_OPEN, mapOf(
                FirebaseAnalyticsParameterName.APP_OPEN_COUNT.value to 1L))

        //Send AppOpen event in Clevertap
        sendAppStateClevertapEvent(ClevertapEventEnum.APP_OPEN)

        externalScope.launch(Dispatchers.IO) {
            val user = userRepository.getLoggedInUserModel()
            user?.let {
                sendUserCountersToClevertap(it)
                sendAppsflyerEvent(AppsflyerEventsNameEnum.APP_OPEN)
            }
        }
    }

    fun sendTokenToServerAndSave() {
        externalScope.launch(Dispatchers.Main) {
            if (duaSharedPrefs.getNotificationToken().isNotEmpty()) {
                sendTokenToServer(duaSharedPrefs.getNotificationToken())
            } else {
                FirebaseMessaging.getInstance().token
                    .addOnSuccessListener { result ->
                        Timber.tag(TAG).d("sendTokenToServerAndSave: addOnSuccessListener $result")
                        if (!TextUtils.isEmpty(result)) {
                            val newToken: String = result
                            Timber.tag(TAG).d("sendTokenToServerAndSave: $newToken")
                            sendTokenToServer(newToken)
                            duaSharedPrefs.setNotificationToken(newToken)
                        }
                    }
            }
        }
    }

    private fun sendTokenToServer(newToken: String) {
        externalScope.launch(ioDispatcher) {
            notificationRepository.addUserDevice(newToken)
        }

        //send here firebase token to clevertap to keep it in sync
        CleverTapAPI.getDefaultInstance(applicationContext)?.pushFcmRegistrationId(newToken, true)
    }

    fun getXUserAgentInfo() =
        "android;${duaSharedPrefs.systemVersion};${duaSharedPrefs.getAppVersion(applicationContext)};${duaSharedPrefs.deviceName}"

    fun getBillingAvailable(): Boolean = remoteBillingEnabled && playBillingAvailable
    fun getPremiumAvailable(): Boolean = shouldShowPremium && playBillingAvailable
    fun getPlayBillingAvailable(): Boolean = playBillingAvailable


    // This function is to Subscription on firebase for Remote config
    private fun subscribeToFirebaseTopic() {

        val topicBase = getString(R.string.android_remote_config)
        val topicState = when (BuildConfig.FLAVOR_environment) {
            "qa" -> "qa-$topicBase"
            "dev" -> "dev-$topicBase"
            else -> "prod-$topicBase"
        }
        FirebaseMessaging.getInstance().subscribeToTopic(topicState)
            .addOnCompleteListener { task ->
                var msg = "failed"
                if (task.isSuccessful) {
                    msg = "Success"
                    duaSharedPrefs.setRegisteredRemoteConfigTopic(true)
                }
                Timber.tag(TAG).d(msg)
            }
    }

    fun setPlayBillingAvailability(enabled: Boolean) {
        Timber.tag(TAG).d("playBilling: $enabled")
        playBillingAvailable = enabled
    }

    fun setRemoteBillingAvailability(enabled: Boolean) {
        Timber.tag(TAG).d("remoteBilling: $enabled")
        remoteBillingEnabled = enabled
    }

    private fun isAppOpenForTheFirstTime() {
        if (duaSharedPrefs.isFirstTimeAppOpen() && duaSharedPrefs.getSavedVersionCode() == -1) {
            firebaseLogEvent(FirebaseAnalyticsEventsName.APP_INSTALLS)
            duaSharedPrefs.setIsFirstTimeAppOpen(false)
        }
    }

    fun isLoggedInUserPremium(): Boolean = duaSharedPrefs.isLoggedInUserPremium()
    fun isLoggedInUserFreeStarterExperience(): Boolean = duaSharedPrefs.isLoggedInUserFreeStarterExperience()
    fun getUserCommunityId(): String? = duaSharedPrefs.getUserCommunityId()
    fun getUserCommunityName(): String? = duaSharedPrefs.getUserCommunityName()

    fun getIsApplovinInitialized(): Boolean = isApplovinInitialized

    private fun setAppTheme() {
        when (duaSharedPrefs.getAppTheme()) {
            AppThemeEnum.LIGHT_THEME.theme -> {
                setDefaultNightMode(MODE_NIGHT_NO)
            }
            AppThemeEnum.DARK_THEME.theme -> {
                setDefaultNightMode(MODE_NIGHT_YES)
            }
            AppThemeEnum.FOLLOW_SYSTEM.theme -> {
                setDefaultNightMode(MODE_NIGHT_FOLLOW_SYSTEM)
            }
            else -> {
                val DOESNT_EXIST = -1
                val currentVersionCode = BuildConfig.VERSION_CODE

                val savedVersionCode = duaSharedPrefs.getSavedVersionCode()
                if (currentVersionCode > savedVersionCode && savedVersionCode != DOESNT_EXIST) {
                    setDefaultNightMode(MODE_NIGHT_NO)
                    duaSharedPrefs.setAppTheme(AppThemeEnum.LIGHT_THEME.theme)
                } else {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                        setDefaultNightMode(MODE_NIGHT_NO)
                        duaSharedPrefs.setAppTheme(AppThemeEnum.LIGHT_THEME.theme)
                    } else {
                        setDefaultNightMode(MODE_NIGHT_FOLLOW_SYSTEM)
                        duaSharedPrefs.setAppTheme(AppThemeEnum.FOLLOW_SYSTEM.theme)
                    }
                }
            }
        }
    }

    fun getSpecialOfferData(): SpecialOfferDataModel? {
        val remoteConfigData = RemoteConfigUtils.getSpecialOfferData()
        val sharedPresData = duaSharedPrefs.getPremiumSpecialOfferData()
        return remoteConfigData ?: sharedPresData
    }

    private fun sendAppStateClevertapEvent(event: ClevertapEventEnum) {
        externalScope.launch(Dispatchers.IO) {
            val user = DuaAccount.user
            if (user != null) {
                if (event == ClevertapEventEnum.APP_OPEN) {
                    val lastPersistStep = getLastSignUpPersistStepUseCase()
                    val profileBuilderLastPersistStep = getProfileBuilderLastStepUseCase()

                    sendAppOpenEvent(
                        user,
                        event,
                        <EMAIL>,
                        duaSharedPrefs,
                        profileBuilderLastPersistStep ?: lastPersistStep
                    )
                } else {
                    sendAppClosedEvent(user, event)
                }
            } else {
                val userModel = userRepository.getLoggedInUserModel()
                if (event == ClevertapEventEnum.APP_OPEN) {
                    val lastPersistStep = getLastSignUpPersistStepUseCase()
                    val profileBuilderLastPersistStep = getProfileBuilderLastStepUseCase()

                    sendAppOpenEvent(
                        userModel,
                        event,
                        <EMAIL>,
                        duaSharedPrefs,
                        profileBuilderLastPersistStep ?: lastPersistStep
                    )
                } else {
                    sendAppClosedEvent(userModel, event)
                }
            }
        }
    }

    fun initializeApplovinSdk(user: UserModel) {
        if (isLoggedInUserDisabled ||
            user.premiumType != null ||
            user.profile.isShadowBanned ||
            isApplovinInitialized ||
            isApplovinStartedToInitialize ||
            !RemoteConfigUtils.isAdsEnabled()
            ) {
            return
        }

        isApplovinStartedToInitialize = true
        val settings = AppLovinSdkSettings(this)
        settings.termsAndPrivacyPolicyFlowSettings.isEnabled = true
        settings.termsAndPrivacyPolicyFlowSettings.privacyPolicyUri = Uri.parse(getStringResourceByName(BuildConfig.PRIVACY_POLICY_LINK_KEY))
        settings.termsAndPrivacyPolicyFlowSettings.termsOfServiceUri = Uri.parse(getStringResourceByName(BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY))
        AdSettings.setDataProcessingOptions( arrayOf("LDU"), 0, 0 )
        val applovinSdk = AppLovinSdk.getInstance(settings ,this)
        applovinSdk.mediationProvider = AppLovinMediationProvider.MAX
        applovinSdk.initializeSdk {
            isApplovinInitialized = true
        }
    }

    private fun subscribeForOpenLink(appsFlyer: AppsFlyerLib) {
        appsFlyer.subscribeForDeepLink { deepLinkResult ->
            when (deepLinkResult.status) {
                DeepLinkResult.Status.FOUND -> Timber.tag(TAG).d("Deep link found")
                DeepLinkResult.Status.NOT_FOUND -> {
                    Timber.tag(TAG).d("Deep link not found")
                    return@subscribeForDeepLink
                }
                DeepLinkResult.Status.ERROR -> {
                    val dlError = deepLinkResult.error
                    Timber.tag(TAG)
                        .d("There was an error getting Deep Link data: ${dlError.toString()}")
                    return@subscribeForDeepLink
                }
            }

            val deepLinkObj = deepLinkResult.deepLink
            try {
                Timber.tag(TAG).d("The DeepLink data is: $deepLinkObj")
            } catch (e: Exception) {
                Timber.tag(TAG).d("DeepLink data came back null")
                return@subscribeForDeepLink
            }

            // An example for using is_deferred
            if (deepLinkObj.isDeferred!!) {
                Timber.tag(TAG).d("This is a deferred deep link")
            } else {
                Timber.tag(TAG).d("This is a direct deep link")
            }

            try {
                val dlData: JSONObject = deepLinkObj.clickEvent
                handleFaceBookFromDeepLink(deepLinkObj, dlData)
                val deeplinkType = "deep_link_value"
                if (dlData.has(deeplinkType)) {
                    val deepLinkType = deepLinkObj.getStringValue(deeplinkType)

                    when (deepLinkType) {
                        DeepLinksTypeEnum.INFLUENCER_LINK.value -> {
                            val influencerIdKey = "deep_link_sub1"
                            val communityKey = "deep_link_sub2"
                            if (dlData.has(influencerIdKey)) {
                                val influencerId = deepLinkObj.getStringValue(influencerIdKey)
                                influencerId?.let {
                                    duaSharedPrefs.setInfluencerName(it)
                                }
                            }

                            if (dlData.has(communityKey)) {
                                val community = deepLinkObj.getStringValue(communityKey)
                                community?.let {
                                    duaSharedPrefs.setPreSelectedCommunity(it)
                                }
                            }
                        }
                        DeepLinksTypeEnum.SCREEN.value -> {
                            val screenKey = "deep_link_sub1"
                            if (dlData.has(screenKey)) {
                                val screenValue = deepLinkObj.getStringValue(screenKey)
                                //duaSharedPrefs.setActionLink(screenValue)
                                val intent = Intent(SplashActivity.SCREENS_INTENT_FILTER)
                                intent.putExtra(SplashActivity.DEEP_LINK_SCREENS,screenValue)
                                sendBroadcast(intent)
                            }

                        }
                        DeepLinksTypeEnum.PREMIUM_OFFER.value -> {
                            val offerIdKey = "deep_link_sub1"

                            if (dlData.has(offerIdKey)) {
                                val offerId = deepLinkObj.getStringValue(offerIdKey)
                                offerId?.let { duaSharedPrefs.setPremiumOfferIdData(PremiumOfferId(it, true)) }

                                val intent = Intent(HAS_PREMIUM_OFFER_INTENT)
                                intent.putExtra(PREMIUM_OFFER_ID, offerId)
                                sendBroadcast(intent)
                            }

                        }
                        DeepLinksTypeEnum.PURCHASELY_PAYWALL.value -> {
                            val offerIdKey = "deep_link_sub1"

                            if (dlData.has(offerIdKey)) {
                                val placementId = deepLinkObj.getStringValue(offerIdKey)
                                placementId?.let { duaSharedPrefs.setPurchaselyPaywallId(placementId) }

                                val intent = Intent(SplashActivity.PURCHASELY_INTENT_FILTER)
                                intent.putExtra(SplashActivity.DEEP_LINK_PURCHASELY, placementId)
                                sendBroadcast(intent)
                            }

                        }
                        else -> return@subscribeForDeepLink
                    }
                }

            } catch (e: Exception) {
                Timber.tag(TAG).d("Custom param was not found in DeepLink data")
            }
        }
    }

    private fun handleFaceBookFromDeepLink(deepLinkObj:DeepLink?, dlData:JSONObject) {

        val fbcKey = "fbc"
        val fbpKey = "fbp"
        val userAgentKey = "client_user_agent"
        val clientIpKey = "client_ip"

        var fbcValue:String? = null
        var fbpValue:String? = null
        var userAgentValue:String? = null
        var clientIpValue:String? = null

        if (dlData.has(fbcKey)) {
            fbcValue = deepLinkObj?.getStringValue(fbcKey)
        }

        if (dlData.has(fbpKey)) {
            fbpValue = deepLinkObj?.getStringValue(fbpKey)
        }

        if (dlData.has(userAgentKey)) {
            userAgentValue = deepLinkObj?.getStringValue(userAgentKey)
        }

        if (dlData.has(clientIpKey)) {
            clientIpValue = deepLinkObj?.getStringValue(clientIpKey)
        }

        if (fbcValue.isNullOrEmpty().not() || fbpKey.isNullOrEmpty().not()) {
            duaSharedPrefs.setFaceBookDataFromDeepLink(FaceBookDeepLinkBodyModel(fbcValue, fbpValue, userAgentValue, clientIpValue))
        }
    }

    private val appsFlyerConversionListener: AppsFlyerConversionListener =
        object : AppsFlyerConversionListener {
            override fun onConversionDataSuccess(conversionDataMap: MutableMap<String, Any>?) {
                val status = conversionDataMap!!["af_status"]
                if (status == "Non-organic") {
                    if (conversionDataMap["is_first_launch"] == "true") {
                        Timber.tag(TAG).d("Conversion: First Launch")
                        //Deferred deep link in case of a legacy link
                        if (conversionDataMap.containsKey("test")) {
                            if (conversionDataMap.containsKey("deep_link_value")) { //Not legacy link
                                Timber.tag(TAG)
                                    .d("onConversionDataSuccess: Link contains deep_link_value, deep linking with UDL")
                            } else { //Legacy link
                                conversionDataMap["deep_link_value"] = conversionDataMap["testID"]!!
                                val test = conversionDataMap["test"] as String?
                            }
                        }
                    } else {
                        Timber.tag(TAG).d("Conversion: Not First Launch")

                    }
                } else {
                    Timber.tag(TAG).d("Conversion: This is an organic install.")

                }
            }

            override fun onConversionDataFail(errorMessage: String?) {
                Timber.tag(TAG).d("error getting conversion data:  $errorMessage")
            }

            override fun onAppOpenAttribution(p0: MutableMap<String, String>?) {
                Timber.tag(TAG).d("onAppOpenAttribution: This is fake call")
            }

            override fun onAttributionFailure(errorMessage: String?) {
                Timber.tag(TAG).d("error onAttributionFailure:  $errorMessage")
            }

        }

    private fun sendQualityUserAppOpenEvent() {
        externalScope.launch(Dispatchers.IO) {
            val user = userRepository.getLoggedInUserModel()

            if(user != null) {
                val isBadge2Verified = (user.badge2 == Badge2Status.APPROVED.status)
                val profilePercentage = user.profilePercentage
                val areNotificationsOn = getNotificationsOnClevertapValue(<EMAIL>, duaSharedPrefs)

                if(isBadge2Verified && profilePercentage >= 50 && areNotificationsOn)
                    sendAppsflyerEvent(AppsflyerEventsNameEnum.QUALITY_USER_APP_OPEN)
            }
        }
    }

    suspend fun sendUserCountersToClevertap(user: UserModel) {
        withContext(Dispatchers.IO) {
            val configurationNames = listOf(
                user.counterConfigurationNames.likeCounterCN,
                user.counterConfigurationNames.dislikeCounterCN,
                user.counterConfigurationNames.instachatCounterCN,
                user.counterConfigurationNames.boostCounterCN,
                user.counterConfigurationNames.undoCounterCN,
                user.counterConfigurationNames.unblurCounterCN,
                user.counterConfigurationNames.flyCounterCN,
                user.counterConfigurationNames.nameChangeCounterCN,
                user.counterConfigurationNames.communityCN,
                user.counterConfigurationNames.birthdayCounterCN,
                user.counterConfigurationNames.genderCounterCN,
                user.counterConfigurationNames.superlikeCounterCN,
                user.counterConfigurationNames.rewardVideosCounterCN,
                user.counterConfigurationNames.rewardedAdUnblurCounterCN,
            )

            getLocalCountersUseCase.invoke(configurationNames)
                .catch { e ->
                    Timber.e(e)
                }
                .collect { list ->
                    var likesCount: Int? = null
                    var dislikesCount: Int? = null
                    var instaChatCount: Int? = null
                    var boostCount: Int? = null
                    var likesLimit: Int? = null
                    var dislikesLimit: Int? = null
                    var instaChatLimit: Int? = null
                    var boostLimit: Int? = null

                    list?.forEach { counterEntity ->
                        when(counterEntity.configurationName){
                            user.counterConfigurationNames.likeCounterCN ->  {
                                likesCount = counterEntity.total
                                likesLimit = counterEntity.configuration.limit
                            }
                            user.counterConfigurationNames.dislikeCounterCN -> {
                                dislikesCount = counterEntity.total
                                dislikesLimit = counterEntity.configuration.limit
                            }
                            user.counterConfigurationNames.instachatCounterCN ->  {
                                instaChatCount = counterEntity.total
                                instaChatLimit = counterEntity.configuration.limit
                            }
                            user.counterConfigurationNames.boostCounterCN ->  {
                                boostCount = counterEntity.total
                                boostLimit = counterEntity.configuration.limit
                            }
                        }
                    }

                    if(likesCount != null &&
                        dislikesCount != null &&
                        instaChatCount != null &&
                        boostCount != null &&
                        likesLimit != null &&
                        dislikesLimit != null &&
                        instaChatLimit != null &&
                        boostLimit != null) {

                        setCountersLeftUserProperties(
                            likesLimit!! - likesCount!!,
                            dislikesLimit!! - dislikesCount!!,
                            instaChatLimit!! - instaChatCount!!,
                            boostLimit!! - boostCount!!
                        )
                    }
                }

        }
    }

    private fun setUninstallTracking() {
        val defaultInstance = CleverTapAPI.getDefaultInstance(this)
        defaultInstance?.let { ins ->
            Timber.i(TAG, "setting object id to firebase : ${ins.cleverTapID}")
            FirebaseAnalytics.getInstance(this).setUserProperty("ct_objectId", ins.cleverTapID)
        } ?: run {
            Timber.e(TAG, "Uninstall tracking not setup cause of non initialised instance")
        }
    }
}