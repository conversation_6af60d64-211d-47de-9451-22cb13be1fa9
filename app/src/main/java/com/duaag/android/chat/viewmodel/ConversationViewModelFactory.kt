package com.duaag.android.chat.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.recommender.domain.model.RmodModel
import dagger.assisted.AssistedFactory

fun provideFactory(
        assistedFactory: ConversationViewModelFactory,
        conversationModel: ConversationModel?,
        rmodModel: RmodModel?,
): ViewModelProvider.Factory =
        object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return assistedFactory.create(conversationModel, rmodModel) as T
            }
        }

@AssistedFactory
interface ConversationViewModelFactory {
    fun create(conversationModel: ConversationModel?, rmodModel: RmodModel?): ConversationViewModel
}