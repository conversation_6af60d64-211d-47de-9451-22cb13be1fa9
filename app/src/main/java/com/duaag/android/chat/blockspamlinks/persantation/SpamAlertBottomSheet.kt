package com.duaag.android.chat.blockspamlinks.persantation

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.duaag.android.databinding.FragmentSpamAlertBottomSheetBinding
import com.duaag.android.utils.updateLocale
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class SpamAlertBottomSheet: BottomSheetDialogFragment() {

    private var _binding: FragmentSpamAlertBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSpamAlertBottomSheetBinding.inflate(inflater, container, false)

        binding.continueBtn.setOnClickListener {
            dismiss()
        }

        return binding.root
    }

    companion object {
        const val TAG = "SpamAlertBottomSheet"
        fun newInstance(): SpamAlertBottomSheet {
            return SpamAlertBottomSheet()
        }
    }
}