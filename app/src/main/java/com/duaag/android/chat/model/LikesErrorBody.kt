package com.duaag.android.chat.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
@Keep
data class LikesErrorBody (
        @field:SerializedName("type")
        val type: String? = null,

        @field:SerializedName("message")
        val message: String? = null
)

enum class LikesErrorType(val value: String){
    USER_DELETED("user_account_missing"),USER_DISABLED("user_is_disabled")
}
