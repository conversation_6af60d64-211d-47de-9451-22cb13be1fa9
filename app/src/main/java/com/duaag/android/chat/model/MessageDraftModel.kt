package com.duaag.android.chat.model

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Keep
@Entity(tableName = "draft_messages")
data class MessageDraftModel(
        @PrimaryKey
        @SerializedName("conversationId")
        val conversationId: String,
        @SerializedName("content")
        val content: String
)