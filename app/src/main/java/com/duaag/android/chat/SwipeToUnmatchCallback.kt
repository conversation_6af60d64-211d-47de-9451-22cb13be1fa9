package com.duaag.android.chat

import android.graphics.*
import android.graphics.Paint.ANTI_ALIAS_FLAG
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.chat.adapters.MessagesAdapter
import com.duaag.android.manage_pictures.adapters.ProfileImagesAdapter
import com.facebook.FacebookSdk.getApplicationContext


class SwipeToUnmatchCallback private constructor(dragDirs: Int, swipeDirs: Int) : ItemTouchHelper.SimpleCallback(dragDirs, swipeDirs) {
    private lateinit var drawableLeft: Drawable
    private lateinit var drawableRight: Drawable
    private lateinit var paintLeft: Paint
    private lateinit var paintRight: Paint
    private lateinit var onItemSwipeLeftListener: OnItemSwipeListener
    private lateinit var onItemSwipeRightListener: OnItemSwipeListener
    private var swipeEnabled = false

    private constructor(builder: Builder) : this(builder.dragDirs, builder.swipeDirs) {
        setPaintColor(Paint(ANTI_ALIAS_FLAG).also { paintLeft = it }, builder.bgColorSwipeLeft)
        setPaintColor(Paint(ANTI_ALIAS_FLAG).also { paintRight = it }, builder.bgColorSwipeRight)
        drawableLeft = builder.drawableSwipeLeft!!
        drawableRight = builder.drawableSwipeRight!!
        swipeEnabled = builder.swipeEnabled
        onItemSwipeLeftListener = builder.onItemSwipeLeftListener!!
        onItemSwipeRightListener = builder.onItemSwipeRightListener!!
    }

    private fun setPaintColor(paint: Paint, color: Int) {
        paint.color = color
    }

    override fun isItemViewSwipeEnabled(): Boolean {
        return swipeEnabled
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        return false
    }

    override fun onChildDraw(c: Canvas, recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, dX: Float, dY: Float, actionState: Int, isCurrentlyActive: Boolean) {
        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
        if (actionState == ItemTouchHelper.ACTION_STATE_SWIPE) {
            val itemView: View = viewHolder.itemView
            val height = itemView.bottom.toFloat() - itemView.top.toFloat()
            val width = height / 3
            if (dX > 0) {


                val background = RectF(itemView.left.toFloat(), itemView.top.toFloat(), dX, itemView.bottom.toFloat())

                @Suppress( "UNUSED_VARIABLE")
                val iconDest = RectF(itemView.left.toFloat() + width, itemView.top.toFloat() + width, itemView.left.toFloat() + 2 * width, itemView.bottom.toFloat() - width)
                c.drawRect(background, paintLeft)
                val drawable = ContextCompat.getDrawable(getApplicationContext(), R.drawable.ic_delete_action)
                drawable?.let { drawIcon2(c, itemView, -dX, it) }

            } else if (dX < 0) {
                val background = RectF(itemView.right.toFloat() + dX, itemView.top.toFloat(),
                        itemView.right.toFloat(), itemView.bottom.toFloat())
                @Suppress("UNUSED_VARIABLE")
                val iconDest = RectF(itemView.right.toFloat() - 2 * width, itemView.top.toFloat() + width, itemView.right.toFloat() - width, itemView.bottom.toFloat() - width)
                c.drawRect(background, paintRight)
                val drawable = ContextCompat.getDrawable(getApplicationContext(), R.drawable.ic_action_unmatch)
                drawable?.let { drawIcon(c, itemView, dX, it) }

            }
        }
    }

    private fun drawIcon2(canvas: Canvas, viewItem: View, dX: Float, icon: Drawable) {
        val topMargin = calculateTopMargin(icon, viewItem)
        val OFFSET_PX = 8
        icon.bounds = getStartContainerRectangle2(viewItem, icon.intrinsicWidth, topMargin, OFFSET_PX, dX)
        icon.draw(canvas)
    }

    private fun getStartContainerRectangle2(viewItem: View, iconWidth: Int, topMargin: Int, sideOffset: Int,
                                            dx: Float): Rect {
        val leftBound = viewItem.left - dx.toInt() - sideOffset
        val rightBound = viewItem.left - dx.toInt() - sideOffset - iconWidth
        val topBound = viewItem.top + topMargin
        val bottomBound = viewItem.bottom - topMargin

        return Rect(rightBound, topBound, leftBound, bottomBound)


    }


    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        val position = viewHolder.adapterPosition
        if (direction == ItemTouchHelper.LEFT) {
            onItemSwipeLeftListener.onItemSwiped(position)
        } else if (direction == ItemTouchHelper.RIGHT) {
            onItemSwipeRightListener.onItemSwiped(position)
        }
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val swipeFlags = ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        //Disable drag on one viewHolder
        return if (viewHolder.itemViewType == MessagesAdapter.EMPTY_VIEW_TYPE || viewHolder.itemViewType == MessagesAdapter.MOB_AD_VIEW_TYPE) 0 else ItemTouchHelper.Callback.makeMovementFlags(0, swipeFlags)
    }


    interface OnItemSwipeListener {
        fun onItemSwiped(position: Int)
    }

    class Builder(val dragDirs: Int, val swipeDirs: Int) {
        var drawableSwipeLeft: Drawable? = null
        var drawableSwipeRight: Drawable? = null
        var bgColorSwipeLeft = 0
        var bgColorSwipeRight = 0
        var onItemSwipeLeftListener: OnItemSwipeListener? = null
        var onItemSwipeRightListener: OnItemSwipeListener? = null
        var swipeEnabled = false
        fun drawableSwipeLeft(`val`: Drawable?): Builder {
            drawableSwipeLeft = `val`
            return this
        }

        fun drawableSwipeRight(`val`: Drawable?): Builder {
            drawableSwipeRight = `val`
            return this
        }

        fun bgColorSwipeLeft(`val`: Int): Builder {
            bgColorSwipeLeft = `val`
            return this
        }

        fun bgColorSwipeRight(`val`: Int): Builder {
            bgColorSwipeRight = `val`
            return this
        }

        fun onItemSwipeLeftListener(`val`: OnItemSwipeListener?): Builder {
            onItemSwipeLeftListener = `val`
            return this
        }

        fun onItemSwipeRightListener(`val`: OnItemSwipeListener?): Builder {
            onItemSwipeRightListener = `val`
            return this
        }

        fun setSwipeEnabled(`val`: Boolean): Builder {
            swipeEnabled = `val`
            return this
        }

        fun build(): SwipeToUnmatchCallback {
            return SwipeToUnmatchCallback(this)
        }

    }
}

private fun calculateTopMargin(icon: Drawable, viewItem: View): Int {
    return (viewItem.height - icon.intrinsicHeight) / 2
}

private fun getStartContainerRectangle(viewItem: View, iconWidth: Int, topMargin: Int, sideOffset: Int,
                                       dx: Float): Rect {
    val leftBound = viewItem.right + dx.toInt() + sideOffset
    val rightBound = viewItem.right + dx.toInt() + iconWidth + sideOffset
    val topBound = viewItem.top + topMargin
    val bottomBound = viewItem.bottom - topMargin

    return Rect(leftBound, topBound, rightBound, bottomBound)
}

private fun drawIcon(canvas: Canvas, viewItem: View, dX: Float, icon: Drawable) {
    val topMargin = calculateTopMargin(icon, viewItem)
    val OFFSET_PX = 8
    icon.bounds = getStartContainerRectangle(viewItem, icon.intrinsicWidth, topMargin, OFFSET_PX, dX)
    icon.draw(canvas)
}

private fun getBackGroundRectangle(viewItem: View, dX: Float): RectF {
    return RectF(viewItem.right.toFloat() + dX, viewItem.top.toFloat(),
            viewItem.right.toFloat(), viewItem.bottom.toFloat())
}

