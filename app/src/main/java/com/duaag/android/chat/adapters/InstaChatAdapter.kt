package com.duaag.android.chat.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.chat.fragments.InstaChatFragment
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.MessageType
import com.duaag.android.databinding.*
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.*
import timber.log.Timber


class InstaChatAdapter(private val clickListener: MessagesClickListener,
                       private val longClickListener: MessagesLong<PERSON>lick,
                       val userId: String?,
                       private val homeViewModel: HomeViewModel) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val EMPTY_VIEW_TYPE = 0
        const val CONVERSATION_VIEW_TYPE = 1
        const val LOADING_VIEW_TYPE = 2
        const val MOB_AD_VIEW_TYPE = 3
        const val MOB_AD_SHIMMER_VIEW_TYPE = 4
        const val FILTER_VIEW_TYPE = 5

        const val SEVENTYTWOHOURSINMS = 259200000
        const val SEVENTYTWOHOURSINMINUTES = 4320F
        const val TAG = "InstaChatTag"
    }

    private val conversations = mutableListOf<ConversationModel>()

    private val filterItem= ConversationModel.getEmptyConversation().apply { isFilterItem = true; isEmpty = false }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            CONVERSATION_VIEW_TYPE -> {
                val start = System.currentTimeMillis()
                val binding = InstaChatItemBinding.inflate(layoutInflater, parent, false)
                Timber.tag("INFLATE").d("VIEWBINDING  Duration to inflate ${System.currentTimeMillis() - start}ms")
                InstaChatViewHolder(binding)
            }
            EMPTY_VIEW_TYPE -> {
                val start = System.currentTimeMillis()
                val binding = EmptyMessagesItemBinding.inflate(layoutInflater, parent, false)
                Timber.tag("INFLATE").d("Messages Duration to inflate ${System.currentTimeMillis() - start}ms")
                EmptyViewHolder(binding)
            }

            MOB_AD_VIEW_TYPE -> {
                val start = System.currentTimeMillis()
                val binding = RecyclerviewAdMessagesItemBinding.inflate(layoutInflater, parent, false)
                Timber.tag("INFLATE").d("Messages Duration to inflate ${System.currentTimeMillis() - start}ms")
                AdMobViewHolder(binding)
            }

            MOB_AD_SHIMMER_VIEW_TYPE -> {
                val binding = ChatAdmobShimmerEffectItemBinding.inflate(layoutInflater, parent, false)
                AdMobShimmerViewHolder(binding)
            }

            FILTER_VIEW_TYPE -> {
                val binding = InstaChatFilterItemBinding.inflate(layoutInflater, parent, false)
                FilterViewHolder(binding)
            }

            else -> {
                val start = System.currentTimeMillis()
                val binding = LoadingMessagesItemBinding.inflate(layoutInflater, parent, false)
                Timber.tag("INFLATE").d("Messages Duration to inflate ${System.currentTimeMillis() - start}ms")
                LoadingViewHolder(binding)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            CONVERSATION_VIEW_TYPE -> (holder as InstaChatViewHolder).bind(conversations[position], userId)
            LOADING_VIEW_TYPE -> (holder as LoadingViewHolder)
            EMPTY_VIEW_TYPE -> (holder as EmptyViewHolder)
            MOB_AD_VIEW_TYPE -> (holder as AdMobViewHolder).bind(conversations[position], clickListener)
            MOB_AD_SHIMMER_VIEW_TYPE -> (holder as AdMobShimmerViewHolder).bind()
            FILTER_VIEW_TYPE -> (holder as FilterViewHolder).bind()


        }
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        if (holder is AdMobShimmerViewHolder) holder.startShimmer()
    }

    override fun onViewDetachedFromWindow(holder: RecyclerView.ViewHolder) {
        if (holder is AdMobShimmerViewHolder) holder.stopShimmer()
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            conversations[position].isFilterItem -> return FILTER_VIEW_TYPE
            conversations[position].isEmpty -> return EMPTY_VIEW_TYPE
            conversations[position].isLoading -> return LOADING_VIEW_TYPE
            conversations[position].isMobAd -> return MOB_AD_VIEW_TYPE
            conversations[position].isMobAdShimmer -> return MOB_AD_SHIMMER_VIEW_TYPE
            else -> CONVERSATION_VIEW_TYPE
        }
    }

    fun addLoadingItem() {
        val loadingItem = ConversationModel.getEmptyConversation()
        loadingItem.isLoading = true
        conversations.add(loadingItem)
        notifyItemInserted(itemCount - 1)
    }

    fun setData(data: ArrayList<ConversationModel>) {

        if (data.size > 0 && !data[0].isFilterItem){
            data.add(0,filterItem)
        }

        val diffCallback = ConversationDiffCallback(conversations, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        conversations.clear()
        conversations.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }


    override fun getItemCount(): Int = conversations.size

    private class LoadingViewHolder(binding: LoadingMessagesItemBinding) : RecyclerView.ViewHolder(binding.root)
    private class EmptyViewHolder(binding: EmptyMessagesItemBinding) : RecyclerView.ViewHolder(binding.root)

    inner class InstaChatViewHolder(var binding: InstaChatItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener { clickListener.onClick(conversations[bindingAdapterPosition]) }
            binding.root.setOnLongClickListener { longClickListener.onMessageLongClick(conversations[bindingAdapterPosition]) }
        }

        fun bind(item: ConversationModel, userId: String?) {
            val context = binding.root.context

            //create chat text
            var text = ""
            if (item.lastMessageText != null) {
                text = if (item.lastMessageType == MessageType.GIF.value) {
                    if (item.lastMessageSender == userId)
                        context.getString(R.string.you_sent_a_gif)
                    else
                        String.format(context.getString(R.string.x_sent_a_gif), item.name)
                } else item.lastMessageText ?: ""
            }

            //create chat text color
            val textColor = if (item.showSeen || item.hasFailed) {
                if (item.hasFailed) R.color.red_500 else R.color.description_primary
            } else {
                R.color.description_secondary
            }

            binding.chatNameTxt.text = item.name
            binding.chatMessageTxt.text = text
            binding.chatMessageTxt.setTextColor(ContextCompat.getColor(context, textColor))
            typeface(binding.chatMessageTxt, if (item.showSeen) 2 else 0)
            binding.badge.setImageResource(if (item.hasFailed) R.drawable.ic_faild_message else R.drawable.ic_budge_message)

            val imageUrl =
                if (!DuaApplication.instance.getBillingAvailable()
                    && homeViewModel.userProfile.value?.premiumType == null) { item.thumbnailUrl ?: item.pictureUrl }
                else if (
                    homeViewModel.userProfile.value?.cognitoUserId != item.lastMessageSender &&
                    item.gender == GenderType.WOMAN.value &&
                    homeViewModel.userProfile.value?.premiumType == null && homeViewModel.userProfile.value?.hasFreeStarterExperience() == false) { item.bluredThumbnailUrl }
                else { item.thumbnailUrl ?: item.pictureUrl }

            imageUrlCircleCrop(binding.profileImage, imageUrl)

            setVisibility(binding.badge, item.showSeen || item.hasFailed)

            when (item.badge2) {
                Badge2Status.APPROVED.status -> {
                    binding.badgeOne.setImageResource(R.drawable.ic_image_verification)
                    binding.badgeOne.visibility = View.VISIBLE
                }
                else -> {
                    binding.badgeOne.visibility = View.GONE
                }
            }

            val showMeetIndicator =
                if (!DuaApplication.instance.getBillingAvailable()
                    && (homeViewModel.userProfile.value?.premiumType == null && homeViewModel.userProfile.value?.hasFreeStarterExperience() == false)) { false }
                else homeViewModel.userProfile.value?.cognitoUserId != item.lastMessageSender &&
                item.gender == GenderType.WOMAN.value &&
                        (homeViewModel.userProfile.value?.premiumType == null  && homeViewModel.userProfile.value?.hasFreeStarterExperience() == false)

            setVisibility(binding.meetIndicator, showMeetIndicator)
            setVisibility(binding.star, item.showStar)

            binding.timeLeft.setProgress(checkTimeLeftPercentage(item.lastMessageTime))
            binding.timeLeft.setRounded(true)
            binding.timeLeft.setProgressColor(ContextCompat.getColor(context, R.color.pink_500))
            binding.timeLeft.setProgressBackgroundColor(ContextCompat.getColor(context, R.color.border))
            binding.timeLeft.setProgressWidth(convertDpToPixel(2f, context))
        }

        private fun checkTimeLeftPercentage(lastMessageTime: Long): Float {
            val end = lastMessageTime + SEVENTYTWOHOURSINMS

            //find the time remaining and convert it to minutes
            val minutesRemaining = (end - System.currentTimeMillis()) / 60000

            //find the percentage of the minutes remaining
            val percentage = (minutesRemaining / SEVENTYTWOHOURSINMINUTES) * 100
            Timber.tag(TAG).d("Percentage remaining: $percentage%")
            return percentage
        }
    }


    private class AdMobViewHolder(val binding: RecyclerviewAdMessagesItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ConversationModel, clickListener: MessagesClickListener) {
            binding.adFrame.removeAllViews()
            if (item.maxNativeAdView?.parent != null) {
                (item.maxNativeAdView?.parent as ViewGroup).findViewById<ImageButton>(R.id.report_button).setOnClickListener(null)
                (item.maxNativeAdView?.parent as ViewGroup).removeAllViews()
            }

            item.maxNativeAdView?.let {
                    binding.adFrame.addView(populateUnifiedNativeAdView(it, item, clickListener))
                }

        }

        private fun populateUnifiedNativeAdView(
            nativeAdView: MaxNativeAdView,
            item: ConversationModel,
            clickListener: MessagesClickListener
        ): View {

            val adView: View = nativeAdView
            val reportButton: ImageButton? = adView.findViewById<ImageButton>(R.id.report_button)

            reportButton?.setOnClickListener {
                clickListener.onClick(item)
            }

//            val drawableIcon = if (nativeAd.baseNativeAd?. == null)
//                ContextCompat.getDrawable(
//                    adView.context,
//                    R.drawable.ic_chat_ad_avatar
//                ) else nativeAd.icon.drawable
//            (adView.iconView as ImageView).setImageDrawable(drawableIcon)
//            adView.iconView.visibility = View.VISIBLE

            return adView

        }
    }

    private class AdMobShimmerViewHolder(val binding: ChatAdmobShimmerEffectItemBinding) :
            RecyclerView.ViewHolder(binding.root) {
        fun bind() {
        }

        fun startShimmer() {
            binding.shimmerViewContainer.visibility = View.GONE;
            binding.shimmerViewContainer.visibility = View.VISIBLE;
            binding.shimmerViewContainer.startShimmer()
        }

        fun stopShimmer() {
            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE;

        }
    }

    inner class FilterViewHolder(val binding: InstaChatFilterItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.filterBtn.setOnClickListener { clickListener.onClick(conversations[bindingAdapterPosition]) }
        }
        fun bind() {
        }
    }

    class ConversationDiffCallback(private val oldList: List<ConversationModel>, private val newList: List<ConversationModel>) : DiffUtil.Callback() {

        override fun getOldListSize() = oldList.size

        override fun getNewListSize() = newList.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }

    //Creating an OnClickListener for our RecyclerView
    class MessagesClickListener(val clickListener: (userModel: ConversationModel) -> Unit) {
        fun onClick(userModel: ConversationModel) = clickListener(userModel)
    }

    class MessagesLongClick(val longClick: (userModel: ConversationModel) -> Unit) {
        fun onMessageLongClick(userModel: ConversationModel): Boolean {
            longClick(userModel)
            return false
        }
    }

    override fun getItemId(position: Int): Long {
        return conversations[position].lastMessageTime
    }

}
