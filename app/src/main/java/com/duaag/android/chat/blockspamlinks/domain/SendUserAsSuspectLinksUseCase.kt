package com.duaag.android.chat.blockspamlinks.domain

import com.duaag.android.chat.blockspamlinks.data.db.models.SuspiciousLinksBody
import com.duaag.android.chat.blockspamlinks.data.db.models.SuspiciousLinksItemBody
import com.duaag.android.chat.blockspamlinks.utils.convertHourToMilliSeconds
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class SendUserAsSuspectLinksUseCase @Inject constructor(
    private val spamLinkRepository: SpamLinkRepository
) {
    suspend operator fun invoke(hours: Long): Flow<Boolean> = flow  {
        val currentTime = System.currentTimeMillis()
        val suspiciousLinksBody =   spamLinkRepository.getSpamLinks(currentTime - hours.convertHourToMilliSeconds(hours)).map {
            SuspiciousLinksItemBody(it.url, it.time)
        }.let {
            SuspiciousLinksBody(it)
        }
        
        spamLinkRepository.suspiciousLinks(suspiciousLinksBody).catch {
            emit(false)
        }.collect {
            emit(true)
            spamLinkRepository.deleteAllSpamLinks()
            spamLinkRepository.makeUserSuspiciousLinksLocally()
        }
    }
}