package com.duaag.android.chat.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.ShadowBannedLockLayoutBinding

class ShadowBannedViewHolder private constructor(val binding: ShadowBannedLockLayoutBinding) :
        RecyclerView.ViewHolder(binding.root){
        companion object {
            fun from(parent: ViewGroup) : ShadowBannedViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    ShadowBannedLockLayoutBinding.inflate(layoutInflater,parent,false)
                return ShadowBannedViewHolder(binding)
            }
        }

    fun bind(callback: (ShadowBannedLockLayoutBinding) -> Unit) {
      callback(binding)
    }

}