package com.duaag.android.chat.repositories

import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.api.UnBlurErrorType
import com.duaag.android.api.UserService
import com.duaag.android.chat.model.*
import com.duaag.android.db.LikesDao
import com.duaag.android.exceptions.UserDeletedExistException
import com.duaag.android.exceptions.UserDisabledException
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

@Singleton
class LikedYouRepository @Inject constructor(val likesDao: LikesDao,
                                             @Named("private") private val privateService: UserService) {


    fun getLikes(nextCursor: String?, firstPage: Boolean, isRefreshing: Boolean = false): Flow<Resource<UserLikedYouResponse>> = flow {
        if (firstPage) {
            emit(Resource.Loading)

            if (!isRefreshing)
                emit(Resource.Success(UserLikedYouResponse(likesDao.getUserLikes(), null, 0), true))
        }
        try {
            val response = privateService.getUserLikes(limit = 30, nextCursor = nextCursor, previousCursor = null)
            if (response.isSuccessful) {
                val body = response.body()
                body?.let { responseBody ->
                    if (responseBody.likes.isNotEmpty())
                        responseBody.likes.last().nextCursor = responseBody.nextCursor
                    emit(Resource.Success(responseBody))

                    if (firstPage) likesDao.updateData(responseBody.likes)
                }
            } else {
                val error = response.errorBody()!!.string()
                val errorObject = Gson().fromJson(error, LikesErrorBody::class.java)
                when (errorObject.type) {
                    LikesErrorType.USER_DELETED.value -> throw  UserDeletedExistException("User was deleted")
                    LikesErrorType.USER_DISABLED.value -> throw UserDisabledException("User is disabled")
                    else -> throw Exception("Error")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    fun getUserLikeYouCount(): Flow<Result<UserLikedYouCountResponse>> = flow {
        try {
            val response = privateService.getUserLikeYouCount()
            if (response.isSuccessful) {
                val body = response.body()
                emit(Result.Success(body!!))
            } else {
                   emit(Result.Error(Exception("Error")))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emit(Result.Error(e))
        }
    }

    fun seenLike(cognitoUserId: String): Flow<Resource<String>> = flow{
            emit(Resource.Loading)
        try {
            val response = privateService.seenUserLike(cognitoUserId)
            if (response.isSuccessful) {
                    emit(Resource.Success("Success"))
            } else {
                throw  Exception("Error")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    suspend fun deleteLike(cognitoUserId: String) {
        likesDao.deleteLike(cognitoUserId)
    }

    suspend fun deleteLikeByInteractionId(exId: String) {
        likesDao.deleteLikeByInteractionId(exId)
    }

    suspend fun insertLike(like: UserLikesModel) {
        likesDao.insertLike(like)
    }


    fun unblurProfile(interactionId: String) : Flow<Resource<UserLikesModel>> = flow {
        emit(Resource.Loading)
        try {
            val response = privateService.unblurProfile(interactionId)
            if (response.isSuccessful) {
                emit(Resource.Success(response.body()!!))
            } else {
                throw Exception("Error")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    fun unBlurLatestLike(): Flow<Resource<UserLikesModel>> = flow {
        try {
            emit(Resource.Loading)

            val response = privateService.unBlurLatestLike()

            when {
                response.isSuccessful -> {
                    val responseBody = response.body()
                    if (responseBody != null) {
                        emit(Resource.Success(responseBody))
                    } else {
                        throw Exception("Empty response body")
                    }
                }
                response.code() == 400 -> {
                    val errorBody = response.errorBody()?.string()
                    if (errorBody != null && errorBody.contains(UnBlurErrorType.NO_LIKES_TO_UNBLUR.errorMessage)) {
                       throw Exception(UnBlurErrorType.NO_LIKES_TO_UNBLUR.errorMessage)
                    } else {
                        throw Exception("Bad Request")
                    }
                }
                response.code() == 403 -> {
                    val errorBody = response.errorBody()?.string()
                    if (errorBody != null && errorBody.contains(UnBlurErrorType.UNBLUR_LIMIT_REACHED.errorMessage)) {
                        throw Exception(UnBlurErrorType.UNBLUR_LIMIT_REACHED.errorMessage)
                    } else {
                        throw Exception("Forbidden")
                    }
                }
                else -> {
                   throw Exception("Unexpected error")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
           throw e
        }
    }


    suspend fun deleteLikes() {
        likesDao.deleteLikes()
    }

}