package com.duaag.android.chat.views.likedyou

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.duaag.android.R
import com.duaag.android.databinding.LikedYouEmptyViewBinding

class LikedYouEmptyView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding = LikedYouEmptyViewBinding.inflate(LayoutInflater.from(context), this, true)


    fun bind(viewEntity: ViewEntity) {

        binding.button.setOnClickListener {
            viewEntity.onButtonClick(viewEntity.state)
        }

        when (viewEntity.state) {
            State.BoostNow -> {
                binding.button.text = context.getString(R.string.boost_now_btn)
                binding.button.isEnabled = true
                binding.icon.setImageResource(R.drawable.ic_liked_you_empty_boost)
                binding.title.text = context.getString(R.string.empty_get_boost_title)
                binding.description.text = context.getString(R.string.empty_get_boost_desc)
            }

            State.BoostProcessing -> {
                binding.button.text = context.getString(R.string.boost_process_btn)
                binding.button.isEnabled = false
                binding.icon.setImageResource(R.drawable.ic_liked_you_empty_boost)
                binding.title.text = context.getString(R.string.empty_boost_active_title)
                binding.description.text = context.getString(R.string.empty_boost_active_desc)
            }

            State.EditProfile -> {
                binding.button.text = context.getString(R.string.edit_my_profile)
                binding.button.isEnabled = true
                binding.icon.setImageResource(R.drawable.ic_liked_you_empty_edit_profile)
                binding.title.text = context.getString(R.string.empty_introduce_yourself_title)
                binding.description.text = context.getString(R.string.empty_introduce_yourself_desc)
            }
        }
    }


    data class ViewEntity(
        val state: State,
        val onButtonClick: (State) -> Unit,
    )

    sealed class State {
        object BoostNow : State()
        object BoostProcessing : State()
        object EditProfile : State()
    }

}