package com.duaag.android.chat.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.base.adapter.BindableRecyclerViewAdapter
import com.duaag.android.chat.model.UserMatchesModel
import com.duaag.android.chat.viewmodel.MatchesTabViewModel
import com.duaag.android.databinding.MatchesEmptyItemBinding
import com.duaag.android.databinding.MatchesLoadingItemBinding
import com.duaag.android.databinding.RecyclerviewMatchesItemBinding
import com.duaag.android.settings.fragments.Badge2Status


class MatchesAdapter(private val clickListener: MatchesClickListener, private val viewModel: MatchesTabViewModel) : RecyclerView.Adapter<RecyclerView.ViewHolder>(), BindableRecyclerViewAdapter<ArrayList<UserMatchesModel>> {

    companion object {
        const val EMPTY_VIEW_TYPE = 0
        const val MATCH_VIEW_TYPE = 1
        const val LOADING_VIEW_TYPE = 2
    }


    private var items: ArrayList<UserMatchesModel> = ArrayList()
    private val emptyMatch = UserMatchesModel.getEmptyUser().apply { isEmpty = true }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            MATCH_VIEW_TYPE -> {
                val binding = RecyclerviewMatchesItemBinding.inflate(layoutInflater, parent, false)
                MatchViewHolder(binding)
            }
            EMPTY_VIEW_TYPE -> {
                val binding = MatchesEmptyItemBinding.inflate(layoutInflater, parent, false)
                EmptyViewHolder(binding)
            }
            else -> {
                val binding = MatchesLoadingItemBinding.inflate(layoutInflater, parent, false)
                LoadingViewHolder(binding)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            items[position].isEmpty -> return EMPTY_VIEW_TYPE
            items[position].isLoading -> return LOADING_VIEW_TYPE
            else -> MATCH_VIEW_TYPE
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            MATCH_VIEW_TYPE -> (holder as MatchViewHolder).bind(items[position], clickListener, viewModel)
            LOADING_VIEW_TYPE -> (holder as LoadingViewHolder)
            EMPTY_VIEW_TYPE -> (holder as EmptyViewHolder)
        }
    }

    private class LoadingViewHolder(binding: MatchesLoadingItemBinding) : RecyclerView.ViewHolder(binding.root)
    private class EmptyViewHolder(binding: MatchesEmptyItemBinding) : RecyclerView.ViewHolder(binding.root)
    class MatchViewHolder(var binding: RecyclerviewMatchesItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserMatchesModel, clickListener: MatchesClickListener, viewModel: MatchesTabViewModel) {
            binding.matchesItem = item
            binding.clickListener = clickListener
            binding.executePendingBindings()

            when (item.user.badge2) {
                Badge2Status.APPROVED.status -> {
                    binding.badge1Img.setImageResource(R.drawable.ic_image_verification)
                    binding.badge1Img.visibility = View.VISIBLE
                }
                else -> {
                    binding.badge1Img.visibility = View.GONE
                }
            }
        }
    }


    inner class DiffUtilCallBack(private val oldList: List<UserMatchesModel>, private val newList: List<UserMatchesModel>) : DiffUtil.Callback() {
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id && oldList[oldItemPosition].showSeen == newList[newItemPosition].showSeen
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }


    override fun getItemCount() = items.size


    override fun setData(data: ArrayList<UserMatchesModel>) {
        if(data.isEmpty()) data.add(emptyMatch)
        val diffCallback = DiffUtilCallBack(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    fun addLoadingItem() {
        val loadingItem = UserMatchesModel.getEmptyUser()
        loadingItem.isLoading = true
        items.add(loadingItem)
        notifyItemInserted(itemCount - 1)
    }

    fun removeLoadingItem() {
        if (items.last().isLoading) {
            items.removeAt(itemCount - 1)
            notifyItemRemoved(itemCount - 1)
        }
    }

}


//Creating an OnClickListener for our RecyclerView
class MatchesClickListener(val clickListener: (userModel: UserMatchesModel) -> Unit) {
    fun onClick(userModel: UserMatchesModel) = clickListener(userModel)
}

