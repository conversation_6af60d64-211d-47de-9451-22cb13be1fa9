package com.duaag.android.chat.viewmodel

import android.view.ViewGroup
import android.widget.ImageButton
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.api.socket.model.UpdateTypingStateChanged
import com.duaag.android.chat.ChatRepository
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.UserMatchesModel
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.firebase.model.UserUnMatchedModel
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@ActivityScope
class MatchesTabViewModel @Inject constructor(
    val chatRepository: ChatRepository,
    val userRepository: UserRepository,
    val duaSharedPrefs: DuaSharedPrefs,
) : ViewModel() {

    var matchesNextCursor: String? = null
    var conversationsNextCursor: String? = null

    var matchesSearchNextCursor: String? = null
    var conversationsSearchNextCursor: String? = null
    var isMatchesLoading = false
    var isConversationsLoading = false
    private var hasSentMatchesCountInPurchasely = false

    private var _isSearching = false
    fun getIsSearching() = _isSearching
    fun setIsSearching(boolean: Boolean) {
        _isSearching = boolean
    }

    //The Internal MutableLiveData List that stores data from API
    private var _matches = MutableLiveData<MutableList<UserMatchesModel>>(mutableListOf())
    val matches: LiveData<MutableList<UserMatchesModel>>
        get() = _matches

    private val _conversations = MutableLiveData<MutableList<ConversationModel>>(mutableListOf())
    val conversations: LiveData<MutableList<ConversationModel>>
        get() = _conversations

    private val _matchesSearch = MutableLiveData<MutableList<UserMatchesModel>>(mutableListOf())
    val matchesSearch: LiveData<MutableList<UserMatchesModel>>
        get() = _matchesSearch

    private val _conversationsSearch =
        MutableLiveData<MutableList<ConversationModel>>(mutableListOf())
    val conversationsSearch: LiveData<MutableList<ConversationModel>>
        get() = _conversationsSearch

    private val _networkError = MutableLiveData<Boolean>()
    val networkError: LiveData<Boolean>
        get() = _networkError

    private val _showFilter = MutableLiveData<Boolean>()
    val showFilter: LiveData<Boolean>
        get() = _showFilter

    private val _statusNoUsers = MutableLiveData<Boolean>()
    val statusNoUsers: LiveData<Boolean>
        get() = _statusNoUsers

    private val _hideSwipeRefresh = SingleLiveData<Boolean>()
    val hideSwipeRefresh: LiveData<Boolean>
        get() = _hideSwipeRefresh

    private val _onSetDefaultData = SingleLiveData<Void>()
    val onSetDefaultData: LiveData<Void>
        get() = _onSetDefaultData

    private val _showChatMobAd = SingleLiveData<Boolean>()
    val showChatMobAd: LiveData<Boolean>
        get() = _showChatMobAd

    var unreadConversationCount: Int = 0
    private val _checkForUnreadConversationBadge = SingleLiveData<Boolean>()

    private val receiveTypingJobsMap = mutableMapOf<String, Job>()
    private val receiveTypingCoroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val typingIndicatorDelayMillis = 20000L

    val checkForUnreadConversationBadge: LiveData<Boolean>
        get() = _checkForUnreadConversationBadge

    var matchesCount: Int = 0
    private val _matchesCountBadge = SingleLiveData<Boolean>()
    val matchesCountBadge: LiveData<Boolean>
        get() = _matchesCountBadge

    // Backing property to avoid flow emissions from other classes
    private val _searchConversation = SingleLiveData<List<ConversationModel>>()
    val searchConversation: LiveData<List<ConversationModel>>
        get() = _searchConversation

    // Backing property to avoid flow emissions from other classes
    private val _searchMatches = SingleLiveData<List<UserMatchesModel>>()
    val searchMatches: LiveData<List<UserMatchesModel>>
        get() = _searchMatches

    var _lastTimeMatchesAreUpdated: Long = 0
        private set

    var _lastTimeConversationsAreUpdated: Long = 0
        private set

    private fun checkForHideSwipeRefresh() {
        if (!isMatchesLoading && !isConversationsLoading) {
            _hideSwipeRefresh.postValue(true)
        }
    }

    fun setMatches(items: List<UserMatchesModel>) {
        val distinctItems = items.toMutableList().distinctBy { it.user.id }
        _matches.value = distinctItems.toMutableList()
    }

    fun setStatusNoUsers(hasNoUsers: Boolean) {
        _statusNoUsers.value = hasNoUsers
    }

    fun setSearchMatches(items: List<UserMatchesModel>) {
        _matchesSearch.value = items.toMutableList()
    }

    fun setConversations(items: List<ConversationModel>) {
        val distinctItems = items.distinctBy { it.id }.toMutableList()
        _conversations.value = distinctItems
    }

    fun setSearchConversations(items: List<ConversationModel>) {
        _conversationsSearch.value = items.toMutableList()
    }

    fun onSetDefaultData() {
        _onSetDefaultData.call()
    }

    init {
        initData()

    }

    fun getConversationsFromDbAsFlow(name: String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getConversations(name)
                .catch { e ->
                    Timber.tag("sharedFlowError").e(e.message.toString())
                }
                .collect {
                    _searchConversation.postValue(it)
                    Timber.tag("sharedFlowMatches").e(it.toString())
                }

        }
    }

    fun getMatchesFromDbAsFlow(name: String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getMatches(name)
                .catch { e ->
                    Timber.tag("sharedFlowError").e(e.message.toString())
                }
                .collect {
                    _searchMatches.postValue(it)
                    Timber.tag("sharedFlowMatches").e(it.toString())
                }
        }
    }

    fun initData(isRefreshing: Boolean = false) {
        var totalMatches: Int? = null
        var totalConversations: Int? = null

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getMatches(null, null, true, isRefreshing)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        matchesLoading(false)
                        when (ex) {
                            is NoConnectivityException -> {
                            }
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                _lastTimeMatchesAreUpdated = System.currentTimeMillis()

                                if (!it.isFromDB) {
                                    matchesLoading(false)
                                    if (!getIsSearching()) {
                                        matchesCount = it.data.unreadCount
                                        _matchesCountBadge.value = true
                                    }

                                    totalMatches = it.data.totalCount
                                    if(!hasSentMatchesCountInPurchasely)
                                        sendMatchesCountInPurchasely(totalMatches, totalConversations)
                                }
                                setMatches(it.data.matches)
                                matchesNextCursor =
                                    if (it.data.matches.isEmpty()) null else it.data.matches.last().nextCursor
                            }

                            is Resource.Error -> {
                            }

                            Resource.Loading -> {
                                matchesLoading(true)
                            }
                        }
                    }
                }
        }

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getConversations(null, null, true, isRefreshing)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        conversationsLoading(false)
                        when (ex) {
                            is NoConnectivityException -> {
                            }
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                _lastTimeConversationsAreUpdated = System.currentTimeMillis()

                                setConversations(it.data.recentConversations)
                                if (!it.isFromDB) {
                                    conversationsLoading(false)
                                    if (!getIsSearching()) {
                                        unreadConversationCount = it.data.unreadCount
                                        _checkForUnreadConversationBadge.value = true
                                    }
                                    if (it.data.recentConversations.isNotEmpty())
                                        _showChatMobAd.value = true

                                    totalConversations = it.data.initiatedCount + it.data.receivedCount
                                    if(!hasSentMatchesCountInPurchasely)
                                        sendMatchesCountInPurchasely(totalMatches, totalConversations)

                                }

                                conversationsNextCursor =
                                    if (it.data.recentConversations.isEmpty()) null else it.data.recentConversations.last().nextCursor
                            }

                            is Resource.Error -> {
                            }

                            Resource.Loading -> {
                                conversationsLoading(true)
                            }
                        }
                    }
                }
        }

    }

    private fun sendMatchesCountInPurchasely(totalMatches: Int?, totalConversations: Int?) {
        if(totalMatches != null && totalConversations != null) {
            PurchaselyManager.setTotalMatchesCount(totalMatches + totalConversations)
            hasSentMatchesCountInPurchasely = true
        }
    }

    fun loadMoreMatches(filter: String?, nextCursor: String?, isRefreshing: Boolean = false) {
        matchesLoading(true)

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getMatches(filter, nextCursor, false, isRefreshing)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        matchesLoading(false)
                        when (ex) {
                            is NoConnectivityException -> {
                            }
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                matchesLoading(false)

                                if (getIsSearching())
                                    matchesSearchNextCursor =
                                        if (it.data.matches.isEmpty()) null else it.data.matches.last().nextCursor
                                else
                                    matchesNextCursor =
                                        if (it.data.matches.isEmpty()) null else it.data.matches.last().nextCursor

                                val existingMatches = mutableListOf<UserMatchesModel>()
                                if (!isRefreshing) {
                                    existingMatches.addAll(if (getIsSearching()) matchesSearch.value!! else matches.value!!)
                                }
                                existingMatches.addAll(it.data.matches)

                                if (getIsSearching())
                                    setSearchMatches(existingMatches)
                                else
                                    setMatches(existingMatches)

                            }

                            is Resource.Loading -> {

                            }

                            else -> {}
                        }
                    }
                }
        }
    }


    fun loadMoreConversations(filter: String?, nextCursor: String?, isRefreshing: Boolean = false) {
        conversationsLoading(true)

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getConversations(filter, nextCursor, false, isRefreshing = isRefreshing)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        conversationsLoading(false)

                        when (ex) {
                            is NoConnectivityException -> {
                                networkError(true)
                            }
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                conversationsLoading(false)

                                if (getIsSearching()) conversationsSearchNextCursor =
                                    if (it.data.recentConversations.isEmpty()) null else it.data.recentConversations.last().nextCursor
                                else
                                    conversationsNextCursor =
                                        if (it.data.recentConversations.isEmpty()) null else it.data.recentConversations.last().nextCursor

                                val existingConversations = mutableListOf<ConversationModel>()

                                if (!isRefreshing) {
                                    existingConversations.addAll(if (getIsSearching()) conversationsSearch.value!! else conversations.value!!)
                                }
                                existingConversations.addAll(it.data.recentConversations)

                                if (getIsSearching()) {
                                    setSearchConversations(existingConversations)
                                } else {
                                    setConversations(existingConversations)
                                    recalculateAdsAfterPagination()
                                    _showChatMobAd.value = false
                                }
                            }

                            is Resource.Loading -> {

                            }

                            else -> {}
                        }
                    }
                }
        }
    }


    fun getPreviousMatches() {
        val previousCursor =
            if (_matches.value.isNullOrEmpty()) null else _matches.value!!.first().time.toString()
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getPreviousMatches(previousCursor)
                .catch { ex ->
                    when (ex) {
                        is NoConnectivityException -> {
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                _lastTimeMatchesAreUpdated = System.currentTimeMillis()

                                if (it.data.isNullOrEmpty()) return@withContext
                                val newItems: MutableList<UserMatchesModel>
                                if (_matches.value != null) {
                                    newItems = _matches.value!!.toMutableList()
                                    newItems.addAll(0, it.data)
                                } else {
                                    newItems = it.data as MutableList<UserMatchesModel>
                                }
                                setMatches(newItems)
                            }

                            is Resource.Error -> {
                            }

                            Resource.Loading -> {
                            }
                        }
                    }
                }
        }
    }

    fun getPreviousConversations() {
        val previousCursor =
            if (_conversations.value.isNullOrEmpty()) null else _conversations.value!!.first().lastMessageTime.toString()

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.getPreviousConversations(previousCursor)
                .catch { ex ->
                    when (ex) {
                        is NoConnectivityException -> {
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                _lastTimeConversationsAreUpdated = System.currentTimeMillis()

                                if (it.data.isNullOrEmpty()) return@withContext
                                val newItems: MutableList<ConversationModel>
                                if (_conversations.value != null) {
                                    newItems = _conversations.value!!.toMutableList()
                                    it.data.asReversed().forEach { newItem ->
                                        val find = newItems.find { it.id == newItem.id }
                                        if (find != null) {
                                            newItems.remove(find)
                                            newItems.add(0, newItem)
                                        } else {
                                            newItems.add(0, newItem)
                                        }
                                    }

                                } else {
                                    newItems = it.data as MutableList<ConversationModel>
                                }
                                setConversations(newItems)
                            }

                            is Resource.Error -> {
                            }

                            Resource.Loading -> {
                            }
                        }
                    }
                }
        }
    }

    fun updateTypingStateChanged(updateTypingStateChanged: UpdateTypingStateChanged) {
        if (_conversations.value != null) {
            val newItems: MutableList<ConversationModel>
            var itemUpdated: ConversationModel? = null
            var indexUpdated: Int? = null
            newItems = ArrayList(_conversations.value!!)
            newItems.firstOrNull { it.id == updateTypingStateChanged.payload.conversationId }?.let {
                indexUpdated = newItems.indexOf(it)
                itemUpdated = it.copy()
                itemUpdated?.isTyping = updateTypingStateChanged.payload.data.isTyping
                itemUpdated?.lastIsTypingTrueTimestamp = System.currentTimeMillis()
            }

            if (itemUpdated != null) {
                newItems.removeAt(indexUpdated!!)
                newItems.add(indexUpdated!!, itemUpdated!!)

                receiveTypingJobsMap[itemUpdated!!.id]?.cancel()

                if (updateTypingStateChanged.payload.data.isTyping) {
                    val job = receiveTypingCoroutineScope.launch {
                        delay(typingIndicatorDelayMillis)
                        ensureActive()
                        makeTypingFalse(itemUpdated!!.id)
                    }
                    receiveTypingJobsMap[itemUpdated!!.id!!] = job
                }
            }

            _conversations.value = newItems
        }
    }

    private fun makeTypingFalse(conversationId: String?) {
        if (_conversations.value != null) {
            val newItems: MutableList<ConversationModel>
            var itemUpdated: ConversationModel? = null
            var indexUpdated: Int? = null
            newItems = ArrayList(_conversations.value!!)

            newItems.firstOrNull { it.id == conversationId }?.let {
                if (System.currentTimeMillis() - it.lastIsTypingTrueTimestamp >= typingIndicatorDelayMillis) {
                    indexUpdated = newItems.indexOf(it)
                    itemUpdated = it.copy()
                    itemUpdated?.isTyping = false
                }
            }

            if (itemUpdated != null) {
                newItems.removeAt(indexUpdated!!)
                newItems.add(indexUpdated!!, itemUpdated!!)
            }
            _conversations.value = newItems
        }
    }

    fun unMatch(userId: String) = chatRepository.unMatch(userId)
    fun seenMatch(matchId: Int) = chatRepository.seenMatch(matchId)
    fun unSeenConversation(conversationId: String): LiveData<Result<String>> =
        chatRepository.makeMessageSeen(conversationId, false)

    fun updateSeenMatchAdapter(id: Int) {
        matches.value?.let {
            val items = ArrayList(it)
            items.find { it.id == id }?.seen = 1
            _matches.value = items
            updateNewMatchCount(-1)
        }
    }

    fun onMessageReceives(conversationWebSocketModel: List<ConversationModel>) {
        val items: ArrayList<ConversationModel>
        if (!conversations.value.isNullOrEmpty()) {
            items = ArrayList(conversations.value!!)
            val reverseItems = conversationWebSocketModel.asReversed()
            reverseItems.forEach { reverseItem ->
                val find = items.find { it.id == reverseItem.id }
                if (find != null) {
                    reverseItem.isStarred = find.isStarred
                    if (find.seen != 0 && reverseItem.seen == 0) {
                        updateUnreadConversationCount(1)
                        updateNewMatchCount(-1)

                    }
                    items.remove(find)
                    items.add(0, reverseItem)
                } else {
                    if (reverseItem.seen == 0) {
                        updateUnreadConversationCount(1)
                        updateNewMatchCount(-1)
                    }
                    items.add(0, reverseItem)
                }
            }
            items.firstOrNull() { it.isMobAd }?.let {
                items.remove(it)
                when {
                    items.size == 1 || items.size == 2 -> {
                        items.add(it)
                    }

                    items.size > 2 -> {
                        items.add(2, it)
                    }

                    else -> {
                    }
                }
            }
        } else {
            items = ArrayList()
            items.addAll(0, conversationWebSocketModel)
            _showChatMobAd.value = true
            items.forEach {
                if (it.seen == 0) {
                    updateUnreadConversationCount(1)
                    updateNewMatchCount(-1)

                }
            }
        }

        _conversations.value = items
    }

    fun removeConversation(conversationModel: ConversationModel) {
        if (conversationModel.seen == 0) {
            updateUnreadConversationCount(-1)
        }

        val items =
            if (_isSearching) _conversationsSearch.value!!.toMutableList() else _conversations.value!!.toMutableList()
        items.remove(conversationModel)
        if (_isSearching) _conversationsSearch.value = items else _conversations.value = items
    }

    fun makeConversationSeen(conversationId: String?, seenValue: Int? = 1) {
        val items =
            if (_isSearching) _conversationsSearch.value!!.toMutableList() else _conversations.value!!.toMutableList()
        val mutable = items.toMutableList()
        val updateItem = mutable.find { it.id == conversationId }?.copy()
        updateItem?.let { _updateItem ->
            val indexItem = mutable.indexOf(_updateItem)
            _updateItem.seen = seenValue ?: 1
            mutable[indexItem] = _updateItem
            if (_isSearching) {
                _conversationsSearch.value = mutable
                _conversations.value?.find { it.id == conversationId }?.seen = seenValue ?: 1
            } else _conversations.value = mutable
            insertConversation(_updateItem)
            updateUnreadConversationCount(if (_updateItem.seen == 1) -1 else 1)
        }
    }

    fun updateUnreadConversationCount(value: Int) {
        unreadConversationCount += value
        _checkForUnreadConversationBadge.value = true
    }

    fun updateNewMatchCount(value: Int) {
        matchesCount += value
        _matchesCountBadge.value = true
    }


    fun makeConversationSeenAfterNotificationClick(conversationId: String?) {
        viewModelScope.launch(Dispatchers.Main) {
            // delay is because to update db  after get conversations from api
            delay(if (conversations.value != null && conversations.value!!.size > 0) 500L else 1000L)
            makeConversationSeen(conversationId)
        }
    }

    private fun insertConversation(conversationModel: ConversationModel) {
        viewModelScope.launch(Dispatchers.IO) {
            Timber.tag("insertConversation:")
                .e("Id: ${conversationModel.id}, hasFailed: ${conversationModel.hasFailed}")
            chatRepository.insertConversation(conversationModel)
        }
    }

    fun unMatchFromOthers(unMatchedModel: UserUnMatchedModel) {
        val conversationItems = if (_isSearching) {
            if (_conversationsSearch.value != null) _conversationsSearch.value!!.toMutableList() else ArrayList<ConversationModel>()
        } else {
            if (_conversations.value != null) _conversations.value!!.toMutableList() else ArrayList<ConversationModel>()
        }

        val matchesItems = if (_isSearching) {
            if (_matchesSearch.value != null) _matchesSearch.value!!.toMutableList() else ArrayList<UserMatchesModel>()
        } else {
            if (_matches.value != null) _matches.value!!.toMutableList() else ArrayList<UserMatchesModel>()
        }

        val findConversation = conversationItems.find { it.userId == unMatchedModel.userId }
        val findMatches = matchesItems.find { it.user.cognitoUserId == unMatchedModel.userId }


        conversationItems.remove(findConversation)
        matchesItems.remove(findMatches)
        if (_isSearching) _conversationsSearch.value = conversationItems else _conversations.value =
            conversationItems
        if (_isSearching) _matchesSearch.value = matchesItems else _matches.value = matchesItems

    }

    // check if new messages existed in matches list and if yes removed them from matches list
    fun checkToRemoveMatch(list: List<ConversationModel>) {
        viewModelScope.launch(Dispatchers.IO) {
            val matchesItems = if (_isSearching) {
                if (_matchesSearch.value != null) _matchesSearch.value!!.toMutableList() else ArrayList<UserMatchesModel>()
            } else {
                if (_matches.value != null) _matches.value!!.toMutableList() else ArrayList<UserMatchesModel>()
            }
            val findMatches = matchesItems.asSequence()
                .filter { match -> list.find { it.userId == match.user.cognitoUserId } != null }
                .toList()
            matchesItems.removeAll(findMatches)
            chatRepository.deleteMatchByList(findMatches)
            if (_isSearching) _matchesSearch.postValue(matchesItems) else _matches.postValue(
                matchesItems
            )
        }
    }

    fun removeMatchByUserId(userId: String) {
        val matchesItems = if (_isSearching) {
            if (_matchesSearch.value != null) _matchesSearch.value!!.toMutableList() else ArrayList<UserMatchesModel>()
        } else {
            if (_matches.value != null) _matches.value!!.toMutableList() else ArrayList<UserMatchesModel>()
        }
        val findMatches = matchesItems.find { it.user.cognitoUserId == userId }
        matchesItems.remove(findMatches)
        if (_isSearching) _matchesSearch.value = matchesItems else _matches.value = matchesItems

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.deleteMatchByUserId(userId)
        }
    }

    fun removeConversationByCognitoId(userId: String) {
        val conversationItems = if (_isSearching) {
            if (_conversationsSearch.value != null) _conversationsSearch.value!!.toMutableList() else ArrayList<ConversationModel>()
        } else {
            if (_conversations.value != null) _conversations.value!!.toMutableList() else ArrayList<ConversationModel>()
        }
        val findConversations = conversationItems.find { it.userId == userId }
        conversationItems.remove(findConversations)
        if (_isSearching) _conversationsSearch.value = conversationItems else _conversations.value =
            conversationItems

        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.deleteConversationByUserId(userId)
        }
    }

    fun addNewMatch(userMatchesModel: UserMatchesModel) {
        if (_matches.value != null) {
            if (_matches.value!!.find { it.user.id == userMatchesModel.user.id } == null) {
                val items = _matches.value!!.toMutableList()
                items.add(0, userMatchesModel)
                _matches.value = items
                updateNewMatchCount(1)
            }
        } else {
            val items = java.util.ArrayList<UserMatchesModel>()
            items.add(userMatchesModel)
            _matches.value = items
            updateNewMatchCount(1)
        }
    }

    fun addNewConversation(conversationModel: ConversationModel) {
        if (_conversations.value != null) {
            if (_conversations.value!!.find { it.id == conversationModel.id } == null) {
                val items = _conversations.value!!.toMutableList()
                items.add(0, conversationModel)
                _conversations.value = items
            }
        } else {
            val items = java.util.ArrayList<ConversationModel>()
            items.add(conversationModel)
            _conversations.value = items
        }
    }

    fun clearSearchData() {
        _matchesSearch.value = mutableListOf()
        _conversationsSearch.value = mutableListOf()
        viewModelScope.launch(Dispatchers.IO) {
            _searchConversation.postValue(mutableListOf())
            _searchMatches.postValue(mutableListOf())
        }
        matchesSearchNextCursor = null
        conversationsSearchNextCursor = null
    }

    fun checkIfDataAreEmpty() {
        viewModelScope.launch {
            delay(500)
            //check if it should show the filter button
            val emptyData = _matches.value.isNullOrEmpty() && _conversations.value.isNullOrEmpty()
            if (_isSearching) {
                _showFilter.value = false
            } else {
                _showFilter.value =
                    emptyData && userRepository.user.value?.profile?.isShadowBanned == false
            }

            //check if it should show no users found
            val emptySearchData =
                _matchesSearch.value.isNullOrEmpty() && _conversationsSearch.value.isNullOrEmpty()
            _statusNoUsers.value =
                _isSearching && emptySearchData && !isMatchesLoading && !isConversationsLoading
        }
    }

    fun matchesLoading(loading: Boolean) {
        isMatchesLoading = loading
        checkForHideSwipeRefresh()
    }

    fun conversationsLoading(loading: Boolean) {
        isConversationsLoading = loading
        checkForHideSwipeRefresh()
    }

    fun networkError(noNetwork: Boolean) {
        _networkError.value = noNetwork
    }

    fun initAdsInChatList() {
        val frequency = RemoteConfigUtils.getChatListAdFrequency();
        val adPosition = 2
        // clear listeners on old ads
        conversations.value?.let {
            val newItems = ArrayList(it)
            newItems.removeAll { model ->
                model.maxNativeAdView?.parent?.let { parent ->
                    if (parent is ViewGroup) {
                        parent.findViewById<ImageButton>(R.id.report_button)
                            .setOnClickListener(null)
                        parent.removeAllViews()
                    }
                }
                model.maxNativeAdView = null
                model.isMobAdShimmer || model.isMobAd

            }
            val conversationModel = ConversationModel.getEmptyConversation()
            conversationModel.isMobAdShimmer = true
            when {
                it.size > adPosition -> {
                    newItems.add(adPosition, conversationModel)
                }

                else -> {
                    newItems.add(conversationModel)
                }
            }

            // Calculate where to insert additional ad items based on the frequency
            var insertionIndex = adPosition + frequency
            while (insertionIndex < newItems.size) {
                val adModel = ConversationModel.getEmptyConversation()
                adModel.isMobAdShimmer = true
                newItems.add(insertionIndex, adModel)
                insertionIndex += frequency + 1 // Add 1 because we're inserting an ad item
            }

            setConversations(newItems)
        }
    }

    private fun recalculateAdsAfterPagination() {
        if (duaSharedPrefs.isLoggedInUserPremium()) return
        val frequency = RemoteConfigUtils.getChatListAdFrequency();
        var adPosition = 2

        conversations.value?.let {
            val newItems = ArrayList(it)

            newItems.lastOrNull { model -> model.isMobAdShimmer || model.isMobAd }?.let { model ->
                val index = newItems.indexOf(model)
                adPosition = index
            }

            // Calculate where to insert additional ad items based on the frequency
            var insertionIndex = adPosition + frequency
            while (insertionIndex < newItems.size) {
                val adModel = ConversationModel.getEmptyConversation()
                adModel.isMobAdShimmer = true
                newItems.add(insertionIndex, adModel)
                insertionIndex += frequency + 1 // Add 1 because we're inserting an ad item
            }

            setConversations(newItems)
        }
    }

    fun setUserDeleted(userId: String) {
        conversations.value?.firstOrNull { it.userId == userId}?.isDeleted = 1
        matches.value?.firstOrNull { it.user.cognitoUserId == userId}?.isDeleted = 1

        if (_isSearching) {
            conversationsSearch.value?.firstOrNull { it.userId == userId}?.isDeleted = 1
            matchesSearch.value?.firstOrNull { it.user.cognitoUserId == userId}?.isDeleted = 1
        }
    }

    fun addAdToChatList(nativeAdView: MaxNativeAdView?, loadNextAd: (Boolean) -> Unit) {
        conversations.value?.let {
            val conversationModel = ConversationModel.getEmptyConversation()
            conversationModel.isMobAd = true
            conversationModel.maxNativeAdView = nativeAdView
            val newItems = ArrayList(it)
            val indexOfFirstShimmerItem = newItems.indexOfFirst { it.isMobAdShimmer }
            if (indexOfFirstShimmerItem != -1) {
                // Replace the first shimmer item with the new model
                newItems[indexOfFirstShimmerItem] = conversationModel
                setConversations(newItems)
            }
            loadNextAd(newItems.any { it.isMobAdShimmer })
        }
    }

    fun removeAllShimmerItemsInChat() {
        conversations.value?.let {
            val newItems = ArrayList(it)
            newItems.removeAll { it.isMobAdShimmer }
            setConversations(newItems)
        }
    }

    fun cleanAdListenersOnChatList() {
        conversations.value?.forEach { model ->
            if (model.isMobAdShimmer || model.isMobAd) {
                model.maxNativeAdView?.parent?.let { parent ->
                    if (parent is ViewGroup) {
                        parent.findViewById<ImageButton>(R.id.report_button)
                            .setOnClickListener(null)
                        parent.removeAllViews()
                    }
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        receiveTypingCoroutineScope.cancel()
    }
}