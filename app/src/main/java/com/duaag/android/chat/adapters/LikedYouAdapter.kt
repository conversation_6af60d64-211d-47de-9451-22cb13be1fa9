package com.duaag.android.chat.adapters

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.adapter.BindableRecyclerViewAdapter
import com.duaag.android.base.models.UserModel
import com.duaag.android.chat.model.UserLikesModel
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.databinding.LikedYouHeaderLayoutBinding
import com.duaag.android.databinding.LikedYouItemBinding
import com.duaag.android.databinding.LikedYouLoadingBinding
import com.duaag.android.databinding.VerifyProfileItemBinding
import com.duaag.android.home.models.InteractionType
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.imageUrlCircleFullResolution
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonHighlightAnimation
import com.skydoves.balloon.TextForm

class LikedYouAdapter(val viewModel: LikedYouViewModel, val infoClickListener: (Pair<Float, Float>) -> Unit,  val onVerifyNowClicked: () -> Unit, val onAdButtonClicked: () -> Unit,val onAddPhotoClicked: () -> Unit) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<List<UserLikesModel>> {

    private var items: ArrayList<UserLikesModel> = ArrayList()
    private var handlers = Handlers()

    private fun createAdTooltip(context:Context): Balloon {
        val textForm = TextForm.Builder(context)
            .setText(context.getString(R.string.watch_ads_tooltip))
            .setTextColorResource(R.color.title_secondary)
            .setTextSize(16f)
            .setTextTypeface(ResourcesCompat.getFont(context,R.font.tt_norms_pro_normal))
            .build()
        return Balloon.Builder(context)
            .setArrowSize(16)
            .setArrowAlignAnchorPaddingRatio(convertDpToPixel(5f, context))
            .setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            .setArrowOrientation(ArrowOrientation.TOP)
            .setArrowPosition(0.5F)
            .setPadding(12)
            .setMarginRight(24)
            .setTextForm(textForm)
            .setDismissWhenClicked(false)
            .setArrowElevation(1)
            .setCornerRadius(10f)
            .setElevation(1)
            .setBackgroundColor(ContextCompat.getColor(context, R.color.bg_secondary_old))
            .setBalloonAnimation(BalloonAnimation.OVERSHOOT)
            .setBalloonHighlightAnimation(BalloonHighlightAnimation.SHAKE)
            .build()
    }

    companion object {
        const val HEADER_VIEW_TYPE = -1
        const val ITEM_VIEW_TYPE = 0
        const val LOADING_VIEW_TYPE = 1
        const val VERIFY_PROFILE_TYPE = 2
        const val USER_SHADOW_BANNED_TYPE = 3
    }

    override fun getItemCount(): Int {
       return if(viewModel.userProfile.value?.profile?.isShadowBanned == true) 2
        else items.size
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            position == 0 -> HEADER_VIEW_TYPE
            viewModel.userProfile.value?.profile?.isShadowBanned == true && position == 1 -> USER_SHADOW_BANNED_TYPE
            items[position].isLoading -> LOADING_VIEW_TYPE
            items[position].isVerifyProfileItem -> VERIFY_PROFILE_TYPE
            else -> ITEM_VIEW_TYPE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            HEADER_VIEW_TYPE -> {
                val binding = LikedYouHeaderLayoutBinding.inflate(layoutInflater, parent, false)

                HeaderViewHolder(binding)
            }
            VERIFY_PROFILE_TYPE -> {
                val binding = VerifyProfileItemBinding.inflate(layoutInflater, parent, false)
                VerifyProfileViewHolder(binding)
            }
            ITEM_VIEW_TYPE -> {
                val binding = LikedYouItemBinding.inflate(layoutInflater, parent, false)
                LikedYouViewHolder(binding)
            }
            USER_SHADOW_BANNED_TYPE -> ShadowBannedViewHolder.from(parent)
            else -> {
                val binding = LikedYouLoadingBinding.inflate(layoutInflater, parent, false)
                LoadingViewHolder(binding)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            HEADER_VIEW_TYPE -> (holder as HeaderViewHolder).bind(createAdTooltip(holder.binding.root.context))
            VERIFY_PROFILE_TYPE -> (holder as VerifyProfileViewHolder).bind(viewModel.userProfile.value)
            ITEM_VIEW_TYPE -> (holder as LikedYouViewHolder).bind(items[position])
            LOADING_VIEW_TYPE -> (holder as LoadingViewHolder)
            USER_SHADOW_BANNED_TYPE -> (holder as ShadowBannedViewHolder).bind { binding ->
            binding.shadowLockBtn.setOnSingleClickListener{
             onAddPhotoClicked()
            }
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    fun isLoadingItem(position: Int): Boolean {
        return items[position].isLoading
    }

    fun isVerifyProfile(position: Int): Boolean {
        return items[position].isVerifyProfileItem
    }

    override fun setData(data: List<UserLikesModel>) {
        val currentUser = viewModel.userProfile.value
        val newData = data.toMutableList()
        newData.add(0, UserLikesModel.getEmptyUser())

        //add verify your profile item if conditions are met
        if(newData.size > 1 &&
            currentUser?.gender == GenderType.WOMAN.value &&
            currentUser?.premiumType == null &&
            currentUser?.badge2 != Badge2Status.APPROVED.status &&
            currentUser?.userAuxiliaryData?.firstTimeVerifiedDate == null) {

            newData.add(1, UserLikesModel.getEmptyUser().apply { isVerifyProfileItem = true })
        }

        val diffCallback = DiffUtilCallBack(items, newData)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(newData)
        diffResult.dispatchUpdatesTo(this)
    }

    fun addLoadingItem() {
        val loadingItem = UserLikesModel.getEmptyUser()
        loadingItem.isLoading = true
        items.add(loadingItem)
        notifyItemInserted(itemCount - 1)
    }

    fun removeLoadingItem() {
        if (items.last().isLoading) {
            items.removeAt(itemCount - 1)
            notifyItemRemoved(itemCount - 1)
        }
    }

    inner class HeaderViewHolder(val binding: LikedYouHeaderLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.infoBtn.setOnSingleClickListener {
                infoClickListener.invoke(Pair(it.x + it.height, it.y + it.width))
            }

            binding.adBtn.setOnSingleClickListener {
                onAdButtonClicked()
            }

            binding.adProgressBar.setOnSingleClickListener {
                onAdButtonClicked()
            }
        }

        fun bind(adTooltip: Balloon) {
            val hasLikesExpiration = viewModel.userProfile.value?.settings?.limits?.interactionExpiration?.like != null
            val adsWatchedCount = viewModel.userProfile.value?.userAuxiliaryData?.likedYouRewardedAdsWatchCount ?:0
            val adsCount = viewModel.userProfile.value?.settings?.rewardedAdUnblurAdsCount
            val isAdProgressVisible = adsWatchedCount >=1

            binding.infoBtn.visibility = if(hasLikesExpiration) View.VISIBLE else View.GONE
            binding.adBtn.isVisible = viewModel.showAdIcon && !isAdProgressVisible
            setVisibility(binding.adProgressBar,viewModel.showAdIcon && isAdProgressVisible)
            binding.likesCount.isVisible = viewModel.likesCount > 0
            binding.likesCount.text = when (viewModel.likesCount) {
                in 1..99 -> { viewModel.likesCount.toString() }
                else -> { "99+" }
            }
            binding.adProgressBar.apply {
                adsCount?.toFloat()?.let { setMax(it) }
                setProgress(adsWatchedCount.toFloat())
            }

            if(viewModel.showAdIcon){
                val lastShownTime = viewModel.duaSharedPrefs.getAdTooltipLastShownTime()

                val currentTime = System.currentTimeMillis()

                val timeDifference = currentTime - lastShownTime

                // Check if 24 hours have passed since the last showing
                val twentyFourHours = if(BuildConfig.DEBUG) 2  * 60 * 1000  else 24 * 60 * 60 * 1000// 24 hours in milliseconds
                if (timeDifference >= twentyFourHours) {
                    binding.adBtn.doOnNextLayout {
                        adTooltip.showAlignBottom(binding.adBtn)
                    }
                    viewModel.duaSharedPrefs.setAdTooltipLastShownTime(currentTime)
                }
            }
        }

    }

    inner class LikedYouViewHolder(val binding: LikedYouItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.image.setOnSingleClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    handlers.onItemClick(items[position])
                }
            }
        }

        fun bind(model: UserLikesModel) {
            val isPremiumAvailable = DuaApplication.instance.getPremiumAvailable()

            imageUrlCircleFullResolution(binding.image, model.user.profile.thumbnailUrl ?: model.user.profile.pictureUrl)
            val isPremium = viewModel.userRepo.user.value?.premiumType != null

            val name = if (isPremium || !model.isBlured) model.user.firstName + ", " else model.user.firstName.substring(0, 2) + " ..."
            binding.txtName.text = name

            binding.txtAge.isVisible = isPremium || !model.isBlured

            binding.txtAge.text = model.user.age.toString()
            binding.dot.visibility = if (model.showSeen) View.VISIBLE else View.GONE

            binding.badge1Img.visibility = if (model.user.badge2.equals(Badge2Status.APPROVED.status)) View.VISIBLE else View.GONE

            val showStar = ((viewModel.userRepo.user.value?.premiumType != null || !isPremiumAvailable) && model.type == InteractionType.SUPER_LIKE.value)
            binding.star.visibility = if (showStar) View.VISIBLE else View.GONE
        }
    }

    inner class VerifyProfileViewHolder(val binding: VerifyProfileItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.verifyNowBtn.setOnSingleClickListener {
                onVerifyNowClicked()
            }
        }

        fun bind(model: UserModel?) {
            if (model == null) return

            when(model.badge2) {
               Badge2Status.PROCESSING.status -> {
                   val constraintSet = ConstraintSet()
                   constraintSet.clone(binding.root)
                   constraintSet.connect(R.id.container, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                   constraintSet.applyTo(binding.root)

                   binding.title.setText(R.string.verification_processing)
                   binding.description.visibility = View.GONE
                   binding.verifyNowBtn.visibility = View.GONE
               }
               else -> {
                   binding.title.setText(R.string.liked_you_verify)
                   binding.description.visibility = View.VISIBLE
                   binding.verifyNowBtn.visibility = View.VISIBLE
               }
           }
        }
    }

    private class LoadingViewHolder(binding: LikedYouLoadingBinding) :
        RecyclerView.ViewHolder(binding.root)

    inner class Handlers {
        fun onItemClick(model: UserLikesModel) {
            viewModel.onItemClick(model)
        }
    }

    inner class DiffUtilCallBack(
        private val oldList: List<UserLikesModel>,
        private val newList: List<UserLikesModel>
    ) : DiffUtil.Callback() {
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].isBlured == newList[newItemPosition].isBlured &&
                    oldList[oldItemPosition].id == newList[newItemPosition].id &&
                    oldList[oldItemPosition].isVerifyProfileItem == newList[newItemPosition].isVerifyProfileItem &&
                    oldList[oldItemPosition].user.profile.thumbnailUrl == newList[newItemPosition].user.profile.thumbnailUrl &&
                    oldList[oldItemPosition].user.profile.pictureUrl == newList[newItemPosition].user.profile.pictureUrl &&
                    oldList[oldItemPosition].user.badge2 == newList[newItemPosition].user.badge2 &&
                    oldList[oldItemPosition].seen == newList[newItemPosition].seen
        }

    }
}