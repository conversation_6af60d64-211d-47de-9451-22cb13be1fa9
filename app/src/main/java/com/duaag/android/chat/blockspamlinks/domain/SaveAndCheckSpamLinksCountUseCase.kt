package com.duaag.android.chat.blockspamlinks.domain

import com.duaag.android.chat.blockspamlinks.data.db.models.SpamLinkModel
import com.duaag.android.chat.blockspamlinks.utils.convertHourToMilliSeconds
import com.duaag.android.chat.blockspamlinks.utils.getOnlyLinks
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class SaveAndCheckSpamLinksCountUseCase @Inject constructor(
    private val spamLinkRepository: SpamLinkRepository
) {
    suspend operator fun invoke(message: String, limitCount:Long, hours: Long): Flow<Boolean> = flow  {
        val urls = message.getOnlyLinks()
        val currentTime = System.currentTimeMillis()

        if (urls.isEmpty()) {
            emit(false)
            spamLinkRepository.deleteSpamLinks(currentTime - hours.convertHourToMilliSeconds(hours))
            return@flow
        }
        val linksCount = spamLinkRepository.getSpamLinks(currentTime - hours.convertHourToMilliSeconds(hours))
        urls.forEach {
            spamLinkRepository.insertSpamLink(SpamLinkModel(url = it, time = currentTime))
        }
        if ((linksCount.size + urls.size) >= limitCount) {
            emit(true)
        } else {
            emit(false)
        }
        spamLinkRepository.deleteSpamLinks(currentTime - hours.convertHourToMilliSeconds(hours))
    }
}