package com.duaag.android.chat.model

import androidx.annotation.Keep
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.duaag.android.firebase.model.UserMatchesNotificationModel
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.InteractionsMatchesModel
import com.duaag.android.home.models.RecommendedUserModel
import com.google.gson.annotations.SerializedName
import java.util.*

@Keep
@Entity(tableName = "matches")
data class UserMatchesModel(
        @SerializedName("id")
        @PrimaryKey
        val id: Int,
        @SerializedName("time")
        val time: Long,
        @SerializedName("user")
        @Embedded(prefix = "match_")
        val user: UserModelMatch,
        var seen: Int,
        @SerializedName("type")
        val type: String?,
        @SerializedName("receivedSuperMatch")
        val receivedSuperMatch: Int,
        @SerializedName("sentSuperMatch")
        val sentSuperMatch: Int,
        @SerializedName("isDeleted")
        var isDeleted: Int,
        var nextCursor: String? = null
) {

    @Ignore
    var isEmpty: Boolean = false
    @Ignore
    var isLoading: Boolean = false
    val showSeen: Boolean get() = seen == 0
    val getIsDeleted: Boolean get() = isDeleted == 1
    val showType: Boolean get() = receivedSuperMatch == 1

    companion object {
        fun getEmptyUser(): UserMatchesModel {
            val profile = MatchPictures("", "")
            val model = UserModelMatch(0, "", "", 0, profile,false,null)
            return UserMatchesModel(0, 0, model, 0, "", receivedSuperMatch = 0, sentSuperMatch = 0, isDeleted = 0)
        }

        fun convertToUserMatchesModel(item: UserMatchesNotificationModel?): UserMatchesModel? {
            item?.let {
                val profile = MatchPictures(item.user.profile.pictureUrl, item.user.profile.pictureUrl)
                val model = UserModelMatch(item.user.id, item.user.cognitoUserId, item.user.firstName, item.user.age, profile,item.user.hasBadge1,item.user.badge2)
                return UserMatchesModel(item.id!!, item.time, model, 0, item.type, if (item.receivedSuperMatch) 1 else 0, if (item.sentSuperMatch) 1 else 0, 0)
            } ?: kotlin.run {
                return null
            }
        }

        fun convertToUserMatchesModel(item: InteractionsMatchesModel, userModel: RecommendedUserModel, interactionType: InteractionType): UserMatchesModel {
            val profile = MatchPictures(userModel.profile.pictureUrl!!, userModel.profile.pictureUrl)
            val model = UserModelMatch(item.userId, userModel.cognitoUserId!!, userModel.firstName, userModel.age, profile,userModel.hasBadge1,userModel.badge2)
            return UserMatchesModel(item.id!!, Date().time, model, 0, interactionType.value, if (item.receivedSuperMatch) 1 else 0, if (item.sentSuperMatch) 1 else 0, 0)
        }
    }

}

data class
UserModelMatch(
        @SerializedName("id")
        val id: Int,
        @SerializedName("cognitoUserId")
        val cognitoUserId: String,
        @SerializedName("firstName")
        val firstName: String,
        @SerializedName("age")
        val age: Int?,
        @SerializedName("profile")
        @Embedded(prefix = "pictures_")
        val profile: MatchPictures,
        @SerializedName("hasBadge1")
        var hasBadge1: Boolean,
        @SerializedName("badge2")
        var badge2: String?

)

data class MatchPictures(
        @SerializedName("pictureUrl")
        val pictureUrl: String,
        @SerializedName("thumbnailUrl")
        val thumbnailUrl: String?
)