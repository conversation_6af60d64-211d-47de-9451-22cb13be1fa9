package com.duaag.android.sharedprefs

import android.content.Context
import android.content.pm.PackageManager
import android.provider.Settings
import androidx.annotation.StringRes
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.data.prefs.SharedPrefsUtils
import com.duaag.android.events_tracker.models.FaceBookDeepLinkBodyModel
import com.duaag.android.firebase.model.ConsumableRewardGivenModel
import com.duaag.android.firebase.model.PushBadge2VerificationModel
import com.duaag.android.home.models.DontLetGoOfferAvailableResponseModel
import com.duaag.android.home.models.PaymentType
import com.duaag.android.premium_subscription.models.PremiumOfferId
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationModel
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationType
import com.google.gson.Gson
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DuaSharedPrefs @Inject constructor(val context: Context) : SharedPrefsUtils(context) {

    fun setUserCognitoId(cognitoId: String) {
        setStringPreference(R.string.pref_dua_user_cognito_id, cognitoId)
    }

    fun getUserCognitoId(): String? {
        return getStringPreference(R.string.pref_dua_user_cognito_id)
    }

    fun setNotificationToken(deviceToke: String) {
        setStringPreference(R.string.pref_dua_notification_token, deviceToke)
    }

    fun getNotificationToken(): String {
        return getStringPreference(R.string.pref_dua_notification_token)!!
    }

    fun getDeviceId(context: Context): String {
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    }

    fun getCurrentReferral(): String {
        return getStringPreference(R.string.pref_dua_current_referral) ?: ""
    }

    fun setCurrentReferral(created: String) {
        setStringPreference(R.string.pref_dua_current_referral, created)
    }

    fun getRetrieveReferral(): String {
        return getStringPreference(R.string.pref_dua_retrieve_referral) ?: ""
    }

    fun setRetrieveReferral(created: String) {
        setStringPreference(R.string.pref_dua_retrieve_referral, created)
    }

    fun getInfluencerName(): String {
        return getStringPreference(R.string.pref_dua_influncer_name) ?: ""
    }

    fun setInfluencerName(influencerName: String) {
        setStringPreference(R.string.pref_dua_influncer_name, influencerName)
    }

    fun getPremiumOfferIdData(): PremiumOfferId? {
        val json = getStringPreference(R.string.pref_dua_premium_offer_id, isPermanent = false)
        return Gson().fromJson(json, PremiumOfferId::class.java)
    }

    fun setPremiumOfferIdData(offerIdData: PremiumOfferId?) {
        val json = Gson().toJson(offerIdData)
        setStringPreference(R.string.pref_dua_premium_offer_id, json, isPermanent = false)
    }

    fun getPurchaselyPaywallId(): String? {
        val placementId = getStringPreference(R.string.pref_dua_placement_id, isPermanent = false)
        return placementId
    }

    fun setPurchaselyPaywallId(placementId: String?) {
        setStringPreference(R.string.pref_dua_placement_id, placementId, isPermanent = false)
    }

    fun getForcePurchaselyPaywallId(): String? {
        val placementId = getStringPreference(R.string.pref_dua_force_placement_id, isPermanent = false)
        return placementId
    }

    fun setForcePurchaselyPaywallId(placementId: String?) {
        setStringPreference(R.string.pref_dua_force_placement_id, placementId, isPermanent = false)
    }

    fun getBoostPurchaselyPaywallId(): String? {
        val placementId = getStringPreference(R.string.pref_dua_boost_placement_id, isPermanent = false)
        return placementId
    }

    fun setBoostPurchaselyPaywallId(placementId: String?) {
        setStringPreference(R.string.pref_dua_boost_placement_id, placementId, isPermanent = false)
    }

    fun getActionLink(): String {
        return getStringPreference(R.string.pref_dua_action_link) ?: ""
    }

    fun setActionLink(string: String?) {
        setStringPreference(R.string.pref_dua_action_link, string)
    }

    fun getCurrentReferralLastTimeSaved(): Long {
        return getLongPreference(R.string.pref_dua_current_referral_last_saved_time, 0L)
    }

    fun setCurrentReferralLastTimeSaved(time: Long) {
        setLongPreference(R.string.pref_dua_current_referral_last_saved_time, time)
    }

    fun getDotNotificationSharedPrefs() = DotNotificationSharedPrefs(this)

    fun getPushNotifications(): List<PushNotificationModel> {
        return listOf(PushNotificationModel(PushNotificationType.NEW_MESSAGE, getBooleanPreference(R.string.pref_dua_push_notifications_conversations, true)), PushNotificationModel(PushNotificationType.NEW_LIKES, getBooleanPreference(R.string.pref_dua_push_notifications_liked_you, true)), PushNotificationModel(PushNotificationType.NEW_MATCHES, getBooleanPreference(R.string.pref_dua_push_notifications_matches, true)), PushNotificationModel(PushNotificationType.INSTA_CHATS, getBooleanPreference(R.string.pref_dua_push_notifications_insta_chats, true)))
    }

    fun isPushMessageNotificationsEnables(): Boolean = getBooleanPreference(R.string.pref_dua_push_notifications_conversations, true)

    fun setPushNotifications(list: List<PushNotificationModel>) {
        list.forEach {
            when (it.name) {
                PushNotificationType.NEW_MESSAGE -> setBooleanPreference(R.string.pref_dua_push_notifications_conversations, it.isChecked!!)
                PushNotificationType.NEW_LIKES -> setBooleanPreference(R.string.pref_dua_push_notifications_liked_you, it.isChecked!!)
                PushNotificationType.NEW_MATCHES -> setBooleanPreference(R.string.pref_dua_push_notifications_matches, it.isChecked!!)
                PushNotificationType.INSTA_CHATS -> setBooleanPreference(R.string.pref_dua_push_notifications_insta_chats, it.isChecked!!)
                PushNotificationType.NEW_OFFER -> setBooleanPreference(R.string.pref_dua_push_notifications_offers, it.isChecked!!)
                PushNotificationType.RMOD -> setBooleanPreference(R.string.pref_dua_push_notifications_rmod, it.isChecked!!)
                PushNotificationType.PROFILE_VISITS -> setBooleanPreference(R.string.pref_dua_push_notifications_profile_visits, it.isChecked!!)
                PushNotificationType.BENEFIT_ALERTS -> setBooleanPreference(R.string.pref_dua_push_notifications_benefit_alerts, it.isChecked!!)
                PushNotificationType.PROFILE_INTRO -> setBooleanPreference(R.string.pref_dua_push_notifications_profile_intro, it.isChecked!!)
                PushNotificationType.OTHER -> setBooleanPreference(R.string.pref_dua_push_notifications_other, it.isChecked!!)
            }
        }
    }

    fun isUnregisteredUsersTopic(): Boolean {
        return getBooleanPreference(R.string.pref_dua_unregistered_users, false)
    }

    fun setUnregisteredUsersTopic(created: Boolean) {
        setBooleanPreference(R.string.pref_dua_unregistered_users, created)
    }


    fun isRegisteredRemoteConfigTopic(): Boolean {
        return getBooleanPreference(R.string.pref_dua_remote_config, false)
    }

    fun setRegisteredRemoteConfigTopic(created: Boolean) {
        setBooleanPreference(R.string.pref_dua_remote_config, created)
    }

    fun getRemoteConfigState(): Boolean {
        return getBooleanPreference(R.string.pref_dua_config_state, false)
    }

    fun settRemoteConfigState(created: Boolean) {
        setBooleanPreference(R.string.pref_dua_config_state, created)
    }

    /*
    0: The APP is First Install
    1: The APP run once time
    2: The APP is Updated
    */
    fun isTheAppUpdated(): Boolean {
        val result: Int
        val currentVersionCode: Int = BuildConfig.VERSION_CODE
        val lastVersionCode: Int = getIntPreference(R.string.first_timerun, -1)
        result = if (lastVersionCode == -1) 0 else if (lastVersionCode == currentVersionCode) 1 else 2
        setIntPreference(R.string.first_timerun, currentVersionCode)
        return result == 2
    }

    /**
     * This function its for special cases , otherwise use #setLongPreference
     * */
    fun setSpecialLongPreference(@StringRes keyRes: Int, value: Long) {
        setLongPreference(keyRes, value)
    }

    /**
     * This function its for special cases , otherwise use #getLongPreference
     * */
    fun getSpecialLongPreference(@StringRes keyRes: Int, defaultValue: Long): Long {
        return getLongPreference(keyRes, defaultValue)
    }

    val systemVersion = android.os.Build.VERSION.SDK_INT.toString()
    val deviceName = android.os.Build.MODEL

    fun getAppVersion(context: Context): String {
        var version = ""
        try {
            val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            version = pInfo.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return version
    }

    fun setTagsVersion(tagsVersion: Int) {
        setIntPreference(R.string.pref_dua_tags_version, tagsVersion)
    }

    fun getTagsVersion(): Int {
        return getIntPreference(R.string.pref_dua_tags_version, 0)
    }

    fun setClevertapUserPropertiesVersion(tagsVersion: Int) {
        setIntPreference(R.string.pref_dua_clevertap_user_properties, tagsVersion)
    }

    fun getClevertapUserPropertiesVersion(): Int {
        return getIntPreference(R.string.pref_dua_clevertap_user_properties, 0)
    }

    fun setSavedVersionCode(versionCode: Int) {
        setIntPreference(R.string.pref_dua_saved_version_code, versionCode)
    }

    fun isLanguageChange(): Boolean {
        return getBooleanPreference(R.string.pref_dua_language_is_change, false)
    }

    fun setLanguageChange(status: Boolean) {
        setBooleanPreference(R.string.pref_dua_language_is_change, status)
    }

    fun getSavedVersionCode(): Int {
        return getIntPreference(R.string.pref_dua_saved_version_code, -1)
    }

    fun getLastTimeOutOfImpressionsDialogPopUp(): Long {
        return getLongPreference(R.string.pref_dua_saved_last_time_out_of_impressions_popup, 0L)
    }

    fun saveLastTimeOutOfImpressionsDialogPopUp(lastTime: Long) {
        setLongPreference(R.string.pref_dua_saved_last_time_out_of_impressions_popup, lastTime)
    }

    fun getShouldUpdateCards(): Boolean {
        return getBooleanPreference(R.string.pref_dua_should_update_cards, false)
    }

    fun setShouldUpdateCards(status: Boolean) {
        setBooleanPreference(R.string.pref_dua_should_update_cards, status)
    }


    fun getHasShownSwitchThemeDialog(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_shown_switch_theme_dialog, false, isPermanent = true)
    }

    fun setHasShownSwitchThemeDialog(hasShown: Boolean) {
        setBooleanPreference(R.string.pref_dua_has_shown_switch_theme_dialog, hasShown, isPermanent = true)
    }


    fun getHasShownUserGuideInCards(): Boolean {
        return getBooleanPreference(R.string.pref_has_shown_user_guide_in_cards, false, isPermanent = true)
    }

    fun setHasShownUserGuideInCards(hasShown: Boolean) {
        setBooleanPreference(R.string.pref_has_shown_user_guide_in_cards, hasShown, isPermanent = true)
    }

    fun getHasShownUserGuideInProfile(): Boolean {
        return getBooleanPreference(R.string.pref_has_shown_user_guide_in_profile, false, isPermanent = true)
    }

    fun setHasShownUserGuideInProfile(hasShown: Boolean) {
        setBooleanPreference(R.string.pref_has_shown_user_guide_in_profile, hasShown, isPermanent = true)
    }

    fun getAppTheme(): String? {
        return getStringPreference(R.string.pref_dua_app_theme, isPermanent = true)
    }

    fun setAppTheme(theme: String){
        setStringPreference(R.string.pref_dua_app_theme, theme, isPermanent = true)
    }

    fun setIsFirstTimeAppOpen(status: Boolean){
        setBooleanPreference(
            R.string.pref_dua_is_first_time_open,
            status,
            isPermanent = true
        )
    }

    fun isFirstTimeAppOpen():Boolean{
      return  getBooleanPreference(R.string.pref_dua_is_first_time_open,
          defaultValue = true,
          isPermanent = true
      )
    }

    fun setHasOfferBeenShownFromCards(hasBeenShown: Boolean){
        setBooleanPreference(
            R.string.pref_dua_offer_shown_from_cards,
            hasBeenShown,
            isPermanent = false
        )
    }

    fun getHasOfferBeenShownFromCards(): Boolean{
        return  getBooleanPreference(R.string.pref_dua_offer_shown_from_cards,
            defaultValue = false,
            isPermanent = false
        )
    }

    fun setDontLetGoOfferData(specialOffer: DontLetGoOfferAvailableResponseModel?){
        val json = Gson().toJson(specialOffer)
        setStringPreference(R.string.pref_dua_dont_let_go_offer, json, isPermanent = false)
    }

    fun getDontLetGoOfferData(): DontLetGoOfferAvailableResponseModel? {
        val json = getStringPreference(R.string.pref_dua_dont_let_go_offer, isPermanent = false)
        return Gson().fromJson(json, DontLetGoOfferAvailableResponseModel::class.java)
    }

    fun setPremiumSpecialOfferData(specialOffer: SpecialOfferDataModel?){
        val json = Gson().toJson(specialOffer)
        setStringPreference(R.string.pref_dua_special_offer, json, isPermanent = false)
    }

    fun getPremiumSpecialOfferData(): SpecialOfferDataModel? {
        val json = getStringPreference(R.string.pref_dua_special_offer, isPermanent = false)
        return Gson().fromJson(json, SpecialOfferDataModel::class.java)
    }

    fun setBoostResultData(boostResult: BoostResultModel?) {
        val json = Gson().toJson(boostResult)
        setStringPreference(R.string.pref_dua_boost_result_data, json, isPermanent = true)
    }

    fun getBoostResultData(): BoostResultModel? {
        val json = getStringPreference(R.string.pref_dua_boost_result_data, isPermanent = true)
        return Gson().fromJson(json, BoostResultModel::class.java)
    }

    fun setBoostResultDialogShown(hasShown: Boolean) {
        setBooleanPreference(R.string.pref_dua_boost_dialog_shown, hasShown, isPermanent = false)
    }

    fun getHasBoostResultDialogShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_boost_dialog_shown, false, isPermanent = false)
    }

    fun getVerifyProfileBadge2DialogModel(): PushBadge2VerificationModel? {
        val json = getStringPreference(R.string.pref_dua_verify_profile_badge2_model, isPermanent = false)
        return Gson().fromJson(json, PushBadge2VerificationModel::class.java)    }

    fun setVerifyProfileBadge2DialogModel(pushBadge2VerificationModel: PushBadge2VerificationModel?) {
        val json = Gson().toJson(pushBadge2VerificationModel)
        setStringPreference(R.string.pref_dua_verify_profile_badge2_model, json, isPermanent = false)
    }

    fun setIsLoggedInUserPremium(isPremium: Boolean) {
        setBooleanPreference(R.string.pref_dua_is_logged_in_user_premium, isPremium, isPermanent = false)
    }

    fun isLoggedInUserPremium(): Boolean {
        return getBooleanPreference(R.string.pref_dua_is_logged_in_user_premium, false, isPermanent = false)
    }

    fun setIsLoggedInUserFreeStarterExperience(isFreeStarterExperience: Boolean) {
        setBooleanPreference(R.string.pref_dua_is_logged_in_user_free_experience, isFreeStarterExperience, isPermanent = false)
    }

    fun isLoggedInUserFreeStarterExperience(): Boolean {
        return getBooleanPreference(R.string.pref_dua_is_logged_in_user_free_experience, false, isPermanent = false)
    }


    fun setDontLetGoOfferShown(hasShown: Boolean) {
        setBooleanPreference(R.string.pref_dua_show_dont_let_go_offer, hasShown)
    }

    fun getDontLetGoOfferShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_show_dont_let_go_offer, true)
    }

    fun setIsRadiusExtended(isRadiusExtended: Boolean) {
        setBooleanPreference(R.string.pref_dua_radius_extended, isRadiusExtended)
    }

    fun getIsRadiusExtended(): Boolean {
        return getBooleanPreference(R.string.pref_dua_radius_extended, false)
    }

    fun setCalledOnLoginCleverTap(hasCalled: Boolean) {
        setBooleanPreference(R.string.pref_dua_has_called_on_login_clever_tap, hasCalled)
    }

    fun setNotificationCalledOnLoginClevertap(hasCalled:Boolean) {
        setBooleanPreference(R.string.pref_dua_has_called_notifications_on_login,hasCalled)
    }

    fun getCalledOnLoginCleverTap(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_called_on_login_clever_tap, false)
    }

    fun getNotificationCalledOnLoginClevertap(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_called_notifications_on_login,false)
    }

    fun getLastSavedAdvertiseId(): String? {
        val value = getStringPreference(R.string.pref_dua_last_saved_advertise_id, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setLastSavedAdvertiseId(advertiseI: String?){
        setStringPreference(R.string.pref_dua_last_saved_advertise_id, advertiseI, isPermanent = false)
    }

    fun getPreSelectedCommunity(): String? {
        val value = getStringPreference(R.string.pref_dua_pre_selected_community, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setPreSelectedCommunity(community: String?){
        setStringPreference(R.string.pref_dua_pre_selected_community, community, isPermanent = false)
    }

    fun getUserCommunityId(): String? {
        val value = getStringPreference(R.string.pref_dua_user_community_id, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setUserCommunityId(id: String?) {
        setStringPreference(R.string.pref_dua_user_community_id, id, isPermanent = false)
    }

    fun getUserCommunityName(): String? {
        val value = getStringPreference(R.string.pref_dua_user_community_name, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setUserCommunityName(name: String?) {
        setStringPreference(R.string.pref_dua_user_community_name, name, isPermanent = false)
    }

    fun getUserCommunityNameBeforeChange(): String? {
        val value = getStringPreference(R.string.pref_dua_user_community_name_before_update, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setUserCommunityNameBeforeChange(name: String?) {
        setStringPreference(R.string.pref_dua_user_community_name_before_update, name, isPermanent = false)
    }

    fun getSignUpWithSpottedToken(): String? {
        val value = getStringPreference(R.string.pref_dua_signup_with_spotted_token, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setSignUpWithSpottedToken(token: String?) {
        setStringPreference(R.string.pref_dua_signup_with_spotted_token, token, isPermanent = false)
    }

    fun getProfileInfoDismissed(): String? {
        val value = getStringPreference(R.string.pref_dua_profile_info_dismissed, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setTimeDismissedByTypeId(value: String?) {
        setStringPreference(R.string.pref_dua_profile_info_dismissed, value, isPermanent = false)
    }

    fun getProfileActivitiesDismissed(): String? {
        val value = getStringPreference(R.string.pref_dua_profile_activities_dismissed, isPermanent = false)
        return if (value.isNullOrBlank()) null else value
    }

    fun setProfileActivitiesTimeDismissedByTypeId(value: String?) {
        setStringPreference(R.string.pref_dua_profile_activities_dismissed, value, isPermanent = false)
    }


    fun showSetupAccountCredentials(): Boolean {
         return getBooleanPreference(
             R.string.pref_dua_setup_account_credentials,
             false,
             isPermanent = false
         )
    }

    fun setShowSetupAccountCredentials(status: Boolean){
        setBooleanPreference(
            R.string.pref_dua_setup_account_credentials,
            status,
            isPermanent = false
        )
    }

    fun getProfileVisitedCognitoId(): String? {
         return getStringPreference(
             R.string.pref_dua_new_profile_visited,
             isPermanent = false
         )
    }

    fun setProfileVisitedCognitoId(cognitoId: String?){
        setStringPreference(
            R.string.pref_dua_new_profile_visited,
            cognitoId,
            isPermanent = false
        )
    }

    fun getRealTimeClevertapShowOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_show_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapShowOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_show_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapPremiumOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_premium_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapPremiumOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_premium_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapBoostOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_boost_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapBoostOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_boost_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapInstachatOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_instachat_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapInstachatOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_instachat_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapImpressionsOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_impressions_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapImpressionsOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_impressions_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapUndoOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_undo_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapUndoOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_undo_offer, json, isPermanent = false)
    }

    fun getRealTimeClevertapFlightOffer() : RealTimeClevertapOfferModel? {
        val json = getStringPreference(R.string.pref_dua_clevertap_real_time_flight_offer, isPermanent = false)
        return Gson().fromJson(json, RealTimeClevertapOfferModel::class.java)
    }

    fun setRealTimeClevertapFlightOffer(offer: RealTimeClevertapOfferModel?) {
        val json = Gson().toJson(offer)
        setStringPreference(R.string.pref_dua_clevertap_real_time_flight_offer, json, isPermanent = false)
    }

    fun getConsumableRewardModel() : ConsumableRewardGivenModel? {
        val json = getStringPreference(R.string.pref_dua_consumable_reward_given, isPermanent = false)
        return Gson().fromJson(json, ConsumableRewardGivenModel::class.java)
    }
    fun setConsumableRewardModel(consumableRewardGivenModel: ConsumableRewardGivenModel?) {
        val json = Gson().toJson(consumableRewardGivenModel)
        setStringPreference(R.string.pref_dua_consumable_reward_given, json, isPermanent = false)
    }

    fun getRmodGenerated(): Boolean {
        return getBooleanPreference(R.string.pref_dua_rmod_generated, false, isPermanent = false)
    }
    fun setRmodGenerated(hasBeenGenerated: Boolean) {
        setBooleanPreference(R.string.pref_dua_rmod_generated, hasBeenGenerated, isPermanent = false)
    }

    fun setPaymentTypePaywall(paymentType: PaymentType?) {
        setStringPreference(R.string.pref_dua_payment_type,paymentType?.name,isPermanent = false)
    }
    fun getPaymentTypePaywall() : String? {
        return getStringPreference(R.string.pref_dua_payment_type,false)
    }
    fun getUserProfileTooltipShown(): Boolean {
         return getBooleanPreference(
             R.string.pref_dua_user_profile_tooltip,
             false,
             isPermanent = false
         )
    }

    fun setUserProfileTooltipShown(hasShown: Boolean){
        setBooleanPreference(
            R.string.pref_dua_user_profile_tooltip,
            hasShown,
            isPermanent = false
        )
    }

    fun deleteAll() {
        val lastToken = getNotificationToken()
        val savedVersionCode = getSavedVersionCode()

        clearPreferences()
        setNotificationToken(lastToken)
        setSavedVersionCode(savedVersionCode)
    }

    fun setAuthMethode(authMethod: String?) {
        setStringPreference(R.string.pref_dua_auth_methode, authMethod)
    }

    fun getAuthMethode(): String? {
        return getStringPreference(R.string.pref_dua_auth_methode)
    }

    fun setHasEverSignedIn(hasLoggedIn: Boolean) {
        setBooleanPreference(R.string.pref_dua_has_ever_logged_in, hasLoggedIn, true)
    }

    fun getHasEverSignedIn(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_ever_logged_in, false, true)
    }

    fun setPremiumOrderIdRetried(orderId: String) {
        setStringPreference(R.string.pref_dua_retried_purchase_id, orderId, false)
    }

    fun getPremiumOrderIdRetried(): String? {
        return getStringPreference(R.string.pref_dua_retried_purchase_id, false)
    }
    fun isNewIndicatorShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_new_indicator, false,isPermanent = true)
    }

    fun showNewIndicator() {
        setBooleanPreference(R.string.pref_dua_new_indicator, true,isPermanent = true)
    }
    fun isCrossPathIntroShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_cross_path_intro, false,isPermanent = true)
    }

    fun showCrossPathIntro() {
        setBooleanPreference(R.string.pref_dua_cross_path_intro, true,isPermanent = true)
    }
    fun isUserActivityStatusTooltipShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_user_activity_status, false,isPermanent = true)
    }

    fun showUserActivityStatusTooltip() {
        setBooleanPreference(R.string.pref_dua_user_activity_status, true,isPermanent = true)
    }

    fun isPrivatePhotosTooltipShown(): Boolean {
        return getBooleanPreference(R.string.pref_dua_private_photos, false,isPermanent = true)
    }

    fun showPrivatePhotosTooltip() {
        setBooleanPreference(R.string.pref_dua_private_photos, true,isPermanent = true)
    }
    fun isFirstLoginAfterInstall(): Boolean {
        return getBooleanPreference(R.string.pref_dua_new_indicator, true,isPermanent = true)
    }

    fun firstLoginAfterInstall() {
        setBooleanPreference(R.string.pref_dua_new_indicator, false,isPermanent = true)
    }

    fun getAdTooltipLastShownTime(): Long {
        return getLongPreference(R.string.pref_dua_ad_tooltip, 0,true)
    }

    fun setAdTooltipLastShownTime(timestamp: Long) {
        setLongPreference(R.string.pref_dua_ad_tooltip, timestamp,true)
    }
    fun hasSeenShadowDialog(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_seen_shadow_dialog, true,isPermanent = true)
    }
    fun setHasSeenShadowDialog(hasSeen: Boolean) {
        setBooleanPreference(R.string.pref_dua_has_seen_shadow_dialog, hasSeen,isPermanent = true)
    }

    fun getLastReceivedNotificationTime(): Long {
        return getLongPreference(R.string.pref_dua_last_received_notification_time, -1,false)
    }

    fun setLastReceivedNotificationTime(timestamp: Long) {
        setLongPreference(R.string.pref_dua_last_received_notification_time, timestamp,false)
    }

    fun setUserGender(gender:String) {
        setStringPreference(R.string.pref_dua_user_gender, gender)
    }

    fun getUserGender(): String? {
        return getStringPreference(R.string.pref_dua_user_gender)
    }

    fun setTESTBefore(int: Int) {
        setIntPreference(R.string.test_before, int)
    }

    fun getTESTBefore(): Int {
        return getIntPreference(R.string.test_before, 0)
    }

    fun setTESTLocation(int: Int) {
        setIntPreference(R.string.test_location, int)
    }

    fun getTESTLocation(): Int {
        return getIntPreference(R.string.test_location, 0)
    }

    fun setTESTBeforeGeo(int: Int) {
        setIntPreference(R.string.test_before_geo, int)
    }

    fun getTESTBeforeGeo(): Int {
        return getIntPreference(R.string.test_before_geo, 0)
    }

    fun setTESTLocationGeo(int: Int) {
        setIntPreference(R.string.test_location_geo, int)
    }

    fun getTESTLocationGeo(): Int {
        return getIntPreference(R.string.test_location_geo, 0)
    }

    fun setSyncWorkerCalled(int: Int) {
        setIntPreference(R.string.test_location_sync_worker, int)
    }

    fun getSyncWorkerCalled(): Int {
        return getIntPreference(R.string.test_location_sync_worker, 0)
    }

    fun setSyncLocationSuccess(int: Int) {
        setIntPreference(R.string.test_location_sync, int)
    }

    fun getSyncLocationSuccess(): Int {
        return getIntPreference(R.string.test_location_sync, 0)
    }

    fun setSyncLocationFailed(int: Int) {
        setIntPreference(R.string.test_location_sync_failed, int)
    }

    fun getSyncLocationFailed(): Int {
        return getIntPreference(R.string.test_location_sync_failed, 0)
    }

    fun getHasUserAttendedSunnyHill(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_user_attended_sunny_hill, false)
    }

    fun setHasUserAttendedSunnyHill(hasAttended: Boolean) {
        return setBooleanPreference(R.string.pref_dua_has_user_attended_sunny_hill, hasAttended)
    }

    fun getHasUserJoinedGiveawaySunnyHill(): Boolean {
        return getBooleanPreference(R.string.pref_dua_has_user_joined_giveaway_sunny_hill, false)
    }

    fun setHasUserJoinedGiveawaySunnyHill(hasJoined: Boolean) {
        return setBooleanPreference(R.string.pref_dua_has_user_joined_giveaway_sunny_hill, hasJoined)
    }

    fun getSunnyHillAttendeeId(): String? {
        return getStringPreference(R.string.pref_dua_sunny_hill_attendee,false,)
    }
    fun setSunnyHillAttendeeId(attendeeId: String?) {
        setStringPreference(R.string.pref_dua_sunny_hill_attendee, attendeeId,isPermanent = false)
    }

    fun setShouldClearPaywallCache(shouldClear: Boolean = true) {
        setBooleanPreference(R.string.pref_dua_should_clear_paywall_cache, shouldClear)
    }
    fun getShouldClearPaywallCache(): Boolean {
        return getBooleanPreference(R.string.pref_dua_should_clear_paywall_cache,false)
    }
    fun getVisitorId(): String? {
        return getStringPreference(R.string.pref_dua_visitor_id,true)
    }

    fun saveVisitorId(visitorId: String?) {
        setStringPreference(R.string.pref_dua_visitor_id,visitorId,isPermanent = true)

    }

    fun setThirdPartyAuthGender(gender: String?) {
        setStringPreference(R.string.pref_dua_third_party_gender, gender)
    }

    fun getThirdPartyAuthGender(): String? {
        return getStringPreference(R.string.pref_dua_third_party_gender)
    }

    fun setThirdPartyAuthName(name: String?) {
        setStringPreference(R.string.pref_dua_third_party_name, name)
    }

    fun getThirdPartyAuthName(): String? {
        return getStringPreference(R.string.pref_dua_third_party_name)
    }

    fun setThirdPartyAuthBirthday(birthday: String?) {
        setStringPreference(R.string.pref_dua_third_party_birthday, birthday)
    }

    fun getThirdPartyAuthBirthday(): String? {
        return getStringPreference(R.string.pref_dua_third_party_birthday)
    }

    fun setBadge2VerificationState(state: String?) {
        setStringPreference(R.string.pref_dua_badge2_verification_state, state,isPermanent = false)
    }

    fun getBadge2VerificationState() : String? {
       return  getStringPreference(R.string.pref_dua_badge2_verification_state,false)
    }


    fun setLastActiveBoostPlaceForEvent(lastPlace: String?) {
        setStringPreference(R.string.pref_dua_last_active_boost_place, lastPlace,isPermanent = false)
    }

    fun getLastActiveBoostPlaceForEvent() : String? {
        return  getStringPreference(R.string.pref_dua_last_active_boost_place,false)
    }

    fun setFaceBookDataFromDeepLink(faceBookDeepLinkBodyModel: FaceBookDeepLinkBodyModel?){
        val json = Gson().toJson(faceBookDeepLinkBodyModel)
        setStringPreference(R.string.pref_dua_facebook_data_from_deep_link, json, isPermanent = true)
    }

    fun clearFaceBookDataFromDeepLink(){
        setStringPreference(R.string.pref_dua_facebook_data_from_deep_link, "", isPermanent = true)
    }
    fun getFaceBookDataFromDeepLink(): FaceBookDeepLinkBodyModel? {
        val json = getStringPreference(R.string.pref_dua_facebook_data_from_deep_link, isPermanent = true)
        if (json.isNullOrEmpty()) return null
        return Gson().fromJson(json, FaceBookDeepLinkBodyModel::class.java)
    }

    fun saveSignUpPersistStep( @StringRes resId: Int, value:String?) {
        setStringPreference(resId, value)
    }

    fun getSignUpPersistStep( @StringRes resId: Int) : String? {
        return getStringPreference(resId)
    }

    fun isProfileBuildingInProgress() : Boolean {
        return getBooleanPreference(R.string.pref_dua_is_profile_building_completed,false)
    }

    fun setIsProfileBuildingInProgress(isProfileBuildingInProgress: Boolean) {
        setBooleanPreference(R.string.pref_dua_is_profile_building_completed,isProfileBuildingInProgress)
    }

    fun shouldShowOnboardingPaywall() : Boolean {
        return getBooleanPreference(R.string.pref_dua_should_show_onboarding_paywall,false)
    }

    fun setShouldShowOnboardingPaywall(shouldShow: Boolean) {
        setBooleanPreference(R.string.pref_dua_should_show_onboarding_paywall, shouldShow)
    }
}