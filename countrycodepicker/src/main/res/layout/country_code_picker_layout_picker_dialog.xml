<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_rly"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    >

  <TextView
      android:id="@+id/title_tv"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginStart="10dp"
      android:layout_marginTop="10dp"
      android:layout_marginEnd="10dp"
      android:layout_marginBottom="10dp"
      android:text="@string/select_country"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:textColor="@color/title_primary" />

  <EditText
      android:id="@+id/search_edt"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_below="@+id/title_tv"
      android:hint="@string/search_hint"
      android:imeOptions="flagNoExtractUi"
      android:maxLines="1"
      android:singleLine="true"
      android:textColorHint="@color/title_secondary"
      android:backgroundTint="@color/title_primary"
      android:textColor="@color/title_primary" />

  <!--<android.support.v7.widget.RecyclerView-->
      <!--android:id="@+id/country_dialog_rv"-->
      <!--android:layout_width="match_parent"-->
      <!--android:layout_height="match_parent"-->
      <!--android:layout_below="@+id/search_edt"-->
      <!--android:dividerHeight="2dp"-->
      <!--/>-->

  <ListView
      android:id="@+id/country_dialog_lv"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_below="@+id/search_edt"
      android:dividerHeight="2dp"
      android:nestedScrollingEnabled="true" />

  <TextView
      android:id="@+id/no_result_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_below="@+id/search_edt"
      android:layout_centerHorizontal="true"
      android:layout_marginTop="50dp"
      android:text="@string/no_result_found"
      android:textColor="@color/title_primary"
      android:visibility="gone"
      />

</RelativeLayout>
