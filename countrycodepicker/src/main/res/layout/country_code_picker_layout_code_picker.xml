<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/country_code_holder_rly"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/click_consumer_rly"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/flag_holder_lly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/rounded_flag_corners"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/selected_country_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/flag_imv"
                android:layout_width="30dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:adjustViewBounds="true"
                android:visibility="gone"
                android:background="@drawable/rounded_flag_corners"
                android:src="@drawable/flag_kosovo" />

        </LinearLayout>

        <TextView
            android:id="@+id/selected_country_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginVertical="16dp"
            android:elevation="2dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/gray_200"
            style="@style/text_style_100"
            android:maxLines="1"
            android:singleLine="true"
            android:text="+383"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/arrow_imv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:elevation="2dp"
            android:src="@drawable/ic_polygon_1"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>