
pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url = uri("https://artifacts.applovin.com/android") }
        maven { url = uri("https://maven.google.com") }
    }
    resolutionStrategy {
        eachPlugin {
            if (requested.id.id.startsWith("androidx.navigation.safeargs")) {
                useModule("androidx.navigation:navigation-safe-args-gradle-plugin:2.8.5")
            }
            if (requested.id.id == "com.applovin.quality") {
                useModule("com.applovin.quality:AppLovinQualityServiceGradlePlugin:5.6.6")
            }
        }
    }
}

include(":app", ":cardstackview", ":countrycodepicker")
