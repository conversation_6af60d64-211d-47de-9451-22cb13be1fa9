// Top-level build file where you can add configuration options common to all sub-projects/modules.
import java.util.Properties
import java.io.FileInputStream


plugins {
    id("com.android.application") version "8.7.3" apply false
    id("com.android.library") version "8.7.3" apply false
    id("org.jetbrains.kotlin.android") version "2.0.0" apply false
    id("com.google.gms.google-services") version "4.4.2" apply false
    id("com.google.firebase.firebase-perf") version "1.4.2" apply false
    id("com.google.firebase.crashlytics") version "3.0.2" apply false
    id("androidx.navigation.safeargs.kotlin") version "2.8.5" apply false
    id("com.applovin.quality") version "5.6.6" apply false
    id("com.google.devtools.ksp") version "2.0.0-1.0.24" apply false
    id("org.jetbrains.kotlin.plugin.compose") version "2.0.0" apply false

}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
        mavenLocal()
        maven { url = uri("https://maven.fpregistry.io/releases") }

        val localPropertiesFile = rootProject.file("localbuild.properties")
        val localProperties = Properties().apply {
            if (localPropertiesFile.exists()) {
                load(FileInputStream(localPropertiesFile))
            }
        }

        val mapboxAccessToken = System.getenv("MAPBOX_ACCESS_TOKEN")
                ?: localProperties["dua_MAPBOX_ACCESS_TOKEN"]?.toString()

        maven {
            url = uri("https://api.mapbox.com/downloads/v2/releases/maven")
            credentials {
                username = "mapbox"
                password = mapboxAccessToken
            }
            authentication {
                create<BasicAuthentication>("basic")
            }
        }
    }
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}

