<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.001265708">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: assembleDuaDevRelease" time="24.573140081">
        
          <failure message="/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in `execute_action'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:255:in `block in execute_action'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:229:in `chdir'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:229:in `execute_action'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:157:in `trigger_action_by_name'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/fast_file.rb:159:in `method_missing'&#10;Fastfile:126:in `block (2 levels) in parsing_binding'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/lane.rb:33:in `call'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:49:in `block in execute'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:45:in `chdir'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/runner.rb:45:in `execute'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/lane_manager.rb:47:in `cruise_lane'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/command_line_handler.rb:36:in `handle'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/commands_generator.rb:109:in `block (2 levels) in run'&#10;/var/lib/gems/2.5.0/gems/commander-4.6.0/lib/commander/command.rb:187:in `call'&#10;/var/lib/gems/2.5.0/gems/commander-4.6.0/lib/commander/command.rb:157:in `run'&#10;/var/lib/gems/2.5.0/gems/commander-4.6.0/lib/commander/runner.rb:444:in `run_active_command'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:76:in `run!'&#10;/var/lib/gems/2.5.0/gems/commander-4.6.0/lib/commander/delegates.rb:18:in `run!'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/commands_generator.rb:353:in `run'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/commands_generator.rb:42:in `start'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/fastlane/lib/fastlane/cli_tools_distributor.rb:122:in `take_off'&#10;/var/lib/gems/2.5.0/gems/fastlane-2.191.0/bin/fastlane:23:in `&lt;top (required)&gt;'&#10;/usr/local/bin/fastlane:23:in `load'&#10;/usr/local/bin/fastlane:23:in `&lt;main&gt;'&#10;&#10;Shell command exited with exit status  instead of 0.&#10;Downloading https://services.gradle.org/distributions/gradle-6.8.3-bin.zip&#10;......................................................................................................&#10;Unzipping /root/.gradle/wrapper/dists/gradle-6.8.3-bin/7ykxq50lst7lb7wx1nijpicxn/gradle-6.8.3-bin.zip to /root/.gradle/wrapper/dists/gradle-6.8.3-bin/7ykxq50lst7lb7wx1nijpicxn&#10;#&#10;# A fatal error has been detected by the Java Runtime Environment:&#10;#&#10;#  SIGSEGV (0xb) at pc=0x0000004015103dff, pid=30, tid=96&#10;#&#10;# JRE version: OpenJDK Runtime Environment 18.9 (11.0.12+7) (build 11.0.12+7)&#10;# Java VM: OpenJDK 64-Bit Server VM 18.9 (11.0.12+7, mixed mode, sharing, tiered, compressed oops, g1 gc, linux-amd64)&#10;# Problematic frame:&#10;# J 1235 c1 java.io.BufferedOutputStream.write([BII)V java.base@11.0.12 (67 bytes) @ 0x0000004015103dff [0x0000004015103da0+0x000000000000005f]&#10;#&#10;# No core dump will be written. Core dumps have been disabled. To enable core dumping, try &quot;ulimit -c unlimited&quot; before starting Java again&#10;#&#10;# An error report file with more information is saved as:&#10;# /dua-android/hs_err_pid30.log&#10;Compiled method (c1)   19816 1235  s    3       java.io.BufferedOutputStream::write (67 bytes)&#10; total in heap  [0x0000004015103b90,0x00000040151047f8] = 3176&#10; relocation     [0x0000004015103d08,0x0000004015103d98] = 144&#10; main code      [0x0000004015103da0,0x00000040151045a0] = 2048&#10; stub code      [0x00000040151045a0,0x0000004015104610] = 112&#10; metadata       [0x0000004015104610,0x0000004015104628] = 24&#10; scopes data    [0x0000004015104628,0x00000040151046e8] = 192&#10; scopes pcs     [0x00000040151046e8,0x00000040151047c8] = 224&#10; dependencies   [0x00000040151047c8,0x00000040151047d0] = 8&#10; nul chk table  [0x00000040151047d0,0x00000040151047f8] = 40&#10;Could not load hsdis-amd64.so; library not loadable; PrintAssembly is disabled&#10;#&#10;# If you would like to submit a bug report, please visit:&#10;#   https://bugreport.java.com/bugreport/crash.jsp&#10;#&#10;qemu: uncaught target signal 6 (Aborted) - core dumped&#10;" />
        
      </testcase>
    
  </testsuite>
</testsuites>
