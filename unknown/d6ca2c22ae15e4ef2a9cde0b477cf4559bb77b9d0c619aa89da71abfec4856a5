<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
  <path
      android:pathData="M50.75,87.19C51.1,91.5 51.85,95.52 53.01,99.14C54.35,103.36 56.23,107.05 58.6,110.11C63.56,116.48 70.6,120 78.44,120C89.26,120 98.69,113.47 102.46,103.36C103.48,100.63 104.04,97.75 104.13,94.8C104.22,91.83 103.84,88.83 102.98,85.91C102.9,85.64 102.82,85.37 102.73,85.1C102.9,84.93 103.08,84.75 103.25,84.57C107.65,79.96 110.19,73.9 110.37,67.72H97.83C98.27,73.99 92.37,79.74 84.52,79.74C84.42,79.74 84.32,79.74 84.22,79.74C97.36,91.29 90.88,107.23 78.43,107.23C64.3,107.23 60.18,85.58 65.36,64.67L65.57,63.97C65.57,63.97 53.88,68.84 51.44,69.71C50.54,75.71 50.3,81.64 50.74,87.19H50.75Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="97.32"
          android:startY="69.51"
          android:endX="60.55"
          android:endY="103.24"
          android:type="linear">
        <item android:offset="0" android:color="#FF016DB4"/>
        <item android:offset="1" android:color="#FF00B5E0"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M69.24,32.82C68.9,28.51 68.14,24.48 66.99,20.86C65.65,16.64 63.76,12.95 61.39,9.9C56.44,3.52 49.39,0 41.55,0C30.73,0 21.3,6.53 17.53,16.64C16.51,19.37 15.95,22.25 15.86,25.2C15.77,28.17 16.16,31.17 17.01,34.09C17.09,34.36 17.17,34.63 17.26,34.9C17.09,35.07 16.92,35.25 16.74,35.43C12.34,40.04 9.8,46.1 9.62,52.28H22.16C21.72,46.01 27.62,40.26 35.48,40.26C35.58,40.26 35.67,40.26 35.77,40.26C22.63,28.71 29.11,12.77 41.56,12.77C55.69,12.77 59.81,34.42 54.63,55.33L54.47,56C54.47,56 66.1,51.16 68.55,50.29C69.45,44.29 69.69,38.36 69.25,32.81L69.24,32.82Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23.45"
          android:startY="51.53"
          android:endX="58.86"
          android:endY="16.34"
          android:type="linear">
        <item android:offset="0" android:color="#FFCC1D9A"/>
        <item android:offset="1" android:color="#FFD862A4"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M9.62,53.93C9.75,57.04 10.4,59.92 11.56,62.49C12.89,65.41 14.82,67.87 17.3,69.81C21.14,72.8 26,74.31 31.76,74.31C38.86,74.31 47.65,72.03 59.45,67.14L65.38,64.68C73.39,61.35 80.42,58.47 86.71,58.47C92.17,58.47 96.38,59.81 97.7,66.59C97.79,67.08 97.85,67.57 97.86,68.06H110.38C110.4,67.4 110.4,66.74 110.38,66.08C110.25,62.97 109.6,60.09 108.43,57.52C107.11,54.6 105.18,52.14 102.69,50.2C98.86,47.21 93.99,45.7 88.24,45.7C81.14,45.7 72.34,47.98 60.54,52.87L54.62,55.33C46.6,58.66 39.58,61.54 33.29,61.54C27.83,61.54 23.62,60.2 22.3,53.42C22.2,52.93 22.15,52.44 22.13,51.96H9.62C9.59,52.61 9.59,53.27 9.62,53.93Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="94.73"
          android:startY="67.43"
          android:endX="24.98"
          android:endY="52.7"
          android:type="linear">
        <item android:offset="0" android:color="#FF016DB4"/>
        <item android:offset="1" android:color="#FFCC1D9A"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
