<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="320dp"
    android:height="180dp"
    android:viewportWidth="320"
    android:viewportHeight="180">
  <group>
    <clip-path
        android:pathData="M0,0h319.74v180h-319.74z"/>
    <path
        android:pathData="M277.68,109.93L264.44,123.87L258.48,130.15L258.32,130.31L251.73,137.25L248.01,141.16L246.38,142.87L244.51,144.85H63.77L63.31,144.36L62.64,143.65L52.56,132.87C52.56,132.87 52.56,132.87 52.56,132.87L50.83,131.02H50.83L50.82,131.01L30.76,109.56C-6.82,69.38 19.25,2.99 74.19,0.11C75.55,0.04 76.93,0 78.31,0C110.39,0 137.91,19.48 149.7,47.25C151.56,51.63 157.7,51.63 159.56,47.25C171.35,19.48 198.88,0 230.95,0C231.62,0 232.29,0.01 232.96,0.03C288.97,1.45 316.28,69.31 277.68,109.93Z"
        android:fillColor="#F4F4F4"/>
    <path
        android:pathData="M276.43,144.85H34.27C34.54,144.53 34.81,144.22 35.09,143.91C35.71,143.21 36.35,142.52 37.03,141.84H37.03C38.07,140.78 39.17,139.74 40.33,138.73C41.89,137.36 43.56,136.02 45.33,134.72C47.07,133.45 48.89,132.21 50.82,131.01C51.86,130.36 52.92,129.72 54.02,129.09C56.73,127.53 59.61,126.03 62.64,124.6C65.34,123.33 68.15,122.11 71.08,120.96C80.66,117.18 91.43,114.02 103.11,111.65C104.3,111.4 105.51,111.17 106.72,110.94C110.75,110.19 114.88,109.52 119.1,108.96C119.42,108.91 119.74,108.87 120.06,108.83C124.9,108.2 129.86,107.69 134.91,107.32C141.57,106.83 148.4,106.58 155.35,106.58C160.17,106.58 164.92,106.7 169.61,106.94C183.21,107.63 196.19,109.29 208.2,111.77H208.21C222.47,114.72 235.38,118.82 246.38,123.83C247.05,124.13 247.71,124.44 248.37,124.75C250.71,125.86 252.96,127.01 255.12,128.21C256.27,128.84 257.38,129.49 258.47,130.15C259.04,130.49 259.59,130.83 260.14,131.18C260.14,131.18 260.14,131.18 260.15,131.18C262.02,132.35 263.8,133.57 265.48,134.81C267.07,135.98 268.57,137.18 269.99,138.4C270.94,139.22 271.85,140.05 272.72,140.9C274.05,142.19 275.29,143.51 276.42,144.85H276.43Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M0.62,135.42H319.12"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M145.21,136.38H186.71C186.71,136.38 207.82,175.38 207.82,175.39C207.56,174.9 201.67,175.41 201.09,175.41C196.86,175.43 192.63,175.46 188.4,175.47C179.57,175.5 170.73,175.49 161.9,175.44C153.57,175.38 145.25,175.03 136.92,175.21C128.36,175.4 119.81,175.88 111.26,176.16C104.93,176.37 98.69,176.39 92.36,176.13C96.34,168.87 100.82,161.86 104.16,154.27C106.15,149.76 107.24,144.72 108.65,139.99C109.01,138.79 109.36,137.58 109.73,136.38C110.09,135.2 145.22,136.38 145.22,136.38H145.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100.33,153.43H104.24"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.2,155.68H103.12"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.67,158.27H101.59"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.36,161H100.28"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.67,163.72H98.59"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.29,166.75H97.21"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.33,169.78H95.25"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89.81,172.8H93.73"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.27,176H92.19"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.18,176.41L91.19,178.99"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.35,176.41L95.36,178.99"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.2,176.35L99.36,179.04"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.87,176.53L104.29,179.38"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M109.92,176.35L108.59,179.33"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.04,176.35L112.71,179.33"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.7,176.28L117.56,179.33"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.77,176.25L121.8,179.36"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.07,176.05L126.43,179.24"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.19,176.05L130.55,179.24"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M135.32,176.05L134.68,179.24"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.2,175.88L139.55,179.07"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M145.17,175.88L144.53,179.07"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M150.31,175.83L149.98,179.07"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.62,175.85L154.86,179.1"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159.09,175.82L159.32,179.07"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.8,175.85L164.38,179.05"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M168.76,175.85L169.34,179.05"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.64,175.52L174.54,178.65"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M178.57,175.52L179.48,178.65"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M183.67,175.7L184.58,178.83"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M188.46,175.52L189.36,178.65"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M192.81,175.7L193.72,178.83"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M197.38,175.52L198.29,178.65"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.92,175.56L203.08,178.61"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M207.3,175.52L208.73,178.45"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M206.02,171.16H210.87"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M204.04,168H208.89"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M202.49,164.83H207.34"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M200.76,161.02H205.61"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M198.45,157.34H203.29"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M196.41,153.8H201.26"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M190.68,143.5H195.53"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M193.76,148.43H198.62"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M188.81,139.84H193.66"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M186.94,136.59H191.55"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M145.21,136.38H186.71C186.71,136.38 207.82,175.38 207.82,175.39C207.56,174.9 201.67,175.41 201.09,175.41C196.86,175.43 192.63,175.46 188.4,175.47C179.57,175.5 170.73,175.49 161.9,175.44C153.57,175.38 145.25,175.03 136.92,175.21C128.36,175.4 119.81,175.88 111.26,176.16C104.93,176.37 98.69,176.39 92.36,176.13C96.34,168.87 100.82,161.86 104.16,154.27C106.15,149.76 107.24,144.72 108.65,139.99C109.01,138.79 109.36,137.58 109.73,136.38C110.09,135.2 145.22,136.38 145.22,136.38H145.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.2,135.42C104.2,135.42 105.06,141.73 110.32,145.43C110.32,145.43 119.98,149.12 128.07,143.8C136.15,138.48 137.11,137.2 137.11,137.2C137.11,137.2 110.26,110.03 105.08,124.86C105.08,124.86 101.98,131.01 104.21,135.42"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M104.2,135.42C104.2,135.42 105.06,141.73 110.32,145.43C110.32,145.43 119.98,149.12 128.07,143.8C136.15,138.48 137.11,137.2 137.11,137.2C137.11,137.2 110.26,110.03 105.08,124.86C105.08,124.86 101.98,131.01 104.21,135.42"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M157.38,149.88C157.38,149.88 179.1,150.23 180.32,148.07C181.53,145.9 179.96,133.32 179.96,133.32L177.42,122.63C177.42,122.63 163.87,124.91 162.58,130C161.28,135.08 155.14,147.84 157.38,149.88Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M133.68,93.31V94.52C133.68,94.52 133.66,101.82 128.31,102.15C122.96,102.49 122.91,94.15 122.91,94.15L124.68,87.9"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M133.68,93.31V94.52C133.68,94.52 133.66,101.82 128.31,102.15C122.96,102.49 122.91,94.15 122.91,94.15L124.68,87.9"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.31,89.97C128.31,89.97 131.94,94.36 135.94,92.39C140.47,90.16 138.73,78.78 138.73,78.78L127.41,78.55L123.72,85.12L124.72,88.34"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M128.31,89.97C128.31,89.97 131.94,94.36 135.94,92.39C140.47,90.16 138.73,78.78 138.73,78.78L127.41,78.55L123.72,85.12L124.72,88.34"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M139.15,118.14C139.06,119.11 134.26,134.84 134.26,134.84C134.26,134.84 132.06,136.82 126.35,134.99C120.63,133.17 116.34,131.15 116.26,128.86C116.18,126.57 112.33,117.44 112.33,117.44C112.33,117.44 110.32,110.19 107.06,110.11C103.79,110.02 101.65,111.77 100.67,112.92C100.67,112.92 94.22,111.89 96.66,107.35L97.77,104.68C97.77,104.68 110.66,96.32 115.09,95.83L122.64,94.52C122.64,94.52 123.2,102.02 127.25,102.51C131.3,103 133.48,97.82 133.48,97.82L134.09,94.82C134.09,94.82 140.46,95.99 142,98.13C142,98.13 148.01,107.66 148.88,112.72C148.88,112.72 154.19,122.06 152.83,124.74C151.47,127.42 143.54,129.1 142.11,127.7C140.69,126.3 139.47,117.53 139.47,117.53L139.15,118.14L139.15,118.14Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M132.12,79.71C132.12,79.71 131.4,81.64 130.59,81.82C130.59,81.82 131.44,83.8 128.52,84.7C128.52,84.7 127.17,81.91 125.46,83.3C123.76,84.69 125.11,87.3 125.92,87.75L124.64,89.1C124.64,89.1 121.01,86.05 122.5,79.49C123.98,72.92 128.92,71.05 135.43,73.14L137.95,74.21C137.95,74.21 141.32,71.17 143.48,74.85C143.68,75.19 143.85,77.26 141.27,78.41C138.54,79.62 132.12,79.71 132.12,79.71V79.71Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M122.14,83.98C122.14,83.98 124.84,77.73 129.1,75.74C133.36,73.76 137.61,74.22 137.61,74.22C137.61,74.22 132.09,65.19 124.14,67.89C116.18,70.58 122.14,83.98 122.14,83.98Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.91,94.15C122.91,94.15 117.37,94.61 113.01,96.14C108.65,97.67 98.85,104.1 97.77,104.68C96.7,105.26 94.22,110.66 98.67,112.73C98.67,112.73 97.91,104.72 110.09,105.76"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122.91,94.15C122.91,94.15 117.37,94.61 113.01,96.14C108.65,97.67 98.85,104.1 97.77,104.68C96.7,105.26 94.22,110.66 98.67,112.73C98.67,112.73 97.91,104.72 110.09,105.76"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.68,94.52C133.68,94.52 139.96,95.38 142.73,98.93C145.5,102.5 154.02,124.2 153.2,124.94C152.37,125.68 149.41,127.9 146.54,128.08C143.68,128.26 141.83,128.08 141.83,128.08L139.43,118.72"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.12,116.78C138.73,118.23 134.85,134.46 134.85,134.46"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M109.99,122.52C109.42,124.68 99.12,145.08 99.12,145.08L90.93,141.49L92.07,136.61L92.21,136.57C95.65,124.67 98.67,112.73 104,110.48C109.33,108.24 111.92,116.78 111.92,116.78L116.75,130.24C119.92,133.49 128.09,135.82 129.67,136.07C131.25,136.32 136.92,134.47 136.92,134.47C136.92,134.47 142.67,133.38 144.46,137.39C146.24,141.41 145.16,147.52 136.56,149.94C127.96,152.36 111.78,155.55 111.78,155.55C111.78,155.55 110.38,153.57 109.42,146.12L133.63,137.46"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M109.99,122.52C109.42,124.68 99.12,145.08 99.12,145.08L90.93,141.49L92.07,136.61L92.21,136.57C95.65,124.67 98.67,112.73 104,110.48C109.33,108.24 111.92,116.78 111.92,116.78L116.75,130.24C119.92,133.49 128.09,135.82 129.67,136.07C131.25,136.32 136.92,134.47 136.92,134.47C136.92,134.47 142.67,133.38 144.46,137.39C146.24,141.41 145.16,147.52 136.56,149.94C127.96,152.36 111.78,155.55 111.78,155.55C111.78,155.55 110.38,153.57 109.42,146.12L133.63,137.46"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.65,97.06C140.65,97.06 134.65,94.32 134.06,94.52C134.06,94.52 132.52,100 130.06,101.65C130.06,101.65 130.95,110.18 130.93,111.63C130.84,118.25 127.96,125.07 127.21,130.07C126.81,132.7 127.39,134.79 128.49,135.74C128.49,135.74 132.85,137.97 134.77,133.96C135.24,132.98 138.85,119.39 138.85,119.39C138.85,119.39 140.2,115.66 139.31,114.29C138.42,112.92 137.24,110.09 137.24,110.09C137.24,110.09 138.83,98.11 140.66,97.07L140.65,97.06Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M137.11,149.85C137.11,149.85 133.64,154.29 125.86,152.75L137.11,149.85Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M137.11,149.85C137.11,149.85 133.64,154.29 125.86,152.75"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.54,142.02L89.5,150.75L95.51,152.26C95.51,152.26 97.52,146.86 98.39,145.12"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M91.54,142.02L89.5,150.75L95.51,152.26C95.51,152.26 97.52,146.86 98.39,145.12"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81,163.07L80.99,165.17C80.99,165.17 83.03,166.63 86.58,165.68C90.14,164.72 91.64,162.86 91.64,162.86L97.05,157.25L97.22,154.48L94.19,156.1L85.79,162.69L81,163.07L81,163.07Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.95,108.86C113.95,108.86 114.93,107.71 115.5,104.46C116.07,101.19 113.95,96.29 113.95,96.29C113.95,96.29 118.98,93.58 122.64,94.52C122.64,94.52 123.78,100.29 126.72,101.74C126.72,101.74 127.76,111.63 124.36,123.98C123.95,125.47 122.1,129 120.75,129.76C119.41,130.52 117.65,130.37 116.75,130.24L111.08,114.41L113.95,108.86V108.86Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M109.42,147.65L101.03,150.22C101.03,150.22 100.43,155.93 102.88,156.3C105.34,156.68 111.08,154.64 111.08,154.64"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M109.42,147.65L101.03,150.22C101.03,150.22 100.43,155.93 102.88,156.3C105.34,156.68 111.08,154.64 111.08,154.64"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.13,111.47C100.24,115.95 119.94,117.54 119.94,117.54C119.94,117.54 123.31,117.85 124.54,118.88C125.77,119.91 129.16,118.9 129.91,118.6C130.67,118.3 131.42,118.88 131.42,118.88C134.76,122.34 141.66,116.51 138.2,113C134.73,109.48 126.41,112.73 126.41,112.73C126.41,112.73 123.98,113.71 117.65,110.41C111.32,107.11 105.06,105.91 105.06,105.91C105.06,105.91 98.03,107.04 99.13,111.47L99.13,111.47Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.96,129.72L164.16,129.58L164.42,129.84C164.45,129.65 164.46,129.62 164.48,129.76C164.51,129.89 164.52,130.02 164.52,130.15C164.44,130.47 164.11,130.51 163.88,130.62C163.62,130.75 163.48,130.81 163.3,131.06C163.07,131.39 163.51,131.71 163.81,131.57C164.64,131.17 165.6,131.43 166.28,130.71C166.66,130.31 166.83,129.95 167.33,129.66C167.6,129.5 167.79,129.32 167.86,128.99C167.98,128.39 167.55,127.67 166.87,127.76C166.22,127.86 166.09,128.55 166.46,129C166.77,129.37 167.29,128.84 166.99,128.47C167.39,128.95 166.59,129.17 166.37,129.35C166.07,129.62 165.94,130.04 165.63,130.29C165.04,130.75 164.12,130.6 163.44,130.92L163.95,131.43C164.11,131.2 164.7,131.24 164.95,131.01C165.16,130.81 165.24,130.54 165.26,130.24C165.29,129.73 165.16,128.9 164.49,128.9C163.98,128.9 163.46,129.54 163.7,130.03C163.92,130.47 164.59,130.42 164.68,129.91C164.76,129.45 164.04,129.24 163.96,129.72V129.72Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M168.61,127.94L168.82,127.79L169.08,128.06C169.1,127.87 169.12,127.84 169.13,127.98C169.16,128.11 169.18,128.24 169.18,128.36C169.09,128.69 168.76,128.73 168.54,128.84C168.27,128.97 168.13,129.03 167.96,129.28C167.73,129.61 168.16,129.93 168.47,129.79C169.29,129.39 170.25,129.65 170.93,128.93C171.31,128.53 171.49,128.17 171.98,127.88C172.26,127.72 172.45,127.54 172.51,127.21C172.63,126.61 172.21,125.89 171.52,125.98C170.87,126.07 170.74,126.77 171.11,127.22C171.42,127.59 171.95,127.06 171.64,126.69C172.04,127.17 171.24,127.39 171.03,127.57C170.73,127.84 170.6,128.26 170.28,128.51C169.7,128.97 168.77,128.81 168.09,129.14L168.6,129.65C168.76,129.42 169.36,129.46 169.6,129.23C169.82,129.03 169.9,128.76 169.92,128.46C169.95,127.95 169.82,127.12 169.15,127.12C168.63,127.12 168.12,127.76 168.36,128.25C168.58,128.69 169.24,128.64 169.33,128.13C169.41,127.66 168.7,127.46 168.61,127.93V127.94Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M173.18,126.19L173.38,126.05L173.64,126.31C173.66,126.12 173.68,126.1 173.7,126.23C173.73,126.36 173.74,126.49 173.74,126.62C173.66,126.94 173.33,126.98 173.1,127.09C172.84,127.22 172.7,127.28 172.52,127.53C172.29,127.86 172.73,128.19 173.03,128.04C173.86,127.64 174.81,127.91 175.49,127.18C175.88,126.78 176.05,126.43 176.55,126.14C176.82,125.97 177.01,125.79 177.07,125.47C177.2,124.86 176.77,124.14 176.08,124.24C175.43,124.33 175.31,125.03 175.68,125.47C175.99,125.84 176.51,125.31 176.21,124.95C176.61,125.43 175.81,125.64 175.59,125.83C175.29,126.09 175.16,126.51 174.85,126.76C174.26,127.22 173.34,127.07 172.65,127.39L173.16,127.9C173.33,127.67 173.92,127.71 174.17,127.49C174.38,127.28 174.46,127.01 174.48,126.71C174.51,126.2 174.38,125.37 173.71,125.37C173.2,125.37 172.68,126.01 172.92,126.5C173.14,126.95 173.8,126.9 173.9,126.39C173.98,125.92 173.26,125.72 173.18,126.19V126.19Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M95.55,151.03C95.03,150.97 94.52,150.85 94.03,150.68C92.81,150.24 90.07,149.55 88.14,151.32C85.58,153.65 79.96,162.03 80.44,163.11C80.92,164.2 84.97,165 88.2,163.53C91.43,162.06 96.99,155.06 97.66,154.84C98.33,154.61 97.12,150.2 96.22,151.1L95.55,151.03H95.55Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M96.77,150.76C95.88,152.52 97.32,157.54 97.32,157.54C97.32,157.54 102.48,157.16 103.11,156.89C103.75,156.62 102.09,150.76 102.09,150.76L102.91,149.62C102.91,149.62 103.42,149.12 101.42,148.24C99.43,147.35 97.48,147.65 97.48,147.65L96.77,150.76Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M158.04,138.3C159.64,135.67 160.6,128.21 160.6,128.21C160.6,128.21 158.21,124.55 156.42,126.78C156.42,126.78 153.14,135.02 152.16,140.98"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M158.04,138.3C159.64,135.67 160.6,128.21 160.6,128.21C160.6,128.21 158.21,124.55 156.42,126.78C156.42,126.78 153.14,135.02 152.16,140.98"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.32,88.98C163.32,88.98 165.5,94.83 167.43,95.4C169.36,95.97 173.22,96.72 174.25,98.59C174.25,98.59 177.38,103.86 177.12,113.74L176.97,122.09C176.97,122.09 168.93,126.8 160.68,126.33L152,118.44"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M163.32,88.98C163.32,88.98 165.5,94.83 167.43,95.4C169.36,95.97 173.22,96.72 174.25,98.59C174.25,98.59 177.38,103.86 177.12,113.74L176.97,122.09C176.97,122.09 168.93,126.8 160.68,126.33L152,118.44"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.82,115.29C152.28,109.88 152.88,102.2 154.29,100.17C155.7,98.14 159.61,98.41 160.16,97.81C160.7,97.21 157.67,91.86 157.67,91.86C157.67,91.86 161.02,87.04 164.98,88.97L172.75,96.97"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M152.82,115.29C152.28,109.88 152.88,102.2 154.29,100.17C155.7,98.14 159.61,98.41 160.16,97.81C160.7,97.21 157.67,91.86 157.67,91.86C157.67,91.86 161.02,87.04 164.98,88.97L172.75,96.97"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M160.29,97.66C160.29,97.66 158.6,100.37 160.42,100.37C163,100.37 166.51,98.69 167.38,95.89"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M160.29,97.66C160.29,97.66 158.6,100.37 160.42,100.37C163,100.37 166.51,98.69 167.38,95.89"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M160.16,89.97C158.87,90.96 157.64,91.48 156.7,91.76C155.62,92.07 154.45,91.67 153.8,90.75C149.81,85.07 154.54,76.88 154.54,76.88C154.54,76.88 168.57,72.96 170.18,94.85"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M160.16,89.97C158.87,90.96 157.64,91.48 156.7,91.76C155.62,92.07 154.45,91.67 153.8,90.75C149.81,85.07 154.54,76.88 154.54,76.88C154.54,76.88 168.57,72.96 170.18,94.85"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M166.63,103.27C166.63,103.27 160.49,116.78 160.16,118.43C160.16,118.43 168.3,125.51 170.26,125.62C172.21,125.73 174.38,105.38 174.38,105.38"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M166.63,103.27C166.63,103.27 160.49,116.78 160.16,118.43C160.16,118.43 168.3,125.51 170.26,125.62C172.21,125.73 174.38,105.38 174.38,105.38"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.42,106C156.42,106 155.26,111.87 155.48,112.85C155.71,113.84 156.95,117.26 156.95,117.26"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M156.42,106C156.42,106 155.26,111.87 155.48,112.85C155.71,113.84 156.95,117.26 156.95,117.26"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.47,128.16L150.85,143.4L155.23,141.15C155.23,141.15 154.26,134.89 153.59,133.43C152.92,131.97 151.81,126.06 151.81,126.06C151.81,126.06 149.57,128.62 143.47,128.17L143.47,128.16Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.73,119.76C161.73,119.76 148.23,113.3 147.74,112.4C147.25,111.51 142.59,107.12 141.61,106.63C140.62,106.13 137.89,106.05 137.04,107.52C136.19,109 136.01,111.7 140.26,112.72C140.26,112.72 142.55,115.36 143.46,115.71C144.38,116.07 148.63,117.32 150.15,119.21C151.68,121.09 157.49,128.84 162.69,128.16C167.88,127.48 168.51,125.16 168.51,125.16L161.73,119.76L161.73,119.76Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#F4F4F4"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M158.03,160.71C158.03,160.71 174.53,159.92 179.5,156.8C179.5,156.8 157.48,152.96 158.03,160.71Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.52,81.28C152.52,81.28 150.37,77.36 154.46,75.53C154.46,75.53 154.79,73.47 157.46,73.36C160.13,73.26 165.87,72.14 169.18,77.21C169.91,78.32 170.09,79.59 170.09,80.89C170.09,82.06 169.83,83.22 169.93,84.38C170.03,85.45 170.53,86.41 171.25,87.19C172.09,88.12 173.15,88.68 174.29,89.19C175.21,89.61 176.23,90.04 176.82,90.96C177.46,91.97 177.5,92.93 177.32,94.13C177.08,95.72 176.05,97.03 174.84,98.03C174.48,98.33 174.1,98.6 173.71,98.86C173.71,98.86 173.5,98.05 170.82,97.03C166.83,95.51 166.77,97.77 164.34,92.17L162.61,88.16C162.61,88.16 165.87,87.44 165.42,85.26C164.97,83.08 163.1,83.5 161.9,85.08C161.9,85.08 161.01,85.12 160.54,84.06C160.07,82.99 154.47,80.72 154.81,76.8L152.52,81.29L152.52,81.28Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M176.97,122.09C176.97,122.09 186.25,132.33 186.09,138.48C185.92,144.64 179.62,148.68 179.62,148.68"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M176.97,122.09C176.97,122.09 186.25,132.33 186.09,138.48C185.92,144.64 179.62,148.68 179.62,148.68"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.67,145.8C161.67,145.8 167.42,146.83 168.51,147.41"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M185.66,151.18C185.66,151.18 196.11,153.31 197.17,153.26L196.76,158.9C196.76,158.9 176.11,157.46 172.68,157.32"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M185.66,151.18C185.66,151.18 196.11,153.31 197.17,153.26L196.76,158.9C196.76,158.9 176.11,157.46 172.68,157.32"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M197.17,153.26C197.17,153.26 197.68,151.34 200.63,150.98C200.63,150.98 203.38,152.61 204.09,152.61C204.81,152.61 207.66,152.92 209.54,155.92C211.42,158.93 213.08,162.31 208.54,162.62"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M197.17,153.26C197.17,153.26 197.68,151.34 200.63,150.98C200.63,150.98 203.38,152.61 204.09,152.61C204.81,152.61 207.66,152.92 209.54,155.92C211.42,158.93 213.08,162.31 208.54,162.62"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M196.75,158.9V153.23C196.75,153.23 195.92,153.09 196.79,152.6C197.65,152.11 200.12,152.39 201.29,152.93C202.46,153.46 203.29,154.34 205.38,154.44C207.48,154.53 211.3,160.26 211.12,161.7C211.12,161.7 210.9,162.19 210.86,162.27C210.26,163.6 207.26,163.36 205.85,162.96L196.76,158.9L196.75,158.9Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M90.93,55.07V55.45C90.93,55.97 90.51,56.4 89.98,56.4H48.29C47.76,56.4 47.34,55.97 47.34,55.45V55.07C47.34,54.75 47.5,54.47 47.75,54.29C47.96,53.68 48.55,53.23 49.24,53.23C49.87,53.23 50.41,53.6 50.67,54.13H51.78C52.32,50.91 55.12,48.46 58.49,48.46C61.86,48.46 64.65,50.91 65.19,54.13H66.11C66.77,52.07 68.69,50.59 70.96,50.59C73.24,50.59 75.15,52.07 75.81,54.13H89.98C90.51,54.13 90.94,54.55 90.94,55.07H90.93Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M214.38,76.23C215.12,76.23 215.72,75.63 215.72,74.89C215.72,74.14 215.12,73.54 214.38,73.54C213.63,73.54 213.03,74.14 213.03,74.89C213.03,75.63 213.63,76.23 214.38,76.23Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M189.25,75.08V75.49C189.25,75.75 189.46,75.95 189.71,75.95H211.1C211.19,75.95 211.27,75.92 211.34,75.88C211.47,75.8 211.55,75.65 211.55,75.49V75.08C211.55,74.88 211.42,74.7 211.23,74.65C210.61,71.11 207.53,68.42 203.81,68.42C200.1,68.42 197.02,71.1 196.4,74.63H195.26C194.67,73.68 193.62,73.06 192.42,73.06C191.22,73.06 190.16,73.7 189.57,74.65C189.38,74.71 189.25,74.88 189.25,75.08L189.25,75.08Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M162.69,128.16C162.69,128.16 160.66,136.69 158.97,137.79C157.28,138.88 148.09,142.43 144.38,147.99C140.67,153.56 140.12,162.25 158.04,161.49C158.04,161.49 157.28,157.86 163.69,157.69C168.89,157.55 172.55,159.31 176.35,154.32C180.14,149.32 186.38,150.62 188.58,151.46C188.58,151.46 172.47,145.38 158.21,148.16"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M162.69,128.16C162.69,128.16 160.66,136.69 158.97,137.79C157.28,138.88 148.09,142.43 144.38,147.99C140.67,153.56 140.12,162.25 158.04,161.49C158.04,161.49 157.28,157.86 163.69,157.69C168.89,157.55 172.55,159.31 176.35,154.32C180.14,149.32 186.38,150.62 188.58,151.46C188.58,151.46 172.47,145.38 158.21,148.16"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M202.02,134.2V136.85L202.82,137.5C202.82,137.5 204.18,150.35 207.12,151.42C210.06,152.5 226.53,152.36 229.56,151.98C232.59,151.6 234.45,137.2 234.45,137.2L235.27,136.68V134.2C235.27,134.2 218.99,135.6 202.02,134.2V134.2Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#ffffff"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M203.4,137.95C203.4,137.95 206.36,147.33 207.54,148.68C208.71,150.04 228.38,148.91 229.91,151.32V151.6C229.91,151.6 209.49,152.88 207.08,151.15C207.08,151.15 203.88,149.92 203.4,137.95L203.4,137.95Z"
        android:fillColor="#EFEFEF"/>
    <path
        android:pathData="M205.58,138.07C205.58,138.07 217.1,139.93 230.48,138.07"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M202.85,135.4C202.85,135.4 221.89,136.89 235.27,135.03"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M205.58,134.2C205.09,131.2 204.97,128.02 205.43,125.01C205.99,121.44 207.47,118.35 211.23,117.36C213.64,116.72 216.18,116.99 218.67,117.04C220.65,117.07 222.64,116.98 224.59,117.29C233.01,118.63 232.31,127.64 232.07,134.2"
        android:strokeLineJoin="round"
        android:strokeWidth="1.98788"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M202.02,134.2V136.85L202.82,137.5C202.82,137.5 204.18,150.35 207.12,151.42C210.06,152.5 226.53,152.36 229.56,151.98C232.59,151.6 234.45,137.2 234.45,137.2L235.27,136.68V134.2C235.27,134.2 218.99,135.6 202.02,134.2V134.2Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M246.67,135.1C246.67,135.1 248.05,123.55 258.87,122.52C269.69,121.49 273.48,128.01 274.87,135.1H246.67Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M243.83,128.82C243.95,128.83 247.16,128.87 246.92,130.41C246.69,131.95 245.1,138.53 243.78,135.93C242.46,133.33 243.83,128.82 243.83,128.82Z"
        android:fillColor="#5F7C06"/>
    <path
        android:pathData="M244.33,109.98C244.33,109.98 245.79,110.88 246.12,112.67C246.46,114.47 246.35,123.21 246.12,125.68C245.9,128.15 245.79,129.26 245.79,129.26L247.02,129.74L246.26,134.71L244.33,136.39C244.33,136.39 242.9,136.47 243.15,134.12C243.4,131.76 243.12,129.58 243.12,129.58L243.83,128.82C243.83,128.82 245.34,121.75 243.99,119.23L242.65,116.71L243.57,115.28L241.3,117.55"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M247.02,129.74C247.02,129.74 249.71,125.87 249.79,125.28C249.88,124.69 249.79,124.1 249.79,124.1C249.79,124.1 250.63,123.18 251.22,123.18"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M246.26,128.31L246.6,127.22C246.6,127.22 248.96,123.1 249.12,120.83C249.29,118.56 252.49,113.43 252.49,113.43"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M233.15,113.43C233.15,113.43 234.66,116.54 235.75,117.55C236.85,118.56 237.6,122.67 238.86,123.18C240.13,123.68 243.12,129.57 243.12,129.57"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M243.88,109.63C243.88,109.63 241.18,110.45 240.85,107.47C240.51,104.49 243.64,104.49 244.36,105.4C245.08,106.31 246.14,107.56 243.88,109.63Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#FBC300"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M246.67,112.23C246.67,112.23 246.91,108.82 247.48,107.66C248.06,106.51 250.03,104.54 250.42,105.93C250.8,107.32 251.04,109.97 246.67,112.23Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#5F7C06"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M254.41,109.54C254.41,109.54 251.23,110.21 252.82,113C254.41,115.79 257.2,113.33 256.67,111.51C256.14,109.68 254.41,109.54 254.41,109.54Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#FF384F"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M233.15,112.85C233.15,112.85 231.13,112.76 231.27,110.79C231.42,108.81 233.05,108.38 234.02,109.29C234.98,110.21 235.59,111.17 233.14,112.85H233.15Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#FBC300"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M237.43,122.38C237.43,122.38 235.89,120.79 234.11,121.08C232.33,121.37 231.26,122.09 231.26,122.09C231.26,122.09 234.54,124.78 237.43,122.38V122.38Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#5F7C06"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M241.71,116.51C241.71,116.51 237.38,115.64 239.5,113.96C241.62,112.28 243.42,114.29 243.03,115.5"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M241.71,116.51C241.71,116.51 237.38,115.64 239.5,113.96C241.62,112.28 243.42,114.29 243.03,115.5"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M251.71,123.18C251.71,123.18 249.7,121.22 251.91,120.17C254.12,119.11 255.13,121.03 254.55,121.85C253.97,122.67 253.3,123.8 251.71,123.18V123.18Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#FBC300"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M262.9,125.65C266.08,125.64 269.09,127.6 270.4,130.5C270.6,130.94 271.24,130.56 271.05,130.13C269.64,127 266.32,124.89 262.9,124.9C262.42,124.9 262.42,125.65 262.9,125.65Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M139.01,97.09C138.5,96.52 137.73,96.35 137.05,96.08C136.72,95.96 136.44,95.81 136.09,95.74C135.75,95.67 135.38,95.63 135.04,95.49C134.77,95.39 134.63,95.8 134.85,95.95C135.1,96.11 135.38,96.07 135.64,96.19C135.79,96.25 135.93,96.39 136.06,96.48C136.2,96.58 136.33,96.68 136.48,96.77C136.75,96.95 137.04,97.1 137.35,97.22C137.63,97.33 137.92,97.43 138.13,97.64L138.31,97.22C137.85,97.21 137.38,96.95 136.96,96.77C136.5,96.59 136.04,96.38 135.61,96.15C135.33,96 135.08,96.42 135.36,96.58C136.18,97.05 137.01,97.51 137.83,97.97L137.95,97.51C137.48,97.5 137.05,97.31 136.62,97.13C136.5,97.08 136.35,97.19 136.32,97.31C136.27,97.45 136.37,97.56 136.49,97.61C136.96,97.81 137.43,97.99 137.95,98.01C138.2,98.02 138.29,97.67 138.08,97.55C137.25,97.08 136.43,96.62 135.61,96.15L135.35,96.58C135.83,96.83 136.32,97.05 136.82,97.26C137.32,97.46 137.79,97.71 138.31,97.72C138.52,97.72 138.63,97.45 138.48,97.29C138.26,97.07 137.97,96.94 137.68,96.82C137.37,96.69 137.07,96.56 136.78,96.38C136.49,96.2 136.23,95.95 135.94,95.78C135.68,95.63 135.35,95.69 135.1,95.52L134.91,95.97C135.22,96.1 135.55,96.14 135.87,96.2C136.2,96.26 136.49,96.39 136.8,96.52C137.43,96.78 138.18,96.92 138.65,97.45C138.87,97.68 139.22,97.33 139.01,97.09H139.01Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M120.16,94.98C119.35,95.13 118.52,95.27 117.73,95.52C117.27,95.66 116.83,95.86 116.38,96.03C116.13,96.11 116.15,96.53 116.44,96.51C117.4,96.46 118.35,96.35 119.28,96.09L119.08,95.64C118.22,96.14 117.26,96.39 116.3,96.63C116.07,96.68 116.06,97.06 116.3,97.11C117.11,97.25 117.93,97.19 118.7,96.94L118.57,96.46C117.78,96.75 116.95,97 116.11,97.08C115.79,97.11 115.79,97.61 116.11,97.58C117,97.5 117.87,97.24 118.7,96.94C119,96.83 118.88,96.36 118.57,96.46C117.88,96.69 117.15,96.76 116.43,96.63V97.11C117.43,96.85 118.44,96.59 119.34,96.07C119.58,95.93 119.42,95.54 119.15,95.61C118.26,95.86 117.36,95.96 116.45,96.01L116.51,96.5C116.97,96.34 117.41,96.14 117.87,96C118.66,95.75 119.48,95.61 120.3,95.45C120.61,95.39 120.48,94.91 120.17,94.97L120.16,94.98Z"
        android:fillColor="#FF0047"/>
    <path
        android:pathData="M202.39,170.22C201.26,170.47 200.51,171.36 201.17,172.45C201.27,172.62 201.5,172.69 201.68,172.58C203.18,171.68 202.04,170.23 200.59,170.33C199.73,170.4 197.41,172.12 199.15,172.6C199.27,172.63 199.43,172.6 199.51,172.5C200.46,171.38 198.75,170.45 197.75,170.46C196.69,170.47 194.53,171.76 195.94,172.87C196.08,172.98 196.24,173.02 196.4,172.93C197.59,172.27 196.23,170.89 195.34,170.76C194.09,170.57 192.39,171.28 192.75,172.76C192.86,173.23 193.58,173.03 193.46,172.56C193.67,171.89 194.06,171.54 194.62,171.52C195.19,171.36 195.66,171.61 196.02,172.28L196.47,172.34C196.65,171.54 197.08,171.16 197.75,171.2C198.16,171.18 198.53,171.3 198.85,171.55C199.26,171.85 199.61,171.78 199.87,171.35C200.46,170.9 200.93,171.09 201.3,171.94L201.81,172.07C201.35,171.31 202.15,171.04 202.59,170.94C203.06,170.84 202.86,170.12 202.39,170.22Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M193.71,172.58C193.86,171.45 193,170.15 191.75,170.26C190.76,170.35 189.76,171.15 190.08,172.19C190.16,172.46 190.49,172.55 190.7,172.35C191.88,171.23 190.06,170.02 188.92,170.23C188.11,170.37 186.04,172 187.7,172.45C187.82,172.49 187.98,172.46 188.06,172.36C189.01,171.23 187.29,170.31 186.3,170.32C185.23,170.32 183.07,171.61 184.49,172.73C184.62,172.83 184.78,172.87 184.94,172.78C186.13,172.12 184.77,170.75 183.89,170.61C182.63,170.42 180.93,171.14 181.29,172.61C181.41,173.08 182.13,172.88 182.01,172.41C182.22,171.74 182.6,171.39 183.17,171.38C183.74,171.21 184.2,171.47 184.57,172.14L185.02,172.19C185.2,171.39 185.62,171.01 186.3,171.06C186.71,171.04 187.07,171.16 187.4,171.41C187.81,171.71 188.15,171.64 188.42,171.21C189.07,170.74 189.65,170.94 190.17,171.82L190.8,171.99C190.96,171.07 191.54,170.84 192.54,171.29C192.96,171.67 193.1,172.1 192.97,172.58C192.91,173.05 193.65,173.05 193.71,172.58L193.71,172.58Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M182.3,172.74C182.4,171.63 181.38,170.29 180.18,170.37C179.15,170.44 178.32,171.33 178.67,172.35C178.76,172.62 179.08,172.71 179.29,172.51C180.47,171.39 178.65,170.18 177.51,170.39C176.7,170.53 174.63,172.16 176.29,172.61C176.41,172.65 176.57,172.62 176.65,172.52C177.6,171.39 175.88,170.47 174.89,170.48C173.82,170.48 171.66,171.77 173.08,172.89C173.21,172.99 173.37,173.03 173.53,172.94C174.73,172.28 173.36,170.91 172.48,170.77C171.22,170.58 169.52,171.3 169.88,172.77C170,173.24 170.72,173.04 170.6,172.58C170.81,171.9 171.19,171.55 171.76,171.54C172.33,171.38 172.79,171.63 173.16,172.3L173.61,172.35C173.79,171.55 174.21,171.17 174.89,171.22C175.3,171.2 175.66,171.32 175.99,171.57C176.4,171.87 176.74,171.8 177.01,171.37C177.66,170.9 178.24,171.1 178.76,171.98L179.39,172.15C180.1,171.23 180.68,171.04 181.1,171.58C181.42,171.87 181.57,172.26 181.56,172.74C181.52,173.21 182.26,173.21 182.3,172.74V172.74Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M170.84,172.7C171.26,169.85 166.35,169.52 167.21,172.21C167.3,172.47 167.63,172.57 167.84,172.37C169.02,171.24 167.19,170.04 166.06,170.24C165.25,170.39 163.18,172.02 164.84,172.47C164.95,172.5 165.11,172.47 165.2,172.37C166.15,171.25 164.43,170.32 163.43,170.33C162.37,170.34 160.21,171.63 161.63,172.74C161.76,172.85 161.92,172.89 162.08,172.8C163.27,172.14 161.91,170.76 161.02,170.63C159.77,170.44 158.07,171.15 158.43,172.63C158.55,173.1 159.26,172.9 159.15,172.43C159.35,171.76 159.74,171.41 160.31,171.39C160.88,171.23 161.34,171.48 161.7,172.15L162.16,172.21C162.33,171.41 162.76,171.03 163.44,171.07C163.84,171.05 164.21,171.17 164.54,171.42C164.95,171.72 165.29,171.65 165.56,171.22C166.2,170.75 166.79,170.96 167.31,171.84L167.93,172C168.18,171.07 168.65,170.8 169.34,171.18C169.81,171.46 170.07,171.9 170.12,172.49C170.05,172.96 170.77,173.16 170.84,172.69V172.7Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M159.48,172.71C159.59,171.65 158.62,170.35 157.46,170.42C156.48,170.49 155.52,171.31 155.84,172.32C155.93,172.59 156.26,172.68 156.47,172.49C157.65,171.36 155.82,170.15 154.69,170.36C153.88,170.51 151.81,172.13 153.46,172.59C153.58,172.62 153.74,172.59 153.83,172.49C154.78,171.37 153.06,170.44 152.06,170.45C151,170.46 148.84,171.75 150.26,172.86C150.39,172.97 150.55,173.01 150.71,172.92C151.25,172.62 151.22,172.07 151,171.53C150.7,170.8 150.07,170.48 149.32,170.37C147.98,170.17 146.74,171.46 147.33,172.76C147.53,173.19 148.17,172.81 147.98,172.38C147.75,171.88 148.25,171.36 148.63,171.18C149.21,170.92 150.94,171.94 150.33,172.27L150.79,172.33C150.96,171.53 151.39,171.15 152.07,171.19C152.47,171.17 152.84,171.29 153.17,171.54C153.58,171.84 153.92,171.77 154.18,171.34C154.83,170.87 155.42,171.07 155.94,171.96L156.56,172.12C156.21,171 158.91,171.1 158.74,172.71C158.68,173.19 159.43,173.18 159.48,172.71L159.48,172.71Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M140.08,171.39C140.28,171.33 140.48,171.3 140.69,171.28C141.23,171.17 141.76,171.28 142.28,171.58L142.34,171.52C143.07,170.71 143.78,170.81 144.49,171.82L145.11,171.98C144.66,170.53 147.57,170.76 147.29,172.47C147.22,172.94 147.94,173.14 148.01,172.67C148.2,171.49 147.51,170.25 146.19,170.13C144.86,170 144.02,170.96 144.39,172.18C144.47,172.45 144.81,172.54 145.02,172.35C146.21,171.2 144.35,170 143.21,170.23C142.42,170.38 140.35,171.99 142.02,172.44C142.14,172.48 142.29,172.45 142.38,172.35C142.81,171.84 142.68,171.21 142.13,170.8C141.51,170.31 140.59,170.51 139.88,170.67C139.41,170.77 139.61,171.49 140.08,171.38L140.08,171.39Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M140.32,170.65C139.19,170.9 138.43,171.79 139.1,172.88C139.2,173.05 139.43,173.12 139.61,173.01C141.11,172.11 139.96,170.66 138.52,170.76C137.65,170.82 135.34,172.55 137.08,173.02C137.2,173.06 137.36,173.03 137.44,172.93C138.39,171.8 136.67,170.88 135.68,170.89C134.61,170.89 132.46,172.18 133.87,173.3C134.01,173.4 134.16,173.44 134.32,173.35C135.6,172.65 133.99,170.94 133.06,170.79C132.22,170.65 131.54,170.85 130.99,171.5C130.49,172.1 130.57,172.51 130.95,173.19C131.18,173.62 131.82,173.24 131.59,172.82C131.23,172.14 131.61,171.83 132.22,171.64C132.69,171.38 133.15,171.45 133.6,171.86C133.96,172.1 134.07,172.39 133.95,172.71L134.4,172.77C134.57,171.96 135,171.59 135.68,171.63C136.09,171.61 136.45,171.73 136.78,171.98C137.19,172.28 137.53,172.21 137.8,171.78C138.38,171.32 138.86,171.52 139.23,172.37L139.74,172.5C139.27,171.74 140.08,171.47 140.52,171.37C140.99,171.26 140.79,170.55 140.32,170.65L140.32,170.65Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M131.64,173.01C131.76,171.99 130.81,170.67 129.72,170.63C128.7,170.6 127.73,171.63 128.01,172.62C128.08,172.89 128.43,172.98 128.63,172.78C129.81,171.66 127.99,170.45 126.85,170.65C126.04,170.8 123.97,172.43 125.63,172.88C125.75,172.91 125.9,172.88 125.99,172.78C126.94,171.66 125.22,170.74 124.22,170.74C123.16,170.75 121,172.04 122.42,173.15C122.55,173.26 122.71,173.3 122.87,173.21C124.06,172.55 122.7,171.18 121.82,171.04C120.56,170.85 118.86,171.56 119.22,173.04C119.34,173.51 120.06,173.31 119.94,172.84C120.14,172.17 120.53,171.82 121.1,171.8C121.67,171.64 122.13,171.9 122.5,172.57L122.95,172.62C123.12,171.82 123.55,171.44 124.23,171.49C124.64,171.47 125,171.58 125.33,171.84C125.74,172.13 126.08,172.07 126.35,171.64C127,171.16 127.58,171.37 128.1,172.25L128.73,172.41C128.36,171.09 131.08,171.47 130.9,173C130.84,173.48 131.59,173.48 131.64,173L131.64,173.01Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M119.94,173.04C120.19,171.95 119.92,170.56 118.48,170.66C117.43,170.74 116.28,171.66 116.59,172.78C116.67,173.05 117.01,173.13 117.21,172.94C118.4,171.81 116.57,170.61 115.44,170.81C114.63,170.96 112.56,172.59 114.21,173.04C114.33,173.07 114.49,173.04 114.58,172.94C115.53,171.82 113.81,170.89 112.81,170.9C111.75,170.91 109.59,172.2 111.01,173.31C111.14,173.42 111.3,173.46 111.46,173.37C112.01,173.07 111.95,172.53 111.75,171.99C111.46,171.19 110.89,170.78 110.07,170.61C109.43,170.48 109,170.55 108.59,171.06C108.18,171.57 107.62,172.25 107.86,172.93C108.03,173.38 108.75,173.19 108.58,172.73C108.49,172.49 108.86,171.95 109,171.8C109.25,171.54 109.69,171.32 110.05,171.39C110.26,171.43 111.63,172.42 111.08,172.73L111.53,172.78C111.71,171.98 112.13,171.6 112.81,171.65C113.22,171.63 113.58,171.74 113.91,171.99C114.32,172.29 114.66,172.23 114.93,171.8C115.58,171.32 116.16,171.53 116.68,172.41L117.31,172.57C116.92,171.21 119.57,171.31 119.22,172.84C119.11,173.31 119.83,173.51 119.94,173.04L119.94,173.04Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M108.58,172.94C108.79,171.89 108.46,170.43 107.1,170.5C106.05,170.55 104.87,171.54 105.14,172.63C105.21,172.91 105.57,172.99 105.76,172.8C106.94,171.67 105.12,170.47 103.99,170.67C103.18,170.82 101.11,172.45 102.76,172.9C102.88,172.93 103.04,172.9 103.13,172.8C104.08,171.68 102.36,170.75 101.36,170.76C100.3,170.77 98.14,172.06 99.56,173.17C99.69,173.28 99.85,173.32 100.01,173.23C101.2,172.57 99.84,171.19 98.95,171.06C97.7,170.87 96,171.58 96.36,173.06C96.47,173.53 97.19,173.33 97.08,172.86C97.28,172.19 97.67,171.84 98.24,171.82C98.81,171.66 99.27,171.91 99.63,172.58L100.08,172.64C100.26,171.84 100.69,171.46 101.36,171.5C101.77,171.49 102.14,171.6 102.47,171.85C102.88,172.15 103.22,172.08 103.48,171.65C104.13,171.18 104.72,171.38 105.24,172.27L105.86,172.43L106.35,171.57C106.58,171.36 106.86,171.32 107.16,171.44C107.94,171.5 108.02,172 107.87,172.73C107.77,173.2 108.49,173.4 108.58,172.93L108.58,172.94Z"
        android:fillColor="#FBC300"/>
    <path
        android:pathData="M186.67,148.68L188.57,146.81C188.57,146.81 190.99,148.85 193.1,149.2C194.28,149.4 195.37,149.88 196.1,150.26C196.69,150.57 197.19,151.02 197.57,151.57L197.81,151.91L196.97,153.26"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M186.67,148.68L188.57,146.81C188.57,146.81 190.99,148.85 193.1,149.2C194.28,149.4 195.37,149.88 196.1,150.26C196.69,150.57 197.19,151.02 197.57,151.57L197.81,151.91L196.97,153.26"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M184.38,149.9L184.78,149.54L186.75,147.81C186.75,147.81 191.64,150.77 193.6,150.74C195.68,150.7 196.97,153.26 196.97,153.26C196.97,153.26 188.03,151.7 186.75,151.44"
        android:fillColor="#000000"/>
    <path
        android:pathData="M66.8,78.68V134.48"
        android:strokeLineJoin="round"
        android:strokeWidth="1.49091"
        android:fillColor="#00000000"
        android:strokeColor="#5F7C06"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.25,97.33C74.25,97.33 69.54,103.27 67.27,102.24"
        android:strokeLineJoin="round"
        android:strokeWidth="1.49091"
        android:fillColor="#00000000"
        android:strokeColor="#5F7C06"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.16,102.8C59.16,102.8 62.27,107.7 66.33,108.27"
        android:strokeLineJoin="round"
        android:strokeWidth="1.49091"
        android:fillColor="#00000000"
        android:strokeColor="#5F7C06"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.05,88.17C78.53,87.66 80.38,87.82 81.65,88.78C83.01,89.82 83.38,91.81 83.06,93.41C82.62,95.65 80.8,97.42 78.56,97.8C73.8,98.61 73.65,98.05 73.65,98.05C73.39,96.75 73.26,95.39 73.31,94.07C73.42,91.36 74.55,89.03 77.05,88.17L77.05,88.17Z"
        android:fillColor="#5F7C06"/>
    <path
        android:pathData="M50.74,98.85C50.48,97.3 50.95,95.5 52.11,94.41C53.35,93.24 55.38,93.2 56.91,93.78C59.04,94.59 60.48,96.67 60.49,98.95C60.5,103.78 59.92,103.83 59.92,103.83C58.6,103.88 57.24,103.78 55.94,103.51C53.29,102.96 51.17,101.45 50.74,98.85V98.85Z"
        android:fillColor="#5F7C06"/>
    <path
        android:pathData="M58.68,85.64C58.22,84.61 58.22,83.28 58.84,82.32C59.49,81.28 60.89,80.9 62.05,81.03C63.67,81.21 65.05,82.4 65.46,83.98C66.33,87.32 65.94,87.46 65.94,87.46C65.03,87.73 64.06,87.9 63.12,87.94C61.18,88.03 59.44,87.37 58.68,85.64H58.68Z"
        android:fillColor="#5F7C06"/>
    <path
        android:pathData="M56.25,99.44C56.94,100.37 57.56,101.36 58.09,102.4C58.23,102.69 58.66,102.43 58.52,102.15C57.99,101.11 57.37,100.12 56.68,99.18C56.6,99.08 56.47,99.02 56.34,99.1C56.23,99.16 56.17,99.33 56.25,99.44Z"
        android:fillColor="#FBFAFB"/>
    <path
        android:pathData="M77.32,93.58C76.64,94.45 75.97,95.32 75.29,96.19C75.21,96.3 75.18,96.44 75.29,96.54C75.38,96.63 75.56,96.65 75.64,96.54C76.32,95.67 76.99,94.8 77.67,93.93C77.75,93.82 77.78,93.68 77.67,93.58C77.58,93.49 77.4,93.47 77.32,93.58Z"
        android:fillColor="#FBFAFB"/>
    <path
        android:pathData="M63.21,85.52C63.71,85.95 64.26,86.3 64.86,86.55C64.98,86.6 65.13,86.59 65.2,86.46C65.25,86.36 65.23,86.18 65.11,86.12C64.82,86 64.55,85.86 64.28,85.69C64.02,85.53 63.79,85.36 63.56,85.17C63.46,85.08 63.31,85.07 63.21,85.17C63.12,85.26 63.11,85.43 63.21,85.52V85.52Z"
        android:fillColor="#FBFAFB"/>
    <path
        android:pathData="M57.07,135.42C57.07,135.42 57.26,127.29 66.8,127.32C75.83,127.35 76.32,135.42 76.32,135.42H57.07Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M244.19,47.65C248.45,47.76 250.52,52.92 247.59,56.01L244.34,59.43L239.44,64.58C238.74,65.33 237.55,65.32 236.85,64.57L232.04,59.43L228.82,55.98C225.96,52.93 227.94,47.88 232.12,47.66C232.22,47.65 232.33,47.65 232.43,47.65C234.87,47.65 236.96,49.13 237.86,51.25C238,51.58 238.47,51.58 238.61,51.25C239.51,49.13 241.6,47.65 244.04,47.65C244.09,47.65 244.14,47.65 244.19,47.65L244.19,47.65Z"
        android:fillColor="#FF384F"/>
    <path
        android:pathData="M258.21,34.63C260.56,34.69 261.71,37.54 260.09,39.25L258.29,41.14L255.58,43.99C255.19,44.4 254.54,44.4 254.15,43.99L251.48,41.14L249.7,39.23C248.12,37.54 249.22,34.75 251.53,34.63C251.59,34.63 251.64,34.63 251.7,34.63C253.05,34.63 254.21,35.44 254.7,36.61C254.78,36.8 255.04,36.8 255.12,36.61C255.62,35.44 256.77,34.63 258.12,34.63C258.15,34.63 258.18,34.63 258.21,34.63L258.21,34.63Z"
        android:fillColor="#FF384F"/>
    <path
        android:pathData="M154.24,83.03C154.13,83.21 154.08,83.41 154.06,83.62C154.03,83.81 154.05,84.02 154.12,84.2C154.15,84.3 154.21,84.38 154.32,84.42C154.36,84.45 154.4,84.46 154.45,84.45C154.51,84.45 154.57,84.44 154.63,84.41C154.84,84.31 155,84.1 155.03,83.87C155.05,83.76 155.05,83.66 155.05,83.56C155.04,83.45 155.02,83.35 155.01,83.24L155.02,83.33C155.01,83.3 155.01,83.26 155.01,83.23C155,83.15 154.98,83.08 154.93,83.02C154.89,82.96 154.83,82.91 154.75,82.89C154.6,82.86 154.44,82.92 154.36,83.05C154.28,83.17 154.21,83.31 154.19,83.45C154.15,83.64 154.16,83.83 154.16,84.01C154.16,84.2 154.32,84.37 154.51,84.36C154.69,84.35 154.85,84.21 154.85,84.01C154.85,83.86 154.85,83.72 154.87,83.57L154.85,83.66C154.86,83.61 154.88,83.55 154.9,83.5L154.86,83.58C154.89,83.52 154.93,83.46 154.96,83.4L154.31,83.22C154.32,83.33 154.34,83.43 154.35,83.53L154.34,83.44C154.35,83.55 154.37,83.66 154.35,83.77L154.36,83.68C154.36,83.72 154.35,83.76 154.33,83.8L154.37,83.72C154.35,83.75 154.34,83.78 154.32,83.81L154.37,83.74C154.35,83.76 154.33,83.78 154.31,83.8L154.38,83.75C154.35,83.77 154.31,83.79 154.28,83.8L154.79,84.01C154.77,83.95 154.75,83.89 154.74,83.82L154.76,83.91C154.74,83.82 154.74,83.72 154.76,83.62L154.74,83.71C154.76,83.61 154.78,83.51 154.82,83.41L154.79,83.49C154.81,83.45 154.83,83.41 154.85,83.38C154.94,83.22 154.89,82.99 154.72,82.9C154.56,82.81 154.35,82.86 154.25,83.02L154.24,83.03Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M154.54,83.55C154.78,83.56 155.03,83.59 155.27,83.56C155.36,83.55 155.44,83.53 155.51,83.46C155.57,83.4 155.62,83.3 155.61,83.22C155.61,83.13 155.58,83.03 155.51,82.97C155.45,82.91 155.35,82.86 155.27,82.87C155.03,82.89 154.78,82.86 154.54,82.86C154.46,82.85 154.36,82.9 154.3,82.96C154.24,83.02 154.19,83.12 154.2,83.2C154.21,83.4 154.35,83.54 154.54,83.55Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M153.56,90.03C153.59,90.02 153.63,90.02 153.67,90.01C154.3,89.89 154.85,89.73 155.11,88.73L155.27,87.74C155.02,88.03 153.74,88.47 153.37,88.55"
        android:fillColor="#000000"/>
    <path
        android:pathData="M153.56,90.27C154.08,90.22 154.62,90.06 154.95,89.64C155.14,89.39 155.29,89.09 155.35,88.79C155.38,88.63 155.4,88.47 155.42,88.31C155.45,88.14 155.48,87.98 155.5,87.81C155.52,87.7 155.49,87.59 155.39,87.53C155.3,87.47 155.16,87.48 155.09,87.57C155.07,87.59 155.05,87.6 155.03,87.62C155.01,87.64 155.07,87.59 155.04,87.62C155.02,87.63 155,87.64 154.99,87.65C154.92,87.69 154.85,87.73 154.77,87.77C154.6,87.86 154.44,87.93 154.27,88C153.96,88.12 153.63,88.24 153.3,88.31C152.99,88.38 153.13,88.86 153.44,88.79C153.79,88.72 154.13,88.58 154.46,88.45C154.66,88.37 154.86,88.29 155.05,88.19C155.19,88.12 155.34,88.03 155.44,87.92L155.03,87.68C154.98,87.99 154.93,88.29 154.88,88.6C154.85,88.8 154.77,89 154.67,89.17C154.61,89.29 154.56,89.34 154.47,89.43C154.45,89.45 154.43,89.47 154.41,89.48C154.38,89.51 154.42,89.48 154.38,89.51C154.33,89.53 154.28,89.56 154.24,89.59C154.22,89.6 154.16,89.63 154.17,89.62C154.18,89.62 154.12,89.64 154.1,89.64C154.05,89.66 153.99,89.68 153.94,89.7C153.82,89.73 153.68,89.76 153.56,89.78C153.43,89.79 153.31,89.88 153.31,90.03C153.31,90.15 153.43,90.29 153.56,90.27H153.56Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M136.13,83.39V84.22C136.13,84.36 136.18,84.5 136.28,84.59C136.38,84.68 136.52,84.75 136.65,84.74C136.78,84.74 136.92,84.69 137.01,84.59C137.11,84.49 137.17,84.36 137.17,84.22V83.39C137.17,83.25 137.11,83.11 137.01,83.02C136.92,82.92 136.78,82.86 136.65,82.86C136.52,82.87 136.37,82.91 136.28,83.02C136.19,83.12 136.13,83.25 136.13,83.39Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M134.97,89.27C135.04,89.27 135.11,89.27 135.18,89.28C135.21,89.28 135.24,89.28 135.26,89.28C135.19,89.27 135.39,89.3 135.26,89.28C135.38,89.31 135.5,89.33 135.63,89.35C135.94,89.41 136.26,89.47 136.59,89.48C136.91,89.49 137.23,89.46 137.55,89.46C137.87,89.46 138.22,89.47 138.52,89.36C138.78,89.26 138.96,89 138.89,88.71C138.82,88.46 138.51,88.25 138.24,88.35C138.18,88.37 138.12,88.38 138.06,88.4C138.1,88.39 138.12,88.4 138.06,88.4C138.02,88.4 137.98,88.41 137.94,88.41C137.79,88.42 137.65,88.42 137.5,88.41C137.18,88.41 136.86,88.45 136.54,88.43C136.46,88.43 136.39,88.42 136.32,88.41C136.26,88.41 136.27,88.41 136.32,88.41C136.28,88.41 136.25,88.4 136.21,88.4C136.09,88.38 135.96,88.35 135.83,88.33C135.55,88.27 135.26,88.23 134.98,88.22C134.69,88.22 134.44,88.46 134.45,88.74C134.46,89.03 134.68,89.26 134.98,89.27H134.97Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M134.46,89.41C134.71,89.79 135.08,90.12 135.49,90.32C135.99,90.56 136.57,90.73 137.13,90.68C137.5,90.64 137.87,90.46 137.97,90.07C138.08,89.68 137.85,89.31 137.47,89.18C137.23,89.1 136.96,89.13 136.72,89.12C136.6,89.12 136.49,89.12 136.37,89.11C136.31,89.11 136.25,89.1 136.18,89.1C136.1,89.09 136.27,89.12 136.18,89.1C136.15,89.09 136.11,89.09 136.07,89.08C136.01,89.06 135.94,89.05 135.87,89.02C135.86,89.02 135.77,88.99 135.83,89.01C135.89,89.03 135.78,88.98 135.77,88.98C135.64,88.92 135.53,88.84 135.4,88.79C135.35,88.76 135.29,88.74 135.23,88.72C135.2,88.71 135.11,88.65 135.21,88.71C135.26,88.75 135.38,88.87 135.38,88.94C135.38,88.93 135.37,88.9 135.37,88.88C135.36,88.86 135.36,88.84 135.35,88.83C135.34,88.78 135.36,88.96 135.36,88.87C135.36,88.85 135.36,88.83 135.36,88.82C135.34,88.74 135.33,88.95 135.35,88.89C135.44,88.63 135.24,88.3 134.98,88.25C134.69,88.18 134.45,88.34 134.34,88.61C134.28,88.78 134.32,89.02 134.37,89.19C134.45,89.49 134.67,89.63 134.96,89.73C134.86,89.7 134.99,89.74 135.02,89.76C135.06,89.78 135.11,89.81 135.16,89.84C135.29,89.91 135.42,89.97 135.55,90.02C135.79,90.1 136.05,90.14 136.3,90.16C136.52,90.17 136.75,90.17 136.97,90.17C137.02,90.17 137.07,90.17 137.12,90.17C137.13,90.17 137.18,90.18 137.19,90.18C137.2,90.17 137.08,90.19 137.13,90.17C137.14,90.17 137.17,90.19 137.18,90.19C137.16,90.2 137.07,90.13 137.05,90.11C136.95,90 136.91,89.79 137.04,89.67C137.05,89.66 137.14,89.61 137.05,89.66C137.06,89.66 137.14,89.62 137.09,89.64C137.03,89.65 137.12,89.63 137.13,89.64C137.13,89.64 137.06,89.64 137.05,89.64C136.93,89.64 136.82,89.64 136.7,89.63C136.63,89.62 136.71,89.63 136.72,89.63C136.69,89.63 136.66,89.62 136.63,89.61C136.57,89.6 136.52,89.59 136.46,89.58C136.35,89.55 136.24,89.51 136.13,89.47C136.11,89.46 136.04,89.43 136.12,89.47C136.09,89.45 136.06,89.44 136.03,89.43C135.98,89.4 135.92,89.37 135.86,89.34C135.83,89.32 135.79,89.3 135.76,89.28C135.75,89.27 135.71,89.25 135.7,89.24C135.72,89.26 135.77,89.3 135.72,89.25C135.64,89.18 135.56,89.11 135.48,89.03C135.45,88.99 135.42,88.95 135.38,88.92C135.34,88.87 135.41,88.95 135.41,88.96C135.41,88.94 135.37,88.9 135.36,88.88C135.21,88.65 134.89,88.54 134.65,88.7C134.42,88.84 134.3,89.17 134.46,89.41L134.46,89.41Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M139.06,84.03C139.06,84.03 139.96,86.7 139.06,87.4V84.03Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M139.06,84.03C139.06,84.03 139.96,86.7 139.06,87.4"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M138.26,84.13C138.26,84.13 138.81,85.61 138.66,86.24C138.51,86.86 137.86,87.48 137.86,87.48L138.26,84.13Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M152.26,83.78C152.26,83.78 151.18,86.45 152.07,87.15L152.26,83.78Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M152.26,83.78C152.26,83.78 151.18,86.45 152.07,87.15"
        android:strokeLineJoin="round"
        android:strokeWidth="1.24242"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M153,83.62C153,83.62 152.32,85.36 152.47,85.98C152.62,86.61 153.27,87.23 153.27,87.23L153,83.61V83.62Z"
        android:fillColor="#ffffff"/>
  </group>
</vector>
