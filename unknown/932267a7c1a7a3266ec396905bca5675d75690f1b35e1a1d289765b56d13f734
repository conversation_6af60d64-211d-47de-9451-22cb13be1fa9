<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="113dp"
    android:height="112dp"
    android:viewportWidth="113"
    android:viewportHeight="112">
  <path
      android:pathData="M56.5,56m-52,0a52,52 0,1 1,104 0a52,52 0,1 1,-104 0"
      android:strokeWidth="8"
      android:fillColor="#00000000"
      android:strokeColor="#E9E9E9"/>
  <path
      android:pathData="M43.042,5.72C30.386,9.113 19.477,17.16 12.496,28.253C5.515,39.346 2.977,52.666 5.389,65.551C7.801,78.435 14.985,89.934 25.504,97.747C36.024,105.561 49.105,109.113 62.129,107.694C75.154,106.275 87.164,99.989 95.756,90.093C104.348,80.197 108.89,67.421 108.474,54.319C108.057,41.218 102.713,28.756 93.509,19.427C84.305,10.097 71.921,4.588 58.832,4"
      android:strokeWidth="8"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startY="4"
          android:startX="108.5"
          android:endY="118.688"
          android:endX="18.0517"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF7847"/>
        <item android:offset="1" android:color="#FFEE276C"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
