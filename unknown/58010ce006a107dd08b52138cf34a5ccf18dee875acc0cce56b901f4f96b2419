<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="28dp"
    android:height="29dp"
    android:viewportWidth="28"
    android:viewportHeight="29">
    <path
        android:fillType="evenOdd"
        android:pathData="M16.9253,0.5182C22.9113,0.5182 27.7527,5.4491 27.7527,11.5182C27.7527,17.5872 22.9113,22.5182 16.9253,22.5182C14.5399,22.5182 12.3363,21.7352 10.5484,20.4092L3.2473,28.2009C2.6808,28.8054 1.7316,28.8363 1.1271,28.2699C0.5226,27.7034 0.4917,26.7542 1.0581,26.1497L1.0581,26.1497L8.4059,18.3076C6.9598,16.437 6.0979,14.0789 6.0979,11.5182C6.0979,5.4491 10.9393,0.5182 16.9253,0.5182ZM16.9253,3.5182C12.6085,3.5182 9.0979,7.0938 9.0979,11.5182C9.0979,15.9426 12.6085,19.5182 16.9253,19.5182C21.242,19.5182 24.7527,15.9426 24.7527,11.5182C24.7527,7.0938 21.242,3.5182 16.9253,3.5182Z"
        android:strokeWidth="1"
        android:strokeColor="#00000000">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="1.4959834"
                android:endY="28.8363"
                android:startX="26.435997"
                android:startY="0.5182"
                android:type="linear">
                <item
                    android:color="#3C3C43"
                    android:offset="0" />
                <item
                    android:color="#3C3C43"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>